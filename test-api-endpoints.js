/**
 * API Endpoint Testing Script
 * 
 * This script tests all the documented API endpoints to ensure they work correctly.
 * Run this script to verify that the API documentation URLs are accurate.
 */

const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:3002';
const TEST_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'demo123'
};

let authToken = '';

// Test results
const testResults = {
  passed: [],
  failed: [],
  total: 0
};

// Helper function to make authenticated requests
async function makeRequest(method, endpoint, data = null, useAuth = true) {
  const config = {
    method,
    url: `${BASE_URL}${endpoint}`,
    headers: {
      'Content-Type': 'application/json'
    }
  };

  if (useAuth && authToken) {
    config.headers['Authorization'] = `Bearer ${authToken}`;
  }

  if (data) {
    config.data = data;
  }

  try {
    const response = await axios(config);
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    return { 
      success: false, 
      error: error.response?.data || error.message, 
      status: error.response?.status || 500 
    };
  }
}

// Test function
async function testEndpoint(name, method, endpoint, data = null, useAuth = true, expectedStatus = 200) {
  testResults.total++;
  console.log(`\n🧪 Testing: ${name}`);
  console.log(`   ${method} ${endpoint}`);

  const result = await makeRequest(method, endpoint, data, useAuth);
  
  if (result.success && result.status === expectedStatus) {
    console.log(`   ✅ PASSED (${result.status})`);
    testResults.passed.push(name);
    return true;
  } else {
    console.log(`   ❌ FAILED (${result.status})`);
    console.log(`   Error: ${JSON.stringify(result.error, null, 2)}`);
    testResults.failed.push({ name, error: result.error, status: result.status });
    return false;
  }
}

// Main testing function
async function runTests() {
  console.log('🚀 Starting PandaPayroll API Endpoint Tests\n');
  console.log(`Base URL: ${BASE_URL}`);

  // 1. Test Authentication
  console.log('\n📋 AUTHENTICATION TESTS');
  console.log('=' .repeat(50));

  // Health check (no auth required)
  await testEndpoint(
    'Health Check',
    'GET',
    '/api/auth/health',
    null,
    false
  );

  // Login to get token
  console.log('\n🔐 Authenticating...');
  const loginResult = await makeRequest('POST', '/api/auth/login', TEST_CREDENTIALS, false);
  
  if (loginResult.success && loginResult.data.token) {
    authToken = loginResult.data.token;
    console.log('✅ Authentication successful');
    testResults.passed.push('Authentication');
  } else {
    console.log('❌ Authentication failed');
    console.log('Cannot proceed with authenticated tests');
    testResults.failed.push({ name: 'Authentication', error: loginResult.error });
    return;
  }

  // Test protected route
  await testEndpoint(
    'Protected Route Test',
    'GET',
    '/api/auth/test'
  );

  // 2. Test Companies API
  console.log('\n📋 COMPANIES API TESTS');
  console.log('=' .repeat(50));

  await testEndpoint(
    'Get Companies List',
    'GET',
    '/api/companies'
  );

  await testEndpoint(
    'Get Specific Company',
    'GET',
    '/api/companies/DEMO001'
  );

  await testEndpoint(
    'Get Company Employees',
    'GET',
    '/api/companies/DEMO001/employees'
  );

  await testEndpoint(
    'Get Company Employees with Includes',
    'GET',
    '/api/companies/DEMO001/employees?include=bank_details,payroll_periods'
  );

  await testEndpoint(
    'Get Company Payroll Periods',
    'GET',
    '/api/companies/DEMO001/payroll-periods'
  );

  // 3. Test Employee API
  console.log('\n📋 EMPLOYEE API TESTS');
  console.log('=' .repeat(50));

  // Note: These will likely fail if no employees exist, but we test the endpoints
  await testEndpoint(
    'Get Employee Hourly Status',
    'GET',
    '/api/employee/sandbox_emp_1/hourly-status',
    null,
    true,
    404 // Expecting 404 since sandbox employee IDs don't exist in real DB
  );

  // 4. Test Payslips API
  console.log('\n📋 PAYSLIPS API TESTS');
  console.log('=' .repeat(50));

  await testEndpoint(
    'Get Employee Payslips',
    'GET',
    '/api/payslips/employees/sandbox_emp_1/payslips',
    null,
    true,
    404 // Expecting 404 since sandbox employee IDs don't exist
  );

  // 5. Test Pay Runs API
  console.log('\n📋 PAY RUNS API TESTS');
  console.log('=' .repeat(50));

  await testEndpoint(
    'Get Company Pay Runs',
    'GET',
    '/api/pay-runs/companies/DEMO001/pay-runs'
  );

  // 6. Test Leave API
  console.log('\n📋 LEAVE API TESTS');
  console.log('=' .repeat(50));

  await testEndpoint(
    'Get Leave Types',
    'GET',
    '/api/leave/types?companyCode=DEMO001'
  );

  // 7. Test EFT API
  console.log('\n📋 EFT API TESTS');
  console.log('=' .repeat(50));

  await testEndpoint(
    'Get EFT Additional Accounts',
    'GET',
    '/api/eft/additional-accounts'
  );

  // 8. Test Payroll Calendar API
  console.log('\n📋 PAYROLL CALENDAR API TESTS');
  console.log('=' .repeat(50));

  await testEndpoint(
    'Get Payroll Calendar Events',
    'GET',
    '/api/payrollCalendar/events'
  );

  // 9. Test Session API
  console.log('\n📋 SESSION API TESTS');
  console.log('=' .repeat(50));

  await testEndpoint(
    'Get Session Status',
    'GET',
    '/api/session/status'
  );

  // 10. Test Sandbox API
  console.log('\n📋 SANDBOX API TESTS');
  console.log('=' .repeat(50));

  await testEndpoint(
    'Sandbox Health Check',
    'GET',
    '/sandbox/auth/health',
    null,
    false
  );

  await testEndpoint(
    'Sandbox Companies',
    'GET',
    '/sandbox/companies',
    null,
    false
  );

  // Print final results
  console.log('\n' + '=' .repeat(60));
  console.log('📊 TEST RESULTS SUMMARY');
  console.log('=' .repeat(60));
  console.log(`Total Tests: ${testResults.total}`);
  console.log(`Passed: ${testResults.passed.length} ✅`);
  console.log(`Failed: ${testResults.failed.length} ❌`);
  console.log(`Success Rate: ${((testResults.passed.length / testResults.total) * 100).toFixed(1)}%`);

  if (testResults.failed.length > 0) {
    console.log('\n❌ FAILED TESTS:');
    testResults.failed.forEach(failure => {
      console.log(`   - ${failure.name} (Status: ${failure.status})`);
    });
  }

  console.log('\n✅ WORKING ENDPOINTS:');
  testResults.passed.forEach(test => {
    console.log(`   - ${test}`);
  });

  console.log('\n📝 NOTES:');
  console.log('   - Some 404 errors are expected for non-existent resources');
  console.log('   - 501 errors indicate endpoints that are documented but not yet implemented');
  console.log('   - Check the server logs for more detailed error information');
  console.log('\n🎯 Use these results to update the API documentation with working URLs');
}

// Run the tests
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { runTests, testEndpoint, makeRequest };
