# PandaPayroll API Sandbox Environment

## Overview

The PandaPayroll API Sandbox provides a safe, isolated environment for developers to test and experiment with the PandaPayroll API without affecting real data. It includes pre-populated demo data and simulates all core functionality of the production API.

## Quick Start

### Access the Sandbox

**Sandbox Interface:** `http://localhost:3002/sandbox`
**API Base URL:** `http://localhost:3002/sandbox`

### Demo Credentials

```
Email: <EMAIL>
Password: demo123
Company Code: DEMO001
```

## Features

### ✅ **Available in Sandbox**
- Employee management (CRUD operations)
- Payroll calculations with South African tax compliance
- Payslip generation and finalization
- Pay run creation and management
- Leave management system
- Authentication and authorization
- Real-time API testing interface
- South African tax calculations (PAYE, UIF, SDL)

### ❌ **Not Available in Sandbox**
- Email notifications
- WhatsApp integration
- Banking/EFT file generation
- External system integrations (Xero, QuickBooks)
- Production data access

## Pre-loaded Demo Data

### Companies
- **Demo Company Ltd** (DEMO001)
  - Registration: 2023/123456/07
  - Tax Number: TAX123456789
  - UIF Number: UIF123456
  - SDL Number: SDL123456

### Employees
1. **John Doe** (EMP001)
   - Basic Salary: R25,000
   - Status: Active
   - ID: *************

2. **Jane Smith** (EMP002)
   - Basic Salary: R30,000
   - Status: Active
   - ID: *************

### Payroll Periods
- January 2025 (Finalized)
- Pre-calculated PAYE, UIF, SDL deductions

### Leave Types
- Annual Leave (21 days)
- Sick Leave (30 days)
- Maternity Leave (120 days)
- Family Responsibility Leave (3 days)

## API Endpoints

### Authentication
```http
POST /sandbox/auth/login
GET /sandbox/auth/health
```

### Companies
```http
GET /sandbox/companies
GET /sandbox/companies/DEMO001
```

### Employees
```http
GET /sandbox/companies/DEMO001/employees
POST /sandbox/companies/DEMO001/employees
GET /sandbox/companies/DEMO001/employees/:employeeId
PUT /sandbox/companies/DEMO001/employees/:employeeId
```

### Payroll
```http
GET /sandbox/companies/DEMO001/payroll-periods
POST /sandbox/companies/DEMO001/payroll-periods
POST /sandbox/companies/DEMO001/payroll/finalize
```

### Pay Runs
```http
GET /sandbox/companies/DEMO001/pay-runs
POST /sandbox/companies/DEMO001/pay-runs
GET /sandbox/companies/DEMO001/pay-runs/:payRunId
```

### Leave Management
```http
GET /sandbox/companies/DEMO001/leave-types
POST /sandbox/companies/DEMO001/leave-types
GET /sandbox/companies/DEMO001/leave-requests
POST /sandbox/companies/DEMO001/leave-requests
```

### Utilities
```http
GET /sandbox/stats
POST /sandbox/reset
GET /sandbox/health
```

## Usage Examples

### 1. Authentication
```javascript
const response = await fetch('/sandbox/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'demo123'
  })
});

const { token } = await response.json();
```

### 2. Get Employees
```javascript
const response = await fetch('/sandbox/companies/DEMO001/employees', {
  headers: { 'Authorization': `Bearer ${token}` }
});

const employees = await response.json();
```

### 3. Create Pay Run
```javascript
const response = await fetch('/sandbox/companies/DEMO001/pay-runs', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    periodIds: ['sandbox_period_1', 'sandbox_period_2'],
    description: 'February 2025 Pay Run'
  })
});
```

## Testing Interface

The sandbox includes a web-based testing interface at `/sandbox` with:

- **Authentication Panel**: Login and token management
- **API Testing Tabs**: Pre-configured tests for different endpoints
- **Custom API Tester**: Build and send custom requests
- **Response Viewer**: Formatted JSON responses with syntax highlighting
- **Quick Actions**: One-click testing for common operations

## Rate Limiting

Sandbox API requests are rate-limited to:
- **1000 requests per 15 minutes** per IP address
- **No authentication required** for health check endpoints
- **JWT token expires** after 24 hours

## Data Management

### Reset Sandbox Data
```http
POST /sandbox/reset
```

This endpoint resets all sandbox data to its initial state, useful for:
- Starting fresh tests
- Cleaning up after experiments
- Resetting to known state

### Sandbox Statistics
```http
GET /sandbox/stats
```

Returns current sandbox data counts:
```json
{
  "totalCompanies": 1,
  "totalEmployees": 2,
  "totalPayrollPeriods": 2,
  "totalPayRuns": 1,
  "totalLeaveTypes": 4
}
```

## South African Tax Calculations

The sandbox implements accurate South African tax calculations:

### PAYE (Pay As You Earn)
- 2024/2025 tax brackets
- Primary rebate: R16,425
- Rates from 18% to 45%

### UIF (Unemployment Insurance Fund)
- Employee contribution: 1%
- Employer contribution: 1%
- Maximum monthly: R177.12

### SDL (Skills Development Levy)
- Employer contribution: 1%
- Threshold: R500,000 annual payroll

## Error Handling

All sandbox responses include:
```json
{
  "success": true/false,
  "sandbox": true,
  "timestamp": "2025-01-10T10:00:00.000Z",
  "data": {},
  "message": "Description"
}
```

Common error codes:
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (invalid token)
- `404` - Not Found (resource doesn't exist)
- `429` - Too Many Requests (rate limit exceeded)
- `500` - Internal Server Error

## Limitations

### Data Limits
- Maximum 50 employees per company
- Maximum 20 pay runs
- Maximum 100 payroll periods
- Data auto-resets after 30 days of inactivity

### Functionality Limits
- No real email sending
- No external API calls
- No file uploads/downloads
- No database persistence (in-memory storage)

## SDK Examples

### JavaScript/Node.js
```javascript
class PandaPayrollSandbox {
  constructor() {
    this.baseUrl = '/sandbox';
    this.token = null;
  }

  async login() {
    const response = await fetch(`${this.baseUrl}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'demo123'
      })
    });
    
    const data = await response.json();
    this.token = data.token;
    return data;
  }

  async getEmployees() {
    const response = await fetch(`${this.baseUrl}/companies/DEMO001/employees`, {
      headers: { 'Authorization': `Bearer ${this.token}` }
    });
    
    return response.json();
  }
}
```

### Python
```python
import requests

class PandaPayrollSandbox:
    def __init__(self, base_url='/sandbox'):
        self.base_url = base_url
        self.token = None
    
    def login(self):
        response = requests.post(f'{self.base_url}/auth/login', json={
            'email': '<EMAIL>',
            'password': 'demo123'
        })
        
        data = response.json()
        self.token = data['token']
        return data
    
    def get_employees(self):
        headers = {'Authorization': f'Bearer {self.token}'}
        response = requests.get(f'{self.base_url}/companies/DEMO001/employees', headers=headers)
        return response.json()
```

## Support

For sandbox-related questions or issues:
- **Documentation**: `/apiDocumentation`
- **Interface**: `/sandbox`
- **Email**: <EMAIL>

## Next Steps

1. **Explore the Interface**: Visit `/sandbox` to try the interactive testing tool
2. **Read the API Docs**: Check `/apiDocumentation` for complete endpoint reference
3. **Build Integration**: Use the sandbox to develop your integration
4. **Test Scenarios**: Try different payroll scenarios and edge cases
5. **Move to Production**: Contact support for production API access

---

*The sandbox environment is designed to help you build robust integrations with the PandaPayroll API. Happy coding! 🐼*
