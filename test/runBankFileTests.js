#!/usr/bin/env node

/**
 * Simple test runner for bank file generators
 * This script validates the implementation without requiring a full test framework
 */

const path = require('path');
const fs = require('fs');

// Import generators
const FNBACBGenerator = require('../utils/bankFileGenerators/FNBACBGenerator');
const FNBEnterpriseCSVGenerator = require('../utils/bankFileGenerators/FNBEnterpriseCSVGenerator');
const StandardBankEFTSGenerator = require('../utils/bankFileGenerators/StandardBankEFTSGenerator');
const ABSABusinessIntegratorCSVGenerator = require('../utils/bankFileGenerators/ABSABusinessIntegratorCSVGenerator');
const NedbankNedInformGenerator = require('../utils/bankFileGenerators/NedbankNedInformGenerator');

// Import utilities
const BankFileUtils = require('../utils/bankFileGenerators/BankFileUtils');
const BankFileGeneratorFactory = require('../utils/bankFileGenerators/BankFileGeneratorFactory');
const { BankFormatConfigManager } = require('../utils/bankFileGenerators/BankFormatConfigs');

// Test data
const testEftSettings = {
  bankName: 'First National Bank',
  branchCode: '250655',
  accountNumber: '***********',
  accountHolder: 'Test Company (Pty) Ltd',
  accountType: 'Current'
};

const testEmployee = {
  firstName: 'John',
  lastName: 'Doe',
  employeeId: 'EMP001',
  accountNumber: '*********',
  branchCode: '518100',
  accountType: 'Current',
  paymentMethod: 'EFT'
};

const testPayslip = {
  employee: testEmployee,
  netPay: 2500.00,
  grossPay: 3000.00,
  totalDeductions: 500.00
};

const testPayRun = {
  id: 'test_payrun_001',
  _id: 'test_payrun_001',
  company: 'test_company_001',
  payrollPeriods: [testPayslip],
  toObject: function() { return this; }
};

const testActionDate = '2024-01-31';

// Test results
let testResults = {
  passed: 0,
  failed: 0,
  errors: []
};

function assert(condition, message) {
  if (condition) {
    testResults.passed++;
    console.log(`✓ ${message}`);
  } else {
    testResults.failed++;
    testResults.errors.push(message);
    console.log(`✗ ${message}`);
  }
}

function assertEqual(actual, expected, message) {
  assert(actual === expected, `${message} (expected: ${expected}, actual: ${actual})`);
}

function assertIncludes(str, substring, message) {
  assert(str.includes(substring), `${message} (expected to include: ${substring})`);
}

async function runTests() {
  console.log('🏦 Bank File Generators Test Suite');
  console.log('=====================================\n');

  try {
    // Test BankFileUtils
    console.log('Testing BankFileUtils...');
    assertEqual(BankFileUtils.formatAmountCents(25.50), '2550', 'Amount formatting (cents)');
    assertEqual(BankFileUtils.formatAmountDecimal(25.50), '25.50', 'Amount formatting (decimal)');
    assertEqual(BankFileUtils.formatDateYYMMDD(new Date('2024-01-31')), '240131', 'Date formatting (YYMMDD)');
    assertEqual(BankFileUtils.validateAccountNumber('*********'), '*********', 'Account number validation');
    assertEqual(BankFileUtils.validateBranchCode('518100'), '518100', 'Branch code validation');
    console.log('');

    // Test FNB ACB Generator
    console.log('Testing FNB ACB Generator...');
    const fnbGenerator = new FNBACBGenerator();
    assertEqual(fnbGenerator.getFileExtension(), 'acb', 'FNB ACB file extension');
    
    const fnbHeader = fnbGenerator.generateHeader(testEftSettings, testActionDate);
    assertEqual(fnbHeader.length, 198, 'FNB ACB header length');
    assert(fnbHeader.startsWith('02'), 'FNB ACB header starts with 02');
    
    const fnbDetail = fnbGenerator.generateDetail(testPayslip, 1, testEftSettings);
    assertEqual(fnbDetail.length, 198, 'FNB ACB detail length');
    assert(fnbDetail.startsWith('10'), 'FNB ACB detail starts with 10');
    
    const fnbBankFile = await fnbGenerator.generate(testPayRun, testEftSettings, testActionDate);
    assert(fnbBankFile.length > 0, 'FNB ACB bank file generated');
    console.log('');

    // Test FNB Enterprise CSV Generator
    console.log('Testing FNB Enterprise CSV Generator...');
    const fnbCSVGenerator = new FNBEnterpriseCSVGenerator();
    assertEqual(fnbCSVGenerator.getFileExtension(), 'csv', 'FNB CSV file extension');
    
    const fnbCSVHeader = fnbCSVGenerator.generateHeader(testEftSettings, testActionDate);
    assertIncludes(fnbCSVHeader, 'Company Name', 'FNB CSV header contains Company Name');
    
    const fnbCSVDetail = fnbCSVGenerator.generateDetail(testPayslip, 1, testEftSettings);
    assertIncludes(fnbCSVDetail, '*********', 'FNB CSV detail contains account number');
    assertIncludes(fnbCSVDetail, '2500.00', 'FNB CSV detail contains amount');
    
    const fnbCSVBankFile = await fnbCSVGenerator.generate(testPayRun, testEftSettings, testActionDate);
    assert(fnbCSVBankFile.length > 0, 'FNB CSV bank file generated');
    console.log('');

    // Test Standard Bank EFTS Generator
    console.log('Testing Standard Bank EFTS Generator...');
    const standardGenerator = new StandardBankEFTSGenerator();
    assertEqual(standardGenerator.getFileExtension(), 'txt', 'Standard Bank file extension');
    
    const standardHeader = standardGenerator.generateHeader(testEftSettings, testActionDate, 1, 2500);
    assertEqual(standardHeader.length, 80, 'Standard Bank header length');
    assert(standardHeader.startsWith('01'), 'Standard Bank header starts with 01');
    
    const standardDetail = standardGenerator.generateDetail(testPayslip, 1, testEftSettings);
    assertEqual(standardDetail.length, 80, 'Standard Bank detail length');
    assert(standardDetail.startsWith('10'), 'Standard Bank detail starts with 10');
    
    const standardBankFile = await standardGenerator.generate(testPayRun, testEftSettings, testActionDate);
    assert(standardBankFile.length > 0, 'Standard Bank file generated');
    console.log('');

    // Test ABSA Business Integrator CSV Generator
    console.log('Testing ABSA Business Integrator CSV Generator...');
    const absaGenerator = new ABSABusinessIntegratorCSVGenerator();
    assertEqual(absaGenerator.getFileExtension(), 'csv', 'ABSA CSV file extension');
    
    const absaHeader = absaGenerator.generateHeader(testEftSettings, testActionDate);
    assertIncludes(absaHeader, 'Account Number', 'ABSA CSV header contains Account Number');
    assert(!absaHeader.includes('"'), 'ABSA CSV header has no quotes');
    
    const absaDetail = absaGenerator.generateDetail(testPayslip, 1, testEftSettings, testActionDate);
    assertIncludes(absaDetail, '*********', 'ABSA CSV detail contains account number');
    assertIncludes(absaDetail, '31/01/2024', 'ABSA CSV detail contains DD/MM/YYYY date');
    
    const absaBankFile = await absaGenerator.generate(testPayRun, testEftSettings, testActionDate);
    assert(absaBankFile.length > 0, 'ABSA CSV bank file generated');
    console.log('');

    // Test Nedbank NedInform Generator
    console.log('Testing Nedbank NedInform Generator...');
    const nedbankGenerator = new NedbankNedInformGenerator('PRE-FORMAT');
    assertEqual(nedbankGenerator.getFileExtension(), 'imp', 'Nedbank file extension');
    
    const nedbankHeader = nedbankGenerator.generateHeader(testEftSettings, testActionDate, 1, 2500);
    assert(nedbankHeader.startsWith('H,'), 'Nedbank header starts with H,');
    assertIncludes(nedbankHeader, 'SALARIES', 'Nedbank header contains SALARIES');
    
    const nedbankDetail = nedbankGenerator.generateDetail(testPayslip, 1, testEftSettings);
    assert(nedbankDetail.startsWith('D,'), 'Nedbank detail starts with D,');
    assertIncludes(nedbankDetail, 'BEN', 'Nedbank PRE-FORMAT detail contains beneficiary reference');
    
    const nedbankBankFile = await nedbankGenerator.generate(testPayRun, testEftSettings, testActionDate);
    assert(nedbankBankFile.length > 0, 'Nedbank bank file generated');
    console.log('');

    // Test Bank File Generator Factory
    console.log('Testing Bank File Generator Factory...');
    const factoryResult = await BankFileGeneratorFactory.generateBankFile(
      'FNB Online Banking (ACB)',
      'fnb',
      testPayRun,
      testEftSettings,
      testActionDate
    );
    
    assert(factoryResult.content && factoryResult.content.length > 0, 'Factory generates bank file content');
    assert(factoryResult.filename && factoryResult.filename.includes('fnb_acb'), 'Factory generates correct filename');
    assertEqual(factoryResult.extension, 'acb', 'Factory returns correct extension');
    console.log('');

    // Test Configuration Manager
    console.log('Testing Bank Format Configuration Manager...');
    const fnbConfig = BankFormatConfigManager.getConfig('fnb');
    assertEqual(fnbConfig.fileExtension, 'acb', 'FNB config has correct extension');
    
    const csvFormats = BankFormatConfigManager.getFormatsByExtension('csv');
    assert(csvFormats.includes('fnb_csv'), 'CSV formats include FNB CSV');
    assert(csvFormats.includes('absa_csv'), 'CSV formats include ABSA CSV');
    
    assert(BankFormatConfigManager.supportsFeature('fnb_csv', 'csv'), 'FNB CSV supports CSV feature');
    assert(BankFormatConfigManager.supportsFeature('fnb', 'fixed-width'), 'FNB ACB supports fixed-width feature');
    console.log('');

    // Generate sample files for manual inspection
    console.log('Generating sample files...');
    const sampleDir = path.join(__dirname, 'sample_files');
    if (!fs.existsSync(sampleDir)) {
      fs.mkdirSync(sampleDir);
    }

    // Generate sample files for each format
    const formats = [
      { generator: fnbGenerator, filename: 'sample_fnb_acb.acb' },
      { generator: fnbCSVGenerator, filename: 'sample_fnb_enterprise.csv' },
      { generator: standardGenerator, filename: 'sample_standard_efts.txt' },
      { generator: absaGenerator, filename: 'sample_absa_integrator.csv' },
      { generator: nedbankGenerator, filename: 'sample_nedbank_nedinform.imp' }
    ];

    for (const format of formats) {
      try {
        const content = await format.generator.generate(testPayRun, testEftSettings, testActionDate);
        const filePath = path.join(sampleDir, format.filename);
        fs.writeFileSync(filePath, content);
        console.log(`✓ Generated sample file: ${format.filename}`);
      } catch (error) {
        console.log(`✗ Failed to generate ${format.filename}: ${error.message}`);
        testResults.failed++;
      }
    }

  } catch (error) {
    console.error('Test execution error:', error);
    testResults.failed++;
    testResults.errors.push(`Test execution error: ${error.message}`);
  }

  // Print results
  console.log('\n=====================================');
  console.log('Test Results:');
  console.log(`✓ Passed: ${testResults.passed}`);
  console.log(`✗ Failed: ${testResults.failed}`);
  
  if (testResults.errors.length > 0) {
    console.log('\nErrors:');
    testResults.errors.forEach(error => console.log(`  - ${error}`));
  }
  
  if (testResults.failed === 0) {
    console.log('\n🎉 All tests passed! Bank file generators are working correctly.');
  } else {
    console.log('\n❌ Some tests failed. Please check the implementation.');
    process.exit(1);
  }
}

// Run tests
runTests().catch(error => {
  console.error('Fatal error:', error);
  process.exit(1);
});
