const { expect } = require('chai');
const moment = require('moment');

// Import generators
const FNBACBGenerator = require('../utils/bankFileGenerators/FNBACBGenerator');
const FNBEnterpriseCSVGenerator = require('../utils/bankFileGenerators/FNBEnterpriseCSVGenerator');
const StandardBankEFTSGenerator = require('../utils/bankFileGenerators/StandardBankEFTSGenerator');
const ABSABusinessIntegratorCSVGenerator = require('../utils/bankFileGenerators/ABSABusinessIntegratorCSVGenerator');
const NedbankNedInformGenerator = require('../utils/bankFileGenerators/NedbankNedInformGenerator');

// Import utilities
const BankFileUtils = require('../utils/bankFileGenerators/BankFileUtils');
const BankFileGeneratorFactory = require('../utils/bankFileGenerators/BankFileGeneratorFactory');
const { BankFormatConfigManager } = require('../utils/bankFileGenerators/BankFormatConfigs');

describe('Bank File Generators Test Suite', function() {
  
  // Test data
  const testEftSettings = {
    bankName: 'First National Bank',
    branchCode: '250655',
    accountNumber: '***********',
    accountHolder: 'Test Company (Pty) Ltd',
    accountType: 'Current'
  };

  const testEmployee = {
    firstName: 'John',
    lastName: 'Doe',
    employeeId: 'EMP001',
    accountNumber: '*********',
    branchCode: '518100',
    accountType: 'Current',
    paymentMethod: 'EFT'
  };

  const testPayslip = {
    employee: testEmployee,
    netPay: 2500.00,
    grossPay: 3000.00,
    totalDeductions: 500.00
  };

  const testPayRun = {
    id: 'test_payrun_001',
    _id: 'test_payrun_001',
    company: 'test_company_001',
    payrollPeriods: [testPayslip]
  };

  const testActionDate = '2024-01-31';

  describe('BankFileUtils', function() {
    
    it('should format amounts correctly', function() {
      expect(BankFileUtils.formatAmountCents(25.50)).to.equal('2550');
      expect(BankFileUtils.formatAmountDecimal(25.50)).to.equal('25.50');
      expect(BankFileUtils.formatAmountPadded(25.50, 10, '0', true)).to.equal('**********');
    });

    it('should format dates correctly', function() {
      const testDate = new Date('2024-01-31');
      expect(BankFileUtils.formatDateYYMMDD(testDate)).to.equal('240131');
      expect(BankFileUtils.formatDateYYYYMMDD(testDate)).to.equal('********');
      expect(BankFileUtils.formatDateDDMMYYYY(testDate)).to.equal('31/01/2024');
    });

    it('should validate account numbers', function() {
      expect(BankFileUtils.validateAccountNumber('*********')).to.equal('*********');
      expect(() => BankFileUtils.validateAccountNumber('123')).to.throw('Account number must be between');
      expect(() => BankFileUtils.validateAccountNumber('**************')).to.throw('Account number must be between');
    });

    it('should validate branch codes', function() {
      expect(BankFileUtils.validateBranchCode('518100')).to.equal('518100');
      expect(() => BankFileUtils.validateBranchCode('123')).to.throw('Branch code must be exactly');
      expect(() => BankFileUtils.validateBranchCode('1234567')).to.throw('Branch code must be exactly');
    });

    it('should format CSV fields correctly', function() {
      expect(BankFileUtils.escapeCSVField('Simple Text')).to.equal('Simple Text');
      expect(BankFileUtils.escapeCSVField('Text, with comma')).to.equal('"Text, with comma"');
      expect(BankFileUtils.escapeCSVField('Text "with quotes"')).to.equal('"Text ""with quotes"""');
    });
  });

  describe('FNB ACB Generator', function() {
    let generator;

    beforeEach(function() {
      generator = new FNBACBGenerator();
    });

    it('should create generator instance', function() {
      expect(generator).to.be.instanceOf(FNBACBGenerator);
      expect(generator.getFileExtension()).to.equal('acb');
      expect(generator.getMimeType()).to.equal('application/octet-stream');
    });

    it('should generate header record', function() {
      const header = generator.generateHeader(testEftSettings, testActionDate);
      expect(header).to.be.a('string');
      expect(header.length).to.equal(198);
      expect(header.startsWith('02')).to.be.true;
    });

    it('should generate detail record', function() {
      const detail = generator.generateDetail(testPayslip, 1, testEftSettings);
      expect(detail).to.be.a('string');
      expect(detail.length).to.equal(198);
      expect(detail.startsWith('10')).to.be.true;
    });

    it('should generate complete bank file', async function() {
      const bankFile = await generator.generate(testPayRun, testEftSettings, testActionDate);
      expect(bankFile).to.be.a('string');
      
      const lines = bankFile.split('\n');
      expect(lines.length).to.equal(5); // Header, User Header, Detail, Contra, Trailer
      expect(lines[0].startsWith('02')).to.be.true; // Header
      expect(lines[1].startsWith('04')).to.be.true; // User Header
      expect(lines[2].startsWith('10')).to.be.true; // Detail
      expect(lines[3].startsWith('92')).to.be.true; // Contra
      expect(lines[4].startsWith('94')).to.be.true; // Trailer
    });
  });

  describe('FNB Enterprise CSV Generator', function() {
    let generator;

    beforeEach(function() {
      generator = new FNBEnterpriseCSVGenerator();
    });

    it('should create generator instance', function() {
      expect(generator).to.be.instanceOf(FNBEnterpriseCSVGenerator);
      expect(generator.getFileExtension()).to.equal('csv');
      expect(generator.getMimeType()).to.equal('text/csv');
    });

    it('should generate CSV header', function() {
      const header = generator.generateHeader(testEftSettings, testActionDate);
      expect(header).to.include('Company Name');
      expect(header).to.include('Account Number');
      expect(header).to.include('Amount');
    });

    it('should generate CSV detail record', function() {
      const detail = generator.generateDetail(testPayslip, 1, testEftSettings);
      expect(detail).to.be.a('string');
      expect(detail).to.include('*********'); // Account number
      expect(detail).to.include('518100'); // Branch code
      expect(detail).to.include('2500.00'); // Amount
    });

    it('should generate complete CSV file', async function() {
      const bankFile = await generator.generate(testPayRun, testEftSettings, testActionDate);
      expect(bankFile).to.be.a('string');
      
      const lines = bankFile.split('\r\n');
      expect(lines.length).to.equal(2); // Header + Detail
      expect(lines[0]).to.include('Company Name'); // Header
      expect(lines[1]).to.include('*********'); // Detail
    });
  });

  describe('Standard Bank EFTS Generator', function() {
    let generator;

    beforeEach(function() {
      generator = new StandardBankEFTSGenerator();
    });

    it('should create generator instance', function() {
      expect(generator).to.be.instanceOf(StandardBankEFTSGenerator);
      expect(generator.getFileExtension()).to.equal('txt');
      expect(generator.getMimeType()).to.equal('text/plain');
    });

    it('should generate header record', function() {
      const header = generator.generateHeader(testEftSettings, testActionDate, 1, 2500);
      expect(header).to.be.a('string');
      expect(header.length).to.equal(80);
      expect(header.startsWith('01')).to.be.true;
    });

    it('should generate detail record', function() {
      const detail = generator.generateDetail(testPayslip, 1, testEftSettings);
      expect(detail).to.be.a('string');
      expect(detail.length).to.equal(80);
      expect(detail.startsWith('10')).to.be.true;
    });

    it('should generate trailer record', function() {
      const trailer = generator.generateTrailer(2500, 1, testEftSettings);
      expect(trailer).to.be.a('string');
      expect(trailer.length).to.equal(80);
      expect(trailer.startsWith('99')).to.be.true;
    });

    it('should generate complete bank file', async function() {
      const bankFile = await generator.generate(testPayRun, testEftSettings, testActionDate);
      expect(bankFile).to.be.a('string');
      
      const lines = bankFile.split('\r\n');
      expect(lines.length).to.equal(3); // Header, Detail, Trailer
      expect(lines[0].startsWith('01')).to.be.true; // Header
      expect(lines[1].startsWith('10')).to.be.true; // Detail
      expect(lines[2].startsWith('99')).to.be.true; // Trailer
    });
  });

  describe('ABSA Business Integrator CSV Generator', function() {
    let generator;

    beforeEach(function() {
      generator = new ABSABusinessIntegratorCSVGenerator();
    });

    it('should create generator instance', function() {
      expect(generator).to.be.instanceOf(ABSABusinessIntegratorCSVGenerator);
      expect(generator.getFileExtension()).to.equal('csv');
      expect(generator.getMimeType()).to.equal('text/csv');
    });

    it('should generate CSV header without quotes', function() {
      const header = generator.generateHeader(testEftSettings, testActionDate);
      expect(header).to.include('Account Number');
      expect(header).to.include('Beneficiary Name');
      expect(header).not.to.include('"'); // ABSA format doesn't use quotes
    });

    it('should generate CSV detail record', function() {
      const detail = generator.generateDetail(testPayslip, 1, testEftSettings);
      expect(detail).to.be.a('string');
      expect(detail).to.include('*********'); // Account number
      expect(detail).to.include('518100'); // Branch code
      expect(detail).to.include('2500.00'); // Amount
      expect(detail).to.include('31/01/2024'); // DD/MM/YYYY date format
    });

    it('should generate complete CSV file', async function() {
      const bankFile = await generator.generate(testPayRun, testEftSettings, testActionDate);
      expect(bankFile).to.be.a('string');
      
      const lines = bankFile.split('\r\n');
      expect(lines.length).to.equal(2); // Header + Detail
      expect(lines[0]).to.include('Account Number'); // Header
      expect(lines[1]).to.include('*********'); // Detail
    });
  });

  describe('Nedbank NedInform Generator', function() {
    let generator;

    beforeEach(function() {
      generator = new NedbankNedInformGenerator('PRE-FORMAT');
    });

    it('should create generator instance', function() {
      expect(generator).to.be.instanceOf(NedbankNedInformGenerator);
      expect(generator.getFileExtension()).to.equal('imp');
      expect(generator.getMimeType()).to.equal('application/octet-stream');
    });

    it('should generate header record', function() {
      const header = generator.generateHeader(testEftSettings, testActionDate, 1, 2500);
      expect(header).to.be.a('string');
      expect(header.startsWith('H,')).to.be.true;
      expect(header).to.include('SALARIES');
    });

    it('should generate detail record for PRE-FORMAT', function() {
      const detail = generator.generateDetail(testPayslip, 1, testEftSettings);
      expect(detail).to.be.a('string');
      expect(detail.startsWith('D,')).to.be.true;
      expect(detail).to.include('*********'); // Account number
      expect(detail).to.include('BEN1'); // Beneficiary reference for PRE-FORMAT
    });

    it('should generate detail record for FREE-FORMAT', function() {
      generator.setFormatOption('FREE-FORMAT');
      const detail = generator.generateDetail(testPayslip, 1, testEftSettings);
      expect(detail).to.be.a('string');
      expect(detail.startsWith('D,')).to.be.true;
      expect(detail).to.include('*********'); // Account number
      expect(detail).not.to.include('BEN'); // No beneficiary reference for FREE-FORMAT
    });

    it('should generate trailer record', function() {
      const trailer = generator.generateTrailer(2500, 1, testEftSettings);
      expect(trailer).to.be.a('string');
      expect(trailer.startsWith('T,')).to.be.true;
      expect(trailer).to.include('1'); // Record count
      expect(trailer).to.include('2500.00'); // Total amount
    });

    it('should generate complete bank file', async function() {
      const bankFile = await generator.generate(testPayRun, testEftSettings, testActionDate);
      expect(bankFile).to.be.a('string');
      
      const lines = bankFile.split('\r\n');
      expect(lines.length).to.equal(3); // Header, Detail, Trailer
      expect(lines[0].startsWith('H,')).to.be.true; // Header
      expect(lines[1].startsWith('D,')).to.be.true; // Detail
      expect(lines[2].startsWith('T,')).to.be.true; // Trailer
    });
  });

  describe('Bank File Generator Factory', function() {
    
    it('should create appropriate generators', function() {
      const fnbGenerator = BankFileGeneratorFactory.createGenerator('FNB Online Banking (ACB)', 'fnb');
      expect(fnbGenerator).to.be.instanceOf(FNBACBGenerator);

      const fnbCSVGenerator = BankFileGeneratorFactory.createGenerator('FNB Enterprise CSV', 'fnb_csv');
      expect(fnbCSVGenerator).to.be.instanceOf(FNBEnterpriseCSVGenerator);
    });

    it('should validate generator requirements', function() {
      const validation = BankFileGeneratorFactory.validateGeneratorRequirements(
        testPayRun,
        testEftSettings,
        testActionDate
      );
      
      expect(validation.valid).to.be.true;
      expect(validation.errors).to.be.an('array').that.is.empty;
    });

    it('should generate bank file through factory', async function() {
      const result = await BankFileGeneratorFactory.generateBankFile(
        'FNB Online Banking (ACB)',
        'fnb',
        testPayRun,
        testEftSettings,
        testActionDate
      );
      
      expect(result).to.have.property('content');
      expect(result).to.have.property('filename');
      expect(result).to.have.property('mimeType');
      expect(result).to.have.property('extension');
      expect(result.extension).to.equal('acb');
    });
  });

  describe('Bank Format Configuration Manager', function() {
    
    it('should get format configurations', function() {
      const fnbConfig = BankFormatConfigManager.getConfig('fnb');
      expect(fnbConfig).to.have.property('name');
      expect(fnbConfig).to.have.property('fileExtension');
      expect(fnbConfig.fileExtension).to.equal('acb');

      const csvConfig = BankFormatConfigManager.getConfig('fnb_csv');
      expect(csvConfig.fileExtension).to.equal('csv');
    });

    it('should validate configurations', function() {
      const validation = BankFormatConfigManager.validateConfig('fnb');
      expect(validation.valid).to.be.true;
      expect(validation.errors).to.be.an('array').that.is.empty;
    });

    it('should get formats by extension', function() {
      const csvFormats = BankFormatConfigManager.getFormatsByExtension('csv');
      expect(csvFormats).to.include('fnb_csv');
      expect(csvFormats).to.include('absa_csv');
    });

    it('should check feature support', function() {
      expect(BankFormatConfigManager.supportsFeature('fnb_csv', 'csv')).to.be.true;
      expect(BankFormatConfigManager.supportsFeature('fnb', 'fixed-width')).to.be.true;
      expect(BankFormatConfigManager.supportsFeature('fnb_csv', 'fixed-width')).to.be.false;
    });
  });

  describe('Error Handling', function() {
    
    it('should handle missing employee data', async function() {
      const invalidPayRun = {
        ...testPayRun,
        payrollPeriods: [{
          employee: null,
          netPay: 2500.00
        }]
      };

      const generator = new FNBACBGenerator();
      
      try {
        await generator.generate(invalidPayRun, testEftSettings, testActionDate);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error.message).to.include('No valid EFT transactions found');
      }
    });

    it('should handle invalid amounts', function() {
      expect(() => BankFileUtils.validateAmount(-100)).to.throw('Amount must be at least');
      expect(() => BankFileUtils.validateAmount(1000000)).to.throw('Amount cannot exceed');
    });

    it('should handle invalid references', function() {
      expect(() => BankFileUtils.validateReference('A'.repeat(25), 20)).to.throw('Reference cannot exceed');
      expect(() => BankFileUtils.validateReference('Invalid@Chars!', 20)).to.throw('Reference contains invalid characters');
    });
  });
});
