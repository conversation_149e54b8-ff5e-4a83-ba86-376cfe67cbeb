const EFTDetails = require('../models/eftDetails');
const EFTSettings = require('../models/EFTSettings');
const Company = require('../models/Company');

/**
 * Priority EFT formats that are fully implemented
 */
const PRIORITY_FORMATS = [
  'FNB Enterprise CSV',
  'FNB Online Banking (ACB)',
  'Standard Bank Value Dated / EFTS',
  'ABSA Business Integrator (.csv)',
  'NedInform'
];

/**
 * Check if an EFT format is a priority format (fully implemented)
 * @param {string} eftFormat - The EFT format to check
 * @returns {boolean} True if the format is a priority format
 */
function isPriorityFormat(eftFormat) {
  return PRIORITY_FORMATS.includes(eftFormat);
}

/**
 * Map EFT format from EFTDetails to bankFileFormat for EFTSettings
 * @param {string} eftFormat - The EFT format from EFTDetails
 * @returns {string} The corresponding bank file format
 */
function mapEftFormatToBankFileFormat(eftFormat) {
  if (!eftFormat) return 'fnb'; // Default to FNB

  // Check if format is implemented
  if (!isPriorityFormat(eftFormat)) {
    console.warn(`EFT format '${eftFormat}' is not yet implemented. Falling back to FNB ACB format.`);
    return 'fnb'; // Fallback to working FNB format
  }

  const formatMapping = {
    // ABSA formats (Priority)
    'ABSA Business Integrator (.csv)': 'absa_csv',

    // ABSA formats (Coming Soon)
    'ABSA Cash Focus': 'fnb', // Fallback
    'ABSA Business Integrator (.txt)': 'fnb', // Fallback
    'ABSA Business Integrator SAP (beta)': 'fnb', // Fallback
    'ABSA Business Integrator Online (.csv)': 'fnb', // Fallback

    // FNB formats (Priority)
    'FNB Enterprise CSV': 'fnb_csv',
    'FNB Online Banking (ACB)': 'fnb',

    // FNB formats (Coming Soon)
    'FNB Bankit': 'fnb', // Fallback
    'FNB CAMS': 'fnb', // Fallback
    'FNB PACS': 'fnb', // Fallback

    // Standard Bank formats (Priority)
    'Standard Bank Value Dated / EFTS': 'standard_efts',

    // Standard Bank formats (Coming Soon)
    'Standard Bank CATS': 'fnb', // Fallback
    'Standard Bank SSVS': 'fnb', // Fallback

    // Nedbank formats (Priority)
    'NedInform': 'nedbank_nedinform',

    // Nedbank formats (Coming Soon)
    'NedBank Business Banking (CSV)': 'fnb', // Fallback

    // Other banks (Coming Soon - all fallback to FNB)
    'Albaraka': 'fnb',
    'Capitec ACB': 'fnb',
    'Investec CSV': 'fnb',
    'Netcash': 'fnb',
    'Sage Pay': 'fnb'
  };

  return formatMapping[eftFormat] || 'fnb';
}

/**
 * Get file extension for a given EFT format
 * @param {string} eftFormat - The EFT format
 * @returns {string} The file extension
 */
function getFileExtensionForFormat(eftFormat) {
  const extensionMapping = {
    'FNB Enterprise CSV': 'csv',
    'FNB Online Banking (ACB)': 'acb',
    'Standard Bank Value Dated / EFTS': 'txt',
    'ABSA Business Integrator (.csv)': 'csv',
    'NedInform': 'imp'
  };

  return extensionMapping[eftFormat] || 'acb'; // Default to ACB
}

/**
 * Get format display name with implementation status
 * @param {string} eftFormat - The EFT format
 * @returns {string} Display name with status indicator
 */
function getFormatDisplayName(eftFormat) {
  if (isPriorityFormat(eftFormat)) {
    return eftFormat;
  }
  return `${eftFormat} (Coming Soon)`;
}

/**
 * Get or create default EFT settings for a company
 * @param {ObjectId} companyId - The ID of the company
 * @param {Object} [defaultSettings] - Optional default settings to use
 * @returns {Promise<EFTSettings>} The EFT settings for the company
 */
async function getOrCreateEFTSettings(companyId, defaultSettings = {}) {

  // Validate companyId
  if (!companyId) {
    throw new Error('Company ID is required');
  }

  try {
    // First, check if the company exists
    const company = await Company.findById(companyId);
    if (!company) {
      throw new Error(`Company not found with ID: ${companyId}`);
    }

    // Try to find existing EFT details
    let eftDetails = await EFTDetails.findOne({ company: companyId });

    // If no details exist, create default details
    if (!eftDetails) {

      // Prepare default details
      const defaultEFTDetails = {
        company: companyId,
        eftFormat: defaultSettings.eftFormat || 'FNB Bankit',
        bank: defaultSettings.bankName || company.name || 'First National Bank',
        branchCode: defaultSettings.branchCode || '123456', // Placeholder
        accountNumber: defaultSettings.accountNumber || '**********', // Placeholder
        accountType: defaultSettings.accountType || 'Current',
        accountHolder: defaultSettings.accountHolder || company.name
      };

      // Create and save new EFT details
      eftDetails = new EFTDetails(defaultEFTDetails);
      await eftDetails.save();

    }

    // Create or find EFT settings using EFT details
    let eftSettings = await EFTSettings.findOne({ company: companyId });
    if (!eftSettings) {
      // Map the EFT format to bank file format
      const bankFileFormat = mapEftFormatToBankFileFormat(eftDetails.eftFormat);

      eftSettings = new EFTSettings({
        company: companyId,
        bankFileFormat: bankFileFormat,
        bankName: eftDetails.bank,
        branchCode: eftDetails.branchCode,
        accountNumber: eftDetails.accountNumber,
        accountHolder: eftDetails.accountHolder || company.name || 'Company Account'
      });
      await eftSettings.save();
    } else {
      // Update existing EFT settings with latest EFT details
      const bankFileFormat = mapEftFormatToBankFileFormat(eftDetails.eftFormat);

      eftSettings.bankFileFormat = bankFileFormat;
      eftSettings.bankName = eftDetails.bank;
      eftSettings.branchCode = eftDetails.branchCode;
      eftSettings.accountNumber = eftDetails.accountNumber;
      eftSettings.accountHolder = eftDetails.accountHolder || company.name || 'Company Account';
      eftSettings.updatedAt = new Date();

      await eftSettings.save();
    }

    return eftSettings;
  } catch (error) {
    console.error('Error in getOrCreateEFTSettings:', error);

    // Log additional context if the error is related to model registration
    if (error.name === 'OverwriteModelError') {
      console.error('Model registration error. Ensure EFTSettings model is imported only once.');
    }

    throw error;
  }
}

module.exports = {
  getOrCreateEFTSettings,
  mapEftFormatToBankFileFormat,
  isPriorityFormat,
  getFileExtensionForFormat,
  getFormatDisplayName,
  PRIORITY_FORMATS
};
