const mongoose = require('mongoose');
const Employee = require('../models/Employee');
const moment = require('moment');

/**
 * Employee Status Migration and Cleanup Utilities
 * 
 * This script provides utilities for:
 * 1. Migrating data from old field names to new ones
 * 2. Cleaning up inconsistent employee status data
 * 3. Validating termination data integrity
 * 4. Updating employee statuses based on termination dates
 */

class EmployeeStatusMigration {
  constructor() {
    this.stats = {
      migrated: 0,
      updated: 0,
      errors: 0,
      warnings: []
    };
  }

  /**
   * Migrate endServiceDate to lastDayOfService
   */
  async migrateEndServiceDate() {
    console.log('Starting migration of endServiceDate to lastDayOfService...');
    
    try {
      // Find employees with endServiceDate but no lastDayOfService
      const employeesNeedingMigration = await Employee.find({
        endServiceDate: { $exists: true, $ne: null },
        lastDayOfService: null
      });

      console.log(`Found ${employeesNeedingMigration.length} employees needing migration`);

      for (const employee of employeesNeedingMigration) {
        try {
          employee.lastDayOfService = employee.endServiceDate;
          await employee.save();
          this.stats.migrated++;
          
          if (this.stats.migrated % 100 === 0) {
            console.log(`Migrated ${this.stats.migrated} employees...`);
          }
        } catch (error) {
          console.error(`Error migrating employee ${employee._id}:`, error.message);
          this.stats.errors++;
        }
      }

      console.log(`Migration completed: ${this.stats.migrated} employees migrated`);
      return this.stats.migrated;
    } catch (error) {
      console.error('Error in migrateEndServiceDate:', error);
      throw error;
    }
  }

  /**
   * Clean up inconsistent employee statuses
   */
  async cleanupEmployeeStatuses() {
    console.log('Starting employee status cleanup...');
    
    try {
      const today = new Date();
      
      // Find employees with inconsistent statuses
      const inconsistentEmployees = await Employee.find({
        $or: [
          // Employees with past termination dates but still active
          {
            lastDayOfService: { $lte: today },
            status: { $ne: "Inactive" }
          },
          // Employees with future termination dates but not serving notice
          {
            lastDayOfService: { $gt: today },
            terminationNoticeDate: { $lte: today },
            status: "Active"
          }
        ]
      });

      console.log(`Found ${inconsistentEmployees.length} employees with inconsistent statuses`);

      for (const employee of inconsistentEmployees) {
        try {
          const oldStatus = employee.status;
          
          if (employee.lastDayOfService && employee.lastDayOfService <= today) {
            employee.status = "Inactive";
          } else if (employee.lastDayOfService && employee.lastDayOfService > today && 
                     employee.terminationNoticeDate && employee.terminationNoticeDate <= today) {
            employee.status = "Serving Notice";
          }
          
          await employee.save();
          this.stats.updated++;
          
          console.log(`Updated employee ${employee.firstName} ${employee.lastName} from ${oldStatus} to ${employee.status}`);
        } catch (error) {
          console.error(`Error updating employee ${employee._id}:`, error.message);
          this.stats.errors++;
        }
      }

      console.log(`Status cleanup completed: ${this.stats.updated} employees updated`);
      return this.stats.updated;
    } catch (error) {
      console.error('Error in cleanupEmployeeStatuses:', error);
      throw error;
    }
  }

  /**
   * Validate termination data integrity
   */
  async validateTerminationData() {
    console.log('Starting termination data validation...');
    
    try {
      const issues = [];
      
      // Find employees with termination dates but no UIF status code
      const noUifCode = await Employee.find({
        lastDayOfService: { $exists: true, $ne: null },
        uifStatusCode: null,
        status: "Inactive"
      });
      
      if (noUifCode.length > 0) {
        issues.push({
          type: 'missing_uif_code',
          count: noUifCode.length,
          message: 'Employees with termination dates but no UIF status code',
          employees: noUifCode.map(emp => ({
            id: emp._id,
            name: `${emp.firstName} ${emp.lastName}`,
            lastDayOfService: emp.lastDayOfService
          }))
        });
      }

      // Find employees with notice dates after termination dates
      const invalidNotice = await Employee.find({
        lastDayOfService: { $exists: true, $ne: null },
        terminationNoticeDate: { $exists: true, $ne: null },
        $expr: { $gt: ["$terminationNoticeDate", "$lastDayOfService"] }
      });
      
      if (invalidNotice.length > 0) {
        issues.push({
          type: 'invalid_notice_date',
          count: invalidNotice.length,
          message: 'Employees with notice dates after termination dates',
          employees: invalidNotice.map(emp => ({
            id: emp._id,
            name: `${emp.firstName} ${emp.lastName}`,
            noticeDate: emp.terminationNoticeDate,
            terminationDate: emp.lastDayOfService
          }))
        });
      }

      // Find employees with termination dates before employment start
      const invalidTermination = await Employee.find({
        lastDayOfService: { $exists: true, $ne: null },
        doa: { $exists: true, $ne: null },
        $expr: { $lt: ["$lastDayOfService", "$doa"] }
      });
      
      if (invalidTermination.length > 0) {
        issues.push({
          type: 'termination_before_start',
          count: invalidTermination.length,
          message: 'Employees with termination dates before employment start',
          employees: invalidTermination.map(emp => ({
            id: emp._id,
            name: `${emp.firstName} ${emp.lastName}`,
            startDate: emp.doa,
            terminationDate: emp.lastDayOfService
          }))
        });
      }

      console.log(`Validation completed. Found ${issues.length} types of issues:`);
      issues.forEach(issue => {
        console.log(`- ${issue.message}: ${issue.count} employees`);
      });

      this.stats.warnings = issues;
      return issues;
    } catch (error) {
      console.error('Error in validateTerminationData:', error);
      throw error;
    }
  }

  /**
   * Generate migration report
   */
  generateReport() {
    const report = {
      timestamp: new Date(),
      statistics: this.stats,
      summary: {
        totalProcessed: this.stats.migrated + this.stats.updated,
        successRate: this.stats.errors === 0 ? 100 : 
          ((this.stats.migrated + this.stats.updated) / 
           (this.stats.migrated + this.stats.updated + this.stats.errors) * 100).toFixed(2)
      }
    };

    console.log('\n=== MIGRATION REPORT ===');
    console.log(`Timestamp: ${report.timestamp}`);
    console.log(`Records Migrated: ${report.statistics.migrated}`);
    console.log(`Records Updated: ${report.statistics.updated}`);
    console.log(`Errors: ${report.statistics.errors}`);
    console.log(`Success Rate: ${report.summary.successRate}%`);
    
    if (report.statistics.warnings.length > 0) {
      console.log('\nWarnings:');
      report.statistics.warnings.forEach(warning => {
        console.log(`- ${warning.message}: ${warning.count} records`);
      });
    }
    
    console.log('========================\n');
    
    return report;
  }

  /**
   * Run complete migration and cleanup process
   */
  async runComplete() {
    console.log('Starting complete employee status migration and cleanup...\n');
    
    try {
      // Step 1: Migrate field names
      await this.migrateEndServiceDate();
      
      // Step 2: Clean up statuses
      await this.cleanupEmployeeStatuses();
      
      // Step 3: Validate data
      await this.validateTerminationData();
      
      // Step 4: Generate report
      const report = this.generateReport();
      
      return report;
    } catch (error) {
      console.error('Error in complete migration:', error);
      throw error;
    }
  }
}

module.exports = EmployeeStatusMigration;

// CLI usage
if (require.main === module) {
  const migration = new EmployeeStatusMigration();
  
  migration.runComplete()
    .then(report => {
      console.log('Migration completed successfully');
      process.exit(0);
    })
    .catch(error => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}
