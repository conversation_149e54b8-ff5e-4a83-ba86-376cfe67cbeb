const { isPriorityFormat, getFileExtensionForFormat } = require('../eftSettingsUtils');

/**
 * Factory class for creating bank file generators
 * Handles format detection and generator instantiation
 */
class BankFileGeneratorFactory {
  
  constructor() {
    this.generators = new Map();
    this.registerGenerators();
  }

  /**
   * Register all available generators
   */
  registerGenerators() {
    // Import generators dynamically to avoid circular dependencies
    try {
      // FNB Generators
      const FNBACBGenerator = require('./FNBACBGenerator');
      const FNBEnterpriseCSVGenerator = require('./FNBEnterpriseCSVGenerator');
      
      // Standard Bank Generators
      const StandardBankEFTSGenerator = require('./StandardBankEFTSGenerator');
      
      // ABSA Generators
      const ABSABusinessIntegratorCSVGenerator = require('./ABSABusinessIntegratorCSVGenerator');
      
      // Nedbank Generators
      const NedbankNedInformGenerator = require('./NedbankNedInformGenerator');

      // Register generators with their format identifiers
      this.generators.set('fnb', FNBACBGenerator);
      this.generators.set('fnb_csv', FNBEnterpriseCSVGenerator);
      this.generators.set('standard_efts', StandardBankEFTSGenerator);
      this.generators.set('absa_csv', ABSABusinessIntegratorCSVGenerator);
      this.generators.set('nedbank_nedinform', NedbankNedInformGenerator);
      
    } catch (error) {
      console.warn('Some generators not yet implemented:', error.message);
    }
  }

  /**
   * Create generator instance for a specific format
   * @param {string} eftFormat - The EFT format from dropdown
   * @param {string} bankFileFormat - The mapped bank file format
   * @returns {Object} Generator instance
   */
  createGenerator(eftFormat, bankFileFormat) {
    console.log(`Creating generator for format: ${eftFormat} (mapped to: ${bankFileFormat})`);
    
    // Check if format is implemented
    if (!isPriorityFormat(eftFormat)) {
      console.warn(`Format '${eftFormat}' not yet implemented, falling back to FNB ACB`);
      bankFileFormat = 'fnb';
    }

    // Get generator class
    const GeneratorClass = this.generators.get(bankFileFormat);
    
    if (!GeneratorClass) {
      console.warn(`No generator found for format '${bankFileFormat}', falling back to FNB ACB`);
      const FallbackGenerator = this.generators.get('fnb');
      if (!FallbackGenerator) {
        throw new Error('No fallback generator available');
      }
      return new FallbackGenerator();
    }

    return new GeneratorClass();
  }

  /**
   * Get available formats
   * @returns {Array} List of available format identifiers
   */
  getAvailableFormats() {
    return Array.from(this.generators.keys());
  }

  /**
   * Check if a format is supported
   * @param {string} bankFileFormat - The bank file format identifier
   * @returns {boolean} True if format is supported
   */
  isFormatSupported(bankFileFormat) {
    return this.generators.has(bankFileFormat);
  }

  /**
   * Get generator information
   * @param {string} bankFileFormat - The bank file format identifier
   * @returns {Object} Generator information
   */
  getGeneratorInfo(bankFileFormat) {
    const GeneratorClass = this.generators.get(bankFileFormat);
    if (!GeneratorClass) {
      return null;
    }

    // Create temporary instance to get info
    try {
      const tempInstance = new GeneratorClass();
      return {
        name: GeneratorClass.name,
        fileExtension: tempInstance.getFileExtension(),
        mimeType: tempInstance.getMimeType(),
        supported: true
      };
    } catch (error) {
      return {
        name: GeneratorClass.name,
        error: error.message,
        supported: false
      };
    }
  }

  /**
   * Generate bank file using appropriate generator
   * @param {string} eftFormat - The EFT format from dropdown
   * @param {string} bankFileFormat - The mapped bank file format
   * @param {Object} payRun - Pay run data
   * @param {Object} eftSettings - EFT settings
   * @param {string} actionDate - Action date for the file
   * @returns {Promise<Object>} Generated file content and metadata
   */
  async generateBankFile(eftFormat, bankFileFormat, payRun, eftSettings, actionDate) {
    try {
      console.log(`\n=== Bank File Generation Request ===`);
      console.log(`EFT Format: ${eftFormat}`);
      console.log(`Bank File Format: ${bankFileFormat}`);
      console.log(`Pay Run ID: ${payRun.id || payRun._id}`);
      console.log(`Action Date: ${actionDate}`);

      // Create appropriate generator
      const generator = this.createGenerator(eftFormat, bankFileFormat);
      
      // Generate file content
      const fileContent = await generator.generate(payRun, eftSettings, actionDate);
      
      // Get file metadata
      const fileExtension = getFileExtensionForFormat(eftFormat);
      const mimeType = generator.getMimeType();
      
      // Generate filename
      const filename = this.generateFileName(payRun.id || payRun._id, eftFormat, fileExtension);
      
      console.log(`\n=== Generation Complete ===`);
      console.log(`Generator: ${generator.constructor.name}`);
      console.log(`Filename: ${filename}`);
      console.log(`File Size: ${fileContent.length} bytes`);
      
      return {
        content: fileContent,
        filename: filename,
        mimeType: mimeType,
        extension: fileExtension,
        generator: generator.constructor.name
      };
      
    } catch (error) {
      console.error('Bank file generation failed:', error);
      throw new Error(`Bank file generation failed: ${error.message}`);
    }
  }

  /**
   * Generate filename for bank file
   * @param {string} payRunId - Pay run identifier
   * @param {string} eftFormat - EFT format
   * @param {string} extension - File extension
   * @returns {string} Generated filename
   */
  generateFileName(payRunId, eftFormat, extension) {
    const formatMap = {
      'FNB Enterprise CSV': 'fnb_enterprise',
      'FNB Online Banking (ACB)': 'fnb_acb',
      'Standard Bank Value Dated / EFTS': 'standard_efts',
      'ABSA Business Integrator (.csv)': 'absa_integrator',
      'NedInform': 'nedbank_nedinform'
    };
    
    const formatCode = formatMap[eftFormat] || 'unknown';
    return `payrun_${payRunId}_${formatCode}.${extension}`;
  }

  /**
   * Validate generator requirements
   * @param {Object} payRun - Pay run data
   * @param {Object} eftSettings - EFT settings
   * @param {string} actionDate - Action date
   * @returns {Object} Validation result
   */
  validateGeneratorRequirements(payRun, eftSettings, actionDate) {
    const errors = [];
    const warnings = [];

    // Validate pay run
    if (!payRun) {
      errors.push('Pay run data is required');
    } else {
      if (!payRun.payrollPeriods || payRun.payrollPeriods.length === 0) {
        errors.push('Pay run must contain payroll periods');
      }
    }

    // Validate EFT settings
    if (!eftSettings) {
      errors.push('EFT settings are required');
    } else {
      if (!eftSettings.bankName) errors.push('Bank name is required in EFT settings');
      if (!eftSettings.branchCode) errors.push('Branch code is required in EFT settings');
      if (!eftSettings.accountNumber) errors.push('Account number is required in EFT settings');
      if (!eftSettings.accountHolder) warnings.push('Account holder name is recommended');
    }

    // Validate action date
    if (!actionDate) {
      errors.push('Action date is required');
    } else {
      const actionMoment = require('moment')(actionDate);
      if (!actionMoment.isValid()) {
        errors.push('Action date must be a valid date');
      }
    }

    return {
      valid: errors.length === 0,
      errors: errors,
      warnings: warnings
    };
  }

  /**
   * Get format statistics
   * @returns {Object} Statistics about available formats
   */
  getFormatStatistics() {
    const stats = {
      totalFormats: this.generators.size,
      implementedFormats: 0,
      formats: {}
    };

    for (const [format, GeneratorClass] of this.generators) {
      try {
        const tempInstance = new GeneratorClass();
        stats.implementedFormats++;
        stats.formats[format] = {
          name: GeneratorClass.name,
          implemented: true,
          fileExtension: tempInstance.getFileExtension()
        };
      } catch (error) {
        stats.formats[format] = {
          name: GeneratorClass.name,
          implemented: false,
          error: error.message
        };
      }
    }

    return stats;
  }
}

// Export singleton instance
module.exports = new BankFileGeneratorFactory();
