const BaseBankFileGenerator = require('./BaseBankFileGenerator');
const BankFileUtils = require('./BankFileUtils');
const { BankFormatConfigManager } = require('./BankFormatConfigs');

/**
 * Nedbank NedInform Generator
 * Generates NedInform format files for Nedbank with PRE-FORMAT and FREE-FORMAT options
 */
class NedbankNedInformGenerator extends BaseBankFileGenerator {
  
  constructor(formatOption = 'PRE-FORMAT') {
    const config = BankFormatConfigManager.getConfig('nedbank_nedinform');
    super(config);
    this.formatConfig = config;
    this.formatOption = formatOption; // 'PRE-FORMAT' or 'FREE-FORMAT'
    this.transactionType = 'SALARIES'; // 'SALARIES' or 'SUPPLIERS'
  }

  /**
   * Generate header record (Type H)
   * @param {Object} eftSettings - EFT settings
   * @param {string} actionDate - Action date
   * @param {number} totalRecords - Total number of detail records
   * @param {number} totalAmount - Total amount
   * @returns {string} Header record
   */
  generateHeader(eftSettings, actionDate, totalRecords, totalAmount) {
    try {
      // Format creation date
      const creationDate = BankFileUtils.formatDateYYYYMMDD(new Date());
      
      // Format company name
      const companyName = BankFileUtils.formatText(eftSettings.accountHolder || 'COMPANY', 40);
      
      // Build header record
      const headerFields = [
        'H',                    // Record Type
        companyName,            // Company Name
        creationDate,           // Creation Date YYYYMMDD
        this.transactionType    // Transaction Type (SALARIES/SUPPLIERS)
      ];
      
      return headerFields.join(',');
      
    } catch (error) {
      console.error('Error generating Nedbank NedInform header:', error);
      throw error;
    }
  }

  /**
   * Generate detail record (Type D)
   * @param {Object} payslip - Payslip/transaction data
   * @param {number} sequence - Sequence number
   * @param {Object} eftSettings - EFT settings
   * @returns {string} Detail record
   */
  generateDetail(payslip, sequence, eftSettings) {
    try {
      // Validate required fields
      this.validatePayslipData(payslip);
      
      // Extract employee data
      const employee = payslip.employee;
      const amount = payslip.netPay || 0;
      
      // Validate bank details
      const accountNumber = BankFileUtils.validateAccountNumber(employee.accountNumber, 'nedbank');
      const branchCode = BankFileUtils.validateBranchCode(employee.branchCode, 'nedbank');
      
      // Validate amount
      BankFileUtils.validateAmount(amount);
      
      // Generate reference
      const reference = this.generateReference(payslip, sequence);
      
      // Format beneficiary name
      const beneficiaryName = this.formatBeneficiaryName(employee);
      
      // Generate beneficiary reference (required for PRE-FORMAT)
      const beneficiaryReference = this.generateBeneficiaryReference(employee, sequence);
      
      // Build detail record based on format option
      let detailFields;
      
      if (this.formatOption === 'PRE-FORMAT') {
        detailFields = [
          'D',                                        // Record Type
          accountNumber,                              // Account Number
          branchCode,                                 // Branch Code
          BankFileUtils.formatAmountDecimal(amount, 2), // Amount
          reference,                                  // Reference
          beneficiaryName,                            // Beneficiary Name
          beneficiaryReference                        // Beneficiary Reference (required for PRE-FORMAT)
        ];
      } else {
        // FREE-FORMAT
        detailFields = [
          'D',                                        // Record Type
          accountNumber,                              // Account Number
          branchCode,                                 // Branch Code
          BankFileUtils.formatAmountDecimal(amount, 2), // Amount
          reference,                                  // Reference
          beneficiaryName                             // Beneficiary Name
        ];
      }
      
      return detailFields.join(',');
      
    } catch (error) {
      console.error(`Error generating Nedbank NedInform detail for employee ${payslip.employee?.firstName} ${payslip.employee?.lastName}:`, error);
      throw error;
    }
  }

  /**
   * Generate trailer record (Type T)
   * @param {number} totalAmount - Total amount
   * @param {number} totalRecords - Total record count
   * @param {Object} eftSettings - EFT settings
   * @returns {string} Trailer record
   */
  generateTrailer(totalAmount, totalRecords, eftSettings) {
    try {
      // Build trailer record
      const trailerFields = [
        'T',                                        // Record Type
        totalRecords.toString(),                    // Record Count
        BankFileUtils.formatAmountDecimal(totalAmount, 2) // Total Amount
      ];
      
      return trailerFields.join(',');
      
    } catch (error) {
      console.error('Error generating Nedbank NedInform trailer:', error);
      throw error;
    }
  }

  /**
   * Validate payslip data
   * @param {Object} payslip - Payslip data
   */
  validatePayslipData(payslip) {
    if (!payslip) {
      throw new Error('Payslip data is required');
    }
    
    if (!payslip.employee) {
      throw new Error('Employee data is required');
    }
    
    const employee = payslip.employee;
    
    if (!employee.firstName || !employee.lastName) {
      throw new Error('Employee first name and last name are required');
    }
    
    if (!employee.accountNumber) {
      throw new Error('Employee account number is required');
    }
    
    if (!employee.branchCode) {
      throw new Error('Employee branch code is required');
    }
    
    if (!payslip.netPay || payslip.netPay <= 0) {
      throw new Error('Valid net pay amount is required');
    }
  }

  /**
   * Generate reference for transaction
   * @param {Object} payslip - Payslip data
   * @param {number} sequence - Sequence number
   * @returns {string} Generated reference
   */
  generateReference(payslip, sequence) {
    const employee = payslip.employee;
    const empId = employee.employeeId || employee._id || sequence;
    const date = BankFileUtils.formatDateYYYYMMDD(new Date()).substring(2); // YYMMDD
    
    // Format: SAL + YYMMDD + EmpId (max 20 chars for Nedbank)
    let reference = `SAL${date}${empId}`;
    
    // Ensure reference doesn't exceed max length
    if (reference.length > 20) {
      reference = reference.substring(0, 20);
    }
    
    // Validate reference format
    BankFileUtils.validateReference(reference, 20);
    
    return reference;
  }

  /**
   * Generate beneficiary reference (required for PRE-FORMAT)
   * @param {Object} employee - Employee data
   * @param {number} sequence - Sequence number
   * @returns {string} Generated beneficiary reference
   */
  generateBeneficiaryReference(employee, sequence) {
    if (this.formatOption !== 'PRE-FORMAT') {
      return '';
    }
    
    const empId = employee.employeeId || employee._id || sequence;
    
    // Format: BEN + EmpId (max 32 chars)
    let beneficiaryRef = `BEN${empId}`;
    
    // Ensure reference doesn't exceed max length
    if (beneficiaryRef.length > 32) {
      beneficiaryRef = beneficiaryRef.substring(0, 32);
    }
    
    return beneficiaryRef;
  }

  /**
   * Format beneficiary name for Nedbank
   * @param {Object} employee - Employee data
   * @returns {string} Formatted beneficiary name
   */
  formatBeneficiaryName(employee) {
    const firstName = (employee.firstName || '').trim();
    const lastName = (employee.lastName || '').trim();
    
    let fullName = `${firstName} ${lastName}`.trim();
    
    // Ensure name doesn't exceed reasonable length
    if (fullName.length > 40) {
      fullName = fullName.substring(0, 40);
    }
    
    // Remove any invalid characters
    fullName = fullName.replace(/[^A-Za-z\s\-']/g, '');
    
    return fullName;
  }

  /**
   * Set format option
   * @param {string} option - 'PRE-FORMAT' or 'FREE-FORMAT'
   */
  setFormatOption(option) {
    if (!['PRE-FORMAT', 'FREE-FORMAT'].includes(option)) {
      throw new Error('Invalid format option. Must be PRE-FORMAT or FREE-FORMAT');
    }
    this.formatOption = option;
  }

  /**
   * Set transaction type
   * @param {string} type - 'SALARIES' or 'SUPPLIERS'
   */
  setTransactionType(type) {
    if (!['SALARIES', 'SUPPLIERS'].includes(type)) {
      throw new Error('Invalid transaction type. Must be SALARIES or SUPPLIERS');
    }
    this.transactionType = type;
  }

  /**
   * Get file extension
   * @returns {string} File extension
   */
  getFileExtension() {
    return 'imp';
  }

  /**
   * Get MIME type
   * @returns {string} MIME type
   */
  getMimeType() {
    return 'application/octet-stream';
  }

  /**
   * Validate Nedbank-specific requirements
   * @param {Object} eftSettings - EFT settings
   * @returns {Object} Validation result
   */
  validateNedbankRequirements(eftSettings) {
    const errors = [];
    const warnings = [];
    
    // Check if bank is Nedbank
    if (eftSettings.bankName && !eftSettings.bankName.toLowerCase().includes('nedbank')) {
      warnings.push('EFT settings bank name does not indicate Nedbank');
    }
    
    // Validate account holder
    if (!eftSettings.accountHolder) {
      warnings.push('Account holder name is recommended for company identification');
    }
    
    // PRE-FORMAT specific validations
    if (this.formatOption === 'PRE-FORMAT') {
      warnings.push('PRE-FORMAT requires pre-registered beneficiary references with Nedbank');
    }
    
    return {
      valid: errors.length === 0,
      errors: errors,
      warnings: warnings
    };
  }

  /**
   * Get format information
   * @returns {Object} Format information
   */
  getFormatInfo() {
    return {
      name: 'Nedbank NedInform',
      description: `NedInform format for Nedbank (${this.formatOption})`,
      fileExtension: 'imp',
      mimeType: 'application/octet-stream',
      encoding: 'utf8',
      lineEnding: 'CRLF',
      formatOption: this.formatOption,
      transactionType: this.transactionType,
      features: [
        'Header, detail, and trailer records',
        'Comma-separated format',
        'Decimal amount format',
        'YYYYMMDD date format',
        'Configurable format options',
        'Transaction type support'
      ],
      limitations: [
        'Maximum 20 character reference',
        'PRE-FORMAT requires beneficiary registration',
        'Format option affects record structure'
      ]
    };
  }
}

module.exports = NedbankNedInformGenerator;
