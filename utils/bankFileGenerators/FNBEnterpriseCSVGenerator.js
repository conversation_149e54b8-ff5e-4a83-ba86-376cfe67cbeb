const BaseBankFileGenerator = require('./BaseBankFileGenerator');
const BankFileUtils = require('./BankFileUtils');
const { BankFormatConfigManager } = require('./BankFormatConfigs');

/**
 * FNB Enterprise CSV Generator
 * Generates CSV files for FNB Enterprise Online Banking
 */
class FNBEnterpriseCSVGenerator extends BaseBankFileGenerator {
  
  constructor() {
    const config = BankFormatConfigManager.getConfig('fnb_csv');
    super(config);
    this.formatConfig = config;
  }

  /**
   * Generate CSV header row
   * @param {Object} eftSettings - EFT settings
   * @param {string} actionDate - Action date
   * @returns {string} CSV header row
   */
  generateHeader(eftSettings, actionDate) {
    const headers = [
      'Company Name',
      'Account Number', 
      'Branch Code',
      'Amount',
      'Reference',
      'Employee Name',
      'Action Date'
    ];
    
    return BankFileUtils.formatCSVRow(headers);
  }

  /**
   * Generate CSV detail row for each transaction
   * @param {Object} payslip - Payslip/transaction data
   * @param {number} sequence - Sequence number
   * @param {Object} eftSettings - EFT settings
   * @returns {string} CSV detail row
   */
  generateDetail(payslip, sequence, eftSettings) {
    try {
      // Validate required fields
      this.validatePayslipData(payslip);
      
      // Extract employee data
      const employee = payslip.employee;
      const amount = payslip.netPay || 0;
      
      // Validate bank details
      const accountNumber = BankFileUtils.validateAccountNumber(employee.accountNumber, 'fnb');
      const branchCode = BankFileUtils.validateBranchCode(employee.branchCode, 'fnb');
      
      // Validate amount
      BankFileUtils.validateAmount(amount);
      
      // Generate reference
      const reference = this.generateReference(payslip, sequence);
      
      // Format employee name
      const employeeName = this.formatEmployeeName(employee);
      
      // Format company name
      const companyName = BankFileUtils.formatText(eftSettings.accountHolder || 'Company', 40);
      
      // Format action date
      const actionDate = BankFileUtils.formatDateISO(new Date());
      
      // Create CSV row
      const rowData = [
        companyName,
        accountNumber,
        branchCode,
        BankFileUtils.formatAmountDecimal(amount, 2),
        reference,
        employeeName,
        actionDate
      ];
      
      return BankFileUtils.formatCSVRow(rowData);
      
    } catch (error) {
      console.error(`Error generating FNB Enterprise CSV detail for employee ${payslip.employee?.firstName} ${payslip.employee?.lastName}:`, error);
      throw error;
    }
  }

  /**
   * Generate CSV trailer (not used for CSV format)
   * @param {number} totalAmount - Total amount
   * @param {number} totalRecords - Total record count
   * @returns {null} No trailer for CSV format
   */
  generateTrailer(totalAmount, totalRecords) {
    // CSV format doesn't require a trailer record
    return null;
  }

  /**
   * Override generate method to handle CSV-specific logic
   */
  async generate(payRun, eftSettings, actionDate) {
    try {
      console.log('\n=== Generating FNB Enterprise CSV Bank File ===');
      
      // Validate inputs
      this.validateInputs(payRun, eftSettings, actionDate);
      
      // Extract valid transactions
      const validTransactions = this.extractValidTransactions(payRun);
      
      if (validTransactions.length === 0) {
        throw new Error('No valid EFT transactions found for bank file generation');
      }
      
      const records = [];
      let totalAmount = 0;
      let sequence = 1;
      
      // Generate header
      records.push(this.generateHeader(eftSettings, actionDate));
      
      // Generate detail records
      for (const transaction of validTransactions) {
        const detailRecord = this.generateDetail(transaction, sequence++, eftSettings);
        records.push(detailRecord);
        totalAmount += transaction.netPay || 0;
      }
      
      // Join records with line ending
      const fileContent = records.join(this.config.lineEnding);
      
      console.log(`Generated ${records.length} records (including header) for ${validTransactions.length} transactions`);
      console.log(`Total amount: R${totalAmount.toFixed(2)}`);
      
      return fileContent;
      
    } catch (error) {
      console.error('Error generating FNB Enterprise CSV bank file:', error);
      throw error;
    }
  }

  /**
   * Validate payslip data
   * @param {Object} payslip - Payslip data
   */
  validatePayslipData(payslip) {
    if (!payslip) {
      throw new Error('Payslip data is required');
    }
    
    if (!payslip.employee) {
      throw new Error('Employee data is required');
    }
    
    const employee = payslip.employee;
    
    if (!employee.firstName || !employee.lastName) {
      throw new Error('Employee first name and last name are required');
    }
    
    if (!employee.accountNumber) {
      throw new Error('Employee account number is required');
    }
    
    if (!employee.branchCode) {
      throw new Error('Employee branch code is required');
    }
    
    if (!payslip.netPay || payslip.netPay <= 0) {
      throw new Error('Valid net pay amount is required');
    }
  }

  /**
   * Generate reference for transaction
   * @param {Object} payslip - Payslip data
   * @param {number} sequence - Sequence number
   * @returns {string} Generated reference
   */
  generateReference(payslip, sequence) {
    const employee = payslip.employee;
    const empId = employee.employeeId || employee._id || sequence;
    const date = BankFileUtils.formatDateYYYYMMDD(new Date()).substring(2); // YYMMDD
    
    // Format: SAL + YYMMDD + EmpId (max 20 chars)
    let reference = `SAL${date}${empId}`;
    
    // Ensure reference doesn't exceed max length
    if (reference.length > 20) {
      reference = reference.substring(0, 20);
    }
    
    // Validate reference format
    BankFileUtils.validateReference(reference, 20);
    
    return reference;
  }

  /**
   * Format employee name for CSV
   * @param {Object} employee - Employee data
   * @returns {string} Formatted employee name
   */
  formatEmployeeName(employee) {
    const firstName = (employee.firstName || '').trim();
    const lastName = (employee.lastName || '').trim();
    
    let fullName = `${firstName} ${lastName}`.trim();
    
    // Ensure name doesn't exceed max length (30 chars for FNB)
    if (fullName.length > 30) {
      fullName = fullName.substring(0, 30);
    }
    
    // Remove any invalid characters
    fullName = fullName.replace(/[^A-Za-z\s\-']/g, '');
    
    return fullName;
  }

  /**
   * Get file extension
   * @returns {string} File extension
   */
  getFileExtension() {
    return 'csv';
  }

  /**
   * Get MIME type
   * @returns {string} MIME type
   */
  getMimeType() {
    return 'text/csv';
  }

  /**
   * Validate FNB-specific requirements
   * @param {Object} eftSettings - EFT settings
   * @returns {Object} Validation result
   */
  validateFNBRequirements(eftSettings) {
    const errors = [];
    const warnings = [];
    
    // Check if bank is FNB
    if (eftSettings.bankName && !eftSettings.bankName.toLowerCase().includes('fnb')) {
      warnings.push('EFT settings bank name does not indicate FNB');
    }
    
    // Validate account holder
    if (!eftSettings.accountHolder) {
      warnings.push('Account holder name is recommended for company identification');
    }
    
    return {
      valid: errors.length === 0,
      errors: errors,
      warnings: warnings
    };
  }

  /**
   * Get format information
   * @returns {Object} Format information
   */
  getFormatInfo() {
    return {
      name: 'FNB Enterprise CSV',
      description: 'CSV format for FNB Enterprise Online Banking',
      fileExtension: 'csv',
      mimeType: 'text/csv',
      encoding: 'utf8',
      lineEnding: 'CRLF',
      features: [
        'CSV format with headers',
        'Decimal amount format',
        'ISO date format',
        'Automatic reference generation',
        'Employee name validation'
      ],
      limitations: [
        'No trailer record',
        'Maximum 20 character reference',
        'Maximum 30 character employee name'
      ]
    };
  }
}

module.exports = FNBEnterpriseCSVGenerator;
