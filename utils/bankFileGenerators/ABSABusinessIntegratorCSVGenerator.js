const BaseBankFileGenerator = require('./BaseBankFileGenerator');
const BankFileUtils = require('./BankFileUtils');
const { BankFormatConfigManager } = require('./BankFormatConfigs');

/**
 * ABSA Business Integrator CSV Generator
 * Generates CSV files for ABSA Business Integrator system
 */
class ABSABusinessIntegratorCSVGenerator extends BaseBankFileGenerator {
  
  constructor() {
    const config = BankFormatConfigManager.getConfig('absa_csv');
    super(config);
    this.formatConfig = config;
  }

  /**
   * Generate CSV header row
   * @param {Object} eftSettings - EFT settings
   * @param {string} actionDate - Action date
   * @returns {string} CSV header row
   */
  generateHeader(eftSettings, actionDate) {
    // ABSA Business Integrator CSV format headers
    const headers = [
      'Account Number',
      'Branch Code',
      'Amount',
      'Reference',
      'Beneficiary Name',
      'Action Date'
    ];
    
    // ABSA format doesn't use quotes around fields
    return headers.join(',');
  }

  /**
   * Generate CSV detail row for each transaction
   * @param {Object} payslip - Payslip/transaction data
   * @param {number} sequence - Sequence number
   * @param {Object} eftSettings - EFT settings
   * @param {string} actionDate - Action date for the transaction
   * @returns {string} CSV detail row
   */
  generateDetail(payslip, sequence, eftSettings, actionDate = null) {
    try {
      // Validate required fields
      this.validatePayslipData(payslip);
      
      // Extract employee data
      const employee = payslip.employee;
      const amount = payslip.netPay || 0;
      
      // Validate bank details
      const accountNumber = BankFileUtils.validateAccountNumber(employee.accountNumber, 'absa');
      const branchCode = BankFileUtils.validateBranchCode(employee.branchCode, 'absa');
      
      // Validate amount
      BankFileUtils.validateAmount(amount, 0.01, 999999.99);
      
      // Generate reference (max 12 chars for ABSA)
      const reference = this.generateReference(payslip, sequence);
      
      // Format beneficiary name (max 32 chars for ABSA)
      const beneficiaryName = this.formatBeneficiaryName(employee);
      
      // Format action date (DD/MM/YYYY format for ABSA)
      const formattedActionDate = BankFileUtils.formatDateDDMMYYYY(
        actionDate ? new Date(actionDate) : new Date(),
        '/'
      );
      
      // Create CSV row without quotes (ABSA format)
      const rowData = [
        accountNumber,
        branchCode,
        BankFileUtils.formatAmountDecimal(amount, 2),
        reference,
        beneficiaryName,
        formattedActionDate
      ];
      
      return rowData.join(',');
      
    } catch (error) {
      console.error(`Error generating ABSA Business Integrator CSV detail for employee ${payslip.employee?.firstName} ${payslip.employee?.lastName}:`, error);
      throw error;
    }
  }

  /**
   * Generate CSV trailer (not used for CSV format)
   * @param {number} totalAmount - Total amount
   * @param {number} totalRecords - Total record count
   * @returns {null} No trailer for CSV format
   */
  generateTrailer(totalAmount, totalRecords) {
    // ABSA CSV format doesn't require a trailer record
    return null;
  }

  /**
   * Override generate method to handle CSV-specific logic
   */
  async generate(payRun, eftSettings, actionDate) {
    try {
      console.log('\n=== Generating ABSA Business Integrator CSV Bank File ===');
      
      // Validate inputs
      this.validateInputs(payRun, eftSettings, actionDate);
      
      // Extract valid transactions
      const validTransactions = this.extractValidTransactions(payRun);
      
      if (validTransactions.length === 0) {
        throw new Error('No valid EFT transactions found for bank file generation');
      }
      
      const records = [];
      let totalAmount = 0;
      let sequence = 1;
      
      // Generate header
      records.push(this.generateHeader(eftSettings, actionDate));
      
      // Generate detail records
      for (const transaction of validTransactions) {
        const detailRecord = this.generateDetail(transaction, sequence++, eftSettings, actionDate);
        records.push(detailRecord);
        totalAmount += transaction.netPay || 0;
      }
      
      // Join records with line ending
      const fileContent = records.join(this.config.lineEnding);
      
      console.log(`Generated ${records.length} records (including header) for ${validTransactions.length} transactions`);
      console.log(`Total amount: R${totalAmount.toFixed(2)}`);
      
      return fileContent;
      
    } catch (error) {
      console.error('Error generating ABSA Business Integrator CSV bank file:', error);
      throw error;
    }
  }

  /**
   * Validate payslip data
   * @param {Object} payslip - Payslip data
   */
  validatePayslipData(payslip) {
    if (!payslip) {
      throw new Error('Payslip data is required');
    }
    
    if (!payslip.employee) {
      throw new Error('Employee data is required');
    }
    
    const employee = payslip.employee;
    
    if (!employee.firstName || !employee.lastName) {
      throw new Error('Employee first name and last name are required');
    }
    
    if (!employee.accountNumber) {
      throw new Error('Employee account number is required');
    }
    
    if (!employee.branchCode) {
      throw new Error('Employee branch code is required');
    }
    
    if (!payslip.netPay || payslip.netPay <= 0) {
      throw new Error('Valid net pay amount is required');
    }
  }

  /**
   * Generate reference for transaction (ABSA max 12 characters)
   * @param {Object} payslip - Payslip data
   * @param {number} sequence - Sequence number
   * @returns {string} Generated reference
   */
  generateReference(payslip, sequence) {
    const employee = payslip.employee;
    const empId = employee.employeeId || employee._id || sequence;
    const date = BankFileUtils.formatDateYYYYMMDD(new Date()).substring(2); // YYMMDD
    
    // Format: SAL + YYMMDD + EmpId (max 12 chars for ABSA)
    let reference = `SAL${date}${empId}`;
    
    // Ensure reference doesn't exceed max length
    if (reference.length > 12) {
      reference = reference.substring(0, 12);
    }
    
    // Validate reference format (ABSA allows alphanumeric, spaces, hyphens, underscores)
    BankFileUtils.validateReference(reference, 12, /^[A-Za-z0-9\s\-_]+$/);
    
    return reference;
  }

  /**
   * Format beneficiary name for ABSA (max 32 characters)
   * @param {Object} employee - Employee data
   * @returns {string} Formatted beneficiary name
   */
  formatBeneficiaryName(employee) {
    const firstName = (employee.firstName || '').trim();
    const lastName = (employee.lastName || '').trim();
    
    let fullName = `${firstName} ${lastName}`.trim();
    
    // Ensure name doesn't exceed max length (32 chars for ABSA)
    if (fullName.length > 32) {
      fullName = fullName.substring(0, 32);
    }
    
    // ABSA allows letters, spaces, hyphens, and apostrophes
    fullName = fullName.replace(/[^A-Za-z\s\-']/g, '');
    
    // Validate beneficiary name format
    if (!/^[A-Za-z\s\-']+$/.test(fullName)) {
      throw new Error('Beneficiary name contains invalid characters');
    }
    
    return fullName;
  }

  /**
   * Get file extension
   * @returns {string} File extension
   */
  getFileExtension() {
    return 'csv';
  }

  /**
   * Get MIME type
   * @returns {string} MIME type
   */
  getMimeType() {
    return 'text/csv';
  }

  /**
   * Validate ABSA-specific requirements
   * @param {Object} eftSettings - EFT settings
   * @returns {Object} Validation result
   */
  validateABSARequirements(eftSettings) {
    const errors = [];
    const warnings = [];
    
    // Check if bank is ABSA
    if (eftSettings.bankName && !eftSettings.bankName.toLowerCase().includes('absa')) {
      warnings.push('EFT settings bank name does not indicate ABSA');
    }
    
    // Validate account holder
    if (!eftSettings.accountHolder) {
      warnings.push('Account holder name is recommended for company identification');
    }
    
    return {
      valid: errors.length === 0,
      errors: errors,
      warnings: warnings
    };
  }

  /**
   * Get format information
   * @returns {Object} Format information
   */
  getFormatInfo() {
    return {
      name: 'ABSA Business Integrator CSV',
      description: 'CSV format for ABSA Business Integrator system',
      fileExtension: 'csv',
      mimeType: 'text/csv',
      encoding: 'utf8',
      lineEnding: 'CRLF',
      features: [
        'CSV format with headers',
        'No quotes around fields',
        'Decimal amount format',
        'DD/MM/YYYY date format',
        'Automatic reference generation',
        'Beneficiary name validation'
      ],
      limitations: [
        'No trailer record',
        'Maximum 12 character reference',
        'Maximum 32 character beneficiary name',
        'Amount range: R0.01 - R999,999.99'
      ]
    };
  }
}

module.exports = ABSABusinessIntegratorCSVGenerator;
