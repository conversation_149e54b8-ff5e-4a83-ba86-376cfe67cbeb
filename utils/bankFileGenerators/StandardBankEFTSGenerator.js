const BaseBankFileGenerator = require('./BaseBankFileGenerator');
const BankFileUtils = require('./BankFileUtils');
const { BankFormatConfigManager } = require('./BankFormatConfigs');

/**
 * Standard Bank EFTS Generator
 * Generates fixed-width text files for Standard Bank EFTS system
 */
class StandardBankEFTSGenerator extends BaseBankFileGenerator {
  
  constructor() {
    const config = BankFormatConfigManager.getConfig('standard_efts');
    super(config);
    this.formatConfig = config;
  }

  /**
   * Generate header record (Type 01)
   * @param {Object} eftSettings - EFT settings
   * @param {string} actionDate - Action date
   * @param {number} totalRecords - Total number of detail records
   * @param {number} totalAmount - Total amount
   * @returns {string} Header record
   */
  generateHeader(eftSettings, actionDate, totalRecords, totalAmount) {
    try {
      // Format creation date (today)
      const creationDate = BankFileUtils.formatDateYYYYMMDD(new Date());
      
      // Format company name
      const companyName = BankFileUtils.formatText(eftSettings.accountHolder || 'COMPANY', 20);
      
      // Generate sequence number (random 3-digit)
      const sequenceNumber = BankFileUtils.generateRandomSequence(3);
      
      // Build header record (80 characters)
      const headerRecord = [
        '01',                           // Record Type (2)
        'HEADER',                       // Record Description (6)
        '    ',                         // Filler (4)
        companyName,                    // Company Name (20)
        '        ',                     // Filler (8)
        creationDate,                   // Creation Date YYYYMMDD (8)
        '    ',                         // Filler (4)
        sequenceNumber,                 // Sequence Number (3)
        ''.padEnd(25, ' ')              // Filler to make 80 chars (25)
      ].join('');
      
      // Validate record length
      return BankFileUtils.validateFixedWidthRecord(headerRecord, 80);
      
    } catch (error) {
      console.error('Error generating Standard Bank EFTS header:', error);
      throw error;
    }
  }

  /**
   * Generate detail record (Type 10)
   * @param {Object} payslip - Payslip/transaction data
   * @param {number} sequence - Sequence number
   * @param {Object} eftSettings - EFT settings
   * @returns {string} Detail record
   */
  generateDetail(payslip, sequence, eftSettings) {
    try {
      // Validate required fields
      this.validatePayslipData(payslip);
      
      // Extract employee data
      const employee = payslip.employee;
      const amount = payslip.netPay || 0;
      
      // Validate and format bank details
      const accountNumber = BankFileUtils.validateAccountNumber(employee.accountNumber, 'standard');
      const branchCode = BankFileUtils.validateBranchCode(employee.branchCode, 'standard');
      
      // Validate amount
      BankFileUtils.validateAmount(amount);
      
      // Format amount in cents
      const amountInCents = BankFileUtils.formatAmountPadded(amount, 12, '0', true);
      
      // Generate reference
      const reference = this.generateReference(payslip, sequence);
      
      // Format employee name
      const employeeName = this.formatEmployeeName(employee);
      
      // Build detail record (80 characters)
      const detailRecord = [
        '10',                                           // Record Type (2)
        BankFileUtils.padLeft(branchCode, 9, '0'),      // Branch Code (9)
        BankFileUtils.padLeft(accountNumber, 8, '0'),   // Account Number (8)
        '*********',                                    // Zeros (9)
        BankFileUtils.padLeft(sequence.toString(), 6, '0'), // Sequence (6)
        amountInCents,                                  // Amount in cents (12)
        '0000000',                                      // Zeros (7)
        BankFileUtils.padLeft(branchCode, 6, '0'),      // Employee Branch (6)
        BankFileUtils.formatText(reference, 12),        // Reference (12)
        BankFileUtils.formatText(employeeName, 9)       // Employee Name (9)
      ].join('');
      
      // Validate record length
      return BankFileUtils.validateFixedWidthRecord(detailRecord, 80);
      
    } catch (error) {
      console.error(`Error generating Standard Bank EFTS detail for employee ${payslip.employee?.firstName} ${payslip.employee?.lastName}:`, error);
      throw error;
    }
  }

  /**
   * Generate trailer record (Type 99)
   * @param {number} totalAmount - Total amount
   * @param {number} totalRecords - Total record count
   * @param {Object} eftSettings - EFT settings
   * @returns {string} Trailer record
   */
  generateTrailer(totalAmount, totalRecords, eftSettings) {
    try {
      // Format total amount in cents
      const totalAmountInCents = BankFileUtils.formatAmountPadded(totalAmount, 15, '0', true);
      
      // Format record count (including header and trailer)
      const recordCount = BankFileUtils.padLeft((totalRecords + 2).toString(), 9, '0');
      
      // Build trailer record (80 characters)
      const trailerRecord = [
        '99',                           // Record Type (2)
        'TRAILER',                      // Record Description (7)
        ' ',                            // Filler (1)
        recordCount,                    // Number of Records (9)
        totalAmountInCents,             // Total Amount in cents (15)
        ''.padEnd(46, '0')              // Filler to make 80 chars (46)
      ].join('');
      
      // Validate record length
      return BankFileUtils.validateFixedWidthRecord(trailerRecord, 80);
      
    } catch (error) {
      console.error('Error generating Standard Bank EFTS trailer:', error);
      throw error;
    }
  }

  /**
   * Validate payslip data
   * @param {Object} payslip - Payslip data
   */
  validatePayslipData(payslip) {
    if (!payslip) {
      throw new Error('Payslip data is required');
    }
    
    if (!payslip.employee) {
      throw new Error('Employee data is required');
    }
    
    const employee = payslip.employee;
    
    if (!employee.firstName || !employee.lastName) {
      throw new Error('Employee first name and last name are required');
    }
    
    if (!employee.accountNumber) {
      throw new Error('Employee account number is required');
    }
    
    if (!employee.branchCode) {
      throw new Error('Employee branch code is required');
    }
    
    if (!payslip.netPay || payslip.netPay <= 0) {
      throw new Error('Valid net pay amount is required');
    }
  }

  /**
   * Generate reference for transaction
   * @param {Object} payslip - Payslip data
   * @param {number} sequence - Sequence number
   * @returns {string} Generated reference
   */
  generateReference(payslip, sequence) {
    const employee = payslip.employee;
    const empId = employee.employeeId || employee._id || sequence;
    const date = BankFileUtils.formatDateYYYYMMDD(new Date()).substring(2); // YYMMDD
    
    // Format: SAL + YYMMDD + EmpId (max 12 chars for Standard Bank)
    let reference = `SAL${date}${empId}`;
    
    // Ensure reference doesn't exceed max length
    if (reference.length > 12) {
      reference = reference.substring(0, 12);
    }
    
    // Validate reference format
    BankFileUtils.validateReference(reference, 12);
    
    return reference;
  }

  /**
   * Format employee name for Standard Bank (max 9 characters)
   * @param {Object} employee - Employee data
   * @returns {string} Formatted employee name
   */
  formatEmployeeName(employee) {
    const firstName = (employee.firstName || '').trim();
    const lastName = (employee.lastName || '').trim();
    
    // Try full name first
    let fullName = `${firstName} ${lastName}`.trim();
    
    // If too long, try first name + last initial
    if (fullName.length > 9) {
      const lastInitial = lastName.charAt(0);
      fullName = `${firstName} ${lastInitial}`.trim();
    }
    
    // If still too long, truncate first name
    if (fullName.length > 9) {
      fullName = firstName.substring(0, 9);
    }
    
    // Remove any invalid characters and convert to uppercase
    fullName = fullName.replace(/[^A-Za-z\s]/g, '').toUpperCase();
    
    return fullName;
  }

  /**
   * Get file extension
   * @returns {string} File extension
   */
  getFileExtension() {
    return 'txt';
  }

  /**
   * Get MIME type
   * @returns {string} MIME type
   */
  getMimeType() {
    return 'text/plain';
  }

  /**
   * Validate Standard Bank specific requirements
   * @param {Object} eftSettings - EFT settings
   * @returns {Object} Validation result
   */
  validateStandardBankRequirements(eftSettings) {
    const errors = [];
    const warnings = [];
    
    // Check if bank is Standard Bank
    if (eftSettings.bankName && !eftSettings.bankName.toLowerCase().includes('standard')) {
      warnings.push('EFT settings bank name does not indicate Standard Bank');
    }
    
    // Validate account holder
    if (!eftSettings.accountHolder) {
      warnings.push('Account holder name is recommended for company identification');
    }
    
    // Check account holder length for header record
    if (eftSettings.accountHolder && eftSettings.accountHolder.length > 20) {
      warnings.push('Account holder name will be truncated to 20 characters in header');
    }
    
    return {
      valid: errors.length === 0,
      errors: errors,
      warnings: warnings
    };
  }

  /**
   * Get format information
   * @returns {Object} Format information
   */
  getFormatInfo() {
    return {
      name: 'Standard Bank EFTS',
      description: 'Fixed-width text format for Standard Bank EFTS system',
      fileExtension: 'txt',
      mimeType: 'text/plain',
      encoding: 'ascii',
      lineEnding: 'CRLF',
      recordLength: 80,
      features: [
        'Fixed-width 80-character records',
        'Header, detail, and trailer records',
        'Amount in cents format',
        'Automatic sequence numbering',
        'Employee name truncation'
      ],
      limitations: [
        'Maximum 12 character reference',
        'Maximum 9 character employee name',
        'Fixed record structure'
      ]
    };
  }
}

module.exports = StandardBankEFTSGenerator;
