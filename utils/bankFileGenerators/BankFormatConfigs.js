/**
 * Bank Format Configuration System
 * Centralized configuration for all bank file formats
 */

const BankFormatConfigs = {
  
  /**
   * FNB Online Banking (ACB) Format Configuration
   */
  'fnb': {
    name: 'FNB Online Banking (ACB)',
    fileExtension: 'acb',
    encoding: 'ascii',
    lineEnding: '\n',
    recordLength: 198,
    amountFormat: 'cents',
    dateFormat: 'YYMMDD',
    validation: {
      accountNumber: { min: 8, max: 11, pattern: /^\d+$/ },
      branchCode: { length: 6, pattern: /^\d+$/ },
      amount: { min: 0.01, max: 999999.99 },
      reference: { maxLength: 20, pattern: /^[A-Za-z0-9\s\-_]+$/ }
    },
    records: {
      header: { type: '02', length: 198 },
      userHeader: { type: '04', length: 198 },
      detail: { type: '10', length: 198 },
      contra: { type: '92', length: 198 },
      trailer: { type: '94', length: 198 }
    }
  },

  /**
   * FNB Enterprise CSV Format Configuration
   */
  'fnb_csv': {
    name: 'FNB Enterprise CSV',
    fileExtension: 'csv',
    encoding: 'utf8',
    lineEnding: '\r\n',
    amountFormat: 'decimal',
    dateFormat: 'YYYY-MM-DD',
    fieldDelimiter: ',',
    useQuotes: true,
    validation: {
      accountNumber: { min: 8, max: 11, pattern: /^\d+$/ },
      branchCode: { length: 6, pattern: /^\d+$/ },
      amount: { min: 0.01, max: 999999.99 },
      reference: { maxLength: 20, pattern: /^[A-Za-z0-9\s\-_]+$/ }
    },
    fields: [
      { name: 'companyName', required: true, maxLength: 40 },
      { name: 'accountNumber', required: true, maxLength: 11 },
      { name: 'branchCode', required: true, maxLength: 6 },
      { name: 'amount', required: true, format: 'decimal' },
      { name: 'reference', required: true, maxLength: 20 },
      { name: 'employeeName', required: true, maxLength: 30 },
      { name: 'actionDate', required: true, format: 'date' }
    ]
  },

  /**
   * Standard Bank EFTS Format Configuration
   */
  'standard_efts': {
    name: 'Standard Bank EFTS',
    fileExtension: 'txt',
    encoding: 'ascii',
    lineEnding: '\r\n',
    recordLength: 80,
    amountFormat: 'cents',
    dateFormat: 'YYYYMMDD',
    validation: {
      accountNumber: { min: 8, max: 11, pattern: /^\d+$/ },
      branchCode: { length: 6, pattern: /^\d+$/ },
      amount: { min: 0.01, max: 999999.99 },
      reference: { maxLength: 12, pattern: /^[A-Za-z0-9\s\-_]+$/ }
    },
    records: {
      header: { type: '01', length: 80 },
      detail: { type: '10', length: 80 },
      trailer: { type: '99', length: 80 }
    }
  },

  /**
   * ABSA Business Integrator CSV Format Configuration
   */
  'absa_csv': {
    name: 'ABSA Business Integrator CSV',
    fileExtension: 'csv',
    encoding: 'utf8',
    lineEnding: '\r\n',
    amountFormat: 'decimal',
    dateFormat: 'DD/MM/YYYY',
    fieldDelimiter: ',',
    useQuotes: false,
    validation: {
      accountNumber: { min: 8, max: 11, pattern: /^\d+$/ },
      branchCode: { length: 6, pattern: /^\d+$/ },
      amount: { min: 0.01, max: 999999.99 },
      reference: { maxLength: 12, pattern: /^[A-Za-z0-9\s\-_]+$/ },
      beneficiaryName: { maxLength: 32, pattern: /^[A-Za-z\s\-']+$/ }
    },
    fields: [
      { name: 'accountNumber', required: true, maxLength: 11 },
      { name: 'branchCode', required: true, maxLength: 6 },
      { name: 'amount', required: true, format: 'decimal' },
      { name: 'reference', required: true, maxLength: 12 },
      { name: 'beneficiaryName', required: true, maxLength: 32 },
      { name: 'actionDate', required: true, format: 'date' }
    ]
  },

  /**
   * Nedbank NedInform Format Configuration
   */
  'nedbank_nedinform': {
    name: 'Nedbank NedInform',
    fileExtension: 'imp',
    encoding: 'utf8',
    lineEnding: '\r\n',
    amountFormat: 'decimal',
    dateFormat: 'YYYYMMDD',
    fieldDelimiter: ',',
    useQuotes: false,
    formatOptions: ['PRE-FORMAT', 'FREE-FORMAT'],
    transactionTypes: ['SALARIES', 'SUPPLIERS'],
    validation: {
      accountNumber: { min: 8, max: 11, pattern: /^\d+$/ },
      branchCode: { length: 6, pattern: /^\d+$/ },
      amount: { min: 0.01, max: 999999.99 },
      reference: { maxLength: 20, pattern: /^[A-Za-z0-9\s\-_]+$/ },
      beneficiaryReference: { maxLength: 32, pattern: /^[A-Za-z0-9\s\-_]+$/ }
    },
    records: {
      header: { type: 'H', fields: ['companyName', 'date', 'transactionType'] },
      detail: { type: 'D', fields: ['accountNumber', 'branchCode', 'amount', 'reference', 'beneficiaryName', 'beneficiaryReference'] },
      trailer: { type: 'T', fields: ['recordCount', 'totalAmount'] }
    }
  }
};

/**
 * Configuration utility functions
 */
class BankFormatConfigManager {
  
  /**
   * Get configuration for a specific format
   * @param {string} formatKey - Format identifier
   * @returns {Object} Format configuration
   */
  static getConfig(formatKey) {
    const config = BankFormatConfigs[formatKey];
    if (!config) {
      throw new Error(`Configuration not found for format: ${formatKey}`);
    }
    return { ...config }; // Return copy to prevent modification
  }

  /**
   * Get all available format configurations
   * @returns {Object} All format configurations
   */
  static getAllConfigs() {
    return { ...BankFormatConfigs };
  }

  /**
   * Get validation rules for a specific format
   * @param {string} formatKey - Format identifier
   * @returns {Object} Validation rules
   */
  static getValidationRules(formatKey) {
    const config = this.getConfig(formatKey);
    return config.validation || {};
  }

  /**
   * Get field definitions for CSV formats
   * @param {string} formatKey - Format identifier
   * @returns {Array} Field definitions
   */
  static getFieldDefinitions(formatKey) {
    const config = this.getConfig(formatKey);
    return config.fields || [];
  }

  /**
   * Get record definitions for fixed-width formats
   * @param {string} formatKey - Format identifier
   * @returns {Object} Record definitions
   */
  static getRecordDefinitions(formatKey) {
    const config = this.getConfig(formatKey);
    return config.records || {};
  }

  /**
   * Validate format configuration
   * @param {string} formatKey - Format identifier
   * @returns {Object} Validation result
   */
  static validateConfig(formatKey) {
    try {
      const config = this.getConfig(formatKey);
      const errors = [];
      const warnings = [];

      // Required fields
      if (!config.name) errors.push('Format name is required');
      if (!config.fileExtension) errors.push('File extension is required');
      if (!config.encoding) errors.push('Encoding is required');
      if (!config.amountFormat) errors.push('Amount format is required');

      // Format-specific validation
      if (config.fileExtension === 'csv') {
        if (!config.fieldDelimiter) warnings.push('Field delimiter not specified for CSV format');
        if (!config.fields || config.fields.length === 0) {
          warnings.push('No field definitions found for CSV format');
        }
      }

      if (config.recordLength && !config.records) {
        warnings.push('Record length specified but no record definitions found');
      }

      return {
        valid: errors.length === 0,
        errors: errors,
        warnings: warnings
      };
    } catch (error) {
      return {
        valid: false,
        errors: [error.message],
        warnings: []
      };
    }
  }

  /**
   * Get format by file extension
   * @param {string} extension - File extension
   * @returns {Array} Matching format keys
   */
  static getFormatsByExtension(extension) {
    const matchingFormats = [];
    for (const [key, config] of Object.entries(BankFormatConfigs)) {
      if (config.fileExtension === extension) {
        matchingFormats.push(key);
      }
    }
    return matchingFormats;
  }

  /**
   * Check if format supports specific feature
   * @param {string} formatKey - Format identifier
   * @param {string} feature - Feature to check
   * @returns {boolean} True if feature is supported
   */
  static supportsFeature(formatKey, feature) {
    const config = this.getConfig(formatKey);
    
    switch (feature) {
      case 'csv':
        return config.fileExtension === 'csv';
      case 'fixed-width':
        return !!config.recordLength;
      case 'multiple-records':
        return !!config.records && Object.keys(config.records).length > 1;
      case 'quotes':
        return !!config.useQuotes;
      case 'format-options':
        return !!config.formatOptions;
      default:
        return false;
    }
  }
}

module.exports = {
  BankFormatConfigs,
  BankFormatConfigManager
};
