const moment = require('moment');

/**
 * Shared utility functions for bank file generation
 * Extracted from existing FNB ACB implementation and enhanced for reusability
 */
class BankFileUtils {
  
  /**
   * Padding utilities
   */
  static padLeft(str, length, char = '0') {
    str = str.toString();
    return (char.repeat(length) + str).slice(-length);
  }

  static padRight(str, length, char = ' ') {
    str = (str || '').toString();
    return (str + char.repeat(length)).substring(0, length);
  }

  /**
   * Amount formatting utilities
   */
  static formatAmountCents(amount) {
    return Math.round(amount * 100).toString();
  }

  static formatAmountDecimal(amount, decimals = 2) {
    return parseFloat(amount).toFixed(decimals);
  }

  static formatAmountPadded(amount, totalLength, padChar = '0', asCents = true) {
    const formattedAmount = asCents ? 
      this.formatAmountCents(amount) : 
      this.formatAmountDecimal(amount).replace('.', '');
    return this.padLeft(formattedAmount, totalLength, padChar);
  }

  /**
   * Date formatting utilities
   */
  static formatDateYYMMDD(date) {
    return moment(date).format('YYMMDD');
  }

  static formatDateYYYYMMDD(date) {
    return moment(date).format('YYYYMMDD');
  }

  static formatDateDDMMYYYY(date, separator = '/') {
    return moment(date).format(`DD${separator}MM${separator}YYYY`);
  }

  static formatDateISO(date) {
    return moment(date).format('YYYY-MM-DD');
  }

  /**
   * Text formatting utilities
   */
  static formatText(text, length, padChar = ' ', padLeft = false) {
    const str = (text || '').toString().substring(0, length);
    return padLeft ? 
      str.padStart(length, padChar) : 
      str.padEnd(length, padChar);
  }

  static formatTextUppercase(text, length, padChar = ' ') {
    return this.formatText((text || '').toUpperCase(), length, padChar);
  }

  static formatTextAlphanumeric(text, length, padChar = ' ') {
    const cleanText = (text || '').replace(/[^A-Za-z0-9\s]/g, '');
    return this.formatText(cleanText, length, padChar);
  }

  /**
   * Validation utilities
   */
  static validateAccountNumber(accountNumber, bank = 'generic') {
    if (!accountNumber) {
      throw new Error('Account number is required');
    }

    const cleanAccountNumber = accountNumber.toString().replace(/\D/g, '');
    
    // Bank-specific validation rules
    const validationRules = {
      'fnb': { min: 8, max: 11, pattern: /^\d+$/ },
      'absa': { min: 8, max: 11, pattern: /^\d+$/ },
      'standard': { min: 8, max: 11, pattern: /^\d+$/ },
      'nedbank': { min: 8, max: 11, pattern: /^\d+$/ },
      'generic': { min: 8, max: 11, pattern: /^\d+$/ }
    };

    const rules = validationRules[bank.toLowerCase()] || validationRules.generic;
    
    if (cleanAccountNumber.length < rules.min || cleanAccountNumber.length > rules.max) {
      throw new Error(`Account number must be between ${rules.min} and ${rules.max} digits`);
    }

    if (!rules.pattern.test(cleanAccountNumber)) {
      throw new Error('Account number contains invalid characters');
    }

    return cleanAccountNumber;
  }

  static validateBranchCode(branchCode, bank = 'generic') {
    if (!branchCode) {
      throw new Error('Branch code is required');
    }

    const cleanBranchCode = branchCode.toString().replace(/\D/g, '');
    
    // Most SA banks use 6-digit branch codes
    const expectedLength = 6;
    
    if (cleanBranchCode.length !== expectedLength) {
      throw new Error(`Branch code must be exactly ${expectedLength} digits`);
    }

    return cleanBranchCode;
  }

  static validateAmount(amount, min = 0.01, max = 999999.99) {
    const numAmount = parseFloat(amount);
    
    if (isNaN(numAmount)) {
      throw new Error('Amount must be a valid number');
    }

    if (numAmount < min) {
      throw new Error(`Amount must be at least R${min.toFixed(2)}`);
    }

    if (numAmount > max) {
      throw new Error(`Amount cannot exceed R${max.toFixed(2)}`);
    }

    return numAmount;
  }

  static validateReference(reference, maxLength = 20, allowedChars = /^[A-Za-z0-9\s\-_]+$/) {
    if (!reference) {
      throw new Error('Reference is required');
    }

    if (reference.length > maxLength) {
      throw new Error(`Reference cannot exceed ${maxLength} characters`);
    }

    if (!allowedChars.test(reference)) {
      throw new Error('Reference contains invalid characters');
    }

    return reference.trim();
  }

  /**
   * Record generation utilities
   */
  static generateSequenceNumber(sequence, length = 6, padChar = '0') {
    return this.padLeft(sequence.toString(), length, padChar);
  }

  static generateRandomSequence(length = 4) {
    return Math.floor(Math.random() * Math.pow(10, length))
      .toString()
      .padStart(length, '0');
  }

  /**
   * File utilities
   */
  static generateFileName(payRunId, format, extension) {
    const formatMap = {
      'FNB Enterprise CSV': 'fnb_enterprise',
      'FNB Online Banking (ACB)': 'fnb_acb',
      'Standard Bank Value Dated / EFTS': 'standard_efts',
      'ABSA Business Integrator (.csv)': 'absa_integrator',
      'NedInform': 'nedbank_nedinform'
    };
    
    const formatCode = formatMap[format] || 'unknown';
    const timestamp = moment().format('YYYYMMDD_HHmmss');
    
    return `payrun_${payRunId}_${formatCode}_${timestamp}.${extension}`;
  }

  /**
   * CSV utilities
   */
  static escapeCSVField(field) {
    if (field === null || field === undefined) {
      return '';
    }
    
    const str = field.toString();
    
    // If field contains comma, quote, or newline, wrap in quotes and escape quotes
    if (str.includes(',') || str.includes('"') || str.includes('\n') || str.includes('\r')) {
      return `"${str.replace(/"/g, '""')}"`;
    }
    
    return str;
  }

  static formatCSVRow(fields) {
    return fields.map(field => this.escapeCSVField(field)).join(',');
  }

  /**
   * Fixed-width record utilities
   */
  static buildFixedWidthRecord(fields) {
    return fields.join('');
  }

  static validateFixedWidthRecord(record, expectedLength) {
    if (record.length !== expectedLength) {
      throw new Error(`Record length ${record.length} does not match expected length ${expectedLength}`);
    }
    return record;
  }

  /**
   * Bank-specific utilities
   */
  static getFNBAccountType(accountType) {
    const typeMap = {
      'CURRENT': '1',
      'SAVINGS': '2',
      'TRANSMISSION': '3'
    };
    return typeMap[accountType?.toUpperCase()] || '1';
  }

  static getStandardBankAccountType(accountType) {
    const typeMap = {
      'CURRENT': '01',
      'SAVINGS': '02',
      'TRANSMISSION': '03'
    };
    return typeMap[accountType?.toUpperCase()] || '01';
  }

  /**
   * Error handling utilities
   */
  static createValidationError(field, value, rule) {
    return {
      code: 'VALIDATION_ERROR',
      field: field,
      value: value,
      rule: rule,
      message: `Validation failed for field '${field}': ${rule}`
    };
  }

  static createFormatError(format, details) {
    return {
      code: 'FORMAT_ERROR',
      format: format,
      details: details,
      message: `Format error in ${format}: ${details}`
    };
  }

  /**
   * Logging utilities
   */
  static logGenerationStart(generatorName, transactionCount, totalAmount) {
    console.log(`\n=== ${generatorName} Bank File Generation ===`);
    console.log(`Transactions: ${transactionCount}`);
    console.log(`Total Amount: R${totalAmount.toFixed(2)}`);
    console.log(`Timestamp: ${moment().format('YYYY-MM-DD HH:mm:ss')}`);
  }

  static logGenerationComplete(generatorName, recordCount, fileSize) {
    console.log(`\n=== ${generatorName} Generation Complete ===`);
    console.log(`Records Generated: ${recordCount}`);
    console.log(`File Size: ${fileSize} bytes`);
    console.log(`Completed: ${moment().format('YYYY-MM-DD HH:mm:ss')}`);
  }
}

module.exports = BankFileUtils;
