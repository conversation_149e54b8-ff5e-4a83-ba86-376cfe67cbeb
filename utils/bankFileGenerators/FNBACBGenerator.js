const BaseBankFileGenerator = require('./BaseBankFileGenerator');
const BankFileUtils = require('./BankFileUtils');
const { BankFormatConfigManager } = require('./BankFormatConfigs');

/**
 * FNB ACB Generator
 * Generates ACB files for FNB Online Banking (existing working implementation)
 */
class FNBACBGenerator extends BaseBankFileGenerator {
  
  constructor() {
    const config = BankFormatConfigManager.getConfig('fnb');
    super(config);
    this.formatConfig = config;
  }

  /**
   * Generate header record (Type 02)
   * @param {Object} eftSettings - EFT settings
   * @param {string} actionDate - Action date
   * @returns {string} Header record
   */
  generateHeader(eftSettings, actionDate) {
    // Type 02 - Header Record (198 characters, all zeros for FNB ACB)
    return '02' + '0'.repeat(196);
  }

  /**
   * Generate user header record (Type 04)
   * @param {Object} eftSettings - EFT settings
   * @returns {string} User header record
   */
  generateUserHeader(eftSettings) {
    // Type 04 - User Header Record (198 characters, all zeros for FNB ACB)
    return '04' + '0'.repeat(196);
  }

  /**
   * Generate detail record (Type 10)
   * @param {Object} payslip - Payslip/transaction data
   * @param {number} sequence - Sequence number
   * @param {Object} eftSettings - EFT settings
   * @returns {string} Detail record
   */
  generateDetail(payslip, sequence, eftSettings) {
    try {
      // Validate required fields
      this.validatePayslipData(payslip);
      
      // Extract employee data
      const employee = payslip.employee;
      const amount = payslip.netPay || 0;
      
      // Validate bank details
      const accountNumber = BankFileUtils.validateAccountNumber(employee.accountNumber, 'fnb');
      const branchCode = BankFileUtils.validateBranchCode(employee.branchCode, 'fnb');
      
      // Validate amount
      BankFileUtils.validateAmount(amount);
      
      // Format amount in cents
      const amountInCents = BankFileUtils.formatAmountCents(amount);
      
      // Format employee name
      const employeeName = this.formatEmployeeName(employee);
      
      // Format company name
      const companyName = BankFileUtils.formatText(eftSettings.accountHolder || 'Demo Company', 40);
      
      // Build detail record (198 characters)
      let record = [
        '10',                                           // Record Type (2)
        BankFileUtils.padLeft(branchCode, 9, '0'),      // Branch Code (9)
        BankFileUtils.padLeft(accountNumber, 8, '0'),   // Account Number (8)
        '0'.repeat(9),                                  // Zeros (9)
        BankFileUtils.padLeft(sequence.toString(), 6, '0'), // Sequence Number (6)
        BankFileUtils.padLeft(amountInCents, 11, '0'),  // Amount in cents (11)
        '0'.repeat(7),                                  // Zeros (7)
        BankFileUtils.padLeft(branchCode, 6, '0'),      // Employee Branch Code (6)
        BankFileUtils.padLeft(accountNumber, 9, '0'),   // Employee Account Number (9)
        '0'.repeat(13),                                 // Zeros (13)
        companyName,                                    // Company Name (40)
        employeeName,                                   // Employee Name (30)
        '0'.repeat(48)                                  // Zeros (48)
      ].join('');
      
      // Ensure exactly 198 characters
      record = record.padEnd(198, '0');
      
      return BankFileUtils.validateFixedWidthRecord(record, 198);
      
    } catch (error) {
      console.error(`Error generating FNB ACB detail for employee ${payslip.employee?.firstName} ${payslip.employee?.lastName}:`, error);
      throw error;
    }
  }

  /**
   * Generate contra record (Type 92)
   * @param {number} totalAmount - Total amount
   * @param {Object} eftSettings - EFT settings
   * @returns {string} Contra record
   */
  generateContra(totalAmount, eftSettings) {
    // Type 92 - Contra Record (198 characters, all zeros for FNB ACB)
    return '92' + '0'.repeat(196);
  }

  /**
   * Generate trailer record (Type 94)
   * @param {number} totalAmount - Total amount
   * @param {number} totalRecords - Total record count
   * @param {Object} eftSettings - EFT settings
   * @returns {string} Trailer record
   */
  generateTrailer(totalAmount, totalRecords, eftSettings) {
    // Type 94 - Trailer Record (198 characters, all zeros for FNB ACB)
    return '94' + '0'.repeat(196);
  }

  /**
   * Override generate method to handle FNB ACB specific record structure
   */
  async generate(payRun, eftSettings, actionDate) {
    try {
      console.log('\n=== Generating FNB ACB Bank File ===');
      
      // Validate inputs
      this.validateInputs(payRun, eftSettings, actionDate);
      
      // Extract valid transactions
      const validTransactions = this.extractValidTransactions(payRun);
      
      if (validTransactions.length === 0) {
        throw new Error('No valid EFT transactions found for bank file generation');
      }
      
      const records = [];
      let totalAmount = 0;
      let sequence = 1;
      
      // Calculate total amount
      validTransactions.forEach(transaction => {
        totalAmount += transaction.netPay || 0;
      });
      
      // Generate header records
      records.push(this.generateHeader(eftSettings, actionDate));
      records.push(this.generateUserHeader(eftSettings));
      
      // Generate detail records
      for (const transaction of validTransactions) {
        const detailRecord = this.generateDetail(transaction, sequence++, eftSettings);
        records.push(detailRecord);
      }
      
      // Generate contra and trailer records
      records.push(this.generateContra(totalAmount, eftSettings));
      records.push(this.generateTrailer(totalAmount, validTransactions.length, eftSettings));
      
      // Join records with line ending
      const fileContent = records.join(this.config.lineEnding);
      
      console.log(`Generated ${records.length} records for ${validTransactions.length} transactions`);
      console.log(`Total amount: R${totalAmount.toFixed(2)}`);
      
      return fileContent;
      
    } catch (error) {
      console.error('Error generating FNB ACB bank file:', error);
      throw error;
    }
  }

  /**
   * Validate payslip data
   * @param {Object} payslip - Payslip data
   */
  validatePayslipData(payslip) {
    if (!payslip) {
      throw new Error('Payslip data is required');
    }
    
    if (!payslip.employee) {
      throw new Error('Employee data is required');
    }
    
    const employee = payslip.employee;
    
    if (!employee.firstName || !employee.lastName) {
      throw new Error('Employee first name and last name are required');
    }
    
    if (!employee.accountNumber) {
      throw new Error('Employee account number is required');
    }
    
    if (!employee.branchCode) {
      throw new Error('Employee branch code is required');
    }
    
    if (!payslip.netPay || payslip.netPay <= 0) {
      throw new Error('Valid net pay amount is required');
    }
  }

  /**
   * Format employee name for FNB ACB (max 30 characters)
   * @param {Object} employee - Employee data
   * @returns {string} Formatted employee name
   */
  formatEmployeeName(employee) {
    const firstName = (employee.firstName || '').trim();
    const lastName = (employee.lastName || '').trim();
    
    let fullName = `${firstName} ${lastName}`.trim();
    
    // Ensure name doesn't exceed max length (30 chars for FNB ACB)
    if (fullName.length > 30) {
      fullName = fullName.substring(0, 30);
    }
    
    // Pad to exactly 30 characters
    return BankFileUtils.formatText(fullName, 30);
  }

  /**
   * Get file extension
   * @returns {string} File extension
   */
  getFileExtension() {
    return 'acb';
  }

  /**
   * Get MIME type
   * @returns {string} MIME type
   */
  getMimeType() {
    return 'application/octet-stream';
  }

  /**
   * Get format information
   * @returns {Object} Format information
   */
  getFormatInfo() {
    return {
      name: 'FNB Online Banking (ACB)',
      description: 'ACB format for FNB Online Banking',
      fileExtension: 'acb',
      mimeType: 'application/octet-stream',
      encoding: 'ascii',
      lineEnding: 'LF',
      recordLength: 198,
      features: [
        'Fixed-width 198-character records',
        'Header, user header, detail, contra, and trailer records',
        'Amount in cents format',
        'Automatic sequence numbering',
        'Employee name padding'
      ],
      limitations: [
        'Maximum 30 character employee name',
        'Fixed record structure',
        'All header/trailer fields are zeros'
      ]
    };
  }
}

module.exports = FNBACBGenerator;
