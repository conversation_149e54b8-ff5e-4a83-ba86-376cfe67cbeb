const moment = require('moment');

/**
 * Abstract Base Class for Bank File Generators
 * Provides common interface and shared utilities for all bank format generators
 */
class BaseBankFileGenerator {
  constructor(config = {}) {
    this.config = {
      fileExtension: 'txt',
      encoding: 'utf8',
      lineEnding: '\r\n',
      amountFormat: 'cents', // 'cents' or 'decimal'
      dateFormat: 'YYYYMMDD',
      ...config
    };
    
    // Validate required methods are implemented
    if (this.constructor === BaseBankFileGenerator) {
      throw new Error('BaseBankFileGenerator is abstract and cannot be instantiated directly');
    }
  }

  /**
   * Abstract method: Generate header record
   * Must be implemented by subclasses
   */
  generateHeader(eftSettings, actionDate, totalRecords, totalAmount) {
    throw new Error('generateHeader method must be implemented by subclass');
  }

  /**
   * Abstract method: Generate detail/transaction record
   * Must be implemented by subclasses
   */
  generateDetail(payslip, sequence, eftSettings) {
    throw new Error('generateDetail method must be implemented by subclass');
  }

  /**
   * Abstract method: Generate trailer record
   * Must be implemented by subclasses
   */
  generateTrailer(totalAmount, totalRecords, eftSettings) {
    throw new Error('generateTrailer method must be implemented by subclass');
  }

  /**
   * Main generation method using template pattern
   * This method orchestrates the file generation process
   */
  async generate(payRun, eftSettings, actionDate) {
    try {
      console.log(`\n=== Generating ${this.constructor.name} Bank File ===`);
      
      // Validate inputs
      this.validateInputs(payRun, eftSettings, actionDate);
      
      // Extract valid transactions
      const validTransactions = this.extractValidTransactions(payRun);
      
      if (validTransactions.length === 0) {
        throw new Error('No valid EFT transactions found for bank file generation');
      }
      
      const records = [];
      let totalAmount = 0;
      let sequence = 1;
      
      // Calculate totals
      validTransactions.forEach(transaction => {
        totalAmount += transaction.netPay || 0;
      });
      
      // Generate header record(s)
      const headerRecords = this.generateHeader(eftSettings, actionDate, validTransactions.length, totalAmount);
      if (Array.isArray(headerRecords)) {
        records.push(...headerRecords);
      } else {
        records.push(headerRecords);
      }
      
      // Generate detail records
      for (const transaction of validTransactions) {
        const detailRecord = this.generateDetail(transaction, sequence++, eftSettings);
        records.push(detailRecord);
      }
      
      // Generate trailer record(s)
      const trailerRecords = this.generateTrailer(totalAmount, validTransactions.length, eftSettings);
      if (Array.isArray(trailerRecords)) {
        records.push(...trailerRecords);
      } else {
        records.push(trailerRecords);
      }
      
      // Join records with appropriate line ending
      const fileContent = records.join(this.config.lineEnding);
      
      console.log(`Generated ${records.length} records for ${validTransactions.length} transactions`);
      console.log(`Total amount: R${totalAmount.toFixed(2)}`);
      
      return fileContent;
      
    } catch (error) {
      console.error(`Error generating ${this.constructor.name} bank file:`, error);
      throw error;
    }
  }

  /**
   * Validate input parameters
   */
  validateInputs(payRun, eftSettings, actionDate) {
    if (!payRun || !payRun.payrollPeriods) {
      throw new Error('Invalid pay run data: missing payrollPeriods');
    }
    
    if (!eftSettings || !eftSettings.bankName || !eftSettings.branchCode || !eftSettings.accountNumber) {
      throw new Error('Invalid EFT settings: missing required bank details');
    }
    
    if (!actionDate) {
      throw new Error('Action date is required');
    }
    
    if (!moment(actionDate).isValid()) {
      throw new Error('Invalid action date format');
    }
  }

  /**
   * Extract valid EFT transactions from pay run
   */
  extractValidTransactions(payRun) {
    const validTransactions = [];
    
    if (payRun.payrollPeriods && payRun.payrollPeriods.length > 0) {
      for (const period of payRun.payrollPeriods) {
        // Check if employee has EFT payment method and required bank details
        if (period.employee && 
            period.employee.paymentMethod === 'EFT' && 
            period.employee.accountNumber && 
            period.employee.branchCode &&
            period.netPay > 0) {
          validTransactions.push(period);
        }
      }
    }
    
    return validTransactions;
  }

  /**
   * Format amount based on configuration
   */
  formatAmount(amount) {
    if (this.config.amountFormat === 'cents') {
      return Math.round(amount * 100).toString();
    } else {
      return amount.toFixed(2);
    }
  }

  /**
   * Format text with padding
   */
  formatText(text, length, padChar = ' ', padLeft = false) {
    const str = (text || '').toString().substring(0, length);
    if (padLeft) {
      return str.padStart(length, padChar);
    } else {
      return str.padEnd(length, padChar);
    }
  }

  /**
   * Format date based on configuration
   */
  formatDate(date, format = null) {
    const dateFormat = format || this.config.dateFormat;
    return moment(date).format(dateFormat);
  }

  /**
   * Validate account number format
   */
  validateAccountNumber(accountNumber, minLength = 8, maxLength = 11) {
    if (!accountNumber || typeof accountNumber !== 'string') {
      throw new Error('Account number is required and must be a string');
    }
    
    const cleanAccountNumber = accountNumber.replace(/\D/g, '');
    if (cleanAccountNumber.length < minLength || cleanAccountNumber.length > maxLength) {
      throw new Error(`Account number must be between ${minLength} and ${maxLength} digits`);
    }
    
    return cleanAccountNumber;
  }

  /**
   * Validate branch code format
   */
  validateBranchCode(branchCode, expectedLength = 6) {
    if (!branchCode || typeof branchCode !== 'string') {
      throw new Error('Branch code is required and must be a string');
    }
    
    const cleanBranchCode = branchCode.replace(/\D/g, '');
    if (cleanBranchCode.length !== expectedLength) {
      throw new Error(`Branch code must be exactly ${expectedLength} digits`);
    }
    
    return cleanBranchCode;
  }

  /**
   * Get file extension for this generator
   */
  getFileExtension() {
    return this.config.fileExtension;
  }

  /**
   * Get MIME type for file download
   */
  getMimeType() {
    const mimeTypes = {
      'csv': 'text/csv',
      'txt': 'text/plain',
      'acb': 'application/octet-stream',
      'imp': 'application/octet-stream'
    };
    
    return mimeTypes[this.config.fileExtension] || 'application/octet-stream';
  }
}

module.exports = BaseBankFileGenerator;
