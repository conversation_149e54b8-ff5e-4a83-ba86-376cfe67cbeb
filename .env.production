# Environment
NODE_ENV=production

# URLs
BASE_URL=https://payroll.pss-group.co.za
PROD_BASE_URL=https://payroll.pss-group.co.za

# Keep your other existing configurations...
EMAIL_HOST=smtppro.zoho.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=2n1Qv9Aqm4UP
EMAIL_FROM_NAME="Panda Solutions"
EMAIL_SECURE=false

# Session and Security
SESSION_SECRET=Ent@ngled2021_very_long_random_string_at_least_32_characters_long_*********
COOKIE_SECRET=Ent@ngled2021_cookie_secret_key_very_long_and_secure_*********
BCRYPT_SALT_ROUNDS=12

# Database
MONGODB_URI=mongodb+srv://premierstaffingsolutionsgroup:<EMAIL>/mydatabase?retryWrites=true&w=majority
DISABLE_MONGODB_TRANSACTIONS=true

# Server
PORT=3002



# DKIM Configuration
DKIM_PRIVATE_KEY=IGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCC4XmzTd7BRHN7X6yI5TUMNFlNPkE+lxcR6WNakF+mnFu27nGouw3b3cV8Zdc+PEl9KlOQXw11DLlOuHCUbeXXumew0zEvmHb2ZsD7ZA5alcCZHaaHLmNkP67fKEx2M9n/u3djAf5PGpdwotro1eGzM0JS96mXMJ9UvNBncmwOEwIDAQAB




# Other configurations...


# NODE_ENV=development
# MONGODB_URI=mongodb+srv://premierstaffingsolutionsgroup:<EMAIL>/mydatabase?retryWrites=true&w=majority
# SESSION_SECRET=dev_secret_key_123
# PORT=3002

# JWT_SECRET=your_jwt_secret
# GMAIL_USER=<EMAIL>
# GMAIL_APP_PASSWORD=gkjk cyrp rfxt fuap

# BCRYPT_SALT_ROUNDS=12



# # Environment
# NODE_ENV=production

# Zoho Mail configuration
# EMAIL_HOST=smtppro.zoho.com
# EMAIL_PORT=587
# EMAIL_USER=<EMAIL>
# EMAIL_PASS=2n1Qv9Aqm4UP
# EMAIL_FROM_NAME="Panda Solutions"
# EMAIL_SECURE=false

# # Database
# MONGODB_URI=mongodb+srv://premierstaffingsolutionsgroup:<EMAIL>/mydatabase?retryWrites=true&w=majority

# # Session
# SESSION_SECRET=Ent@ngled2021_very_long_random_string_at_least_32_characters_long_*********

# # Server
# PORT=3002

# # Security
# BCRYPT_SALT_ROUNDS=12
# COOKIE_SECRET=Ent@ngled2021_cookie_secret_key_very_long_and_secure_*********

# # URLs
# BASE_URL=https://pss-group.co.za
# PROD_BASE_URL=https://pss-group.co.za

# # API Keys

# BLOG_APP_API_KEY=*********

# Development Email Settings


# Production Email Settings (keep these different)
# EMAIL_SERVICE=your_production_email_service
# EMAIL_USER=your_production_email
# EMAIL_PASS=your_production_password

# # Staging Environment
# STAGING_MONGODB_URI=mongodb+srv://premierstaffingsolutionsgroup:<EMAIL>/staging?retryWrites=true&w=majority
# STAGING_PORT=3002
# STAGING_BASE_URL=http://localhost:3002

BANK_NAME=First National Bank
BANK_ACCOUNT=628 311 47120
BANK_BRANCH_CODE=250655

# Paystack Configuration
PAYSTACK_SECRET_KEY=************************************************
PAYSTACK_PUBLIC_KEY=pk_live_82b29c44caad9793dc39039af74a1f4e0d00d598
