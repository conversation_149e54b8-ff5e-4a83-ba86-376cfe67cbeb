const QuickBooks = require("node-quickbooks");
const Integration = require("../models/Integration");
const debug = require("debug")("quickbooks:token-manager");

/**
 * Centralized QuickBooks Token Manager
 * Provides consistent token field naming and management across all components
 * Based on the XeroTokenManager pattern but adapted for QuickBooks OAuth 2.0
 */
class QuickBooksTokenManager {
  constructor() {
    // Standardized field names for token storage
    this.FIELD_NAMES = {
      ACCESS_TOKEN: 'accessToken',
      REFRESH_TOKEN: 'refreshToken',
      EXPIRES_AT: 'expiresAt',
      REALM_ID: 'realmId', // <PERSON>Books uses realmId instead of tenantId
      TOKEN_TYPE: 'tokenType',
      SCOPE: 'scope',
      STATE: 'state'
    };

    // Token refresh mutex to prevent concurrent refreshes
    this.refreshMutex = new Map();
  }

  /**
   * Get standardized token data from integration
   * @param {Object} integration - Integration document
   * @returns {Object} Standardized token data
   */
  getTokenData(integration) {
    if (!integration || !integration.credentials) {
      return null;
    }

    const credentials = integration.credentials;
    return {
      accessToken: credentials.get(this.FIELD_NAMES.ACCESS_TOKEN),
      refreshToken: credentials.get(this.FIELD_NAMES.REFRESH_TOKEN),
      expiresAt: credentials.get(this.FIELD_NAMES.EXPIRES_AT),
      realmId: credentials.get(this.FIELD_NAMES.REALM_ID),
      tokenType: credentials.get(this.FIELD_NAMES.TOKEN_TYPE),
      scope: credentials.get(this.FIELD_NAMES.SCOPE),
      state: credentials.get(this.FIELD_NAMES.STATE)
    };
  }

  /**
   * Set standardized token data in integration
   * @param {Object} integration - Integration document
   * @param {Object} tokenData - Token data to set
   */
  setTokenData(integration, tokenData) {
    if (!integration.credentials) {
      integration.credentials = new Map();
    }

    if (tokenData.accessToken) {
      integration.credentials.set(this.FIELD_NAMES.ACCESS_TOKEN, tokenData.accessToken);
    }
    if (tokenData.refreshToken) {
      integration.credentials.set(this.FIELD_NAMES.REFRESH_TOKEN, tokenData.refreshToken);
    }
    if (tokenData.expiresAt) {
      integration.credentials.set(this.FIELD_NAMES.EXPIRES_AT, tokenData.expiresAt);
    }
    if (tokenData.realmId) {
      integration.credentials.set(this.FIELD_NAMES.REALM_ID, tokenData.realmId);
    }
    if (tokenData.tokenType) {
      integration.credentials.set(this.FIELD_NAMES.TOKEN_TYPE, tokenData.tokenType);
    }
    if (tokenData.scope) {
      integration.credentials.set(this.FIELD_NAMES.SCOPE, tokenData.scope);
    }
    if (tokenData.state) {
      integration.credentials.set(this.FIELD_NAMES.STATE, tokenData.state);
    }
  }

  /**
   * Check if token is expired or will expire soon
   * @param {Object} integration - Integration document
   * @param {number} bufferMinutes - Buffer time in minutes (default: 5)
   * @returns {boolean} True if token is expired or will expire soon
   */
  isTokenExpired(integration, bufferMinutes = 5) {
    const tokenData = this.getTokenData(integration);
    if (!tokenData || !tokenData.expiresAt) {
      return true;
    }

    const expiresAt = new Date(tokenData.expiresAt);
    const bufferTime = bufferMinutes * 60 * 1000; // Convert to milliseconds
    const now = new Date();

    return (expiresAt.getTime() - now.getTime()) <= bufferTime;
  }

  /**
   * Get time until token expiry in milliseconds
   * @param {Object} integration - Integration document
   * @returns {number} Time until expiry in milliseconds (negative if expired)
   */
  getTimeUntilExpiry(integration) {
    const tokenData = this.getTokenData(integration);
    if (!tokenData || !tokenData.expiresAt) {
      return -1;
    }

    const expiresAt = new Date(tokenData.expiresAt);
    const now = new Date();
    return expiresAt.getTime() - now.getTime();
  }

  /**
   * Create QuickBooks client with standardized configuration
   * @returns {QuickBooks} Configured QuickBooks client
   */
  createQuickBooksClient() {
    const protocol = process.env.PROTOCOL || 'http';
    const host = process.env.HOST || 'localhost:3002';
    const redirectUri = `${protocol}://${host}/quickbooks/callback`;

    return new QuickBooks(
      process.env.QUICKBOOKS_CLIENT_ID,
      process.env.QUICKBOOKS_CLIENT_SECRET,
      null, // oauth_token - will be set later
      false, // no token secret for OAuth 2.0
      null, // realmId - will be set later
      process.env.QUICKBOOKS_USE_SANDBOX === 'true', // use sandbox
      true, // enable debugging
      null, // minorversion - use latest
      '2.0', // OAuth version
      null // refresh_token - will be set later
    );
  }

  /**
   * Initialize QuickBooks client with token data
   * @param {Object} tokenData - Token data
   * @returns {QuickBooks} Initialized QuickBooks client
   */
  initializeClient(tokenData) {
    const qbo = this.createQuickBooksClient();

    if (tokenData.accessToken) {
      qbo.token = tokenData.accessToken;
    }
    if (tokenData.refreshToken) {
      qbo.refreshToken = tokenData.refreshToken;
    }
    if (tokenData.realmId) {
      qbo.realmId = tokenData.realmId;
    }

    return qbo;
  }

  /**
   * Validate and refresh token if necessary
   * @param {string} companyId - Company ID
   * @returns {Promise<Object>} Validation result
   */
  async validateAndRefreshToken(companyId) {
    const context = { companyId, operation: 'validateAndRefreshToken' };

    try {
      // Get integration
      const integration = await Integration.findOne({
        company: companyId,
        type: 'accounting',
        provider: 'quickbooks'
      });

      if (!integration) {
        return {
          success: false,
          error: 'QuickBooks integration not found',
          needsReauthentication: true
        };
      }

      // Check if token is expired
      if (!this.isTokenExpired(integration)) {
        return {
          success: true,
          refreshed: false,
          expiresAt: this.getTokenData(integration).expiresAt,
          timeUntilExpiry: this.getTimeUntilExpiry(integration)
        };
      }

      // Refresh token
      return await this.refreshToken(companyId);

    } catch (error) {
      debug("Token validation error:", error);
      return {
        success: false,
        error: error.message,
        needsReauthentication: true
      };
    }
  }

  /**
   * Acquire refresh mutex for a company
   * @param {string} companyId - Company ID
   * @returns {boolean} True if mutex acquired
   */
  acquireRefreshMutex(companyId) {
    if (this.refreshMutex.has(companyId)) {
      return false;
    }
    this.refreshMutex.set(companyId, Date.now());
    return true;
  }

  /**
   * Release refresh mutex for a company
   * @param {string} companyId - Company ID
   */
  releaseRefreshMutex(companyId) {
    this.refreshMutex.delete(companyId);
  }

  /**
   * Refresh QuickBooks access token
   * @param {string} companyId - Company ID
   * @returns {Promise<Object>} Refresh result
   */
  async refreshToken(companyId) {
    const context = { companyId, operation: 'refreshToken' };

    // Acquire mutex to prevent concurrent refreshes
    if (!this.acquireRefreshMutex(companyId)) {
      debug("Token refresh already in progress for company:", companyId);
      return {
        success: false,
        error: 'Token refresh already in progress',
        isRetryable: true
      };
    }

    try {
      // Get integration
      const integration = await Integration.findOne({
        company: companyId,
        type: 'accounting',
        provider: 'quickbooks'
      });

      if (!integration) {
        throw new Error("QuickBooks integration not found");
      }

      const tokenData = this.getTokenData(integration);
      if (!tokenData || !tokenData.refreshToken) {
        throw new Error("No refresh token available");
      }

      // Create QuickBooks client and refresh token
      const qbo = this.initializeClient(tokenData);

      debug("Attempting token refresh...");

      // Note: node-quickbooks doesn't have a direct refresh method
      // We need to implement the OAuth 2.0 refresh flow manually
      const newTokenData = await this.performTokenRefresh(tokenData);

      // Update integration with new tokens
      this.setTokenData(integration, newTokenData);
      await integration.save();

      debug("Token refresh successful", {
        newExpiresAt: newTokenData.expiresAt,
        timeUntilExpiry: this.getTimeUntilExpiry(integration)
      });

      return {
        success: true,
        refreshed: true,
        expiresAt: newTokenData.expiresAt,
        timeUntilExpiry: this.getTimeUntilExpiry(integration)
      };

    } catch (error) {
      debug("Token refresh error:", error);

      return {
        success: false,
        error: error.message,
        needsReauthentication: true,
        isRetryable: false
      };

    } finally {
      // Always release the mutex
      this.releaseRefreshMutex(companyId);
    }
  }

  /**
   * Perform OAuth 2.0 token refresh using QuickBooks API
   * @param {Object} tokenData - Current token data
   * @returns {Promise<Object>} New token data
   */
  async performTokenRefresh(tokenData) {
    const axios = require('axios');
    const qs = require('querystring');

    const refreshData = {
      grant_type: 'refresh_token',
      refresh_token: tokenData.refreshToken
    };

    const config = {
      method: 'POST',
      url: 'https://oauth.platform.intuit.com/oauth2/v1/tokens/bearer',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Basic ${Buffer.from(
          `${process.env.QUICKBOOKS_CLIENT_ID}:${process.env.QUICKBOOKS_CLIENT_SECRET}`
        ).toString('base64')}`
      },
      data: qs.stringify(refreshData)
    };

    const response = await axios(config);

    if (response.status !== 200) {
      throw new Error(`Token refresh failed with status: ${response.status}`);
    }

    const responseData = response.data;

    return {
      accessToken: responseData.access_token,
      refreshToken: responseData.refresh_token || tokenData.refreshToken, // Keep old refresh token if not provided
      expiresAt: new Date(Date.now() + responseData.expires_in * 1000),
      tokenType: responseData.token_type,
      realmId: tokenData.realmId // Preserve realm ID
    };
  }

  /**
   * Get OAuth authorization URL
   * @param {string} state - State parameter for security
   * @param {string} scope - OAuth scope
   * @returns {string} Authorization URL
   */
  getAuthorizationUrl(state, scope = 'com.intuit.quickbooks.accounting') {
    const protocol = process.env.PROTOCOL || 'http';
    const host = process.env.HOST || 'localhost:3002';
    const redirectUri = `${protocol}://${host}/quickbooks/callback`;

    const params = new URLSearchParams({
      client_id: process.env.QUICKBOOKS_CLIENT_ID,
      scope: scope,
      redirect_uri: redirectUri,
      response_type: 'code',
      access_type: 'offline',
      state: state
    });

    const baseUrl = process.env.QUICKBOOKS_USE_SANDBOX === 'true'
      ? 'https://appcenter.intuit.com/connect/oauth2'
      : 'https://appcenter.intuit.com/connect/oauth2';

    return `${baseUrl}?${params.toString()}`;
  }

  /**
   * Exchange authorization code for tokens
   * @param {string} code - Authorization code
   * @param {string} realmId - QuickBooks company ID
   * @param {string} state - State parameter
   * @returns {Promise<Object>} Token data
   */
  async exchangeCodeForTokens(code, realmId, state) {
    const axios = require('axios');
    const qs = require('querystring');

    const protocol = process.env.PROTOCOL || 'http';
    const host = process.env.HOST || 'localhost:3002';
    const redirectUri = `${protocol}://${host}/quickbooks/callback`;

    const tokenData = {
      grant_type: 'authorization_code',
      code: code,
      redirect_uri: redirectUri
    };

    const config = {
      method: 'POST',
      url: 'https://oauth.platform.intuit.com/oauth2/v1/tokens/bearer',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Basic ${Buffer.from(
          `${process.env.QUICKBOOKS_CLIENT_ID}:${process.env.QUICKBOOKS_CLIENT_SECRET}`
        ).toString('base64')}`
      },
      data: qs.stringify(tokenData)
    };

    const response = await axios(config);

    if (response.status !== 200) {
      throw new Error(`Token exchange failed with status: ${response.status}`);
    }

    const responseData = response.data;

    return {
      accessToken: responseData.access_token,
      refreshToken: responseData.refresh_token,
      expiresAt: new Date(Date.now() + responseData.expires_in * 1000),
      tokenType: responseData.token_type,
      realmId: realmId,
      scope: responseData.scope,
      state: state
    };
  }

  /**
   * Revoke QuickBooks tokens
   * @param {string} companyId - Company ID
   * @returns {Promise<boolean>} True if successful
   */
  async revokeTokens(companyId) {
    try {
      const integration = await Integration.findOne({
        company: companyId,
        type: 'accounting',
        provider: 'quickbooks'
      });

      if (!integration) {
        return true; // Already disconnected
      }

      const tokenData = this.getTokenData(integration);
      if (tokenData && tokenData.refreshToken) {
        const axios = require('axios');
        const qs = require('querystring');

        const config = {
          method: 'POST',
          url: 'https://developer.api.intuit.com/v2/oauth2/tokens/revoke',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Authorization': `Basic ${Buffer.from(
              `${process.env.QUICKBOOKS_CLIENT_ID}:${process.env.QUICKBOOKS_CLIENT_SECRET}`
            ).toString('base64')}`
          },
          data: qs.stringify({
            token: tokenData.refreshToken
          })
        };

        await axios(config);
      }

      // Update integration status
      integration.status = 'inactive';
      integration.credentials = new Map();
      await integration.save();

      return true;
    } catch (error) {
      debug("Token revocation error:", error);
      return false;
    }
  }
}

module.exports = QuickBooksTokenManager;