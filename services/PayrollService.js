const moment = require("moment-timezone");
const PayrollPeriod = require("../models/PayrollPeriod");
const { DEFAULT_TIMEZONE } = require("../config/constants");
const Employee = require("../models/Employee");
const Payroll = require("../models/Payroll");
const { VALIDATION_RULES } = require("../constants/payrollValidation");
const mongoose = require("mongoose");
const PeriodCalculations = require("../utils/periodCalculations");
const BusinessDate = require("../utils/BusinessDate");
const {
  calculatePAYE,
  calculateUIF,
  calculateRFI,
  calculatePeriodUIF,
  calculateLossOfIncome,
} = require("../utils/payrollCalculations");
const {
  calculateTravelAllowance,
  SARS_KILOMETER_RATE,
  calculateEnhancedUIF,
  calculateEnhancedPAYE,
} = require("../utils/payrollCalculations");
const payrollCalculations = require("../utils/payrollCalculations");

class PayrollService {
  static async ensureFuturePeriodsExist(employee, currentDate) {

    // Comprehensive status check
    if (employee.status === "Inactive" || employee.status === "Terminated") {
      return;
    }

    // Check if employee is serving notice
    if (employee.status === "Serving Notice" && employee.lastDayOfService) {
      const terminationDate = moment(employee.lastDayOfService).endOf("day");
      const today = moment(currentDate).endOf("day");

      if (today.isAfter(terminationDate)) {
        return;
      }
    }

    try {
      // Get the current open period
      const currentPeriod = await PayrollPeriod.findOne({
        employee: employee._id,
        isFinalized: false,
      }).sort({ startDate: 1 });

      // If no open period exists, create one (with termination validation)
      if (!currentPeriod) {

        // Additional validation for termination date
        if (employee.lastDayOfService) {
          const terminationDate = moment(employee.lastDayOfService).endOf(
            "day"
          );
          const today = moment(currentDate).endOf("day");

          if (today.isAfter(terminationDate)) {
            return;
          }
        }

        // Calculate period dates using BusinessDate for complete timezone independence
        // SURGICAL FIX: Handle different frequencies correctly
        let startDateStr;
        let endDateStr;
        const doaStr = BusinessDate.normalize(employee.doa);

        if (employee.payFrequency?.frequency === 'monthly') {
          // For monthly periods, start from the first day of the month containing DOA
          startDateStr = BusinessDate.startOfMonth(doaStr);
          endDateStr = BusinessDate.calculatePeriodEndDate(startDateStr, employee.payFrequency);
        } else if (employee.payFrequency?.frequency === 'weekly') {
          // For weekly periods, calculate proper 7-day period
          // First calculate the period end date based on DOA
          endDateStr = BusinessDate.calculatePeriodEndDate(doaStr, employee.payFrequency);
          // Then calculate start date as exactly 7 days before end date
          startDateStr = BusinessDate.addDays(endDateStr, -6);
        } else {
          // For biweekly and other frequencies, start from DOA
          startDateStr = doaStr;
          endDateStr = BusinessDate.calculatePeriodEndDate(startDateStr, employee.payFrequency);
        }


        // Check if period already exists using BusinessDate fields
        const existingPeriod = await PayrollPeriod.findOne({
          employee: employee._id,
          $or: [
            // Check using BusinessDate fields (preferred)
            {
              startDateBusiness: startDateStr,
              endDateBusiness: endDateStr,
              frequency: employee.payFrequency.frequency,
            },
            // Fallback to legacy Date fields for backward compatibility
            {
              startDate: BusinessDate.toDate(startDateStr),
              endDate: BusinessDate.toDate(endDateStr, true),
              frequency: employee.payFrequency.frequency,
            }
          ]
        });

        if (!existingPeriod) {

          // Use the createInitialPeriod method which includes tax calculations
          await PayrollPeriod.createInitialPeriod(employee);
        } else {
        }
      }
    } catch (error) {
      console.error("Error in ensureFuturePeriodsExist:", error);
      // Log error but don't throw to prevent page load failure
      return;
    }
  }

  static async cleanupFuturePeriods(employee) {

    // Only cleanup periods if employee is terminated or serving notice
    if (
      employee.status === "Terminated" ||
      employee.status === "Serving Notice"
    ) {
      const terminationDate = employee.lastDayOfService
        ? moment(employee.lastDayOfService).endOf("day")
        : moment().endOf("day");


      // Delete periods after termination date
      const result = await PayrollPeriod.deleteMany({
        employee: employee._id,
        startDate: { $gt: terminationDate.toDate() },
      });

    } else {
    }
  }

  static async generatePayPeriods(employee, startDate, endDate) {

    try {
      // Get the latest payroll with basic salary
      const latestPayroll = await Payroll.findOne({
        employee: employee._id,
        company: employee.company,
        basicSalary: { $exists: true, $gt: 0 },
      }).sort({ month: -1 });


      // Get employee with populated data
      const populatedEmployee = await Employee.findById(employee._id)
        .populate("payFrequency")
        .populate("company");

      if (!populatedEmployee?.payFrequency) {
        throw new Error("Employee pay frequency not found");
      }

      // BusinessDate period calculation for complete timezone independence
      let employeeStartDateStr = BusinessDate.normalize(populatedEmployee.doa);

      // SURGICAL FIX: Adjust employee start date based on frequency
      if (populatedEmployee.payFrequency?.frequency === 'monthly') {
        // For monthly periods, adjust employee start date to first day of month containing DOA
        employeeStartDateStr = BusinessDate.startOfMonth(employeeStartDateStr);
      } else if (populatedEmployee.payFrequency?.frequency === 'weekly') {
        // For weekly periods, calculate the proper 7-day period start
        // First calculate the period end date based on DOA
        const firstPeriodEndStr = BusinessDate.calculatePeriodEndDate(employeeStartDateStr, populatedEmployee.payFrequency);
        // Then calculate start date as exactly 7 days before end date
        employeeStartDateStr = BusinessDate.addDays(firstPeriodEndStr, -6);
      }

      const requestedStartDateStr = BusinessDate.normalize(startDate);
      const requestedEndDateStr = BusinessDate.normalize(endDate);

      // Use the later of employee start date or requested start date
      const periodStartDateStr = BusinessDate.isAfter(employeeStartDateStr, requestedStartDateStr)
        ? employeeStartDateStr
        : requestedStartDateStr;


      // Generate periods
      const periods = [];
      let currentStartStr = periodStartDateStr;

      while (BusinessDate.isSameOrBefore(currentStartStr, requestedEndDateStr)) {
        // Calculate period end date using BusinessDate for complete timezone independence
        const periodEndStr = BusinessDate.calculatePeriodEndDate(currentStartStr, populatedEmployee.payFrequency);


        // Create period data for database storage
        const periodData = {
          employee: populatedEmployee._id,
          company: new mongoose.Types.ObjectId(populatedEmployee.company._id),
          startDate: currentStartStr,
          endDate: periodEndStr,
          frequency: populatedEmployee.payFrequency.frequency,
          basicSalary: latestPayroll?.basicSalary || 0,
          status: "open",
        };


        // Find or create period using BusinessDate fields
        const period = await PayrollPeriod.findOneAndUpdate(
          {
            employee: periodData.employee,
            company: periodData.company,
            $or: [
              // Check using BusinessDate fields (preferred)
              {
                startDateBusiness: periodData.startDate,
                endDateBusiness: periodData.endDate,
              },
              // Fallback to legacy Date fields for backward compatibility
              {
                startDate: BusinessDate.toDate(periodData.startDate),
                endDate: BusinessDate.toDate(periodData.endDate, true),
              }
            ]
          },
          { $set: {
            ...periodData,
            // Set both Date and BusinessDate fields
            startDate: BusinessDate.toDate(periodData.startDate),
            endDate: BusinessDate.toDate(periodData.endDate, true),
            startDateBusiness: periodData.startDate,
            endDateBusiness: periodData.endDate,
          }},
          { new: true, upsert: true }
        );

        // Create or update corresponding payroll record
        await Payroll.findOneAndUpdate(
          {
            employee: periodData.employee,
            company: periodData.company,
            month: BusinessDate.toDate(periodData.endDate, true),
          },
          {
            $set: {
              basicSalary: periodData.basicSalary,
              status: "draft",
            },
          },
          { new: true, upsert: true }
        );

        periods.push(period);

        // Move to next period start using BusinessDate
        currentStartStr = BusinessDate.addDays(periodEndStr, 1);

        // Safety check
        if (periods.length > 24) {
          break;
        }
      }


      return periods;
    } catch (error) {
      console.error("Error generating pay periods:", error);
      throw error;
    }
  }

  // Add validation helper
  static async validatePeriod(period) {
    try {

      // Basic validation
      if (
        !period.employee ||
        !period.company ||
        !period.startDate ||
        !period.endDate
      ) {
        return false;
      }

      // Check dates
      const start = moment(period.startDate);
      const end = moment(period.endDate);

      if (!start.isValid() || !end.isValid()) {
        return false;
      }

      // For monthly periods, ensure we're using full calendar months
      if (period.frequency === "monthly") {
        // If it's the first period of employment, allow partial month
        const employee = await Employee.findById(period.employee);
        const isFirstPeriod = moment(employee.doa).isSame(start, "month");

        if (!isFirstPeriod) {
          // For regular months, ensure we're using full calendar months
          if (
            !start.isSame(start.clone().startOf("month")) ||
            !end.isSame(end.clone().endOf("month"))
          ) {
            return false;
          }
        }
      }

      // Check for overlapping periods
      const overlapping = await PayrollPeriod.findOne({
        employee: period.employee,
        company: period.company,
        _id: { $ne: period._id }, // Exclude current period
        $or: [
          {
            startDate: { $lte: period.endDate },
            endDate: { $gte: period.startDate },
          },
        ],
      });

      if (overlapping) {
        return false;
      }


      return true;
    } catch (error) {
      console.error("Period validation error:", error);
      return false;
    }
  }

  // In PayrollService.js
  static async finalizePeriod(employeeId, currentPeriodEndDate, company) {

    try {
      // 1. Get employee with pay frequency
      const employee = await Employee.findById(employeeId).populate(
        "payFrequency"
      );
      if (!employee) {
        throw new Error("Employee not found");
      }

      // 2. Find current period and payroll
      const currentPeriod = await PayrollPeriod.findOne({
        employee: employeeId,
        company: company,
        endDate: new Date(currentPeriodEndDate),
        isFinalized: false,
      });

      if (!currentPeriod) {
        throw new Error("Period not found or already finalized");
      }

      // Get current payroll data
      const currentPayroll = await Payroll.findOne({
        employee: employeeId,
        company: company,
        month: currentPeriod.endDate,
      });

      if (!currentPayroll) {
        throw new Error("No payroll data found for current period");
      }

      // 3. Calculate next period dates
      const nextStartDate = moment(currentPeriodEndDate)
        .add(1, "day")
        .startOf("day");
      let nextEndDate;

      if (employee.payFrequency.frequency === "monthly") {
        nextEndDate = moment(nextStartDate).endOf("month");
      } else if (employee.payFrequency.frequency === "weekly") {
        nextEndDate = nextStartDate.clone().add(6, "days").endOf("day");
      } else {
        nextEndDate = nextStartDate.clone().add(13, "days").endOf("day");
      }

      // 4. Calculate tax deductions for next period
      const basicSalary = currentPayroll.basicSalary;
      let paye = 0;
      let uif = 0;
      let totalDeductions = 0;
      let netPay = basicSalary;

      if (basicSalary > 0) {
        try {
          const { calculateEnhancedPAYE, calculatePeriodUIF } = require('../utils/payrollCalculations');
          const payrollCalculations = require('../utils/payrollCalculations');

          // Calculate PAYE
          const annualSalary = basicSalary * 12;
          const payeResult = await calculateEnhancedPAYE({
            annualSalary,
            age: payrollCalculations.calculateAge(employee.dob),
            frequency: employee.payFrequency.frequency,
          });
          paye = payeResult.monthlyPAYE || 0; // Keep full precision

          // Calculate UIF
          uif = calculatePeriodUIF(basicSalary, employee.payFrequency.frequency);

          // Calculate totals
          totalDeductions = paye + uif;
          netPay = basicSalary - totalDeductions;
        } catch (error) {
          console.error("Error calculating next period taxes:", error);
        }
      }

      // Create next period with carried forward values and tax calculations
      const nextPeriod = await PayrollPeriod.create({
        employee: employeeId,
        company: new mongoose.Types.ObjectId(company),
        startDate: nextStartDate.toDate(),
        endDate: nextEndDate.toDate(),
        frequency: employee.payFrequency.frequency,
        basicSalary: basicSalary,
        grossPay: basicSalary,
        PAYE: paye,
        UIF: uif,
        totalDeductions: totalDeductions,
        netPay: netPay,
        status: "open",
      });

      // 5. Create next payroll record with carried forward values
      const nextPayroll = await Payroll.create({
        employee: employeeId,
        company: new mongoose.Types.ObjectId(company),
        month: nextEndDate.toDate(),
        basicSalary: currentPayroll.basicSalary,
        status: "draft",
        // Carry forward regular inputs
        travelAllowance: currentPayroll.travelAllowance,
        medical: currentPayroll.medical,
        pensionFund: currentPayroll.pensionFund,
        providentFund: currentPayroll.providentFund,
        retirementAnnuityFund: currentPayroll.retirementAnnuityFund,
        incomeProtection: currentPayroll.incomeProtection,
        unionMembershipFee: currentPayroll.unionMembershipFee,
        maintenanceOrder: currentPayroll.maintenanceOrder,
        garnishee: currentPayroll.garnishee,
        // Add any other regular inputs you want to carry forward
      });

      // 6. Update all future periods with the same values
      // IMPORTANT: Only update periods that are truly in the future and not finalized
      // Use a more restrictive query to prevent affecting historical periods
      const futurePeriodsUpdateResult = await PayrollPeriod.updateMany(
        {
          employee: employeeId,
          company: new mongoose.Types.ObjectId(company),
          startDate: { $gte: nextStartDate }, // Use >= instead of > to be more precise
          endDate: { $gt: nextEndDate }, // Ensure the period is completely after the current period
          isFinalized: false,
          status: { $in: ["open", "processing"] }, // Only update open or processing periods
        },
        {
          $set: { basicSalary: currentPayroll.basicSalary },
        }
      );


      // 7. Update all future payrolls with the same values
      await Payroll.updateMany(
        {
          employee: employeeId,
          company: new mongoose.Types.ObjectId(company),
          month: { $gt: nextEndDate },
          status: "draft",
        },
        {
          $set: {
            basicSalary: currentPayroll.basicSalary,
            travelAllowance: currentPayroll.travelAllowance,
            medical: currentPayroll.medical,
            pensionFund: currentPayroll.pensionFund,
            providentFund: currentPayroll.providentFund,
            retirementAnnuityFund: currentPayroll.retirementAnnuityFund,
            incomeProtection: currentPayroll.incomeProtection,
            unionMembershipFee: currentPayroll.unionMembershipFee,
            maintenanceOrder: currentPayroll.maintenanceOrder,
            garnishee: currentPayroll.garnishee,
            // Add any other regular inputs you want to carry forward
          },
        }
      );

      // 8. Finalize current period and payroll
      currentPeriod.isFinalized = true;
      currentPeriod.status = "finalized";
      await currentPeriod.save();

      currentPayroll.status = "finalized";
      currentPayroll.finalisedDate = new Date();
      await currentPayroll.save();

      return {
        currentPeriod,
        nextPeriod,
        currentPayroll,
        nextPayroll,
      };
    } catch (error) {
      console.error("Error in finalizePeriod:", error);
      throw error;
    }
  }

  // Helper function to calculate next period end date (BusinessDate implementation)
  static getNextPeriodEndDate(startDate, frequency) {
    const startDateStr = BusinessDate.normalize(startDate);
    return BusinessDate.calculatePeriodEndDate(startDateStr, frequency);
  }

  // Add new validation methods
  static async validatePeriodForFinalization(
    employeeId,
    periodEndDate,
    company
  ) {

    const validationResult = {
      isValid: true,
      errors: [],
      warnings: [],
    };

    try {
      // 1. Check if period exists and is current
      const period = await PayrollPeriod.findOne({
        employee: employeeId,
        company: company,
        endDate: new Date(periodEndDate),
        isFinalized: false,
      });

      if (!period) {
        validationResult.isValid = false;
        validationResult.errors.push("Period not found or already finalized");
        return validationResult;
      }

      // 2. Check employee status
      const employee = await Employee.findById(employeeId);
      if (!employee) {
        validationResult.isValid = false;
        validationResult.errors.push("Employee not found");
        return validationResult;
      }

      // 3. Validate against termination date
      if (employee.lastDayOfService) {
        const terminationDate = new Date(employee.lastDayOfService);
        const periodEnd = new Date(periodEndDate);

        if (periodEnd > terminationDate) {
          validationResult.isValid = false;
          validationResult.errors.push(
            "Cannot finalize period after termination date"
          );
          return validationResult;
        }
      }

      // 4. Check for required inputs
      const payroll = await Payroll.findOne({
        employee: employeeId,
        company: company,
        month: new Date(periodEndDate),
      });

      if (!payroll || !payroll.basicSalary) {
        validationResult.warnings.push("Basic salary not set for this period");
      }

      // 5. Check processing window
      const today = new Date();
      const processingWindow = {
        start: moment(period.endDate).subtract(7, "days").toDate(),
        end: moment(period.endDate).add(7, "days").toDate(),
      };

      if (today < processingWindow.start || today > processingWindow.end) {
        validationResult.warnings.push(
          "Period is being finalized outside normal processing window"
        );
      }

      // 6. Check for previous unfinalized periods
      const previousUnfinalized = await PayrollPeriod.findOne({
        employee: employeeId,
        company: company,
        endDate: { $lt: new Date(periodEndDate) },
        isFinalized: false,
      });

      if (previousUnfinalized) {
        validationResult.errors.push(
          "Previous periods must be finalized first"
        );
        validationResult.isValid = false;
      }

      return validationResult;
    } catch (error) {
      console.error("Error in validatePeriodForFinalization:", error);
      validationResult.isValid = false;
      validationResult.errors.push("Internal validation error");
      return validationResult;
    }
  }

  static async validateRegularInputs(
    employeeId,
    periodEndDate,
    inputType,
    inputData
  ) {

    const validationResult = {
      isValid: true,
      errors: [],
      warnings: [],
    };

    try {
      // 1. Check if period is open
      const period = await PayrollPeriod.findOne({
        employee: employeeId,
        endDate: new Date(periodEndDate),
        isFinalized: false,
      });

      if (!period) {
        validationResult.isValid = false;
        validationResult.errors.push("Period is not available for input");
        return validationResult;
      }

      // 2. Validate input type specific rules
      switch (inputType) {
        case "basicSalary":
          if (!inputData.amount || inputData.amount <= 0) {
            validationResult.errors.push("Basic salary must be greater than 0");
            validationResult.isValid = false;
          }
          break;

        case "deduction":
          // Check if deduction exceeds available income
          const payroll = await Payroll.findOne({
            employee: employeeId,
            month: new Date(periodEndDate),
          });

          if (payroll && inputData.amount > payroll.basicSalary) {
            validationResult.warnings.push(
              "Deduction amount exceeds basic salary"
            );
          }
          break;

        // Add other input type validations as needed
      }

      return validationResult;
    } catch (error) {
      console.error("Error in validateRegularInputs:", error);
      validationResult.isValid = false;
      validationResult.errors.push("Internal validation error");
      return validationResult;
    }
  }

  // Helper method to check processing windows
  static isWithinProcessingWindow(periodEndDate) {
    const today = new Date();
    const endDate = new Date(periodEndDate);

    const processingStart = moment(endDate).subtract(7, "days");
    const processingEnd = moment(endDate).add(7, "days");

    return moment(today).isBetween(processingStart, processingEnd, "day", "[]");
  }

  // 1. Period Sequence Validation
  static async validatePeriodSequence(employeeId, periodDate) {

    const validationResult = {
      isValid: true,
      errors: [],
      warnings: [],
    };

    try {
      const previousPeriod = await PayrollPeriod.findOne({
        employee: employeeId,
        endDate: { $lt: periodDate },
      }).sort({ endDate: -1 });

      const nextPeriod = await PayrollPeriod.findOne({
        employee: employeeId,
        startDate: { $gt: periodDate },
      }).sort({ startDate: 1 });

      // Check for gaps with previous period
      if (previousPeriod) {
        const dayAfterPreviousPeriod = moment(previousPeriod.endDate).add(
          1,
          "day"
        );
        if (!moment(periodDate).isSame(dayAfterPreviousPeriod, "day")) {
          validationResult.errors.push("Gap detected with previous period");
          validationResult.isValid = false;
        }
      }

      // Check for gaps with next period
      if (nextPeriod) {
        const dayBeforeNextPeriod = moment(nextPeriod.startDate).subtract(
          1,
          "day"
        );
        if (!moment(periodDate).isSame(dayBeforeNextPeriod, "day")) {
          validationResult.errors.push("Gap detected with next period");
          validationResult.isValid = false;
        }
      }

      return validationResult;
    } catch (error) {
      console.error("Error in validatePeriodSequence:", error);
      throw error;
    }
  }

  // 2. Input Validation with Dependencies
  static async validateInputDependencies(employeeId, inputType, inputData) {

    const validationResult = {
      isValid: true,
      errors: [],
      warnings: [],
    };

    try {
      const payroll = await Payroll.findOne({ employee: employeeId });

      // Dependency rules
      const dependencies = {
        pension_fund: ["basicSalary"],
        medical_aid: ["basicSalary"],
        travel_allowance: ["basicSalary"],
        commission: ["basicSalary"],
      };

      if (dependencies[inputType]) {
        for (const requiredInput of dependencies[inputType]) {
          if (!payroll || !payroll[requiredInput]) {
            validationResult.errors.push(
              `${requiredInput} must be set before adding ${inputType}`
            );
            validationResult.isValid = false;
          }
        }
      }

      // Validate amount limits
      if (inputData.amount) {
        const limits = await this.calculateInputLimits(employeeId, inputType);
        if (inputData.amount > limits.maximum) {
          validationResult.warnings.push(
            `Amount exceeds recommended maximum of ${limits.maximum}`
          );
        }
      }

      return validationResult;
    } catch (error) {
      console.error("Error in validateInputDependencies:", error);
      throw error;
    }
  }

  // 3. Calculate Input Limits
  static async calculateInputLimits(employeeId, inputType) {
    const payroll = await Payroll.findOne({ employee: employeeId });
    const basicSalary = payroll?.basicSalary || 0;

    // Define limits based on input type
    const limits = {
      pension_fund: { maximum: basicSalary * 0.275 }, // 27.5% limit
      medical_aid: { maximum: basicSalary * 0.333 }, // 33.3% limit
      garnishee: { maximum: basicSalary * 0.25 }, // 25% limit
      travel_allowance: { maximum: basicSalary * 0.8 }, // 80% limit
    };

    return limits[inputType] || { maximum: basicSalary };
  }

  // 4. Net Pay Impact Validation
  static async validateNetPayImpact(employeeId, periodId, newDeduction = 0) {

    const validationResult = {
      isValid: true,
      errors: [],
      warnings: [],
    };

    try {
      const period = await PayrollPeriod.findById(periodId);
      const payroll = await Payroll.findOne({
        employee: employeeId,
        month: period.endDate,
      });

      if (!payroll) {
        validationResult.errors.push("No payroll data found for period");
        validationResult.isValid = false;
        return validationResult;
      }

      // Calculate total deductions including new deduction
      const totalDeductions = (payroll.totalDeductions || 0) + newDeduction;
      const grossPay = payroll.basicSalary + (payroll.totalAllowances || 0);
      const projectedNetPay = grossPay - totalDeductions;

      // Minimum net pay threshold (e.g., 25% of gross)
      const minimumNetPay = grossPay * 0.25;

      if (projectedNetPay < minimumNetPay) {
        validationResult.errors.push(
          "Total deductions would reduce net pay below allowed minimum"
        );
        validationResult.isValid = false;
      }

      if (projectedNetPay < grossPay * 0.4) {
        validationResult.warnings.push(
          "Net pay would be less than 40% of gross pay"
        );
      }

      return validationResult;
    } catch (error) {
      console.error("Error in validateNetPayImpact:", error);
      throw error;
    }
  }

  static async generateNextPeriod(employee, currentPeriod, session) {

    // Validate input parameters
    if (!employee._id || !employee.company || !employee.payFrequency) {
      throw new Error("Invalid employee data - missing required fields");
    }

    if (!employee.payFrequency.frequency) {
      throw new Error("Employee pay frequency not properly configured");
    }


    try {
      // Validate employee status
      if (employee.status === "Inactive" || employee.status === "Terminated") {
        throw new Error(
          `Cannot generate next period for ${employee.status} employee`
        );
      }

      // Get current payroll to carry forward values
      const periodEndMoment = moment.tz(
        currentPeriod.endDate,
        DEFAULT_TIMEZONE
      );
      const monthStart = periodEndMoment.clone().startOf("month");
      const monthEnd = periodEndMoment.clone().endOf("month");

      let currentPayroll = await Payroll.findOne({
        employee: employee._id,
        month: {
          $gte: monthStart.toDate(),
          $lte: monthEnd.toDate(),
        },
      }).session(session);

      if (!currentPayroll) {
        // If no payroll exists, get or create one
        currentPayroll = await this.getOrCreatePayroll(
          employee._id,
          periodEndMoment.toDate()
        );
        if (!currentPayroll) {
          throw new Error("Failed to create or retrieve payroll record");
        }
      }

      // Calculate next period dates using BusinessDate for complete timezone independence
      const currentPeriodEndDateStr = currentPeriod.businessEndDate || BusinessDate.fromDate(currentPeriod.endDate);
      const nextEndDateStr = BusinessDate.calculateNextPeriodEndDate(
        currentPeriodEndDateStr,
        employee.payFrequency
      );
      const nextStartDateStr = BusinessDate.addDays(currentPeriodEndDateStr, 1);


      // Validate against termination date if exists
      let finalNextEndDateStr = nextEndDateStr;
      if (employee.lastDayOfService) {
        const terminationDateStr = BusinessDate.normalize(employee.lastDayOfService);
        if (BusinessDate.isAfter(nextEndDateStr, terminationDateStr)) {
          finalNextEndDateStr = terminationDateStr;
        }
      }

      // Check if period already exists using BusinessDate fields
      const existingPeriod = await PayrollPeriod.findOne({
        employee: employee._id,
        company: employee.company,
        $or: [
          // Check using BusinessDate fields (preferred)
          {
            startDateBusiness: nextStartDateStr,
            endDateBusiness: finalNextEndDateStr,
          },
          // Fallback to legacy Date fields for backward compatibility
          {
            startDate: BusinessDate.toDate(nextStartDateStr),
            endDate: BusinessDate.toDate(finalNextEndDateStr, true),
          }
        ]
      }).session(session);

      if (existingPeriod) {
        existingPeriod.basicSalary = currentPayroll.basicSalary;
        existingPeriod.PAYE = currentPeriod.PAYE;
        existingPeriod.UIF = currentPeriod.UIF;
        existingPeriod.calculations = currentPeriod.calculations;
        // Ensure BusinessDate fields are set
        existingPeriod.startDateBusiness = nextStartDateStr;
        existingPeriod.endDateBusiness = finalNextEndDateStr;
        await existingPeriod.save({ session });
        return existingPeriod;
      }

      // Calculate statutory deductions for next period
      const annualSalary = currentPayroll.basicSalary * 12;
      const payeResult = await calculatePAYE(
        annualSalary, // Use full annual salary
        100, // Use 100% for full month
        employee.payFrequency.frequency
      );
      const paye = payeResult.finalTax || 0; // Keep full precision

      // Calculate UIF using the period-based function
      const uif = calculatePeriodUIF(
        currentPayroll.basicSalary,
        employee.payFrequency.frequency
      );

      // Calculate total deductions and net pay for next period
      const totalDeductions = paye + uif;
      const netPay = currentPayroll.basicSalary - totalDeductions;

      // Create next period with calculations using BusinessDate
      const nextPeriod = new PayrollPeriod({
        employee: employee._id,
        company: new mongoose.Types.ObjectId(employee.company),
        // Legacy Date fields for backward compatibility
        startDate: BusinessDate.toDate(nextStartDateStr),
        endDate: BusinessDate.toDate(finalNextEndDateStr, true),
        // New BusinessDate fields (preferred)
        startDateBusiness: nextStartDateStr,
        endDateBusiness: finalNextEndDateStr,
        frequency: employee.payFrequency.frequency,
        basicSalary: currentPayroll.basicSalary,
        grossPay: currentPayroll.basicSalary,
        PAYE: paye,
        UIF: uif,
        totalDeductions: totalDeductions,
        netPay: netPay,
        isFinalized: false,
        calculations: currentPeriod.calculations,
      });

      await nextPeriod.save({ session });

      // Create or update next payroll
      const nextPayroll = await Payroll.findOneAndUpdate(
        {
          employee: employee._id,
          company: new mongoose.Types.ObjectId(employee.company),
          month: BusinessDate.toDate(finalNextEndDateStr, true),
        },
        {
          $set: {
            basicSalary: currentPayroll.basicSalary,
            status: "draft",
            travelAllowance: currentPayroll.travelAllowance,
            medical: currentPayroll.medical,
            pensionFund: currentPayroll.pensionFund,
            providentFund: currentPayroll.providentFund,
            retirementAnnuityFund: currentPayroll.retirementAnnuityFund,
            incomeProtection: currentPayroll.incomeProtection,
            unionMembershipFee: currentPayroll.unionMembershipFee,
            maintenanceOrder: currentPayroll.maintenanceOrder,
            garnishee: currentPayroll.garnishee,
            calculations: nextPeriod.calculations,
          },
        },
        {
          new: true,
          upsert: true,
          session,
        }
      );


      return nextPeriod;
    } catch (error) {
      console.error("Error in generateNextPeriod:", error);
      throw error; // Re-throw to be handled by caller
    }
  }

  // Add method to handle component changes
  static async updateComponent(employeeId, periodId, componentData, user) {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const period = await PayrollPeriod.findById(periodId);
      if (!period || period.isFinalized) {
        throw new Error("Invalid or finalized period");
      }

      // Get current component value if exists
      const currentSnapshot = period.componentSnapshots.find(
        (s) => s.type === componentData.type
      );

      // Create new snapshot
      const newSnapshot = {
        type: componentData.type,
        amount: componentData.amount,
        effectiveDate: new Date(),
        previousAmount: currentSnapshot?.amount || 0,
        reason: componentData.reason || "Component update",
        updatedBy: user._id,
      };

      // Update period snapshots
      if (currentSnapshot) {
        Object.assign(currentSnapshot, newSnapshot);
      } else {
        period.componentSnapshots.push(newSnapshot);
      }

      // Update current Payroll record
      await Payroll.findOneAndUpdate(
        {
          employee: employeeId,
          month: period.endDate,
        },
        {
          $set: {
            [componentData.type]: componentData.amount,
            updatedAt: new Date(),
          },
        },
        { upsert: true, session }
      );

      await period.save({ session });
      await session.commitTransaction();
    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  // Add this new method to PayrollService class
  static async updateBasicSalary(employeeId, amount, periodEndDate) {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Get employee and company information
      const employee = await Employee.findById(employeeId).populate(
        "payFrequency"
      );
      if (!employee) {
        throw new Error("Employee not found");
      }

      // Find current period using BusinessDate for complete timezone independence
      const periodEndDateStr = BusinessDate.normalize(periodEndDate);

      // Try BusinessDate field first (preferred)
      let currentPeriod = await PayrollPeriod.findOne({
        employee: employeeId,
        endDateBusiness: periodEndDateStr,
      });

      // If BusinessDate field not found, try legacy Date field for backward compatibility
      if (!currentPeriod) {
        currentPeriod = await PayrollPeriod.findOne({
          employee: employeeId,
          endDate: BusinessDate.toDate(periodEndDateStr, true),
        });
      }

      if (!currentPeriod) {
        throw new Error("Period not found");
      }

      // Update basic salary
      currentPeriod.basicSalary = amount;

      // Calculate PAYE
      const annualSalary = amount * 12;
      const payeResult = await calculatePAYE(
        annualSalary,
        100,
        employee.payFrequency.frequency
      );
      const paye = payeResult.finalTax || 0; // Keep full precision

      // Calculate UIF using the period-based function
      const uif = calculatePeriodUIF(amount, employee.payFrequency.frequency);

      // Calculate total deductions and net pay
      const totalDeductions = paye + uif;
      const netPay = amount - totalDeductions;

      // Update period with new calculations - using correct field names
      currentPeriod.basicSalary = amount;
      currentPeriod.PAYE = paye;
      currentPeriod.UIF = uif;
      currentPeriod.totalDeductions = totalDeductions;
      currentPeriod.netPay = netPay;
      currentPeriod.grossPay = amount; // For basic salary, gross pay equals basic salary
      await currentPeriod.save({ session });

      // Update or create payroll record
      await Payroll.findOneAndUpdate(
        {
          employee: employeeId,
          month: currentPeriod.endDate,
        },
        {
          $set: {
            basicSalary: amount,
            updatedAt: new Date(),
          },
        },
        { upsert: true, session }
      );

      await session.commitTransaction();
      return currentPeriod;
    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  /**
   * Enhanced proration calculation that considers both DOA and termination dates
   * @param {Object} employee - Employee object with doa and lastDayOfService
   * @param {Object} period - PayrollPeriod object
   * @param {Date} periodStart - Period start date
   * @param {Date} periodEnd - Period end date
   * @returns {Object} Proration details with percentage and working days
   */
  static async calculateEnhancedProration(employee, period, periodStart, periodEnd) {
    try {
      const periodStartMoment = moment(periodStart).startOf("day");
      const periodEndMoment = moment(periodEnd).endOf("day");

      // Employee active period boundaries
      const employeeStartDate = moment(employee.doa).startOf("day");
      const employeeEndDate = employee.lastDayOfService
        ? moment(employee.lastDayOfService).endOf("day")
        : null; // No termination date means still active

      console.log('Proration calculation inputs:', {
        periodStart: periodStartMoment.format('YYYY-MM-DD'),
        periodEnd: periodEndMoment.format('YYYY-MM-DD'),
        employeeDOA: employeeStartDate.format('YYYY-MM-DD'),
        employeeTermination: employeeEndDate ? employeeEndDate.format('YYYY-MM-DD') : 'Still Active'
      });

      // Calculate the overlap between payroll period and employee active period
      const workingPeriodStart = moment.max(periodStartMoment, employeeStartDate);
      const workingPeriodEnd = employeeEndDate
        ? moment.min(periodEndMoment, employeeEndDate)
        : periodEndMoment;

      // Check if there's any overlap (employee was active during this period)
      if (workingPeriodStart.isAfter(workingPeriodEnd)) {
        // No overlap - employee was not active during this period
        return {
          isProrated: true,
          percentage: 0,
          daysWorked: 0,
          totalDays: periodEndMoment.diff(periodStartMoment, "days") + 1,
          reason: 'Employee not active during this period'
        };
      }

      // Calculate working days and total days
      const daysWorked = workingPeriodEnd.diff(workingPeriodStart, "days") + 1;
      const totalDays = periodEndMoment.diff(periodStartMoment, "days") + 1;
      const percentage = (daysWorked / totalDays) * 100;

      // Determine if this period should be prorated
      const isProrated = percentage < 100;

      console.log('Proration calculation results:', {
        workingPeriodStart: workingPeriodStart.format('YYYY-MM-DD'),
        workingPeriodEnd: workingPeriodEnd.format('YYYY-MM-DD'),
        daysWorked,
        totalDays,
        percentage: percentage.toFixed(2) + '%',
        isProrated
      });

      return {
        isProrated,
        percentage,
        daysWorked,
        totalDays,
        reason: isProrated ? 'Partial period due to DOA or termination' : 'Full period'
      };

    } catch (error) {
      console.error('Error in enhanced proration calculation:', error);
      // Fallback to no proration
      return {
        isProrated: false,
        percentage: 100,
        daysWorked: 0,
        totalDays: 0,
        reason: 'Error in calculation - using full period'
      };
    }
  }

  /**
   * Recalculate and update a PayrollPeriod with correct proration
   * This can be used for both finalized and non-finalized periods
   */
  static async recalculatePayrollPeriod(periodId, forceUpdate = false) {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      console.log(`🔄 Recalculating PayrollPeriod ${periodId} with enhanced proration...`);

      // Get the period with employee data
      const period = await PayrollPeriod.findById(periodId)
        .populate('employee')
        .session(session);

      if (!period) {
        throw new Error('PayrollPeriod not found');
      }

      const employee = period.employee;
      if (!employee) {
        throw new Error('Employee not found for period');
      }

      console.log(`📊 Original period data:`, {
        periodId: period._id,
        isFinalized: period.isFinalized,
        originalGrossPay: period.grossPay,
        originalProratedPercentage: period.proratedPercentage,
        employeeDOA: employee.doa,
        periodStart: period.startDate,
        periodEnd: period.endDate
      });

      // Calculate enhanced proration
      const proRataDetails = await this.calculateEnhancedProration(
        employee,
        period,
        period.startDate,
        period.endDate
      );

      console.log(`📈 Enhanced proration result:`, proRataDetails);

      // Calculate prorated amounts
      const basicSalary = period.basicSalary || 0;
      const shouldApplyProRata = proRataDetails.isProrated && proRataDetails.percentage < 100;

      const proRatedAmount = shouldApplyProRata
        ? (basicSalary * proRataDetails.percentage) / 100
        : basicSalary;

      // Recalculate taxes on prorated amount
      const { calculateEnhancedPAYE, calculatePeriodUIF } = require('../utils/payrollCalculations');
      const payrollCalculations = require('../utils/payrollCalculations');

      // Calculate PAYE on full salary, then prorate
      const fullAnnualSalary = basicSalary * 12;
      const payeResult = await calculateEnhancedPAYE({
        annualSalary: fullAnnualSalary,
        age: payrollCalculations.calculateAge(employee.dob),
        frequency: employee.payFrequency?.frequency || 'monthly'
      });

      const fullMonthlyPAYE = payeResult.monthlyPAYE || 0;
      const paye = shouldApplyProRata
        ? (fullMonthlyPAYE * proRataDetails.percentage) / 100
        : fullMonthlyPAYE;

      // Calculate UIF on prorated amount
      const uif = calculatePeriodUIF(proRatedAmount, employee.payFrequency?.frequency || 'monthly');

      // Calculate SDL for employer reporting (not included in deductions)
      const sdl = proRatedAmount * 0.01;

      // Calculate totals
      const totalDeductions = paye + uif;
      const netPay = proRatedAmount - totalDeductions;

      console.log(`💰 Recalculated amounts:`, {
        basicSalary,
        proRatedAmount,
        paye,
        uif,
        sdl: `${sdl} (employer contribution)`,
        totalDeductions,
        netPay,
        proRataPercentage: proRataDetails.percentage
      });

      // Update the period (even if finalized, if forceUpdate is true)
      if (!period.isFinalized || forceUpdate) {
        const updateData = {
          grossPay: proRatedAmount,
          PAYE: paye,
          UIF: uif,
          SDL: sdl,
          totalDeductions: totalDeductions,
          netPay: netPay,
          proratedPercentage: proRataDetails.percentage,
          workedDays: proRataDetails.daysWorked,
          totalDaysInPeriod: proRataDetails.totalDays,
          isFirstPeriodWithDOA: proRataDetails.isProrated
        };

        await PayrollPeriod.findByIdAndUpdate(periodId, updateData, { session });

        console.log(`✅ PayrollPeriod ${periodId} updated successfully`);
      } else {
        console.log(`⚠️ PayrollPeriod ${periodId} is finalized - use forceUpdate=true to update`);
      }

      await session.commitTransaction();

      return {
        success: true,
        period: await PayrollPeriod.findById(periodId),
        proRataDetails,
        calculatedAmounts: {
          basicSalary,
          proRatedAmount,
          paye,
          uif,
          sdl,
          totalDeductions,
          netPay
        }
      };

    } catch (error) {
      await session.abortTransaction();
      console.error(`❌ Error recalculating PayrollPeriod ${periodId}:`, error);
      throw error;
    } finally {
      session.endSession();
    }
  }

  static async calculateTotalDeductions(payroll, employee, period) {
    if (!period || !payroll) {
      return {
        paye: 0,
        uif: 0,
        maintenanceOrder: 0,
        unionMembershipFee: payroll?.unionMembershipFee || 0,
        total: payroll?.unionMembershipFee || 0,
      };
    }

    const totalDeductions = {
      paye: period.PAYE || 0,
      uif: period.UIF || 0,
      maintenanceOrder: payroll.maintenanceOrder || 0,
      unionMembershipFee: payroll.unionMembershipFee || 0,
      total: 0,
    };

    // Calculate total
    totalDeductions.total =
      totalDeductions.paye +
      totalDeductions.uif +
      totalDeductions.maintenanceOrder +
      totalDeductions.unionMembershipFee;

    return totalDeductions;
  }

  static async calculatePayrollTotals(employeeId, endDate, companyId) {
    try {

      // Get employee and period data
      const employee = await Employee.findById(employeeId).populate(
        "payFrequency"
      );
      if (!employee) {
        throw new Error("Employee not found");
      }

      // Convert endDate to proper Date object if it isn't already
      const periodEndDate =
        endDate instanceof Date ? endDate : new Date(endDate);

      const currentPeriod = await PayrollPeriod.findOne({
        employee: employeeId,
        endDate: periodEndDate,
      });

      if (!currentPeriod) {
        return {
          grossIncome: 0,
          deductions: {
            statutory: { paye: 0, uif: 0, sdl: 0 },
            courtOrders: { maintenanceOrder: 0 },
            total: 0,
          },
          netPay: 0,
        };
      }

      const payroll = await Payroll.findOne({
        employee: employeeId,
        month: currentPeriod.endDate,
      });

      // ✅ REMOVED: Enhanced proration logic that was overriding BusinessDate results
      // Use stored pro-rata information from PayrollPeriod (calculated by BusinessDate)
      let proRataDetails = {
        isProrated: currentPeriod.isFirstPeriodWithDOA || false,
        percentage: currentPeriod.proratedPercentage || 100,
        daysWorked: currentPeriod.workedDays || 0,
        totalDays: currentPeriod.totalDaysInPeriod || 0,
        reason: currentPeriod.isFirstPeriodWithDOA ? 'Using BusinessDate calculation' : 'Full period'
      };

      console.log('Using BusinessDate proration calculation from database:', {
        employeeId: employee._id,
        periodStart: currentPeriod.startDate,
        periodEnd: currentPeriod.endDate,
        employeeDOA: employee.doa,
        employeeTermination: employee.lastDayOfService,
        proRataDetails
      });

      // Ensure basicSalary is a valid number
      let basicSalary = 0;
      try {
        basicSalary = parseFloat(currentPeriod.basicSalary);
        if (isNaN(basicSalary)) basicSalary = 0;
      } catch (error) {
        basicSalary = 0;
      }

      // Calculate prorated amount for gross pay and UIF (but not for PAYE calculation)
      // FIXED: Only apply pro-rata when percentage is actually less than 100%
      let proRatedAmount = 0;
      try {
        // Check if pro-rata should be applied: must be first period AND percentage < 100%
        const shouldApplyProRata = proRataDetails.isProrated && proRataDetails.percentage < 100;

        console.log('Pro-rata calculation debug:', {
          isProrated: proRataDetails.isProrated,
          percentage: proRataDetails.percentage,
          shouldApplyProRata: shouldApplyProRata,
          basicSalary: basicSalary
        });

        if (shouldApplyProRata) {
          proRatedAmount = (basicSalary * proRataDetails.percentage) / 100;
          console.log(`Applied pro-rata: ${basicSalary} * ${proRataDetails.percentage}% = ${proRatedAmount}`);
        } else {
          proRatedAmount = basicSalary;
          console.log(`Using full salary (100% or not prorated): ${proRatedAmount}`);
        }

        if (isNaN(proRatedAmount)) proRatedAmount = 0;
      } catch (error) {
        console.error('Error in pro-rata calculation:', error);
        proRatedAmount = 0;
      }

      // Get accommodation benefit amount with validation
      const accommodationBenefit = parseFloat(payroll?.accommodationBenefit) || 0;

      // SURGICAL FIX: Calculate PAYE using frequency-specific method
      // Use full basic salary for PAYE calculation (not prorated amount)
      const periodsPerYear = employee.payFrequency.frequency === 'weekly' ? 52 :
                            employee.payFrequency.frequency === 'biweekly' ? 26 : 12;
      const fullAnnualSalary = basicSalary * periodsPerYear;

      const payeResult = await calculateEnhancedPAYE({
        annualSalary: fullAnnualSalary,
        age: payrollCalculations.calculateAge(employee.dob),
        frequency: employee.payFrequency.frequency,
        accommodationBenefit: payroll?.accommodationBenefit || 0,
        travelAllowance: payroll?.travelAllowance?.fixedAllowanceAmount || 0
      });

      // Apply pro-rata to the period-specific PAYE (matching Calculator card method)
      // FIXED: Only apply pro-rata when percentage is actually less than 100%
      const fullPeriodPAYE = payeResult.periodPAYE || 0; // Use frequency-specific PAYE
      const shouldApplyProRataToPAYE = proRataDetails.isProrated && proRataDetails.percentage < 100;

      const paye = shouldApplyProRataToPAYE
        ? (fullPeriodPAYE * proRataDetails.percentage) / 100
        : fullPeriodPAYE;

      console.log('PAYE calculation debug:', {
        frequency: employee.payFrequency.frequency,
        fullPeriodPAYE: fullPeriodPAYE,
        shouldApplyProRataToPAYE: shouldApplyProRataToPAYE,
        finalPAYE: paye,
        periodsPerYear: periodsPerYear
      });

      // Calculate UIF using the period-based function
      const uif = calculatePeriodUIF(
        proRatedAmount, // UIF is calculated on basic salary only
        employee.payFrequency.frequency
      );

      // Calculate SDL for employer reporting (NOT included in employee deductions)
      // SDL is an employer contribution, not an employee deduction
      const sdl = proRatedAmount * 0.01; // 1% of remuneration for employer reporting only

      // Calculate total deductions (EXCLUDING SDL - it's an employer contribution)
      const totalDeductions = paye + uif + (payroll?.maintenanceOrder || 0);

      console.log('SDL calculation (employer contribution only):', {
        proRatedAmount,
        sdlAmount: sdl,
        note: 'SDL is NOT included in employee deductions'
      });

      // Add Loss of Income Policy Payout (non-taxable income)
      const lossOfIncomePayout = payroll?.lossOfIncome || 0;

      // Calculate total income (Basic Salary + Accommodation Benefit + Loss of Income)
      const totalIncome =
        Number(proRatedAmount.toFixed(2)) +
        Number(accommodationBenefit.toFixed(2)) +
        Number(lossOfIncomePayout.toFixed(2));

      // Calculate net pay
      const netPay = Number((totalIncome - totalDeductions).toFixed(2));

      const calculations = {
        basicSalary: {
          full: Number(basicSalary.toFixed(2)),
          prorated: Number(proRatedAmount.toFixed(2)),
        },
        proRataDetails,
        taxableIncome: Number((proRatedAmount + accommodationBenefit).toFixed(2)), // Add taxable income to calculations
        benefits: {
          accommodationBenefit: Number(accommodationBenefit.toFixed(2)),
        },
        nonTaxableIncome: {
          lossOfIncomePayout: Number(lossOfIncomePayout.toFixed(2)),
        },
        totalIncome, // New field for total income
        grossIncome: totalIncome, // Alias for backward compatibility
        totalDeductions, // New field for total deductions
        paye: Number(paye.toFixed(2)), // New field for PAYE
        uif: Number(uif.toFixed(2)), // New field for UIF
        netPay: Number(netPay.toFixed(2)), // New field for net pay
        deductions: {
          statutory: {
            paye: Number(paye.toFixed(2)),
            uif: Number(uif.toFixed(2)),
            sdl,
          },
          courtOrders: {
            maintenanceOrder: payroll?.maintenanceOrder || 0,
          },
          total: Number(totalDeductions.toFixed(2)),
        },
      };


      return calculations;
    } catch (error) {
      console.error("Error calculating payroll totals:", error);
      throw error;
    }
  }

  static async updateMaintenanceOrder(employeeId, amount, periodEndDate) {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Update payroll record
      const payroll = await Payroll.findOneAndUpdate(
        {
          employee: employeeId,
          month: periodEndDate,
        },
        {
          $set: {
            maintenanceOrder: parseFloat(amount),
            updatedAt: new Date(),
          },
        },
        {
          session,
          new: true,
          upsert: true, // Create if doesn't exist
        }
      );

      // Recalculate totals
      const totals = await this.calculatePayrollTotals(
        employeeId,
        periodEndDate
      );

      // Update payroll with new totals
      payroll.calculations = totals.calculations;
      await payroll.save({ session });

      await session.commitTransaction();

      return {
        success: true,
        payroll,
        calculations: totals.calculations,
      };
    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  static async calculateStatutoryDeductions(employeeId, periodEndDate) {
    try {
      const payroll = await Payroll.findOne({
        employee: employeeId,
        month: periodEndDate,
      });

      const employee = await Employee.findById(employeeId).populate(
        "payFrequency"
      );
      const basicSalary = payroll?.basicSalary || 0;

      // 1. Travel Allowance
      const travelAllowance = {
        fixedAllowance: payroll?.travelAllowance?.fixedAllowance === "on",
        fixedAllowanceAmount:
          payroll?.travelAllowance?.fixedAllowanceAmount || 0,
        reimbursedExpenses:
          payroll?.travelAllowance?.reimbursedExpenses === "on",
        companyPetrolCard: payroll?.travelAllowance?.companyPetrolCard === "on",
        reimbursedPerKmTravelled:
          payroll?.travelAllowance?.reimbursedPerKmTravelled === "on",
        ratePerKm: payroll?.travelAllowance?.ratePerKm || 0,
        only20PercentTax: payroll?.travelAllowance?.only20PercentTax === "on",
      };

      // Calculate kms reimbursement if applicable
      if (payroll?.travelAllowance?.reimbursedPerKmTravelled === "on") {
        travelAllowance.kmsReimbursement =
          (payroll.travelAllowance.kmsTravelled || 0) *
          (payroll.travelAllowance.ratePerKm || 0);
      }

      // Calculate total and taxable amounts
      travelAllowance.total =
        travelAllowance.fixedAllowanceAmount +
        travelAllowance.reimbursedExpenses +
        travelAllowance.companyPetrolCard +
        travelAllowance.kmsReimbursement;

      travelAllowance.taxableAmount = payroll?.travelAllowance?.only20PercentTax
        ? travelAllowance.total * 0.2
        : travelAllowance.total * 0.8;

      // 2. Medical Aid
      const medicalAid = {
        employeeContribution: payroll?.medical?.medicalAid || 0,
        employerContribution: payroll?.medical?.employerContribution || 0,
        members: payroll?.medical?.members || 0,
        employeeHandlesPayment:
          payroll?.medical?.employeeHandlesPayment || false,
        dontApplyTaxCredits: payroll?.medical?.dontApplyTaxCredits || false,
        taxCredit: 0,
        taxableAmount: 0,
        totalDeduction: 0,
      };

      // Calculate tax credit if applicable
      if (!medicalAid.dontApplyTaxCredits) {
        medicalAid.taxCredit = this.calculateMedicalAidTaxCredit(
          medicalAid.members,
          employee.payFrequency.frequency
        );
      }

      // Calculate taxable benefit
      if (!medicalAid.employeeHandlesPayment) {
        medicalAid.taxableAmount = medicalAid.employerContribution;
      }

      medicalAid.totalDeduction = medicalAid.employeeContribution;

      // 3. Company Car
      const companyCar = {
        deemedValue: payroll?.companyCar?.deemedValue || 0,
        includesMaintenancePlan:
          payroll?.companyCar?.includesMaintenancePlan || false,
        taxablePercentage: payroll?.companyCar?.taxablePercentage || "80%",
        monthlyBenefit: 0,
        taxableAmount: 0,
      };

      if (companyCar.deemedValue > 0) {
        const basePercentage = companyCar.includesMaintenancePlan
          ? 0.0325
          : 0.035;
        companyCar.monthlyBenefit = companyCar.deemedValue * basePercentage;

        switch (companyCar.taxablePercentage) {
          case "20%":
            companyCar.taxableAmount = companyCar.monthlyBenefit * 0.2;
            break;
          case "80%":
            companyCar.taxableAmount = companyCar.monthlyBenefit * 0.8;
            break;
          case "100%":
            companyCar.taxableAmount = companyCar.monthlyBenefit;
            break;
        }
      }

      // 4. Company Car Under Operating Lease
      const companyCarLease = {
        amount: payroll?.companyCarUnderOperatingLease?.amount || 0,
        taxablePercentage:
          payroll?.companyCarUnderOperatingLease?.taxablePercentage || "80%",
        taxableAmount: 0,
      };

      if (companyCarLease.amount > 0) {
        switch (companyCarLease.taxablePercentage) {
          case "20%":
            companyCarLease.taxableAmount = companyCarLease.amount * 0.2;
            break;
          case "80%":
            companyCarLease.taxableAmount = companyCarLease.amount * 0.8;
            break;
          case "100%":
            companyCarLease.taxableAmount = companyCarLease.amount;
            break;
        }
      }

      // 5. Accommodation Benefit
      const accommodation = {
        amount: parseFloat(payroll?.accommodationBenefit) || 0,
        taxableAmount: parseFloat(payroll?.accommodationBenefit) || 0, // Fully taxable
        description: "Accommodation provided by employer",
        taxablePercentage: "100%", // Accommodation benefit is always 100% taxable
      };

      // Log accommodation benefit details

      // Calculate total benefits and allowances
      const totalBenefits = {
        grossAmount:
          travelAllowance.total +
          medicalAid.employerContribution +
          companyCar.monthlyBenefit +
          companyCarLease.amount +
          accommodation.amount,
        taxableAmount:
          travelAllowance.taxableAmount +
          medicalAid.taxableAmount +
          companyCar.taxableAmount +
          companyCarLease.taxableAmount +
          accommodation.taxableAmount,
        taxCredit: medicalAid.taxCredit,
        deductions: medicalAid.totalDeduction,
      };

      return {
        travelAllowance,
        medicalAid,
        companyCar,
        companyCarLease,
        accommodation,
        summary: totalBenefits,
        validations: {
          travelAllowanceExceedsLimit:
            travelAllowance.total > basicSalary * 0.8,
          warnings: [],
        },
      };
    } catch (error) {
      console.error("Error calculating benefits and allowances:", error);
      throw error;
    }
  }

  static async updateBenefitOrAllowance(
    employeeId,
    componentType,
    data,
    relevantDate
  ) {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Find employee and company
      const employee = await Employee.findById(employeeId)
        .populate("payFrequency")
        .populate("company");

      if (!employee) {
        throw new Error("Employee not found");
      }

      // Find current active period
      const currentPeriod = await PayrollPeriod.findOne({
        employee: employeeId,
        company: employee.company,
        isFinalized: false,
        status: "open",
      }).sort({ startDate: 1 });

      if (!currentPeriod) {
        throw new Error("No active payroll period found");
      }


      // Find or create payroll for current period
      let payroll = await Payroll.findOne({
        employee: employeeId,
        company: employee.company,
        month: currentPeriod.endDate,
      });

      if (!payroll) {
        payroll = new Payroll({
          employee: employeeId,
          company: new mongoose.Types.ObjectId(employee.company),
          month: currentPeriod.endDate,
          basicSalary: employee.basicSalary || 0,
        });
      }

      // Ensure basic salary is set
      if (!payroll.basicSalary && employee.basicSalary) {
        payroll.basicSalary = employee.basicSalary;
      }

      // Parse amount for numeric fields
      const amount = data.amount ? parseFloat(data.amount) : 0;

      // Handle specific component types
      switch (componentType) {
        case "loss-of-income":
          const lossOfIncomeCalc = calculateLossOfIncome(
            amount,
            employee.payFrequency?.frequency || "monthly"
          );
          payroll.lossOfIncome = lossOfIncomeCalc.amount;
          payroll.lossOfIncomeEnabled = true;

          // Store calculation details in the data Map
          if (!payroll.data) {
            payroll.data = new Map();
          }
          payroll.data.set("lossOfIncomeDetails", {
            amount: lossOfIncomeCalc.amount,
            taxableAmount: lossOfIncomeCalc.taxableAmount,
            nonTaxableAmount: lossOfIncomeCalc.nonTaxableAmount,
            lastUpdated: new Date(),
          });
          break;

        case "travel-allowance":
          payroll.travelAllowance = {
            fixedAllowance: data.fixedAllowance === "on",
            fixedAllowanceAmount: parseFloat(data.fixedAllowanceAmount) || 0,
            reimbursedExpenses: data.reimbursedExpenses === "on",
            companyPetrolCard: data.companyPetrolCard === "on",
            reimbursedPerKmTravelled: data.reimbursedPerKmTravelled === "on",
            ratePerKm: parseFloat(data.ratePerKm) || 0,
            only20PercentTax: data.only20PercentTax === "on",
          };
          payroll.travelAllowanceEnabled = true;
          break;

        case "accommodation-benefit":
          const validatedAmount = parseFloat(amount) || 0;
          payroll.accommodationBenefit = validatedAmount;
          payroll.accommodationBenefitEnabled = validatedAmount > 0;
          
          // Store calculation details in the data Map
          if (!payroll.data) {
            payroll.data = new Map();
          }
          
          payroll.data.set("accommodationBenefit", {
            amount: validatedAmount,
            taxableAmount: validatedAmount, // Accommodation benefit is 100% taxable
            lastUpdated: new Date(),
            isValid: !isNaN(validatedAmount)
          });
          break;

        case "bursaries-and-scholarships":
          // Ensure we have a valid date
          const parsedDate = moment(data.relevantDate || new Date())
            .endOf("day")
            .toDate();

          // Update bursaries and scholarships
          await Payroll.findOneAndUpdate(
            {
              employee: employeeId,
              month: {
                $gte: currentPeriod.startDate,
                $lte: currentPeriod.endDate,
              },
            },
            {
              $set: {
                bursariesAndScholarships: {
                  type: data.type,
                  taxablePortion: parseFloat(data.taxablePortion) || 0,
                  exemptPortion: parseFloat(data.exemptPortion) || 0,
                  // Remove date field as it's not needed
                  enabled: true,
                  employeeHandlesPayment: data.employeeHandlesPayment === "on",
                  toDisabledPerson: data.toDisabledPerson === "on",
                },
              },
              $inc: {
                totalEarnings: parseFloat(data.amount) || 0,
              },
            },
            { new: true }
          );
          break;

        case "company-car":
          payroll.companyCar = {
            deemedValue: parseFloat(data.deemedValue) || 0,
            includesMaintenancePlan: data.includesMaintenancePlan === "on",
            taxablePercentage: data.taxablePercentage || "80%",
          };
          payroll.companyCarEnabled = true;
          break;

        case "company-car-lease":
          await Payroll.findOneAndUpdate(
            { employee: employeeId, month: relevantDate },
            { $set: { "companyCarUnderOperatingLease.amount": data.amount } },
            { new: true, upsert: true }
          );
          payroll.companyCarUnderOperatingLeaseEnabled = true;
          break;

        case "commission":
          payroll.commission = parseFloat(data.amount) || 0;
          payroll.commissionEnabled = true;
          break;

        case "pension-fund":
          payroll.pensionFund = {
            contributionCalculation:
              data.contributionCalculation || data.contributionMethod,
            beneficiary: data.beneficiary || "",
            categoryFactor: parseFloat(data.categoryFactor) || 1,
            fixedContributionEmployee:
              parseFloat(data.fixedContributionEmployee) || 0,
            fixedContributionEmployer:
              parseFloat(data.fixedContributionEmployer) || 0,
            employeeContribution: parseFloat(data.employeeContribution) || 0,
            employerContribution: parseFloat(data.employerContribution) || 0,
            rfiEmployee: parseFloat(data.rfiEmployee) || 0,
            rfiEmployer: parseFloat(data.rfiEmployer) || 0,
          };
          payroll.pensionFundEnabled = true;
          break;

        default:
          throw new Error(`Unsupported component type: ${componentType}`);
      }

      await payroll.save({ session });

      // Update period's basic salary if not set
      if (!currentPeriod.basicSalary && payroll.basicSalary) {
        currentPeriod.basicSalary = payroll.basicSalary;
      }

      // Calculate and update PAYE
      const annualSalary =
        (currentPeriod.basicSalary || payroll.basicSalary) * 12;
      const payeResult = await calculatePAYE(
        annualSalary,
        100,
        employee.payFrequency.frequency
      );
      const paye = payeResult.finalTax || 0; // Keep full precision

      // Calculate UIF using the period-based function
      const uif = calculatePeriodUIF(
        currentPeriod.basicSalary || payroll.basicSalary,
        employee.payFrequency.frequency
      );

      // Update period with new calculations
      currentPeriod.PAYE = paye;
      currentPeriod.UIF = uif;
      await currentPeriod.save({ session });

      // Commit the transaction
      await session.commitTransaction();


      return {
        success: true,
        payroll,
        message: `${componentType} updated successfully`,
      };
    } catch (error) {
      // Rollback the transaction on error
      await session.abortTransaction();
      console.error("Error updating benefit/allowance:", error);
      return {
        success: false,
        error: error.message || `Error updating ${componentType}`,
      };
    } finally {
      session.endSession();
    }
  }

  static async updateMedicalAid(employeeId, data, relevantDate) {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Get employee and company information
      const employee = await Employee.findById(employeeId)
        .populate("payFrequency")
        .session(session);


      if (!employee) {
        throw new Error("Employee not found");
      }

      // Find or create payroll record
      let payroll = await Payroll.findOne({
        employee: employeeId,
        month: moment(relevantDate).endOf("month").toDate(),
      }).session(session);

      if (!payroll) {
        payroll = new Payroll({
          employee: employeeId,
          company: new mongoose.Types.ObjectId(employee.company),
          month: moment(relevantDate).endOf("month").toDate(),
          status: "draft",
          medical: {},
          calculations: {
            grossIncome: 0,
            totalDeductions: 0,
            taxableIncome: 0,
            netPay: 0,
            periodTax: 0,
          },
        });
      }

      // Update medical aid information
      payroll.medical = {
        employerContribution: parseFloat(data.employerContribution) || 0,
        medicalAid: parseFloat(data.amount) || 0,
        employeeHandlesPayment: false,
        members: parseInt(data.members) || 0,
        dontApplyTaxCredits: false,
      };


      // Save the updated payroll
      await payroll.save({ session });

      // Recalculate totals
      const totals = await this.calculatePayrollTotals(
        employeeId,
        moment(relevantDate).endOf("month").toDate()
      );

      // Update payroll with new totals
      payroll.calculations = totals.calculations;
      await payroll.save({ session });

      await session.commitTransaction();
      return {
        success: true,
        payroll,
        calculations: totals.calculations,
      };
    } catch (error) {
      await session.abortTransaction();
      console.error("Error updating medical aid:", error);
      throw error;
    } finally {
      session.endSession();
    }
  }

  static async calculateAdditionalIncome(employeeId, periodEndDate) {
    try {
      const payroll = await Payroll.findOne({
        employee: employeeId,
        month: periodEndDate,
      });

      const employee = await Employee.findById(employeeId).populate(
        "payFrequency"
      );
      const basicSalary = payroll?.basicSalary || 0;

      // 1. Commission
      const commission = {
        amount: payroll?.commission || 0,
        enabled: payroll?.commissionEnabled || false,
        taxableAmount: 0,
        periodTax: 0,
      };

      if (commission.enabled && commission.amount > 0) {
        commission.taxableAmount = commission.amount;
        const payeResult = await calculatePAYE(
          commission.amount,
          employee.payFrequency.frequency,
          true // isAdditionalIncome flag
        );
        commission.periodTax = payeResult.finalTax || 0; // Keep full precision
      }

      // 2. Bonus
      const bonus = {
        amount: payroll?.annualBonus?.amount || 0,
        description: payroll?.annualBonus?.description || "",
        date: payroll?.annualBonus?.date,
        taxableAmount: 0,
        periodTax: 0,
      };

      if (bonus.amount > 0) {
        const bonusTaxCalc = await this.calculateAnnualBonusTax(
          bonus.amount,
          basicSalary,
          employee.payFrequency.frequency
        );
        bonus.taxableAmount = bonus.amount;
        bonus.periodTax = bonusTaxCalc.bonusTax;
      }

      // 3. Leave Pay
      const leavePay = {
        amount: payroll?.leavePaidOut || 0,
        taxableAmount: 0,
        periodTax: 0,
      };

      if (leavePay.amount > 0) {
        leavePay.taxableAmount = leavePay.amount;
        const payeResult = await calculatePAYE(
          leavePay.amount,
          employee.payFrequency.frequency,
          true // isAdditionalIncome flag
        );
        leavePay.periodTax = payeResult.finalTax || 0; // Keep full precision
      }

      // 4. Overtime
      const overtime = {
        normalHours: payroll?.overtime?.normalHours || 0,
        normalRate: payroll?.overtime?.normalRate || 0,
        sundayHours: payroll?.overtime?.sundayHours || 0,
        sundayRate: payroll?.overtime?.sundayRate || 0,
        publicHolidayHours: payroll?.overtime?.publicHolidayHours || 0,
        publicHolidayRate: payroll?.overtime?.publicHolidayRate || 0,
        amount: 0,
        taxableAmount: 0,
        periodTax: 0,
      };

      // Calculate total overtime amount
      overtime.amount =
        overtime.normalHours * overtime.normalRate +
        overtime.sundayHours * overtime.sundayRate +
        overtime.publicHolidayHours * overtime.publicHolidayRate;

      if (overtime.amount > 0) {
        overtime.taxableAmount = overtime.amount;
        const payeResult = await calculatePAYE(
          overtime.amount,
          employee.payFrequency.frequency,
          true // isAdditionalIncome flag
        );
        overtime.periodTax = payeResult.finalTax || 0; // Keep full precision
      }

      // 5. Loss of Income
      const lossOfIncome = {
        amount: payroll?.lossOfIncome || 0,
        enabled: payroll?.lossOfIncomeEnabled || false,
        taxableAmount: 0,
        periodTax: 0,
      };

      if (lossOfIncome.enabled && lossOfIncome.amount > 0) {
        lossOfIncome.taxableAmount = lossOfIncome.amount;
        const payeResult = await calculatePAYE(
          lossOfIncome.amount,
          employee.payFrequency.frequency,
          true // isAdditionalIncome flag
        );
        lossOfIncome.periodTax = payeResult.finalTax || 0; // Keep full precision
      }

      // Calculate totals
      const totalAdditionalIncome = {
        grossAmount:
          commission.amount +
          bonus.amount +
          leavePay.amount +
          overtime.amount +
          lossOfIncome.amount,
        taxableAmount:
          commission.taxableAmount +
          bonus.taxableAmount +
          leavePay.taxableAmount +
          overtime.taxableAmount +
          lossOfIncome.taxableAmount,
        totalTax:
          commission.periodTax +
          bonus.periodTax +
          leavePay.periodTax +
          overtime.periodTax +
          lossOfIncome.periodTax,
      };

      // Validation checks
      const validations = {
        commissionExceedsBasic: commission.amount > basicSalary * 2,
        overtimeExceedsLimit: overtime.amount > basicSalary * 0.5,
        warnings: [],
      };

      if (validations.commissionExceedsBasic) {
        validations.warnings.push(
          `Commission (${commission.amount}) exceeds twice the basic salary (${
            basicSalary * 2
          })`
        );
      }

      if (validations.overtimeExceedsLimit) {
        validations.warnings.push(
          `Overtime (${overtime.amount}) exceeds 50% of basic salary (${
            basicSalary * 0.5
          })`
        );
      }

      return {
        commission,
        bonus,
        leavePay,
        overtime,
        lossOfIncome,
        total: totalAdditionalIncome,
        validations,
      };
    } catch (error) {
      console.error("Error calculating additional income:", error);
      throw error;
    }
  }

  static async updateAdditionalIncome(
    employeeId,
    componentType,
    data,
    periodEndDate,
    companyId
  ) {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Validate the component type
      const validTypes = [
        "commission",
        "annualBonus",
        "leavePaidOut",
        "overtime",
        "lossOfIncome",
      ];
      if (!validTypes.includes(componentType)) {
        throw new Error(
          `Invalid additional income type. Must be one of: ${validTypes.join(
            ", "
          )}`
        );
      }

      // Get employee to get company
      const employee = await Employee.findById(employeeId).lean();

      if (!employee) {
        throw new Error("Employee not found");
      }

      // Get company from currentCompany
      if (!companyId) {
        throw new Error("Company ID is required to create new payroll record");
      }

      // Get or create payroll record
      let payroll = await Payroll.findOne({
        employee: employeeId,
        month: periodEndDate,
      });

      if (!payroll) {
        payroll = new Payroll({
          employee: employeeId,
          company: new mongoose.Types.ObjectId(companyId),
          month: periodEndDate,
          calculations: {
            additionalIncome: {},
            deductions: {},
            statutory: {},
          },
        });
      }

      // Parse amount to float
      let amount = 0;
      if (data.amount !== undefined && data.amount !== "") {
        amount = parseFloat(data.amount);
        if (isNaN(amount) || amount < 0) {
          throw new Error("Invalid amount. Must be a non-negative number.");
        }
      }

      // Update the component
      switch (componentType) {
        case "lossOfIncome":
          payroll.lossOfIncome = amount;
          payroll.lossOfIncomeEnabled = true;
          break;
        case "commission":
          payroll.commission = amount;
          payroll.commissionEnabled = true;
          break;
        case "annualBonus":
          payroll.annualBonus = amount;
          payroll.annualBonusEnabled = true;
          break;
        case "leavePaidOut":
          payroll.leavePaidOut = amount;
          payroll.leavePaidOutEnabled = true;
          break;
        case "overtime":
          payroll.overtime = amount;
          payroll.overtimeEnabled = true;
          break;
      }

      // Save the payroll record
      await payroll.save({ session });

      // Recalculate all additional income
      const calculations = await this.calculateAdditionalIncome(
        employeeId,
        periodEndDate
      );

      // Update payroll with new calculations
      payroll.calculations = {
        ...payroll.calculations,
        additionalIncome: calculations,
      };

      await payroll.save({ session });
      await session.commitTransaction();

      return {
        success: true,
        payroll,
        calculations,
      };
    } catch (error) {
      await session.abortTransaction();
      console.error("Error in updateAdditionalIncome:", error);
      return {
        success: false,
        error: error.message,
      };
    } finally {
      session.endSession();
    }
  }

  static async updateOtherDeduction(
    employeeId,
    componentType,
    data,
    periodEndDate
  ) {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Validate component type
      if (
        ![
          "unionMembershipFee",
          "incomeProtection",
          "savings",
          "voluntaryTaxOverDeduction",
        ].includes(componentType)
      ) {
        throw new Error("Invalid deduction type");
      }

      // Special handling for savings target amount
      if (componentType === "savings" && data.targetAmount) {
        if (data.targetAmount < data.currentBalance) {
          throw new Error("Target amount cannot be less than current balance");
        }
      }

      // Update payroll record
      const payroll = await Payroll.findOneAndUpdate(
        {
          employee: employeeId,
          month: periodEndDate,
        },
        {
          $set:
            componentType === "unionMembershipFee"
              ? { unionMembershipFee: parseFloat(data.amount) }
              : componentType === "voluntaryTaxOverDeduction"
              ? { voluntaryTaxOverDeduction: parseFloat(data.amount) }
              : {
                  [componentType]: {
                    ...data,
                    lastUpdated: new Date(),
                  },
                },
        },
        { session, new: true }
      );

      // Recalculate all deductions
      const calculations = await PayrollService.calculateTotalDeductions(
        payroll,
        await Employee.findById(employeeId),
        await PayrollPeriod.findOne({
          employee: employeeId,
          endDate: new Date(periodEndDate),
        })
      );

      // Update payroll with new calculations
      payroll.calculations = {
        ...payroll.calculations,
        deductions: calculations,
      };

      // Update total deductions and net pay
      const totalDeductions =
        (payroll.calculations.statutoryDeductions?.total || 0) +
        (payroll.calculations.courtOrders?.total || 0) +
        calculations.total;

      payroll.calculations.totalDeductions = totalDeductions;
      payroll.calculations.netPay =
        (payroll.basicSalary || 0) - totalDeductions;

      await payroll.save({ session });
      await session.commitTransaction();

      return {
        success: true,
        payroll,
        calculations,
      };
    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  // Helper method to validate deduction amounts
  static validateDeductionLimits(basicSalary, deductions) {
    const totalDeductions = Object.values(deductions).reduce(
      (sum, val) => sum + val,
      0
    );
    const maxDeduction = basicSalary * 0.75; // 75% limit

    return {
      isValid: totalDeductions <= maxDeduction,
      maxAmount: maxDeduction,
      currentTotal: totalDeductions,
      remaining: maxDeduction - totalDeductions,
    };
  }

  // Add this method to the PayrollService class
  static async calculateTaxableIncome(payroll) {
    try {
      if (!payroll) return 0;

      const basicSalary = payroll.basicSalary || 0;
      let taxableIncome = basicSalary;

      // Add taxable benefits
      if (payroll.travelAllowance) {
        const travelAmount = payroll.travelAllowance.fixedAllowanceAmount || 0;
        taxableIncome += payroll.travelAllowance.only20PercentTax
          ? travelAmount * 0.2
          : travelAmount * 0.8;
      }

      // Add company car benefit
      if (payroll.companyCar?.deemedValue) {
        const monthlyBenefit =
          payroll.companyCar.deemedValue *
          (payroll.companyCar.includesMaintenancePlan ? 0.0325 : 0.035);

        switch (payroll.companyCar.taxablePercentage) {
          case "20%":
            taxableIncome += monthlyBenefit * 0.2;
            break;
          case "80%":
            taxableIncome += monthlyBenefit * 0.8;
            break;
          case "100%":
            taxableIncome += monthlyBenefit;
            break;
        }
      }

      // Add medical aid taxable benefit
      if (payroll.medical && !payroll.medical.employeeHandlesPayment) {
        taxableIncome += payroll.medical.employerContribution || 0;
      }

      // Add accommodation benefit (100% taxable)
      if (payroll.accommodationBenefit) {
        const accommodationAmount = parseFloat(payroll.accommodationBenefit) || 0;
        taxableIncome += accommodationAmount;
        
        // Log accommodation benefit addition
      }

      // Add commission if enabled
      if (payroll.commissionEnabled && payroll.commission) {
        taxableIncome += payroll.commission;
      }

      // Add bonus
      if (payroll.annualBonus?.amount) {
        taxableIncome += payroll.annualBonus.amount;
      }

      // Subtract tax deductible items
      const deductions = await this.calculateTaxDeductions(payroll);
      taxableIncome -= deductions;


      return Math.max(0, taxableIncome);
    } catch (error) {
      console.error("Error calculating taxable income:", error);
      throw error;
    }
  }

  // Add helper method for tax deductions
  static async calculateTaxDeductions(payroll) {
    let deductions = 0;

    // Pension fund contributions (up to 27.5% of remuneration)
    if (payroll.pensionFund?.fixedContributionEmployee) {
      const maxDeduction = payroll.basicSalary * 0.275;
      deductions += Math.min(
        payroll.pensionFund.fixedContributionEmployee,
        maxDeduction
      );
    }

    // Retirement annuity fund
    if (payroll.retirementAnnuityFund?.employeeContribution) {
      deductions += payroll.retirementAnnuityFund.employeeContribution;
    }

    // Income protection if tax deductible
    if (payroll.incomeProtection?.taxDeductible) {
      deductions += payroll.incomeProtection.amount || 0;
    }

    return deductions;
  }

  static async calculateMedicalAidTaxImpact(medical, frequency, taxableIncome) {
    try {
      const members = medical?.members || 0;
      const employerContribution = medical?.employerContribution || 0;
      const employeeHandlesPayment = medical?.employeeHandlesPayment || false;
      const medicalAid = medical?.medicalAid || 0;

      // Calculate tax credit based on number of members
      let taxCredit = 0;
      if (members > 0) {
        // Monthly tax credit amounts for 2024/2025
        const mainMemberCredit = 364; // R364 for main member
        const additionalMemberCredit = 246; // R246 for each additional member

        taxCredit = mainMemberCredit + (members - 1) * additionalMemberCredit;

        // Adjust for frequency if not monthly
        if (frequency !== "monthly") {
          // Add frequency adjustments here if needed
        }
      }

      // Calculate taxable benefit
      const taxableBenefit = !employeeHandlesPayment ? employerContribution : 0;

      // Calculate net deduction
      const netDeduction = medicalAid - taxCredit;

      return {
        taxCredit,
        taxableBenefit,
        netDeduction,
      };
    } catch (error) {
      console.error("Error calculating medical aid tax impact:", error);
      return {
        taxCredit: 0,
        taxableBenefit: 0,
        netDeduction: 0,
      };
    }
  }

  // Add this method to the PayrollService class
  static async getOrCreatePayroll(employeeId, relevantDate, session = null) {
    try {

      // Ensure we have a valid date object
      let date;
      if (relevantDate instanceof Date) {
        date = moment(relevantDate).toDate();
      } else if (typeof relevantDate === "string") {
        date = moment(relevantDate).toDate();
      } else {
        throw new Error("Invalid date format provided");
      }

      // Find employee to get company ID
      const employee = await Employee.findById(employeeId)
        .populate("company")
        .session(session);

      if (!employee) {
        throw new Error("Employee not found");
      }

      // Find or create payroll record
      let payroll = await Payroll.findOne({
        employee: employeeId,
        company: new mongoose.Types.ObjectId(employee.company),
        month: moment(relevantDate).endOf("month").toDate(),
      }).session(session);


      // If no payroll exists, create one
      if (!payroll) {
        const newPayroll = {
          employee: employeeId,
          company: new mongoose.Types.ObjectId(employee.company),
          month: moment(relevantDate).endOf("month").toDate(),
          status: "draft",
          incomeProtection: {
            amountPaidByEmployee: 0,
            amountDeductedFromEmployee: 0,
            amountPaidByEmployer: 0,
            employerOwnsPolicy: false,
          },
        };


        const created = await Payroll.create([newPayroll], { session });
        payroll = created[0];
      }

      return payroll;
    } catch (error) {
      console.error("Error in getOrCreatePayroll:", error);
      throw error;
    }
  }

  static async updateRetirementFunds(
    employeeId,
    componentType,
    data,
    relevantDate
  ) {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Validate employeeId
      if (!employeeId || typeof employeeId !== "string") {
        await session.abortTransaction();
        throw new Error("Invalid employee ID");
      }

      // Validate form data
      if (!data || typeof data !== "object") {
        await session.abortTransaction();
        throw new Error("Invalid form data");
      }

      const employee = await Employee.findById(employeeId)
        .populate("payFrequency")
        .session(session);

      if (!employee) {
        await session.abortTransaction();
        throw new Error("Employee not found");
      }

      // Get or create payroll record
      const payroll = await this.getOrCreatePayroll(
        employeeId,
        relevantDate,
        session
      );
      if (!payroll) {
        await session.abortTransaction();
        throw new Error("Failed to get or create payroll record");
      }

      // For pension fund updates
      if (componentType === "pension-fund") {
        // Validate contribution calculation method
        if (
          !data.contributionCalculation ||
          !["fixedAmount", "percentageRFI"].includes(
            data.contributionCalculation
          )
        ) {
          await session.abortTransaction();
          throw new Error(
            `Invalid contribution method. Received: ${data.contributionCalculation}`
          );
        }

        const pensionFundData = {
          contributionCalculation: data.contributionCalculation,
          categoryFactor: parseFloat(data.categoryFactor) || 1,
          beneficiary: data.beneficiary || "",
          fixedContributionEmployee: 0,
          fixedContributionEmployer: 0,
          rfiEmployee: 0,
          rfiEmployer: 0,
        };

        if (data.contributionCalculation === "fixedAmount") {
          pensionFundData.fixedContributionEmployee =
            parseFloat(data.fixedContributionEmployee) || 0;
          pensionFundData.fixedContributionEmployer =
            parseFloat(data.fixedContributionEmployer) || 0;
        } else {
          // percentageRFI
          pensionFundData.rfiEmployee = parseFloat(data.rfiEmployee) || 0;
          pensionFundData.rfiEmployer = parseFloat(data.rfiEmployer) || 0;

          // Calculate contributions based on RFI
          const rfiAmount = await calculateRFI(employeeId, relevantDate);
          pensionFundData.employeeContribution =
            (rfiAmount * pensionFundData.rfiEmployee) / 100;
          pensionFundData.employerContribution =
            (rfiAmount * pensionFundData.rfiEmployer) / 100;
        }

        const updatedPayroll = await Payroll.findByIdAndUpdate(
          payroll._id,
          { $set: { pensionFund: pensionFundData } },
          {
            session,
            new: true,
            runValidators: true,
          }
        );

        if (!updatedPayroll) {
          await session.abortTransaction();
          throw new Error("Failed to update pension fund");
        }

        await session.commitTransaction();
        return {
          success: true,
          message: "Pension fund updated successfully",
          data: updatedPayroll.pensionFund,
        };
      }

      // ... handle other retirement fund types ...
    } catch (error) {
      console.error("Error updating retirement funds:", error);
      if (session.inTransaction()) {
        await session.abortTransaction();
      }
      throw error;
    } finally {
      session.endSession();
    }
  }

  static async updateCourtOrder(employeeId, orderType, amount, relevantDate) {
    try {
      // Get or create payroll for the period
      const payroll = await this.getOrCreatePayroll(employeeId, relevantDate);

      // Parse amount to float
      const parsedAmount = parseFloat(amount);
      if (isNaN(parsedAmount) || parsedAmount < 0) {
        throw new Error("Invalid amount. Must be a non-negative number.");
      }

      // Update the specific court order type
      switch (orderType) {
        case "garnishee":
          payroll.garnishee = parsedAmount;
          break;
        case "maintenance-order":
          payroll.maintenanceOrder = parsedAmount;
          break;
        default:
          throw new Error(`Unsupported court order type: ${orderType}`);
      }

      // Save the payroll
      await payroll.save();
      return payroll;
    } catch (error) {
      console.error("Error updating court order:", error);
      throw error;
    }
  }

  static async updateIncomeProtection(employeeId, data, relevantDate) {
    try {
      // Parse and validate the date
      const parsedDate = moment(relevantDate).endOf("month").toDate();
      if (!parsedDate || isNaN(parsedDate.getTime())) {
        return {
          success: false,
          error: "Invalid date format for payroll",
        };
      }

      // Get or create payroll for the period
      const payroll = await this.getOrCreatePayroll(employeeId, parsedDate);

      payroll.incomeProtection = {
        amountPaidByEmployee: parseFloat(data.amountPaidByEmployee) || 0,
        amountDeductedFromEmployee:
          parseFloat(data.amountDeductedFromEmployee) || 0,
        amountPaidByEmployer: parseFloat(data.amountPaidByEmployer) || 0,
        employerOwnsPolicy: false, // Default value as per model
      };

      // Save the updated payroll
      await payroll.save();

      return {
        success: true,
        message: "Income protection updated successfully",
        data: payroll,
      };
    } catch (error) {
      console.error("Error updating income protection:", error);
      return {
        success: false,
        error: error.message || "Error updating income protection",
      };
    }
  }

  static async updateEmployerLoan(employeeId, employerLoanData, relevantDate) {
    try {
      const employee = await Employee.findById(employeeId);
      if (!employee) {
        throw new Error("Employee not found");
      }

      // Find or create payroll record for the relevant date
      let payroll = await Payroll.findOne({
        employee: employeeId,
        month: {
          $lte: relevantDate,
        },
      }).sort({ month: -1 });

      if (!payroll) {
        payroll = new Payroll({
          employee: employeeId,
          month: relevantDate,
          company: employee.currentCompany, // Get company from employee record
        });
      }

      // Update employer loan data
      payroll.employerLoan = {
        ...(payroll.employerLoan || {}),
        interestRate: employerLoanData.interestRate,
        regularRepayment: employerLoanData.regularRepayment,
        calculateInterestBenefit: employerLoanData.calculateInterestBenefit,
        lastUpdated: new Date(),
      };

      await payroll.save();
      return payroll;
    } catch (error) {
      throw error;
    }
  }

  static async updateForeignServiceIncome(
    employeeId,
    foreignServiceData,
    relevantDate
  ) {
    try {
      const employee = await Employee.findById(employeeId);
      if (!employee) {
        throw new Error("Employee not found");
      }

      // Find or create payroll record for the relevant date
      let payroll = await Payroll.findOne({
        employee: employeeId,
        month: {
          $lte: relevantDate,
        },
      }).sort({ month: -1 });

      if (!payroll) {
        payroll = new Payroll({
          employee: employeeId,
          month: relevantDate,
          company: employee.currentCompany, // Get company from employee record
        });
      }

      // Update foreign service income data
      payroll.foreignServiceIncome = {
        ...(payroll.foreignServiceIncome || {}),
        foreignServiceTaxExemption:
          foreignServiceData.foreignServiceTaxExemption === "on" || false,
        lastUpdated: new Date(),
      };

      await payroll.save();
      return payroll;
    } catch (error) {
      throw error;
    }
  }

  static async processTravelAllowance(employee, payroll) {
    try {
      // Ensure travel allowance exists
      if (!payroll.travelAllowance) {
        return {
          travelAllowanceProcessed: false,
          message: "No travel allowance details found",
        };
      }

      // Calculate travel allowance
      const travelAllowanceCalculation = calculateTravelAllowance(
        payroll.travelAllowance,
        employee.basicSalary,
        employee
      );

      // Update payroll with calculation results
      payroll.travelAllowance.calculationDetails = {
        totalAllowance: travelAllowanceCalculation.totalAllowance,
        taxableAmount: travelAllowanceCalculation.taxableAmount,
        nonTaxableAmount: travelAllowanceCalculation.nonTaxableAmount,
        sarsPrescribedRate: travelAllowanceCalculation.sarsPrescribedRate,
        irpCodes: travelAllowanceCalculation.irpCodes,
      };

      // Optional: Log unusual travel allowance scenarios
      if (
        travelAllowanceCalculation.totalAllowance >
        employee.basicSalary * 0.5
      ) {
      }

      return {
        travelAllowanceProcessed: true,
        calculationDetails: travelAllowanceCalculation,
      };
    } catch (error) {
      console.error("Error processing travel allowance:", error);
      return {
        travelAllowanceProcessed: false,
        error: error.message,
      };
    }
  }

  static async calculatePayrollDetails(employee, payroll, currentPeriod) {
    try {

      // Default values if no period or payroll exists
      const defaultResponse = {
        basicSalary: 0,
        totalIncome: 0,
        totalPAYE: 0,
        totalUIF: 0,
        totalDeductions: 0,
        nettPay: 0,
        travelAllowance: { total: 0 },
      };

      if (!currentPeriod || !payroll) {
        return defaultResponse;
      }

      const calculations = currentPeriod.calculations || {};
      const {
        basicSalary = payroll.basicSalary || 0,
        totalIncome = 0,
        totalPAYE = 0,
        totalUIF = 0,
        totalDeductions = 0,
        nettPay = 0,
        travelAllowance = { total: 0 },
      } = calculations;

      return {
        basicSalary,
        totalIncome,
        totalPAYE,
        totalUIF,
        totalDeductions,
        nettPay,
        travelAllowance,
      };
    } catch (error) {
      console.error("Error calculating payroll details:", error);
      throw error;
    }
  }

  static async updateLossOfIncome(payrollId, lossOfIncomeData) {
    try {
      const payroll = await Payroll.findById(payrollId);
      if (!payroll) {
        throw new Error("Payroll not found");
      }

      // Calculate loss of income amounts
      const { amount, taxableAmount, nonTaxableAmount } = calculateLossOfIncome(
        lossOfIncomeData.amount,
        payroll.frequency
      );

      // Update payroll with loss of income data
      payroll.lossOfIncome = amount;
      payroll.lossOfIncomeEnabled = true;

      // Store calculation details in the data Map if needed
      if (!payroll.data) {
        payroll.data = new Map();
      }

      payroll.data.set("lossOfIncomeDetails", {
        amount,
        taxableAmount,
        nonTaxableAmount,
        lastUpdated: new Date(),
      });

      await payroll.save();
      return payroll;
    } catch (error) {
      console.error("Error updating loss of income:", error);
      throw error;
    }
  }

  static async updateArbitrationAward(employeeId, data, relevantDate) {
    // 1. Pre-process data
    const parsedData = {
      amount: parseFloat(data.amount) || 0,
      directiveIncomeAmount: parseFloat(data.directiveIncomeAmount) || 0,
      taxDeductAmount: parseFloat(data.taxDeductAmount) || 0,
      directiveNumber: data.directiveNumber || "",
      directiveIssueDate: data.directiveIssueDate
        ? new Date(data.directiveIssueDate)
        : new Date(),
      description: data.description || "",
      date: data.date ? new Date(data.date) : new Date(),
    };

    const monthEndDate = moment(relevantDate).endOf("month").toDate();

    // 2. Create update operation with status tracking
    const updateOperation = {
      $set: {
        arbitrationAward: {
          amount: parsedData.amount,
          directiveNumber: parsedData.directiveNumber,
          directiveIssueDate: parsedData.directiveIssueDate,
          directiveIncomeAmount: parsedData.directiveIncomeAmount,
          taxDeductAmount: parsedData.taxDeductAmount,
          description: parsedData.description,
          date: parsedData.date,
          enabled: true,
          status: "pending",
          lastAttempt: new Date(),
        },
        arbitrationAwardEnabled: true,
      },
      $inc: {
        oidEarnings: parsedData.amount,
      },
    };

    try {
      // Get employee and company information
      const employee = await Employee.findById(employeeId).populate("company");
      if (!employee) {
        throw new Error("Employee not found");
      }

      // 3. First phase: Update main document
      const payroll = await Payroll.findOneAndUpdate(
        {
          employee: employeeId,
          month: monthEndDate,
        },
        {
          ...updateOperation,
          $set: {
            ...updateOperation.$set,
            company: employee.company._id,
          },
        },
        {
          new: true,
          upsert: true,
          writeConcern: { w: 1 },
        }
      );

      if (!payroll) {
        throw new Error("Failed to update payroll record");
      }

      // 4. Calculate totals separately
      const totals = await this.calculatePayrollTotals(
        employeeId,
        monthEndDate
      );

      // 5. Second phase: Update calculations
      const calculationsUpdate = await Payroll.findOneAndUpdate(
        {
          _id: payroll._id,
          "arbitrationAward.status": "pending",
        },
        {
          $set: {
            calculations: totals.calculations,
            "arbitrationAward.status": "completed",
            lastUpdated: new Date(),
          },
        },
        { new: true }
      );

      if (!calculationsUpdate) {
        // If second phase fails, attempt cleanup
        await this.handleFailedArbitrationAward(payroll._id);
        throw new Error("Failed to update calculations");
      }

      return {
        success: true,
        payroll: calculationsUpdate,
        calculations: totals.calculations,
      };
    } catch (error) {
      // 6. Error handling and cleanup
      if (error.code === 112) {
        // Handle write conflict by retrying the operation
        await Payroll.updateOne(
          { employee: employeeId, month: monthEndDate },
          {
            $set: {
              "arbitrationAward.status": "failed",
              "arbitrationAward.error": error.message,
              "arbitrationAward.lastError": new Date(),
            },
          }
        ).catch(console.error); // Don't throw if cleanup fails
      }
      throw error;
    }
  }

  // Add new helper method for cleanup
  static async cleanupPendingArbitrationAwards() {
    const threshold = new Date();
    threshold.setMinutes(threshold.getMinutes() - 5); // 5 minutes threshold

    const pendingPayrolls = await Payroll.find({
      "arbitrationAward.status": "pending",
      "arbitrationAward.lastAttempt": { $lt: threshold },
    });

    for (const payroll of pendingPayrolls) {
      try {
        await this.retryArbitrationAward(payroll);
      } catch (error) {
        console.error(`Failed to cleanup payroll ${payroll._id}:`, error);
        // Update status to failed
        await this.handleFailedArbitrationAward(payroll._id);
      }
    }
  }

  // Add helper method for retrying failed arbitration awards
  static async retryArbitrationAward(payroll) {
    if (!payroll.arbitrationAward) {
      return;
    }

    try {
      // Recalculate totals
      const totals = await this.calculatePayrollTotals(
        payroll.employee,
        payroll.month
      );

      // Update with new calculations
      const updated = await Payroll.findOneAndUpdate(
        {
          _id: payroll._id,
          "arbitrationAward.status": "pending",
        },
        {
          $set: {
            calculations: totals.calculations,
            "arbitrationAward.status": "completed",
            lastUpdated: new Date(),
          },
        },
        { new: true }
      );

      if (!updated) {
        throw new Error("Failed to update calculations during retry");
      }

      return updated;
    } catch (error) {
      console.error(`Retry failed for payroll ${payroll._id}:`, error);
      await this.handleFailedArbitrationAward(payroll._id);
      throw error;
    }
  }

  // Add helper method for handling failed arbitration awards
  static async handleFailedArbitrationAward(payrollId) {
    try {
      await Payroll.findByIdAndUpdate(payrollId, {
        $set: {
          "arbitrationAward.status": "failed",
          "arbitrationAward.lastError": new Date(),
        },
      });
    } catch (error) {
      console.error(`Failed to update status for payroll ${payrollId}:`, error);
    }
  }
  /**
   * Validate unfinalization constraints (NEW METHOD - does not modify existing logic)
   * @param {string} employeeId - Employee ID
   * @param {string} company - Company ID
   * @param {Date|string} periodEndDate - Period end date
   * @returns {Object} Validation result
   */
  static async validateUnfinalization(employeeId, company, periodEndDate) {
    const UnfinalizationService = require("./UnfinalizationService");
    return await UnfinalizationService.validateUnfinalization(employeeId, company, periodEndDate);
  }

  /**
   * Check if period can be unfinalized (NEW METHOD - does not modify existing logic)
   * @param {string} periodId - PayrollPeriod ID
   * @returns {Object} Result with canUnfinalize boolean
   */
  static async canUnfinalizePeriod(periodId) {
    const UnfinalizationService = require("./UnfinalizationService");
    return await UnfinalizationService.canUnfinalize(periodId);
  }

  /**
   * Unfinalize a payroll period (NEW METHOD - does not modify existing logic)
   * @param {string} employeeId - Employee ID
   * @param {string} company - Company ID
   * @param {Date|string} periodEndDate - Period end date
   * @param {string} userId - User performing the action
   * @returns {Object} Result with success boolean
   */
  static async unfinalizePeriod(employeeId, company, periodEndDate, userId) {
    const UnfinalizationService = require("./UnfinalizationService");
    return await UnfinalizationService.unfinalizePeriod(employeeId, company, periodEndDate, userId);
  }

  /**
   * Get periods affected by unfinalization (NEW METHOD - does not modify existing logic)
   * @param {string} employeeId - Employee ID
   * @param {string} company - Company ID
   * @param {Date|string} periodEndDate - Period end date
   * @returns {Array} Array of affected periods
   */
  static async getAffectedPeriods(employeeId, company, periodEndDate) {
    const UnfinalizationService = require("./UnfinalizationService");
    return await UnfinalizationService.getAffectedPeriods(employeeId, company, periodEndDate);
  }
}

module.exports = PayrollService;

// Show me the content of this file and I'll help fix the export/import issue
