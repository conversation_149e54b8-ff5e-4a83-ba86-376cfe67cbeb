# Bank File Error Handling and Validation

## Overview

This document outlines the comprehensive error handling and validation strategies implemented for the South African bank file generators.

## Error Categories

### 1. Input Validation Errors

#### Missing Required Data
- **Error Code**: `MISSING_REQUIRED_DATA`
- **Description**: Essential data fields are missing or null
- **Examples**:
  - Missing employee data
  - Missing EFT settings
  - Missing action date

```javascript
// Example error response
{
  code: 'MISSING_REQUIRED_DATA',
  message: 'Employee data is required',
  field: 'employee',
  value: null
}
```

#### Invalid Data Format
- **Error Code**: `INVALID_DATA_FORMAT`
- **Description**: Data is present but in incorrect format
- **Examples**:
  - Invalid date format
  - Non-numeric amounts
  - Invalid account numbers

### 2. Bank-Specific Validation Errors

#### Account Number Validation
- **FNB**: 8-11 digits
- **ABSA**: 8-11 digits
- **Standard Bank**: 8-11 digits
- **Nedbank**: 8-11 digits

```javascript
// Validation function
function validateAccountNumber(accountNumber, bank) {
  const rules = {
    'fnb': { min: 8, max: 11, pattern: /^\d+$/ },
    'absa': { min: 8, max: 11, pattern: /^\d+$/ },
    'standard': { min: 8, max: 11, pattern: /^\d+$/ },
    'nedbank': { min: 8, max: 11, pattern: /^\d+$/ }
  };
  
  const rule = rules[bank.toLowerCase()];
  if (!rule.pattern.test(accountNumber) || 
      accountNumber.length < rule.min || 
      accountNumber.length > rule.max) {
    throw new ValidationError('Invalid account number format');
  }
}
```

#### Branch Code Validation
- **All Banks**: Exactly 6 digits

#### Amount Validation
- **Minimum**: R0.01
- **Maximum**: R999,999.99 (varies by bank)
- **Format**: Must be positive numeric value

#### Reference Validation
- **FNB**: Max 20 characters, alphanumeric + spaces, hyphens, underscores
- **ABSA**: Max 12 characters, alphanumeric + spaces, hyphens, underscores
- **Standard Bank**: Max 12 characters, alphanumeric + spaces, hyphens, underscores
- **Nedbank**: Max 20 characters, alphanumeric + spaces, hyphens, underscores

### 3. Format-Specific Errors

#### FNB ACB Format
- **Record Length**: Must be exactly 198 characters
- **Record Types**: Must use correct type codes (02, 04, 10, 92, 94)
- **Field Padding**: All fields must be properly padded

#### CSV Formats (FNB Enterprise, ABSA Business Integrator)
- **Field Escaping**: Proper CSV escaping for special characters
- **Header Validation**: Required headers must be present
- **Field Count**: Each row must have correct number of fields

#### Fixed-Width Formats (Standard Bank EFTS)
- **Record Length**: Must be exactly 80 characters
- **Field Alignment**: Numeric fields left-padded, text fields right-padded
- **Record Sequence**: Header, details, trailer in correct order

#### Nedbank NedInform Format
- **Format Option**: Must specify PRE-FORMAT or FREE-FORMAT
- **Transaction Type**: Must specify SALARIES or SUPPLIERS
- **Beneficiary Reference**: Required for PRE-FORMAT option

## Error Handling Strategies

### 1. Graceful Degradation

When non-critical errors occur, the system attempts to continue processing:

```javascript
// Example: Handle missing optional fields
function formatEmployeeName(employee) {
  try {
    const firstName = employee.firstName || '';
    const lastName = employee.lastName || '';
    return `${firstName} ${lastName}`.trim() || 'UNKNOWN';
  } catch (error) {
    console.warn('Error formatting employee name:', error);
    return 'UNKNOWN';
  }
}
```

### 2. Early Validation

Validate all inputs before beginning file generation:

```javascript
function validateGeneratorRequirements(payRun, eftSettings, actionDate) {
  const errors = [];
  
  if (!payRun || !payRun.payrollPeriods) {
    errors.push('Pay run data is required');
  }
  
  if (!eftSettings || !eftSettings.bankName) {
    errors.push('EFT settings are required');
  }
  
  if (!actionDate || !moment(actionDate).isValid()) {
    errors.push('Valid action date is required');
  }
  
  return { valid: errors.length === 0, errors };
}
```

### 3. Detailed Error Messages

Provide specific, actionable error messages:

```javascript
// Good error message
throw new Error(`Account number must be between 8 and 11 digits. Received: ${accountNumber} (${accountNumber.length} digits)`);

// Poor error message
throw new Error('Invalid account number');
```

## Bank-Specific Error Handling

### FNB Formats

#### Common Issues
1. **Account Number Format**: Must be numeric, 8-11 digits
2. **Employee Name Length**: Max 30 characters for ACB format
3. **Reference Length**: Max 20 characters for Enterprise CSV

#### Error Recovery
- Truncate long names/references
- Pad short account numbers with leading zeros
- Default to 'UNKNOWN' for missing employee names

### ABSA Business Integrator

#### Common Issues
1. **Date Format**: Must be DD/MM/YYYY
2. **Beneficiary Name**: Max 32 characters, letters/spaces/hyphens/apostrophes only
3. **Reference Length**: Max 12 characters

#### Error Recovery
- Format dates consistently
- Clean beneficiary names of invalid characters
- Truncate long references

### Standard Bank EFTS

#### Common Issues
1. **Fixed Record Length**: Must be exactly 80 characters
2. **Employee Name**: Max 9 characters
3. **Amount Format**: Must be in cents, padded to 12 digits

#### Error Recovery
- Pad records to exact length
- Truncate employee names intelligently (First name + last initial)
- Format amounts consistently

### Nedbank NedInform

#### Common Issues
1. **Format Option**: Must specify PRE-FORMAT or FREE-FORMAT
2. **Beneficiary Reference**: Required for PRE-FORMAT
3. **Transaction Type**: Must be SALARIES or SUPPLIERS

#### Error Recovery
- Default to FREE-FORMAT if not specified
- Generate beneficiary references for PRE-FORMAT
- Default to SALARIES transaction type

## Error Logging and Monitoring

### Log Levels

1. **ERROR**: Critical failures that prevent file generation
2. **WARN**: Issues that were handled but may need attention
3. **INFO**: Normal processing information
4. **DEBUG**: Detailed processing information

### Example Logging

```javascript
console.error('Bank file generation failed:', {
  error: error.message,
  payRunId: payRun.id,
  format: eftFormat,
  timestamp: new Date().toISOString()
});

console.warn('Employee name truncated:', {
  originalName: fullName,
  truncatedName: truncatedName,
  employeeId: employee.id
});
```

## Testing Error Scenarios

### Unit Tests

Test each validation function with invalid inputs:

```javascript
describe('Account Number Validation', () => {
  it('should reject account numbers that are too short', () => {
    expect(() => validateAccountNumber('123')).to.throw('Account number must be between');
  });
  
  it('should reject account numbers that are too long', () => {
    expect(() => validateAccountNumber('************')).to.throw('Account number must be between');
  });
  
  it('should reject non-numeric account numbers', () => {
    expect(() => validateAccountNumber('ABC123')).to.throw('Account number contains invalid characters');
  });
});
```

### Integration Tests

Test complete file generation with invalid data:

```javascript
describe('Error Handling Integration', () => {
  it('should handle missing employee data gracefully', async () => {
    const invalidPayRun = { payrollPeriods: [{ employee: null }] };
    
    try {
      await generator.generate(invalidPayRun, eftSettings, actionDate);
      expect.fail('Should have thrown an error');
    } catch (error) {
      expect(error.message).to.include('No valid EFT transactions found');
    }
  });
});
```

## Best Practices

1. **Validate Early**: Check all inputs before processing
2. **Fail Fast**: Stop processing on critical errors
3. **Log Everything**: Comprehensive logging for debugging
4. **Provide Context**: Include relevant data in error messages
5. **Test Edge Cases**: Test with invalid, missing, and boundary data
6. **Document Errors**: Clear documentation of all error conditions
7. **Monitor Production**: Track error rates and patterns in production

## Error Response Format

Standardized error response structure:

```javascript
{
  success: false,
  error: {
    code: 'VALIDATION_ERROR',
    message: 'Bank file validation failed',
    details: {
      format: 'FNB Enterprise CSV',
      field: 'accountNumber',
      value: 'invalid_value',
      rule: 'Must be 8-11 digits',
      suggestion: 'Please check the employee bank details'
    }
  },
  timestamp: '2024-01-31T10:30:00Z',
  requestId: 'req_123456'
}
```

This comprehensive error handling ensures robust, reliable bank file generation with clear feedback when issues occur.
