const mongoose = require("mongoose");
const moment = require("moment-timezone");
const BusinessDate = require("../utils/BusinessDate");

const payrollPeriodSchema = new mongoose.Schema({
  // Legacy Date fields - maintained for backward compatibility
  startDate: {
    type: Date,
    required: true,
    set: function (val) {
      // SIMPLE FIX: Store dates as-is without timezone conversion to prevent +1 day issues
      if (typeof val === 'string') {
        // For YYYY-MM-DD strings, create date at midnight UTC
        return new Date(val + 'T00:00:00.000Z');
      }
      return new Date(val);
    },
    validate: {
      validator: function (v) {
        return moment(v).isValid();
      },
      message: "Invalid start date",
    },
  },
  endDate: {
    type: Date,
    required: true,
    set: function (val) {
      // SIMPLE FIX: Store dates as-is without timezone conversion to prevent +1 day issues
      if (typeof val === 'string') {
        // For YYYY-MM-DD strings, create date at end of day UTC
        return new Date(val + 'T23:59:59.999Z');
      }
      return new Date(val);
    },
    validate: {
      validator: function (v) {
        return moment(v).isValid() && moment(v).isSameOrAfter(this.startDate);
      },
      message: "End date must be valid and after start date",
    },
  },

  // New Business Date fields - timezone-independent YYYY-MM-DD strings
  startDateBusiness: {
    type: String,
    validate: {
      validator: function (v) {
        return !v || BusinessDate.isValid(v);
      },
      message: "Invalid business start date format. Expected YYYY-MM-DD",
    },
  },
  endDateBusiness: {
    type: String,
    validate: {
      validator: function (v) {
        if (!v) return true;
        if (!BusinessDate.isValid(v)) return false;
        if (this.startDateBusiness) {
          return BusinessDate.isSameOrAfter(v, this.startDateBusiness);
        }
        return true;
      },
      message: "Invalid business end date format or end date before start date",
    },
  },
  frequency: {
    type: String,
    required: true,
    enum: ["weekly", "biweekly", "monthly"],
    default: "monthly",
    set: function (val) {
      // Normalize bi-weekly to biweekly
      return val === "bi-weekly" ? "biweekly" : val;
    },
  },
  employee: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Employee",
    required: true,
  },
  company: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Company",
    required: true,
  },
  payslip: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Payslip"
  },
  status: {
    type: String,
    enum: ["open", "processing", "finalized", "locked"],
    default: "open",
  },
  isFinalized: {
    type: Boolean,
    default: false,
  },
  basicSalary: {
    type: Number,
    default: 0,
  },
  grossPay: {
    type: Number,
    default: 0,
  },
  totalDeductions: {
    type: Number,
    default: 0,
  },
  netPay: {
    type: Number,
    default: 0,
  },
  PAYE: {
    type: Number,
    default: 0,
  },
  UIF: {
    type: Number,
    default: 0,
  },
  SDL: {
    type: Number,
    default: 0,
  },

  // Pro-rata calculation fields for consistency with employeeProfile
  proratedPercentage: {
    type: Number,
    default: 100,
    min: 0,
    max: 100,
  },
  workedDays: {
    type: Number,
    default: 0,
    min: 0,
  },
  totalDaysInPeriod: {
    type: Number,
    default: 0,
    min: 0,
  },
  isFirstPeriodWithDOA: {
    type: Boolean,
    default: false,
  },
  fullPeriodSalary: {
    type: Number,
    default: 0,
    min: 0,
  },

  // Finalization tracking fields
  finalizedAt: {
    type: Date,
  },
  finalizedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
  },
  // Unfinalization tracking fields
  unfinalizedAt: {
    type: Date,
  },
  unfinalizedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
  },
  // Pay run relationship
  payRun: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "PayRun",
  },
});

// Middleware to sync between Date and BusinessDate fields
payrollPeriodSchema.pre('save', function(next) {
  // Sync Date fields to BusinessDate fields
  if (this.startDate && !this.startDateBusiness) {
    this.startDateBusiness = BusinessDate.fromDate(this.startDate);
  }
  if (this.endDate && !this.endDateBusiness) {
    this.endDateBusiness = BusinessDate.fromDate(this.endDate);
  }

  // Sync BusinessDate fields to Date fields (for backward compatibility)
  if (this.startDateBusiness && !this.startDate) {
    this.startDate = BusinessDate.toDate(this.startDateBusiness);
  }
  if (this.endDateBusiness && !this.endDate) {
    this.endDate = BusinessDate.toDate(this.endDateBusiness, true);
  }

  next();
});

// Virtual getters for business dates (preferred interface)
payrollPeriodSchema.virtual('businessStartDate').get(function() {
  return this.startDateBusiness || BusinessDate.fromDate(this.startDate);
});

payrollPeriodSchema.virtual('businessEndDate').get(function() {
  return this.endDateBusiness || BusinessDate.fromDate(this.endDate);
});

payrollPeriodSchema.index(
  {
    employee: 1,
    startDate: 1,
    endDate: 1,
    frequency: 1,
  },
  {
    unique: true,
  }
);

// Add index for business date fields
payrollPeriodSchema.index(
  {
    employee: 1,
    startDateBusiness: 1,
    endDateBusiness: 1,
    frequency: 1,
  }
);

payrollPeriodSchema.statics.createInitialPeriod = async function (employee) {

  // Validate required data
  if (!employee.doa) {
    throw new Error("Employee must have date of appointment (doa) set");
  }
  if (!employee.payFrequency) {
    throw new Error("Employee must have pay frequency set");
  }
  if (!employee.payFrequency.frequency) {
    throw new Error("Pay frequency must have frequency set");
  }
  if (!employee.payFrequency.lastDayOfPeriod) {
    console.error("Pay Frequency Validation Error:", {
      payFrequencyId: employee.payFrequency._id,
      frequency: employee.payFrequency.frequency,
      lastDayOfPeriod: employee.payFrequency.lastDayOfPeriod,
    });
    throw new Error("Pay frequency must have lastDayOfPeriod set");
  }

  // 🔧 FIX: Use proper period dates for pro-rata calculation
  // For monthly periods, use first day of month as start, not employee DOA
  const doaBusinessDate = BusinessDate.normalize(employee.doa);
  let startDateBusiness;
  let endDateBusiness;

  if (employee.payFrequency.frequency === "monthly") {
    // For monthly periods, always use first day of month as period start
    const doaYear = doaBusinessDate.split('-')[0];
    const doaMonth = doaBusinessDate.split('-')[1];
    startDateBusiness = `${doaYear}-${doaMonth}-01`; // First day of month

    // Calculate last day of month for end date
    const lastDayOfMonth = new Date(parseInt(doaYear), parseInt(doaMonth), 0).getDate();
    endDateBusiness = `${doaYear}-${doaMonth}-${lastDayOfMonth.toString().padStart(2, '0')}`;

    console.log('  - Monthly period dates corrected:', {
      employeeDOA: doaBusinessDate,
      periodStart: startDateBusiness,
      periodEnd: endDateBusiness,
      daysInMonth: lastDayOfMonth,
      explanation: 'Period spans full month (1st to last day) for proper pro-rata calculation'
    });
  } else if (employee.payFrequency.frequency === "weekly") {
    // SURGICAL FIX: For weekly periods, calculate proper 7-day period
    // First calculate the period end date based on DOA
    endDateBusiness = BusinessDate.calculatePeriodEndDate(doaBusinessDate, employee.payFrequency);
    // Then calculate start date as exactly 7 days before end date (6 days back + end day = 7 total days)
    startDateBusiness = BusinessDate.addDays(endDateBusiness, -6);

    console.log('  - Weekly period dates corrected:', {
      employeeDOA: doaBusinessDate,
      periodStart: startDateBusiness,
      periodEnd: endDateBusiness,
      totalDays: 7,
      explanation: 'Weekly periods are always 7 days long, start date = end date - 6 days'
    });
  } else {
    // biweekly - start from DOA
    startDateBusiness = doaBusinessDate;
    endDateBusiness = BusinessDate.addDays(startDateBusiness, 13);
  }


  // Calculate tax deductions for initial period
  // Get basic salary from the most recent Payroll record, not from employee object
  const Payroll = require('./Payroll');
  const latestPayroll = await Payroll.findOne({
    employee: employee._id,
    company: employee.company,
    basicSalary: { $exists: true, $gt: 0 }
  }).sort({ month: -1 });

  const basicSalary = latestPayroll?.basicSalary || employee.basicSalary || 0;

  // Initialize calculation results object to ensure proper scope
  const calculationResults = {
    paye: 0,
    uif: 0,
    sdl: 0,
    totalDeductions: 0,
    netPay: basicSalary,
    grossPay: basicSalary
  };

  if (latestPayroll) {
    // Add travel allowance if exists
    if (latestPayroll.travelAllowance?.fixedAllowanceAmount) {
      calculationResults.grossPay += latestPayroll.travelAllowance.fixedAllowanceAmount;
    }

    // Add commission if exists
    if (latestPayroll.commission) {
      calculationResults.grossPay += latestPayroll.commission;
    }

    // Add loss of income if exists
    if (latestPayroll.lossOfIncome) {
      calculationResults.grossPay += latestPayroll.lossOfIncome;
    }

    // Add accommodation benefit if exists
    if (latestPayroll.accommodationBenefit) {
      calculationResults.grossPay += parseFloat(latestPayroll.accommodationBenefit);
    }
  }


  console.log('🔍 INITIAL PERIOD CALCULATION DEBUG:');
  console.log('  - Employee ID:', employee._id);
  console.log('  - Basic Salary:', basicSalary);
  console.log('  - Gross Pay (before calculations):', calculationResults.grossPay);
  console.log('  - Period Start:', startDateBusiness);
  console.log('  - Period End:', endDateBusiness);
  console.log('  - Employee DOA:', employee.doa);

  if (basicSalary > 0) {
    try {
      console.log('  - Calculating pro-rata using EXACT employeeProfile method...');

      // Use the EXACT same method as employeeProfile.ejs Calculator card
      const BusinessDate = require('../utils/BusinessDate');

      // Use the EXACT same parameter names and values as employeeProfile
      const fullBasicSalary = basicSalary; // Direct from PayrollPeriod.basicSalary
      const correctedPeriodStart = startDateBusiness; // BusinessDate format
      const correctedPeriodEnd = endDateBusiness; // BusinessDate format

      console.log('  - Using EXACT employeeProfile parameters:', {
        doa: employee.doa,
        fullBasicSalary: fullBasicSalary,
        correctedPeriodStart: correctedPeriodStart,
        correctedPeriodEnd: correctedPeriodEnd
      });

      const proratedSalaryResult = BusinessDate.calculateProratedSalary(
        employee.doa,
        fullBasicSalary, // EXACT same parameter as employeeProfile
        employee,
        correctedPeriodStart, // EXACT same parameter as employeeProfile
        correctedPeriodEnd // EXACT same parameter as employeeProfile
      );

      console.log('  - Pro-rata calculation result:', proratedSalaryResult);

      // Apply pro-rata calculation EXACTLY like employeeProfile Calculator card
      if (proratedSalaryResult.isFirstPeriodWithDOA && Number(proratedSalaryResult.proratedPercentage) < 100) {
        // Use prorated salary as gross pay (same logic as employeeProfile)
        calculationResults.grossPay = Number(proratedSalaryResult.proratedSalary);
        console.log('  - Applied pro-rata (EXACT employeeProfile logic): ', {
          isFirstPeriodWithDOA: proratedSalaryResult.isFirstPeriodWithDOA,
          percentage: (proratedSalaryResult.proratedPercentageFormatted || proratedSalaryResult.proratedPercentage.toFixed(2)) + '%',
          workedDays: proratedSalaryResult.workedDays,
          totalDays: proratedSalaryResult.totalDaysInPeriod,
          fullPeriodSalary: proratedSalaryResult.fullPeriodSalaryFormatted || proratedSalaryResult.fullPeriodSalary.toFixed(2),
          proratedAmount: calculationResults.grossPay.toFixed(2)
        });
      } else {
        // If 100% or not first period, use full basic salary (same as employeeProfile)
        calculationResults.grossPay = Number(proratedSalaryResult.fullPeriodSalary || basicSalary);
        console.log('  - Using full salary (100% or not first period): ', {
          percentage: (proratedSalaryResult.proratedPercentageFormatted || proratedSalaryResult.proratedPercentage.toFixed(2)) + '%',
          fullAmount: calculationResults.grossPay.toFixed(2)
        });
      }

      console.log('  - Using EXACT PayrollService method (identical to employeeProfile)...');

      // Create the period first with basic data
      const period = await this.create({
        employee: employee._id,
        company: employee.company,
        startDate: BusinessDate.toDate(startDateBusiness),
        endDate: BusinessDate.toDate(endDateBusiness, true),
        startDateBusiness: startDateBusiness,
        endDateBusiness: endDateBusiness,
        frequency: employee.payFrequency.frequency,
        basicSalary: basicSalary,
        grossPay: calculationResults.grossPay,
        PAYE: 0, // Will be calculated below
        UIF: 0,  // Will be calculated below
        SDL: 0,  // Will be calculated below
        totalDeductions: 0, // Will be calculated below
        netPay: calculationResults.grossPay, // Will be calculated below
        status: "open",
        isFinalized: false,
        // Store pro-rata information for consistency with employeeProfile
        proratedPercentage: proratedSalaryResult.proratedPercentage,
        workedDays: proratedSalaryResult.workedDays,
        totalDaysInPeriod: proratedSalaryResult.totalDaysInPeriod,
        isFirstPeriodWithDOA: proratedSalaryResult.isFirstPeriodWithDOA,
        fullPeriodSalary: proratedSalaryResult.fullPeriodSalary
      });

      console.log('  - Period created, now ensuring Payroll record exists for PayrollService...');

      // Ensure Payroll record exists (required by PayrollService.calculatePayrollTotals)
      const Payroll = require('./Payroll');
      let payroll = await Payroll.findOne({
        employee: employee._id,
        month: BusinessDate.toDate(endDateBusiness, true)
      });

      if (!payroll) {
        console.log('  - Creating Payroll record with pro-rated amount for PayrollService...');

        // Check if there's a recent payroll record to copy benefits from
        const recentPayroll = await Payroll.findOne({
          employee: employee._id
        }).sort({ month: -1 });

        // Use benefits from recent payroll if available, otherwise default to zero
        const accommodationBenefit = recentPayroll?.accommodationBenefit || 0;
        const travelAllowance = recentPayroll?.travelAllowance || { fixedAllowanceAmount: 0 };
        const lossOfIncome = recentPayroll?.lossOfIncome || 0;
        const maintenanceOrder = recentPayroll?.maintenanceOrder || 0;

        payroll = await Payroll.create({
          employee: employee._id,
          company: employee.company,
          month: BusinessDate.toDate(endDateBusiness, true),
          basicSalary: calculationResults.grossPay, // Use pro-rated amount, not full basic salary
          // Copy benefits from recent payroll to ensure consistent PAYE/UIF calculation
          accommodationBenefit: accommodationBenefit,
          travelAllowance: travelAllowance,
          lossOfIncome: lossOfIncome,
          maintenanceOrder: maintenanceOrder
        });
        console.log('  - Payroll record created with pro-rated basicSalary:', calculationResults.grossPay);
        console.log('  - Copied benefits from recent payroll:', {
          accommodationBenefit,
          travelAllowance,
          lossOfIncome,
          maintenanceOrder
        });
      } else {
        // Update existing payroll with pro-rated amount
        payroll.basicSalary = calculationResults.grossPay;
        await payroll.save();
        console.log('  - Updated existing Payroll record with pro-rated amount:', calculationResults.grossPay);
      }

      console.log('  - Now using EXACT PayrollService.calculatePayrollTotals...');

      // Use the EXACT same calculation service that employeeProfile.ejs uses
      const PayrollService = require('../services/PayrollService');
      const calculations = await PayrollService.calculatePayrollTotals(
        employee._id,
        BusinessDate.toDate(endDateBusiness, true),
        employee.company
      );

      console.log('  - PayrollService calculations (identical to employeeProfile):', calculations);

      // Extract the calculated values using the EXACT same field names as employeeProfile
      const finalPAYE = calculations.paye || 0;
      const finalUIF = calculations.uif || 0;
      const finalSDL = calculations.deductions?.statutory?.sdl || 0;
      const finalTotalDeductions = calculations.totalDeductions || 0;
      const finalNetPay = calculations.netPay || calculationResults.grossPay;
      const finalTotalIncome = calculations.totalIncome || calculationResults.grossPay;

      console.log('  - Final PAYE (identical to employeeProfile):', finalPAYE);
      console.log('  - Final UIF (identical to employeeProfile):', finalUIF);
      console.log('  - Final SDL (identical to employeeProfile):', finalSDL);
      console.log('  - Final Total Deductions (identical to employeeProfile):', finalTotalDeductions);
      console.log('  - Final Net Pay (identical to employeeProfile):', finalNetPay);
      console.log('  - Final Total Income (identical to employeeProfile):', finalTotalIncome);

      // Calculate SDL for employer reporting (NOT included in employee deductions)
      const sdlForEmployer = finalTotalIncome * 0.01;

      // Ensure totalDeductions excludes SDL (SDL is employer contribution, not employee deduction)
      const correctedTotalDeductions = finalPAYE + finalUIF;

      // Update the period with the EXACT same calculations as employeeProfile
      await this.findByIdAndUpdate(period._id, {
        grossPay: finalTotalIncome,
        PAYE: finalPAYE,
        UIF: finalUIF,
        SDL: sdlForEmployer, // Store SDL for employer reporting only
        totalDeductions: correctedTotalDeductions, // Excludes SDL
        netPay: finalTotalIncome - correctedTotalDeductions, // Recalculate net pay
      });

      console.log('PayrollPeriod recalculation - SDL handling:', {
        finalTotalIncome,
        finalPAYE,
        finalUIF,
        sdlForEmployer: `${sdlForEmployer.toFixed(2)} (employer contribution only)`,
        correctedTotalDeductions: `${correctedTotalDeductions.toFixed(2)} (excludes SDL)`,
        netPay: (finalTotalIncome - correctedTotalDeductions).toFixed(2)
      });

      console.log('✅ Period updated with IDENTICAL PayrollService calculations');

      // Return the updated period
      return await this.findById(period._id);

    } catch (error) {
      console.error("❌ Error calculating initial period taxes:", error);
      console.error("❌ Error stack:", error.stack);
      // Reset to zero values if calculation fails
      calculationResults.paye = 0;
      calculationResults.uif = 0;
      calculationResults.sdl = 0;
      calculationResults.totalDeductions = 0;
      calculationResults.netPay = calculationResults.grossPay;
    }
  } else {
    console.log('  - Basic salary is 0, skipping calculations');
  }

  // If we reach here, it means PayrollService calculations failed, so create with basic values
  console.log('📊 FALLBACK: Creating period with basic values (PayrollService failed):');
  console.log('  - basicSalary:', basicSalary);
  console.log('  - grossPay:', calculationResults.grossPay);
  console.log('  - PAYE:', calculationResults.paye);
  console.log('  - UIF:', calculationResults.uif);
  console.log('  - SDL:', calculationResults.sdl);
  console.log('  - totalDeductions:', calculationResults.totalDeductions);
  console.log('  - netPay:', calculationResults.netPay);

  // Create and return the period with both Date and BusinessDate fields
  console.log('💾 CREATING INITIAL PERIOD WITH FALLBACK DATA:');

  // Use direct object creation like generateNextPeriod to avoid scope issues
  const period = await this.create({
    employee: employee._id,
    company: employee.company,
    // Legacy Date fields for backward compatibility
    startDate: BusinessDate.toDate(startDateBusiness),
    endDate: BusinessDate.toDate(endDateBusiness, true),
    // New BusinessDate fields (preferred)
    startDateBusiness: startDateBusiness,
    endDateBusiness: endDateBusiness,
    frequency: employee.payFrequency.frequency,
    basicSalary: basicSalary,
    grossPay: calculationResults.grossPay,
    PAYE: calculationResults.paye,
    UIF: calculationResults.uif,
    SDL: calculationResults.sdl,
    totalDeductions: calculationResults.totalDeductions,
    netPay: calculationResults.netPay,
    status: "open",
    isFinalized: false,
  });

  console.log('✅ FALLBACK PERIOD CREATED:');
  console.log('  - Saved period ID:', period._id);
  console.log('  - Saved basicSalary:', period.basicSalary);
  console.log('  - Saved grossPay:', period.grossPay);
  console.log('  - Saved PAYE:', period.PAYE);
  console.log('  - Saved UIF:', period.UIF);
  console.log('  - Saved SDL:', period.SDL);
  console.log('  - Saved totalDeductions:', period.totalDeductions);
  console.log('  - Saved netPay:', period.netPay);

  return period;
};

payrollPeriodSchema.statics.generateNextPeriod = async function (
  employee,
  lastPeriod
) {
  // Validate inputs
  if (!employee || !lastPeriod) {
    throw new Error("Employee and last period are required");
  }

  // Check if employee is terminating
  if (
    employee.terminationDate &&
    moment(employee.terminationDate).isSameOrBefore(lastPeriod.endDate)
  ) {
    return null;
  }

  // Use BusinessDate for timezone-independent calculations
  const lastPeriodEndBusiness = lastPeriod.businessEndDate;
  const startDateBusiness = BusinessDate.addDays(lastPeriodEndBusiness, 1);
  let endDateBusiness;

  // Ensure pay frequency is populated
  if (!employee.payFrequency || !employee.payFrequency.frequency) {
    throw new Error(
      `Pay frequency not set for employee ${employee.firstName} ${employee.lastName}`
    );
  }

  // Use BusinessDate for all frequency calculations
  endDateBusiness = BusinessDate.calculateNextPeriodEndDate(lastPeriodEndBusiness, employee.payFrequency);


  // Check for duplicate period using BusinessDate fields
  const existingPeriod = await this.findOne({
    employee: employee._id,
    $or: [
      // Check using BusinessDate fields (preferred)
      {
        startDateBusiness: startDateBusiness,
        endDateBusiness: endDateBusiness,
      },
      // Fallback to legacy Date fields for backward compatibility
      {
        startDate: BusinessDate.toDate(startDateBusiness),
        endDate: BusinessDate.toDate(endDateBusiness, true),
      }
    ]
  });

  if (existingPeriod) {
    return existingPeriod;
  }

  // Calculate tax deductions for next period
  // Get basic salary from the most recent Payroll record or last period
  const Payroll = require('./Payroll');
  const latestPayroll = await Payroll.findOne({
    employee: employee._id,
    company: employee.company,
    basicSalary: { $exists: true, $gt: 0 }
  }).sort({ month: -1 });

  const basicSalary = latestPayroll?.basicSalary || lastPeriod.basicSalary || employee.basicSalary || 0;
  let paye = 0;
  let uif = 0;
  let sdl = 0;
  let totalDeductions = 0;
  let netPay = basicSalary;

  // Calculate gross pay including all income components
  let grossPay = basicSalary;

  if (latestPayroll) {
    // Add travel allowance if exists
    if (latestPayroll.travelAllowance?.fixedAllowanceAmount) {
      grossPay += latestPayroll.travelAllowance.fixedAllowanceAmount;
    }

    // Add commission if exists
    if (latestPayroll.commission) {
      grossPay += latestPayroll.commission;
    }

    // Add loss of income if exists
    if (latestPayroll.lossOfIncome) {
      grossPay += latestPayroll.lossOfIncome;
    }

    // Add accommodation benefit if exists
    if (latestPayroll.accommodationBenefit) {
      grossPay += parseFloat(latestPayroll.accommodationBenefit);
    }
  } else if (lastPeriod.grossPay && lastPeriod.grossPay > lastPeriod.basicSalary) {
    // If no payroll record but last period had additional income components, use that gross pay
    grossPay = lastPeriod.grossPay;
  }


  if (basicSalary > 0) {
    try {
      // Import calculation functions
      const { calculateEnhancedPAYE, calculatePeriodUIF } = require('../utils/payrollCalculations');
      const payrollCalculations = require('../utils/payrollCalculations');

      // Calculate PAYE
      const annualSalary = basicSalary * 12;
      const payeResult = await calculateEnhancedPAYE({
        annualSalary,
        age: payrollCalculations.calculateAge(employee.dob),
        frequency: employee.payFrequency.frequency,
      });
      paye = Number(payeResult.monthlyPAYE || 0);

      // Calculate UIF
      uif = calculatePeriodUIF(basicSalary, employee.payFrequency.frequency);

      // Calculate SDL (Skills Development Levy) - 1% of basic salary for employer reporting
      sdl = basicSalary * 0.01;

      // Calculate totals - SDL is excluded from employee deductions (employer contribution only)
      totalDeductions = paye + uif; // Excludes SDL
      netPay = grossPay - totalDeductions;

    } catch (error) {
      console.error("Error calculating next period taxes:", error);
      // Continue with zero values if calculation fails
    }
  }

  // Create and return the new period with both Date and BusinessDate fields
  return this.create({
    employee: employee._id,
    company: employee.company,
    // Legacy Date fields for backward compatibility
    startDate: BusinessDate.toDate(startDateBusiness),
    endDate: BusinessDate.toDate(endDateBusiness, true),
    // New BusinessDate fields (preferred)
    startDateBusiness: startDateBusiness,
    endDateBusiness: endDateBusiness,
    frequency: employee.payFrequency.frequency,
    basicSalary: basicSalary,
    grossPay: grossPay,
    PAYE: paye,
    UIF: uif,
    SDL: sdl,
    totalDeductions: totalDeductions,
    netPay: netPay,
    status: "open",
    isFinalized: false,
  });
};

// Static method to validate unfinalization constraints
payrollPeriodSchema.statics.validateUnfinalization = async function(employeeId, company, periodEndDate) {
  const validationResult = {
    isValid: true,
    errors: [],
    warnings: [],
  };

  try {
    // Check if any newer periods are finalized
    const newerFinalized = await this.findOne({
      employee: employeeId,
      company: company,
      endDate: { $gt: new Date(periodEndDate) },
      isFinalized: true,
    });

    if (newerFinalized) {
      validationResult.errors.push(
        "Cannot unfinalize period - newer periods are finalized. Unfinalize newer periods first."
      );
      validationResult.isValid = false;
    }

    // Check if period has an associated pay run
    const PayRun = mongoose.model("PayRun");
    const associatedPayRun = await PayRun.findOne({
      payrollPeriods: { $in: [periodEndDate] },
      company: company,
      status: { $nin: ["deleted", "cancelled"] },
    });

    if (associatedPayRun) {
      validationResult.errors.push(
        "Cannot unfinalize period - pay run exists. Delete the pay run first."
      );
      validationResult.isValid = false;
    }

    return validationResult;
  } catch (error) {
    console.error("Error validating unfinalization:", error);
    validationResult.isValid = false;
    validationResult.errors.push("Validation error occurred");
    return validationResult;
  }
};

// Static method to check if period can be unfinalized
payrollPeriodSchema.statics.canUnfinalize = async function(periodId) {
  try {
    const period = await this.findById(periodId);
    if (!period || !period.isFinalized) {
      return { canUnfinalize: false, reason: "Period not found or not finalized" };
    }

    if (period.status === "locked") {
      return { canUnfinalize: false, reason: "Period is locked by pay run" };
    }

    const validation = await this.validateUnfinalization(
      period.employee,
      period.company,
      period.endDate
    );

    return {
      canUnfinalize: validation.isValid,
      reason: validation.errors.join("; "),
      warnings: validation.warnings,
    };
  } catch (error) {
    console.error("Error checking unfinalization eligibility:", error);
    return { canUnfinalize: false, reason: "Error checking eligibility" };
  }
};

// Static method to recalculate existing periods with PayrollService
payrollPeriodSchema.statics.recalculateExistingPeriod = async function(periodId) {
  console.log('🔄 RECALCULATING EXISTING PERIOD:', periodId);

  try {
    const period = await this.findById(periodId).populate('employee');
    if (!period) {
      console.error('❌ Period not found:', periodId);
      return null;
    }

    const employee = period.employee;
    console.log('  - Employee:', employee.firstName, employee.lastName);
    console.log('  - Current stored values:', {
      basicSalary: period.basicSalary,
      grossPay: period.grossPay,
      PAYE: period.PAYE,
      UIF: period.UIF,
      totalDeductions: period.totalDeductions,
      netPay: period.netPay
    });

    // Calculate pro-rata using BusinessDate - EXACT same method as employeeProfile
    const BusinessDate = require('../utils/BusinessDate');

    // Use the EXACT same parameters as employeeProfile.ejs Calculator card
    const fullBasicSalary = period.basicSalary; // Direct from PayrollPeriod model

    // 🔧 FIX: Ensure correct period dates for monthly periods
    let correctedPeriodStart = period.startDateBusiness; // BusinessDate format
    let correctedPeriodEnd = period.endDateBusiness; // BusinessDate format

    // For monthly periods, ensure period starts on 1st of month, not DOA
    if (employee.payFrequency?.frequency === 'monthly') {
      const doaBusinessDate = BusinessDate.normalize(employee.doa);
      const doaYear = doaBusinessDate.split('-')[0];
      const doaMonth = doaBusinessDate.split('-')[1];
      const expectedStart = `${doaYear}-${doaMonth}-01`;

      // Calculate correct last day of month
      const lastDayOfMonth = new Date(parseInt(doaYear), parseInt(doaMonth), 0).getDate();
      const expectedEnd = `${doaYear}-${doaMonth}-${lastDayOfMonth.toString().padStart(2, '0')}`;

      if (correctedPeriodStart !== expectedStart) {
        console.log('  - 🔧 APPLYING PERIOD DATE CORRECTION:', {
          originalStart: correctedPeriodStart,
          originalEnd: correctedPeriodEnd,
          correctedStart: expectedStart,
          correctedEnd: expectedEnd,
          reason: 'Monthly periods should start on 1st of month for proper pro-rata'
        });

        correctedPeriodStart = expectedStart;
        correctedPeriodEnd = expectedEnd;

        console.log('  - ✅ Period dates corrected successfully:', {
          newStart: correctedPeriodStart,
          newEnd: correctedPeriodEnd,
          expectedDays: 'Should now calculate 30 worked days out of 31 total days'
        });
      }
    }

    // 🔍 DETAILED DATE ANALYSIS FOR PRO-RATA DEBUGGING
    const doaFormatted = employee.doa ? employee.doa.toISOString().split('T')[0] : 'null';

    console.log('  - DETAILED pro-rata analysis:', {
      employeeName: `${employee.firstName} ${employee.lastName}`,
      doa: employee.doa,
      doaFormatted: doaFormatted,
      fullBasicSalary: fullBasicSalary,
      periodStart: correctedPeriodStart,
      periodEnd: correctedPeriodEnd,
      expectedScenario: 'DOA: 2025-05-02, Period: 2025-05-01 to 2025-05-31 = 30/31 days = 96.77%'
    });

    // Check if the period dates are correct
    if (correctedPeriodStart === '2025-05-02' && correctedPeriodEnd === '2025-05-31') {
      console.log('  ⚠️ ISSUE FOUND: Period starts on DOA date, should start on 2025-05-01');
      console.log('  📝 Current period: 2025-05-02 to 2025-05-31 = 30 days (incorrect)');
      console.log('  ✅ Should be: 2025-05-01 to 2025-05-31 = 31 days (correct)');
    }

    const proratedSalaryResult = BusinessDate.calculateProratedSalary(
      employee.doa,
      fullBasicSalary, // Use exact same parameter name as employeeProfile
      employee,
      correctedPeriodStart, // Use exact same parameter name as employeeProfile
      correctedPeriodEnd // Use exact same parameter name as employeeProfile
    );

    console.log('  - Pro-rata calculation result with expected vs actual:', {
      ...proratedSalaryResult,
      expected: {
        totalDays: 31,
        workedDays: 30, // From May 2nd to May 31st
        percentage: '96.77%',
        explanation: 'Employee started May 2nd, worked 30 out of 31 days in May'
      },
      actual: {
        totalDays: proratedSalaryResult.totalDaysInPeriod,
        workedDays: proratedSalaryResult.workedDays,
        percentage: proratedSalaryResult.proratedPercentage + '%'
      },
      issue: proratedSalaryResult.totalDaysInPeriod === 30 ? 'Period should be 31 days (full month)' : 'Period length looks correct'
    });

    // Create/update Payroll record for PayrollService
    const Payroll = require('./Payroll');
    let payroll = await Payroll.findOne({
      employee: employee._id,
      month: period.endDate
    });

    // Calculate the correct gross pay amount to use for PayrollService
    let grossPayForCalculation = period.basicSalary;
    if (proratedSalaryResult.isFirstPeriodWithDOA && Number(proratedSalaryResult.proratedPercentage) < 100) {
      grossPayForCalculation = Number(proratedSalaryResult.proratedSalary);
      console.log('  - Using pro-rated amount for PayrollService:', grossPayForCalculation);
    } else {
      grossPayForCalculation = Number(proratedSalaryResult.fullPeriodSalary || period.basicSalary);
      console.log('  - Using full amount for PayrollService:', grossPayForCalculation);
    }

    if (!payroll) {
      console.log('  - Creating Payroll record with correct amount...');

      // Check if there's a recent payroll record to copy benefits from
      const recentPayroll = await Payroll.findOne({
        employee: employee._id
      }).sort({ month: -1 });

      // Use benefits from recent payroll if available, otherwise default to zero
      const accommodationBenefit = recentPayroll?.accommodationBenefit || 0;
      const travelAllowance = recentPayroll?.travelAllowance || { fixedAllowanceAmount: 0 };
      const lossOfIncome = recentPayroll?.lossOfIncome || 0;
      const maintenanceOrder = recentPayroll?.maintenanceOrder || 0;

      payroll = await Payroll.create({
        employee: employee._id,
        company: employee.company,
        month: period.endDate,
        basicSalary: grossPayForCalculation, // Use calculated amount
        // Copy benefits from recent payroll to ensure consistent PAYE/UIF calculation
        accommodationBenefit: accommodationBenefit,
        travelAllowance: travelAllowance,
        lossOfIncome: lossOfIncome,
        maintenanceOrder: maintenanceOrder
      });
      console.log('  - Payroll record created with benefits from recent payroll:', {
        accommodationBenefit,
        travelAllowance,
        lossOfIncome,
        maintenanceOrder
      });
    } else {
      // Update existing payroll with correct amount
      payroll.basicSalary = grossPayForCalculation;
      await payroll.save();
      console.log('  - Updated Payroll record with correct amount:', grossPayForCalculation);
    }

    // Use PayrollService for calculations
    const PayrollService = require('../services/PayrollService');
    const calculations = await PayrollService.calculatePayrollTotals(
      employee._id,
      period.endDate,
      employee.company
    );

    console.log('  - PayrollService calculations:', calculations);

    // Calculate SDL for employer reporting (NOT included in employee deductions)
    const sdlForEmployer = grossPayForCalculation * 0.01;

    // Ensure totalDeductions excludes SDL
    const correctedTotalDeductions = (calculations.paye || 0) + (calculations.uif || 0);
    const correctedNetPay = grossPayForCalculation - correctedTotalDeductions;

    // Update the period with calculated values and corrected dates
    const updatedPeriod = await this.findByIdAndUpdate(periodId, {
      grossPay: grossPayForCalculation, // Use the correct pro-rated or full amount
      PAYE: calculations.paye || 0,
      UIF: calculations.uif || 0,
      SDL: sdlForEmployer, // Store SDL for employer reporting only
      totalDeductions: correctedTotalDeductions, // Excludes SDL
      netPay: correctedNetPay,
      // Update corrected period dates
      startDateBusiness: correctedPeriodStart,
      endDateBusiness: correctedPeriodEnd,
      // Update pro-rata fields
      proratedPercentage: proratedSalaryResult.proratedPercentage,
      workedDays: proratedSalaryResult.workedDays,
      totalDaysInPeriod: proratedSalaryResult.totalDaysInPeriod,
      isFirstPeriodWithDOA: proratedSalaryResult.isFirstPeriodWithDOA,
      fullPeriodSalary: proratedSalaryResult.fullPeriodSalary
    }, { new: true });

    console.log('✅ Period recalculated successfully:', {
      grossPay: updatedPeriod.grossPay,
      PAYE: updatedPeriod.PAYE,
      UIF: updatedPeriod.UIF,
      totalDeductions: updatedPeriod.totalDeductions,
      netPay: updatedPeriod.netPay,
      proratedPercentage: updatedPeriod.proratedPercentage
    });

    return updatedPeriod;

  } catch (error) {
    console.error('❌ Error recalculating period:', error);
    throw error;
  }
};

module.exports =
  mongoose.models.PayrollPeriod ||
  mongoose.model("PayrollPeriod", payrollPeriodSchema);
