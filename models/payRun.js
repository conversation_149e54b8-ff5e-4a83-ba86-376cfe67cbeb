const mongoose = require("mongoose");
const moment = require("moment");

const PayRunSchema = new mongoose.Schema({
  company: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Company",
    required: true
  },
  period: {
    type: String,
    required: true,
  },
  monthYear: {
    type: String,
    required: true,
  },
  payRunType: {
    type: String,
    enum: [
      "regular",
      "bonus",
      "commission", 
      "adjustment",
      "termination",
      "leave",
      "supplementary",
      "correction",
      "additional"
    ],
    default: "regular",
    required: true
  },
  description: {
    type: String,
    trim: true,
    required: false,
    help: "Optional description to identify the purpose of this specific payrun"
  },
  sequence: {
    type: Number,
    default: 1
  },
  payRunIdentifier: {
    type: String,
    required: true,
    unique: true,
    default: function() {
      return `${this.company}_${this.monthYear}_${this.payRunType}_${this.sequence}_${Date.now()}`;
    }
  },
  reference: {
    type: String,
    required: true,
    default: function() {
      return `PR-${this.monthYear}-${this.payRunType.toUpperCase()}-${this.sequence}`;
    }
  },
  frequency: {
    type: String,
    enum: ["weekly", "biweekly", "monthly"],
    required: true,
  },
  startDate: {
    type: Date,
    required: true,
  },
  endDate: {
    type: Date,
    required: true,
  },
  paymentDate: {
    type: Date,
    required: true,
  },
  taxPeriod: {
    type: String,
    required: true,
  },
  status: {
    type: String,
    enum: [
      "draft",
      "processing",
      "review",
      "approved",
      "finalized",
      "released",
    ],
    default: "draft",
  },
  payrollPeriods: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: "PayrollPeriod",
    },
  ],
  payslips: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Payslip",
    },
  ],
  totals: {
    grossPay: Number,
    totalDeductions: Number,
    netPay: Number,
    employerContributions: Number,
  },
  containsPendingPayslips: {
    type: Boolean,
    default: false,
  },
  pendingPayslipCount: {
    type: Number,
    default: 0,
  },
  totalPayslipCount: {
    type: Number,
    default: 0,
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
  approvedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
  },
  finalized: {
    type: Boolean,
    default: false,
  },
  finalizedAt: Date,
  // Xero Integration Fields
  xeroSynced: {
    type: Boolean,
    default: false,
  },
  xeroSyncedAt: {
    type: Date,
  },
  xeroTransactionId: {
    type: String,
  },
  quickbooksSynced: {
    type: Boolean,
    default: false,
  },
  quickbooksSyncedAt: {
    type: Date,
  },
  quickbooksTransactionId: {
    type: String,
  },
  eftSettings: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'EFTSettings',
    default: null
  },
  relatedPayRun: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'PayRun'
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
  // Deletion tracking fields
  deletedAt: {
    type: Date,
  },
  deletedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
  },
  deletionReason: {
    type: String,
    trim: true,
  }
}, {
  timestamps: true,
  autoIndex: false // Disable automatic indexing
});

// Create compound index for finding pay runs
PayRunSchema.index({
  company: 1,
  monthYear: 1,
  payRunType: 1,
  sequence: 1
}, {
  unique: true,
  background: true,
  name: 'company_monthYear_payRunType_sequence'
});

// Create index for payRunIdentifier
PayRunSchema.index({
  payRunIdentifier: 1
}, {
  unique: true,
  background: true,
  name: 'payRunIdentifier_unique'
});

// Pre-save middleware to update timestamps and set sequence
PayRunSchema.pre('save', async function(next) {
  try {
    if (this.isNew) {
      // Find the highest sequence number for this company, month, and type
      const highestSequence = await mongoose.model('PayRun').findOne({
        company: this.company,
        monthYear: this.monthYear,
        payRunType: this.payRunType
      }).sort('-sequence');

      // Set sequence number
      this.sequence = highestSequence ? highestSequence.sequence + 1 : 1;
      
      // Update identifiers
      this.payRunIdentifier = `${this.company}_${this.monthYear}_${this.payRunType}_${this.sequence}_${Date.now()}`;
      this.reference = `PR-${this.monthYear}-${this.payRunType.toUpperCase()}-${this.sequence}`;
    }
    
    this.updatedAt = new Date();
    next();
  } catch (error) {
    next(error);
  }
});

// Pre-save middleware to set monthYear
PayRunSchema.pre('save', function(next) {
  if (this.startDate) {
    this.monthYear = moment(this.startDate).format('YYYY-MM');
  }
  next();
});

// Static method to find active pay runs for a period
PayRunSchema.statics.findActivePayRuns = async function(company, monthYear) {
  return this.find({
    company,
    monthYear,
    status: { $in: ['draft', 'processing', 'review'] }
  }).sort({ payRunType: 1, sequence: 1 });
};

// Static method to find finalized pay runs for a period
PayRunSchema.statics.findFinalizedPayRuns = async function(company, monthYear) {
  return this.find({
    company,
    monthYear,
    status: { $in: ['finalized', 'released'] }
  }).sort({ payRunType: 1, sequence: 1 });
};

// Instance method to update totals
PayRunSchema.methods.updateTotals = async function() {
  const payslips = await mongoose.model('Payslip').find({
    _id: { $in: this.payslips }
  });

  this.totals = payslips.reduce((acc, slip) => {
    acc.grossPay = (acc.grossPay || 0) + (slip.totals?.grossPay || 0);
    acc.totalDeductions = (acc.totalDeductions || 0) + (slip.totals?.totalDeductions || 0);
    acc.netPay = (acc.netPay || 0) + (slip.totals?.netPay || 0);
    acc.employerContributions = (acc.employerContributions || 0) + (slip.totals?.employerContributions || 0);
    return acc;
  }, {});

  return this.save();
};

// Static method to validate pay run deletion
PayRunSchema.statics.validateDeletion = async function(payRunId) {
  const validationResult = {
    isValid: true,
    errors: [],
    warnings: [],
  };

  try {
    const payRun = await this.findById(payRunId);
    if (!payRun) {
      validationResult.errors.push("Pay run not found");
      validationResult.isValid = false;
      return validationResult;
    }

    // Check if pay run is already finalized/released
    if (payRun.status === "finalized" || payRun.status === "released") {
      validationResult.warnings.push(
        "Pay run is finalized/released. Deletion will require additional confirmation."
      );
    }

    // Check if pay run has been processed for banking
    if (payRun.eftSettings) {
      validationResult.warnings.push(
        "Pay run has banking information. Ensure bank processing is handled appropriately."
      );
    }

    return validationResult;
  } catch (error) {
    console.error("Error validating pay run deletion:", error);
    validationResult.isValid = false;
    validationResult.errors.push("Validation error occurred");
    return validationResult;
  }
};

// Instance method to soft delete pay run
PayRunSchema.methods.softDelete = async function(userId, reason = "Manual deletion") {
  this.deletedAt = new Date();
  this.deletedBy = userId;
  this.deletionReason = reason;
  this.status = "deleted";

  // Unlock associated payroll periods
  const PayrollPeriod = mongoose.model("PayrollPeriod");
  await PayrollPeriod.updateMany(
    { _id: { $in: this.payrollPeriods } },
    {
      $set: { status: "finalized" },
      $unset: { payRun: 1 }
    }
  );

  return this.save();
};

// Export the model
module.exports = mongoose.models.PayRun || mongoose.model("PayRun", PayRunSchema);
