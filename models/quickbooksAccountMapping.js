const mongoose = require("mongoose");

const quickbooksAccountMappingSchema = new mongoose.Schema(
  {
    company: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Company",
      required: true,
    },
    payrollAccountId: {
      type: String,
      required: true,
      enum: [
        // <PERSON><PERSON> and <PERSON>age Accounts
        "BASIC_SALARY",
        "OVERTIME_PAY",
        "BONUS_PAY",
        "COMMISSION_PAY",
        "PUBLIC_HOLIDAY_NOT_WORKED",
        "BASIC_HOURLY_PAY",

        // Employer Contributions
        "MEDICAL_AID_EMPLOYER",
        "PENSION_FUND_EMPLOYER",
        "SDL_EMPLOYER",
        "UIF_EMPLOYER",
        "WORKERS_COMP_EMPLOYER",
        "GROUP_LIFE_EMPLOYER",

        // Employee Deductions
        "MEDICAL_AID_EMPLOYEE",
        "PENSION_FUND_EMPLOYEE",
        "UIF_EMPLOYEE",
        "PAYE_DEDUCTION",
        "LOA<PERSON>_DEDUCTION",
        "UNION_DUES",
        "GARNISHMENT",

        // Liability Accounts
        "MEDICAL_AID_LIABILITY",
        "PENSION_FUND_TOTAL",
        "SDL_LIABILITY",
        "PAYE_LIABILITY",
        "UIF_TOTAL",
        "WORKERS_COMP_LIABILITY",
        "GROUP_LIFE_LIABILITY",

        // Net Pay and Clearing
        "NET_PAY_CLEARING",
        "PAYROLL_BANK_ACCOUNT",

        // Leave Provisions
        "ANNUAL_LEAVE_PROVISION",
        "SICK_LEAVE_PROVISION",
        "MATERNITY_LEAVE_PROVISION",

        // Other Benefits
        "TRAVEL_ALLOWANCE",
        "CELL_PHONE_ALLOWANCE",
        "MEAL_ALLOWANCE",
        "HOUSING_ALLOWANCE",
      ],
    },
    quickbooksAccountId: {
      type: String,
      required: true,
    },
    quickbooksAccountCode: {
      type: String,
      required: false, // QuickBooks doesn't always use account codes
    },
    quickbooksAccountName: {
      type: String,
      required: true,
    },
    quickbooksAccountType: {
      type: String,
      required: true,
      enum: [
        "Expense",
        "Other Current Liability",
        "Bank",
        "Other Current Asset",
        "Accounts Payable",
        "Credit Card",
        "Long Term Liability",
        "Other Expense",
        "Cost of Goods Sold"
      ],
    },
    type: {
      type: String,
      required: true,
      enum: ["DEBIT", "CREDIT"],
    },
    category: {
      type: String,
      required: true,
      enum: [
        "SALARY_EXPENSE",
        "EMPLOYER_CONTRIBUTION",
        "EMPLOYEE_DEDUCTION",
        "LIABILITY",
        "CLEARING_ACCOUNT",
        "PROVISION"
      ],
    },
    isRequired: {
      type: Boolean,
      default: true,
    },
    description: {
      type: String,
      required: false,
    },
    lastSyncedAt: {
      type: Date,
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
    updatedAt: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: true,
  }
);

// Create a compound unique index
quickbooksAccountMappingSchema.index(
  { company: 1, payrollAccountId: 1 },
  { unique: true }
);

// Add index for QuickBooks account lookups
quickbooksAccountMappingSchema.index(
  { company: 1, quickbooksAccountId: 1 }
);

// Add a method to check if all required mappings exist for a company
quickbooksAccountMappingSchema.statics.validateCompanyMappings = async function (
  companyId
) {
  const mappings = await this.find({ company: companyId });
  const requiredMappings = [
    // Core salary accounts
    "BASIC_SALARY",
    "BASIC_HOURLY_PAY",

    // Essential employer contributions
    "MEDICAL_AID_EMPLOYER",
    "PENSION_FUND_EMPLOYER",
    "SDL_EMPLOYER",
    "UIF_EMPLOYER",

    // Essential liabilities
    "MEDICAL_AID_LIABILITY",
    "PENSION_FUND_TOTAL",
    "SDL_LIABILITY",
    "PAYE_LIABILITY",
    "UIF_TOTAL",

    // Net pay handling
    "NET_PAY_CLEARING",
  ];

  const missingMappings = requiredMappings.filter(
    (required) =>
      !mappings.some((mapping) => mapping.payrollAccountId === required)
  );

  return {
    isValid: missingMappings.length === 0,
    missingMappings,
    totalMappings: mappings.length,
    requiredCount: requiredMappings.length
  };
};

// Method to get mappings by category
quickbooksAccountMappingSchema.statics.getMappingsByCategory = async function (
  companyId,
  category
) {
  return await this.find({
    company: companyId,
    category: category
  }).sort({ payrollAccountId: 1 });
};

// Method to get mapping for specific payroll account
quickbooksAccountMappingSchema.statics.getMappingForPayrollAccount = async function (
  companyId,
  payrollAccountId
) {
  return await this.findOne({
    company: companyId,
    payrollAccountId: payrollAccountId
  });
};

// Pre-save middleware to update the updatedAt field
quickbooksAccountMappingSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Check if the model already exists before compiling
module.exports =
  mongoose.models.QuickBooksAccountMapping ||
  mongoose.model("QuickBooksAccountMapping", quickbooksAccountMappingSchema);