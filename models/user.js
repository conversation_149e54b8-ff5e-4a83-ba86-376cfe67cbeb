const express = require("express");
const mongoose = require("mongoose");
const session = require("express-session");
const passport = require("passport");
const crypto = require("crypto");
const bcrypt = require("bcrypt");
const flash = require("express-flash");
const bodyParser = require("body-parser");
const path = require("path");
const MongoStore = require("connect-mongo");
const Role = require("./role"); // Correct import for the Role model

const SALT_ROUNDS = 12;

const userSchema = new mongoose.Schema(
  {
    firstName: String,
    lastName: String,
    username: {
      type: String,
      unique: true,
      sparse: true, // This allows null values while maintaining uniqueness
    },
    email: {
      type: String,
      required: true,
      unique: true,
      lowercase: true,
    },
    bio: {
      type: String,
      default: "Tell us about yourself",
    },
    phone: String, // Add this line
    cellNumber: String, // Added cell number field
    password: { 
      type: String,
      required: function() {
        return this.isVerified; // Only require password if user is verified
      }
    },
    isVerified: { type: Boolean, default: false, required: true },
    verificationToken: String,
    resetPasswordToken: String,
    resetPasswordExpires: Date,
    companies: {
      type: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Company',
        required: true,
        validate: {
          validator: function(v) {
            return mongoose.Types.ObjectId.isValid(v);
          },
          message: props => `${props.value} is not a valid company ID!`
        }
      }],
      default: [],
      validate: {
        validator: function(v) {
          return Array.isArray(v);
        },
        message: props => 'Companies must be an array!'
      }
    },
    currentCompany: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Company'
    },
    defaultCompany: { type: mongoose.Schema.Types.ObjectId, ref: "Company" }, // Add this line if not already present
    role: { type: mongoose.Schema.Types.ObjectId, ref: "Role", required: true },
    roleName: String,
    createdAt: { type: Date, default: Date.now },
    twoFactorEnabled: {
      type: Boolean,
      default: false,
    },
    twoFactorSecret: String,
    backupCodes: [
      {
        code: String,
        used: {
          type: Boolean,
          default: false,
        },
      },
    ],
    passwordLastChanged: {
      type: Date,
      default: Date.now,
    },
    failedLoginAttempts: {
      type: Number,
      default: 0,
    },
    accountLocked: {
      type: Boolean,
      default: false,
    },
    lockUntil: Date,
    onboardingComplete: {
      type: Boolean,
      default: false,
    },
    onboardingStep: {
      type: Number,
      default: 1,
    },
    promotionalCode: {
        type: String,
        default: null
    },
    promotionStartDate: {
        type: Date,
        default: null
    },
    promotionEndDate: {
        type: Date,
        default: null
    },
    howDidYouHearAboutUs: {
        type: String,
        enum: {
            values: ['LinkedIn', 'YouTube', 'Website', 'Other'],
            message: 'Please select a valid option for how you heard about us'
        },
        default: null,
        validate: {
            validator: function(v) {
                // Allow null/undefined values (truly optional field)
                return v === null || v === undefined || ['LinkedIn', 'YouTube', 'Website', 'Other'].includes(v);
            },
            message: 'Please select a valid option for how you heard about us'
        }
    },
  },
  {
    timestamps: true,
  }
);

userSchema.pre("save", async function (next) {
  try {

    // Set username to email if not provided
    if (!this.username) {
      this.username = this.email;
    }

    // If this is the first save (_previousIsVerified is undefined),
    // initialize it to false to properly track changes
    if (this._previousIsVerified === undefined) {
      this._previousIsVerified = false;
    }

    // Ensure isVerified is false for new users unless explicitly set otherwise
    if (this.isNew && this.isVerified !== true) {
      this.isVerified = false;
    }

    // Only hash password if it's been modified or is new
    if (this.isModified("password")) {
      const hashedPassword = await bcrypt.hash(this.password, SALT_ROUNDS);
      this.password = hashedPassword;
    }

    // Track if isVerified changed from false to true
    if (this.isModified("isVerified") || (this.isNew && this.isVerified)) {
      
      if (this.isVerified && !this._previousIsVerified) {
        // Mark for welcome email sending after save
        this._sendWelcomeEmail = true;
      } else {
      }
    } else {
    }
    
    // Store current isVerified state for next save operation
    this._previousIsVerified = this.isVerified;

    next();
  } catch (error) {
    console.error("Error in pre-save hook:", error);
    next(error);
  }
});

// Post-save hook to send welcome email if needed
userSchema.post("save", async function () {
  try {
    
    // Skip if welcome email doesn't need to be sent
    if (!this._sendWelcomeEmail) {
      return;
    }
    
    // Reset the flag to prevent multiple emails
    this._sendWelcomeEmail = false;
    
    
    // Emit an event to notify the application to send welcome email
    // This avoids circular dependencies and keeps model logic separate
    let eventEmitted = false;
    if (mongoose.connection.emit) {
      
      // Try both approaches to ensure event emission works
      try {
        // First approach: regular event emission
        mongoose.connection.emit("userVerified", {
          userId: this._id,
          email: this.email,
          firstName: this.firstName || this.email.split('@')[0],
        });
        
        eventEmitted = true;
      } catch (emitError) {
        console.error("Error with mongoose.connection.emit:", emitError);
        
        // Second approach: try process.nextTick for reliable event emission
        process.nextTick(() => {
          try {
            mongoose.connection.emit("userVerified", {
              userId: this._id,
              email: this.email,
              firstName: this.firstName || this.email.split('@')[0],
            });
            eventEmitted = true;
          } catch (nextTickError) {
            console.error("Error emitting event via process.nextTick:", nextTickError);
          }
        });
      }
      
      // Third approach: as a fallback, try direct email sending if possible
      try {
        // Only continue with direct email if event emission failed
        if (!eventEmitted) {
          const path = require('path');
          const fs = require('fs');
          const ejs = require('ejs');
          const nodemailer = require('nodemailer');
          
          const welcomeTemplatePath = path.join(process.cwd(), "views/emails/welcome.ejs");
          
          // Create directory and template if needed
          const emailsDir = path.join(process.cwd(), "views/emails");
          if (!fs.existsSync(emailsDir)) {
            try {
              fs.mkdirSync(emailsDir, { recursive: true });
            } catch (dirError) {
              console.error("Error creating emails directory:", dirError);
              throw dirError;
            }
          }
          
          // Create template if it doesn't exist
          if (!fs.existsSync(welcomeTemplatePath)) {
            
            const defaultTemplate = `<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Welcome to Panda Solutions</title>
  <style>
    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
    .header { text-align: center; margin-bottom: 30px; }
    .logo { max-width: 200px; }
    h1 { color: #2c3e50; }
    .footer { margin-top: 30px; font-size: 12px; color: #7f8c8d; text-align: center; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <img src="cid:logo" alt="Panda Solutions Logo" class="logo">
      <h1>Welcome to Panda Solutions!</h1>
    </div>
    
    <p>Hello <%= firstName %>,</p>
    
    <p>Thank you for joining Panda Solutions. Your account has been verified and is now ready to use.</p>
    
    <p>Here's what you need to know to get started:</p>
    
    <ul>
      <li>Our support team is available via <NAME_EMAIL> with a 48-hour turnaround time.</li>
      <li>Check out our <a href="https://www.youtube.com/watch?v=yourvideolink">video guide</a> for help navigating the platform.</li>
      <li>For urgent matters, contact us at +27 00 000 0000.</li>
    </ul>
    
    <p>We're excited to have you on board!</p>
    
    <p>Best regards,<br>
    The Panda Solutions Team</p>
    
    <div class="footer">
      <p>© <%= new Date().getFullYear() %> Panda Solutions. All rights reserved.</p>
      <p>This email was sent to <%= email %>.</p>
    </div>
  </div>
</body>
</html>`;
            
            try {
              fs.writeFileSync(welcomeTemplatePath, defaultTemplate);
            } catch (templateError) {
              console.error("Error creating welcome template:", templateError);
              throw templateError;
            }
          } else {
          }
          
          // Helper function to find logo
          const ensureLogoExists = () => {
            try {
              // Check common locations for the logo
              const possiblePaths = [
                path.join(process.cwd(), "public/images/pandalogo.png"),
                path.join(process.cwd(), "public/images/logo.png"),
                path.join(process.cwd(), "public/img/logo.png"),
                path.join(process.cwd(), "public/assets/images/logo.png")
              ];
          
              // Return the first path that exists
              for (const logoPath of possiblePaths) {
                if (fs.existsSync(logoPath)) {
                  return logoPath;
                } else {
                }
              }
          
              // Fallback to the SVG logo if exists
              const svgPath = path.join(process.cwd(), "public/images/pandalogo.svg");
              if (fs.existsSync(svgPath)) {
                return svgPath;
              } else {
              }
          
              
              // Create public/images directory if needed
              const imagesDir = path.join(process.cwd(), "public/images");
              if (!fs.existsSync(imagesDir)) {
                try {
                  fs.mkdirSync(imagesDir, { recursive: true });
                } catch (dirError) {
                  console.error("Error creating images directory:", dirError);
                }
              }
              
              // Create a very basic logo as SVG
              const logoSvgPath = path.join(process.cwd(), "public/images/pandalogo.svg");
              const basicSvg = `<svg width="200" height="50" xmlns="http://www.w3.org/2000/svg">
  <rect width="100%" height="100%" fill="#f0f0f0"/>
  <text x="20" y="35" font-family="Arial" font-size="24" fill="#333">Panda Solutions</text>
</svg>`;
              
              try {
                fs.writeFileSync(logoSvgPath, basicSvg);
                return logoSvgPath;
              } catch (logoError) {
                console.error("Error creating basic logo:", logoError);
                return null;
              }
            } catch (error) {
              console.error("Error finding logo:", error);
              return null;
            }
          };
          
          // Render welcome email template
          const emailHtml = await ejs.renderFile(
            welcomeTemplatePath,
            {
              firstName: this.firstName || this.email.split('@')[0],
              email: this.email
            }
          );
          
          // Find logo
          const logoPath = ensureLogoExists();
          
          // Create transporter
          const transporter = nodemailer.createTransport({
            host: process.env.EMAIL_HOST,
            port: process.env.EMAIL_PORT,
            secure: process.env.EMAIL_SECURE === "true",
            auth: {
              user: process.env.EMAIL_USER,
              pass: process.env.EMAIL_PASS,
            },
            tls: {
              rejectUnauthorized: false, // Only use this in development
            }
          });
          
          // Prepare mail options
          const mailOptions = {
            from: {
              name: process.env.EMAIL_FROM_NAME || "Panda Solutions",
              address: process.env.EMAIL_USER,
            },
            to: this.email,
            subject: "Welcome to Panda Solutions - Getting Started",
            html: emailHtml,
            attachments: logoPath ? [
              {
                filename: "logo.png",
                path: logoPath,
                cid: "logo",
              },
            ] : [],
            headers: {
              "X-Priority": "1",
              "X-MSMail-Priority": "High",
              Importance: "high",
              "Message-ID": `<${Date.now()}.${Math.random().toString(36).substring(2)}@pss-group.co.za>`,
              "List-Unsubscribe": `<mailto:${process.env.EMAIL_USER}?subject=unsubscribe>`,
              "X-Mailer": "Panda Solutions Mailer/1.0",
              "X-Entity-Ref-ID": require("crypto").randomBytes(32).toString("hex"),
            },
          };
          
          // Send email
          const info = await transporter.sendMail(mailOptions);
        }
      } catch (directError) {
        console.error("Error with direct email sending:", directError);
        console.error("Stack trace:", directError.stack);
      }
    } else {
      console.error("ERROR: mongoose.connection.emit is not available");
      
      // Alternative approach: try event emission via mongoose itself
      if (mongoose.emit) {
        try {
          mongoose.emit("userVerified", {
            userId: this._id,
            email: this.email,
            firstName: this.firstName || this.email.split('@')[0],
          });
          eventEmitted = true;
        } catch (mongooseEmitError) {
          console.error("Error with mongoose.emit:", mongooseEmitError);
        }
      } else {
        console.error("ERROR: Both mongoose.connection.emit and mongoose.emit are not available");
      }
    }
    
  } catch (error) {
    console.error("Error in post-save hook for welcome email:", error);
    console.error("Stack trace:", error.stack);
  }
});

userSchema.methods.comparePassword = async function (candidatePassword) {
  try {

    if (!this.password || !candidatePassword) {
      return false;
    }

    // Use bcrypt's built-in compare function
    const isMatch = await bcrypt.compare(candidatePassword, this.password);
    
    return isMatch;
  } catch (error) {
    console.error("Password comparison error:", error);
    return false;
  }
};

userSchema.methods.generateResetToken = async function () {
  const token = crypto.randomBytes(20).toString("hex");
  this.resetPasswordToken = token;
  this.resetPasswordExpires = Date.now() + 3600000; // Token expires in 1 hour
  await this.save();
  return token;
};

userSchema.methods.verifyPassword = async function (password) {
  const isMatch = await bcrypt.compare(password, this.password);
  return isMatch;
};

const User = mongoose.models.User || mongoose.model("User", userSchema);
module.exports = User;
