{"name": "sidebar", "version": "1.1.0", "main": "index.js", "scripts": {"test": "jest --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "start": "NODE_ENV=production node app.js", "build": "echo 'Build step completed'", "setup-roles": "node setupRoles.js", "dev": "NODE_ENV=development nodemon app.js --env-file=.env.development", "prod": "NODE_ENV=production node app.js --env-file=.env.production", "xero:monitor": "node monitor-xero-integration.js", "xero:test-reliability": "node test-xero-connection-reliability.js", "xero:monitor-token": "node monitor-xero-token-refresh.js", "xero:refresh-tokens": "node scripts/schedule-xero-token-refresh.js", "xero:reset": "node scripts/reset-xero-integration.js", "xero:install": "node scripts/install-xero-dependencies.js", "xero:check": "node scripts/check-xero-dependencies.js", "xero:setup": "node scripts/setup-xero-directories.js", "xero:init": "node scripts/setup-xero-integration.js", "test:jwt": "node test-jwt.js", "perf:optimize": "node scripts/deploy-performance-optimizations.js", "perf:indexes": "node scripts/add-database-indexes.js", "perf:test": "node scripts/performance-monitor.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.821.0", "@aws-sdk/s3-request-presigner": "^3.821.0", "@sendgrid/mail": "^8.1.4", "@shadcn/ui": "^0.0.4", "archiver": "^7.0.1", "autoprefixer": "^10.4.19", "axios": "^1.10.0", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "blob-stream": "^0.1.3", "body-parser": "^1.20.2", "bson": "^6.1.0", "bull": "^4.16.5", "chalk": "^5.4.1", "chart.js": "^3.9.1", "compression": "^1.7.4", "connect-flash": "^0.1.1", "connect-mongo": "^5.0.0", "connect-mongodb-session": "^5.0.0", "connect-redis": "^7.1.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "csurf": "^1.11.0", "csv-writer": "^1.6.0", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "ejs": "^3.1.10", "excel4node": "^1.8.2", "exceljs": "^4.4.0", "express": "^4.21.2", "express-fileupload": "^1.5.1", "express-flash": "^0.0.2", "express-rate-limit": "^7.1.5", "express-session": "^1.18.1", "express-validator": "^7.0.1", "geoip-lite": "^1.4.10", "helmet": "^7.2.0", "html-pdf": "^3.0.1", "html-pdf-node": "^1.0.8", "html2pdf.js": "^0.10.1", "ioredis": "^5.6.1", "json2csv": "^6.0.0-alpha.2", "jsonwebtoken": "^9.0.2", "method-override": "^3.0.0", "module_name": "^1.0.0", "moment": "^2.30.1", "moment-timezone": "^0.5.46", "mongodb": "^5.8.1", "mongoose": "^7.8.6", "multer": "^1.4.5-lts.1", "node-cache": "^5.1.2", "node-cron": "^3.0.3", "node-quickbooks": "^2.0.46", "nodemailer": "^6.10.0", "nodemon": "^3.1.7", "open": "^9.1.0", "otplib": "^12.0.1", "passport": "^0.6.0", "passport-local": "^1.0.0", "paystack-node": "^0.3.0", "pdf-lib": "^1.17.1", "pdfkit": "^0.15.2", "pdfkit-table": "^0.1.99", "postcss": "^8.4.38", "puppeteer": "^21.0.0", "qrcode": "^1.5.4", "redis": "^4.7.0", "signature_pad": "^5.0.4", "speakeasy": "^2.0.0", "tailwindcss": "^3.4.3", "ua-parser-js": "^1.0.39", "uuid": "^9.0.1", "xero-node": "^9.3.0", "xlsx": "^0.18.5", "yargs": "^17.7.2"}, "description": "Panda Payroll with JWT Authentication", "devDependencies": {"@babel/core": "^7.24.5", "@babel/preset-env": "^7.24.5", "@babel/preset-react": "^7.24.1", "babel-loader": "^9.1.3", "difflib": "^0.2.4", "ejs-lint": "^2.0.0", "jest": "^29.7.0", "node-fetch": "^2.7.0", "postcss-cli": "^11.0.1", "sinon": "^17.0.1", "supertest": "^7.0.0", "tough-cookie": "^5.1.2", "webpack": "^5.91.0", "webpack-cli": "^5.1.4"}}