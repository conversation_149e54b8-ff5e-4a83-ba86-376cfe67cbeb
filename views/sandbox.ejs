<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PandaPayroll API Sandbox</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/themes/prism.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8f9fa;
        }
        .sandbox-header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .sandbox-badge {
            background: rgba(255, 255, 255, 0.2);
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.875rem;
            display: inline-block;
            margin-bottom: 1rem;
        }
        .api-tester {
            background: white;
            border-radius: 0.5rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            margin-bottom: 2rem;
        }
        .api-tester-header {
            background: #f8f9fa;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #dee2e6;
            border-radius: 0.5rem 0.5rem 0 0;
        }
        .api-tester-body {
            padding: 1.5rem;
        }
        .method-badge {
            font-size: 0.75rem;
            font-weight: 600;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            margin-right: 0.5rem;
        }
        .method-get { background: #d4edda; color: #155724; }
        .method-post { background: #d1ecf1; color: #0c5460; }
        .method-put { background: #fff3cd; color: #856404; }
        .method-delete { background: #f8d7da; color: #721c24; }
        .endpoint-url {
            font-family: 'Monaco', 'Menlo', monospace;
            background: #f8f9fa;
            padding: 0.5rem;
            border-radius: 0.25rem;
            border: 1px solid #dee2e6;
        }
        .response-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.25rem;
            padding: 1rem;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 0.875rem;
            max-height: 400px;
            overflow-y: auto;
        }
        .credentials-box {
            background: #e7f3ff;
            border: 1px solid #b3d7ff;
            border-radius: 0.5rem;
            padding: 1rem;
            margin-bottom: 2rem;
        }
        .quick-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
            margin-bottom: 1rem;
        }
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-warning { background: #ffc107; }
        .json-key { color: #0066cc; }
        .json-string { color: #009900; }
        .json-number { color: #cc6600; }
        .json-boolean { color: #990099; }
        .json-null { color: #999999; }
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
        .tab-content {
            border: 1px solid #dee2e6;
            border-top: none;
            border-radius: 0 0 0.375rem 0.375rem;
            padding: 1rem;
        }
        .nav-tabs .nav-link.active {
            background-color: #007bff;
            border-color: #007bff;
            color: white;
        }
    </style>
</head>
<body>
    <div class="sandbox-header">
        <div class="container">
            <div class="sandbox-badge">
                <i class="fas fa-flask"></i> SANDBOX ENVIRONMENT
            </div>
            <h1 class="mb-3">🐼 PandaPayroll API Sandbox</h1>
            <p class="lead mb-0">Test and explore the PandaPayroll API in a safe environment</p>
        </div>
    </div>

    <div class="container">
        <!-- Credentials Section -->
        <div class="credentials-box">
            <h5><i class="fas fa-key"></i> Sandbox Credentials</h5>
            <div class="row">
                <div class="col-md-6">
                    <strong>Email:</strong> <code><EMAIL></code><br>
                    <strong>Password:</strong> <code>demo123</code>
                </div>
                <div class="col-md-6">
                    <strong>Company Code:</strong> <code>DEMO001</code><br>
                    <strong>Base URL:</strong> <code id="base-url"></code>
                </div>
            </div>
        </div>

        <!-- Authentication Section -->
        <div class="api-tester">
            <div class="api-tester-header">
                <h5><i class="fas fa-lock"></i> Authentication</h5>
            </div>
            <div class="api-tester-body">
                <div class="quick-actions">
                    <button class="btn btn-primary btn-sm" onclick="authenticateUser()">
                        <i class="fas fa-sign-in-alt"></i> Login to Sandbox
                    </button>
                    <button class="btn btn-info btn-sm" onclick="checkHealth()">
                        <i class="fas fa-heartbeat"></i> Health Check
                    </button>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <label class="form-label">JWT Token:</label>
                        <textarea id="jwt-token" class="form-control" rows="3" placeholder="JWT token will appear here after login"></textarea>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Auth Status:</label>
                        <div id="auth-status" class="response-area">Not authenticated</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- API Testing Tabs -->
        <div class="api-tester">
            <div class="api-tester-header">
                <h5><i class="fas fa-code"></i> API Testing</h5>
            </div>
            <div class="api-tester-body">
                <ul class="nav nav-tabs" id="apiTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="employees-tab" data-bs-toggle="tab" data-bs-target="#employees" type="button">
                            <i class="fas fa-users"></i> Employees
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="payroll-tab" data-bs-toggle="tab" data-bs-target="#payroll" type="button">
                            <i class="fas fa-calculator"></i> Payroll
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="payruns-tab" data-bs-toggle="tab" data-bs-target="#payruns" type="button">
                            <i class="fas fa-play-circle"></i> Pay Runs
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="leave-tab" data-bs-toggle="tab" data-bs-target="#leave" type="button">
                            <i class="fas fa-calendar-alt"></i> Leave
                        </button>
                    </li>
                </ul>
                
                <div class="tab-content" id="apiTabContent">
                    <!-- Employees Tab -->
                    <div class="tab-pane fade show active" id="employees" role="tabpanel">
                        <div class="quick-actions">
                            <button class="btn btn-success btn-sm" onclick="getEmployees()">
                                <i class="fas fa-download"></i> Get Employees
                            </button>
                            <button class="btn btn-info btn-sm" onclick="getEmployee('sandbox_emp_1')">
                                <i class="fas fa-user"></i> Get Employee Details
                            </button>
                        </div>
                        <div id="employees-response" class="response-area">Click "Get Employees" to fetch sandbox employee data</div>
                    </div>

                    <!-- Payroll Tab -->
                    <div class="tab-pane fade" id="payroll" role="tabpanel">
                        <div class="quick-actions">
                            <button class="btn btn-success btn-sm" onclick="getPayrollPeriods()">
                                <i class="fas fa-download"></i> Get Payroll Periods
                            </button>
                            <button class="btn btn-warning btn-sm" onclick="finalizePayroll()">
                                <i class="fas fa-check"></i> Finalize Period (Demo)
                            </button>
                        </div>
                        <div id="payroll-response" class="response-area">Click "Get Payroll Periods" to fetch sandbox payroll data</div>
                    </div>

                    <!-- Pay Runs Tab -->
                    <div class="tab-pane fade" id="payruns" role="tabpanel">
                        <div class="quick-actions">
                            <button class="btn btn-success btn-sm" onclick="getPayRuns()">
                                <i class="fas fa-download"></i> Get Pay Runs
                            </button>
                            <button class="btn btn-primary btn-sm" onclick="createPayRun()">
                                <i class="fas fa-plus"></i> Create Pay Run
                            </button>
                        </div>
                        <div id="payruns-response" class="response-area">Click "Get Pay Runs" to fetch sandbox pay run data</div>
                    </div>

                    <!-- Leave Tab -->
                    <div class="tab-pane fade" id="leave" role="tabpanel">
                        <div class="quick-actions">
                            <button class="btn btn-success btn-sm" onclick="getLeaveTypes()">
                                <i class="fas fa-download"></i> Get Leave Types
                            </button>
                            <button class="btn btn-info btn-sm" onclick="getSandboxStats()">
                                <i class="fas fa-chart-bar"></i> Sandbox Stats
                            </button>
                            <button class="btn btn-danger btn-sm" onclick="resetSandbox()">
                                <i class="fas fa-redo"></i> Reset Sandbox
                            </button>
                        </div>
                        <div id="leave-response" class="response-area">Click "Get Leave Types" to fetch sandbox leave data</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Custom API Tester -->
        <div class="api-tester">
            <div class="api-tester-header">
                <h5><i class="fas fa-terminal"></i> Custom API Tester</h5>
            </div>
            <div class="api-tester-body">
                <div class="row mb-3">
                    <div class="col-md-2">
                        <select id="custom-method" class="form-select">
                            <option value="GET">GET</option>
                            <option value="POST">POST</option>
                            <option value="PUT">PUT</option>
                            <option value="DELETE">DELETE</option>
                        </select>
                    </div>
                    <div class="col-md-8">
                        <input type="text" id="custom-endpoint" class="form-control" placeholder="/sandbox/companies/DEMO001/employees">
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-primary w-100" onclick="makeCustomRequest()">
                            <i class="fas fa-paper-plane"></i> Send
                        </button>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <label class="form-label">Request Body (JSON):</label>
                        <textarea id="custom-body" class="form-control" rows="6" placeholder='{"key": "value"}'></textarea>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Response:</label>
                        <div id="custom-response" class="response-area" style="height: 150px;">Response will appear here</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Set base URL
        document.getElementById('base-url').textContent = window.location.origin + '/sandbox';
        
        let authToken = '';
        const baseUrl = '/sandbox';

        // Utility functions
        function formatJson(obj) {
            return JSON.stringify(obj, null, 2)
                .replace(/(".*?"):/g, '<span class="json-key">$1</span>:')
                .replace(/: (".*?")/g, ': <span class="json-string">$1</span>')
                .replace(/: (\d+)/g, ': <span class="json-number">$1</span>')
                .replace(/: (true|false)/g, ': <span class="json-boolean">$1</span>')
                .replace(/: (null)/g, ': <span class="json-null">$1</span>');
        }

        function updateResponse(elementId, data, isError = false) {
            const element = document.getElementById(elementId);
            const status = isError ? 'error' : 'success';
            const icon = isError ? 'fas fa-times-circle' : 'fas fa-check-circle';
            
            element.innerHTML = `
                <div class="mb-2">
                    <span class="status-indicator status-${status}"></span>
                    <i class="${icon}"></i> ${isError ? 'Error' : 'Success'}
                </div>
                <pre>${formatJson(data)}</pre>
            `;
        }

        async function makeRequest(method, endpoint, body = null) {
            const headers = {
                'Content-Type': 'application/json'
            };
            
            if (authToken) {
                headers['Authorization'] = `Bearer ${authToken}`;
            }

            const options = {
                method,
                headers
            };

            if (body) {
                options.body = JSON.stringify(body);
            }

            try {
                const response = await fetch(baseUrl + endpoint, options);
                const data = await response.json();
                return { data, status: response.status, ok: response.ok };
            } catch (error) {
                return { data: { error: error.message }, status: 500, ok: false };
            }
        }

        // Authentication functions
        async function authenticateUser() {
            const result = await makeRequest('POST', '/auth/login', {
                email: '<EMAIL>',
                password: 'demo123'
            });

            if (result.ok && result.data.success) {
                authToken = result.data.token;
                document.getElementById('jwt-token').value = authToken;
                updateResponse('auth-status', result.data);
            } else {
                updateResponse('auth-status', result.data, true);
            }
        }

        async function checkHealth() {
            const result = await makeRequest('GET', '/auth/health');
            updateResponse('auth-status', result.data, !result.ok);
        }

        // API testing functions
        async function getEmployees() {
            const result = await makeRequest('GET', '/companies/DEMO001/employees');
            updateResponse('employees-response', result.data, !result.ok);
        }

        async function getEmployee(employeeId) {
            const result = await makeRequest('GET', `/employees/${employeeId}`);
            updateResponse('employees-response', result.data, !result.ok);
        }

        async function getPayrollPeriods() {
            const result = await makeRequest('GET', '/companies/DEMO001/payroll-periods');
            updateResponse('payroll-response', result.data, !result.ok);
        }

        async function finalizePayroll() {
            const result = await makeRequest('POST', '/companies/DEMO001/payroll/finalize', {
                periodIds: ['sandbox_period_1']
            });
            updateResponse('payroll-response', result.data, !result.ok);
        }

        async function getPayRuns() {
            const result = await makeRequest('GET', '/companies/DEMO001/pay-runs');
            updateResponse('payruns-response', result.data, !result.ok);
        }

        async function createPayRun() {
            const result = await makeRequest('POST', '/companies/DEMO001/pay-runs', {
                periodIds: ['sandbox_period_1', 'sandbox_period_2'],
                description: 'Sandbox Test Pay Run'
            });
            updateResponse('payruns-response', result.data, !result.ok);
        }

        async function getLeaveTypes() {
            const result = await makeRequest('GET', '/companies/DEMO001/leave-types');
            updateResponse('leave-response', result.data, !result.ok);
        }

        async function getSandboxStats() {
            const result = await makeRequest('GET', '/stats');
            updateResponse('leave-response', result.data, !result.ok);
        }

        async function resetSandbox() {
            if (confirm('Are you sure you want to reset the sandbox data?')) {
                const result = await makeRequest('POST', '/reset');
                updateResponse('leave-response', result.data, !result.ok);
            }
        }

        // Custom API tester
        async function makeCustomRequest() {
            const method = document.getElementById('custom-method').value;
            const endpoint = document.getElementById('custom-endpoint').value;
            const bodyText = document.getElementById('custom-body').value;
            
            let body = null;
            if (bodyText.trim() && (method === 'POST' || method === 'PUT')) {
                try {
                    body = JSON.parse(bodyText);
                } catch (e) {
                    updateResponse('custom-response', { error: 'Invalid JSON in request body' }, true);
                    return;
                }
            }

            const result = await makeRequest(method, endpoint, body);
            updateResponse('custom-response', result.data, !result.ok);
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-authenticate on load
            setTimeout(authenticateUser, 1000);
        });
    </script>
</body>
</html>
