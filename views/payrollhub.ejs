<!DOCTYPE html>

<% const formatPayPeriod = (startDate, endDate) => { if (!startDate || !endDate)
return 'N/A'; return `${moment(startDate).format('DD MMM')} -
${moment(endDate).format('DD MMM YYYY')}`; }; const calculateDeductions =
(payslip) => {
  // ✅ PRODUCTION FIX: Calculate correct employee deductions (exclude SDL)
  // SDL is an employer contribution and should NEVER be deducted from employee pay
  const paye = Number(payslip.PAYE || 0);
  const uif = Number(payslip.UIF || 0);
  const sdl = Number(payslip.SDL || 0);

  // Calculate correct totalDeductions (PAYE + UIF only, exclude SDL)
  const correctTotalDeductions = paye + uif;

  // Validation: Check if database totalDeductions incorrectly includes SDL
  const storedTotalDeductions = Number(payslip.totalDeductions || 0);
  const expectedWithSDL = correctTotalDeductions + sdl;

  // If stored value includes SDL, use corrected value; otherwise use stored value
  const finalDeductions = (Math.abs(storedTotalDeductions - expectedWithSDL) < 0.01)
    ? correctTotalDeductions  // Use corrected value (exclude SDL)
    : storedTotalDeductions;  // Use stored value (already correct)

  return finalDeductions;
}; const
calculateGrossIncome = (payslip) => { let totalIncome = payslip.basicSalary ||
0; if (payslip.commission) totalIncome += payslip.commission; if
(payslip.lossOfIncome) totalIncome += payslip.lossOfIncome; if
(payslip.travelAllowance && payslip.travelAllowance.fixedAllowanceAmount) {
totalIncome += payslip.travelAllowance.fixedAllowanceAmount; } return
totalIncome; }; const getProgressStatusIcon = (status) => { switch
(status.toLowerCase()) { case 'draft': case 'pending': return 'ph-clock'; case
'processing': return 'ph-spinner'; case 'finalized': return 'ph-check-circle';
case 'released': return 'ph-upload'; default: return 'ph-circle'; } }; %>

<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="csrf-token" content="<%= csrfToken %>" />
    <meta name="company-code" content="<%= company.companyCode %>" />
    <meta name="description" content="PandaPayroll Hub - Manage your payroll workflow, review payslips, and process pay runs for <%= company.name %>" />
    <title>Payroll Hub | <%= company.name %></title>

    <!-- Consolidated Payroll Hub Styles -->
    <link rel="stylesheet" href="/css/payrollhub-desktop.css" />
    <link rel="stylesheet" href="/css/payrollhub-mobile.css" media="screen and (max-width: 768px)" />

    <!-- Preload Critical Resources for Speed Index -->
    <link rel="preload" href="/css/header.css" as="style" />
    <link rel="preload" href="/css/sidebar.css" as="style" />

    <!-- Eliminate render-blocking CSS - Load all non-critical -->
    <link rel="stylesheet" href="/css/header.css" media="print" onload="this.media='all'" />
    <link rel="stylesheet" href="/css/sidebar.css" media="print" onload="this.media='all'" />
    <link rel="stylesheet" href="/css/dashboard.css" media="print" onload="this.media='all'" />
    <link rel="stylesheet" href="/css/styles.css" media="print" onload="this.media='all'" />
    <link rel="stylesheet" href="/css/mobile-nav.css" media="print" onload="this.media='all'" />
    <link rel="stylesheet" href="/css/toast.css" media="print" onload="this.media='all'" />

    <!-- Loading spinner animation moved to consolidated CSS -->

    <!-- Fallback for browsers without JS -->
    <noscript>
      <link rel="stylesheet" href="/css/payrollhub-desktop.css" />
      <link rel="stylesheet" href="/css/payrollhub-mobile.css" />
      <link rel="stylesheet" href="/css/header.css" />
      <link rel="stylesheet" href="/css/sidebar.css" />
      <link rel="stylesheet" href="/css/dashboard.css" />
    </noscript>

    <!-- Resource Hints for Performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link rel="preconnect" href="https://cdnjs.cloudflare.com" />
    <link rel="dns-prefetch" href="https://unpkg.com" />

    <!-- Optimized Font Loading -->
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap"
      rel="stylesheet"
      media="print"
      onload="this.media='all'"
    />

    <!-- Defer Non-Critical Scripts with low priority -->
    <script defer src="https://unpkg.com/@phosphor-icons/web@2.0.3/dist/index.umd.js" fetchpriority="low"></script>
    <script defer src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js" fetchpriority="low"></script>

    <!-- Consolidated Custom Script -->
    <script src="/js/payrollhub-consolidated.js?v=<%= Date.now() %>"></script>
  </head>

  <body data-company-code="<%= company.companyCode %>">
    <!-- Payslips data passed via script tag to avoid HTML attribute encoding issues -->
    <script type="application/json" id="payslips-data"><%- JSON.stringify(typeof payslipsByFrequency !== 'undefined' ? payslipsByFrequency : {}) %></script>








    <div class="layout-wrapper">
      <%- include('partials/sidebar', { user: user, company: company }) %>
      <div class="content-wrapper">
        <%- include('partials/header', { user: user, company: company, currentPage: 'payroll' }) %>

        <main class="main-container">
          <!-- Page Header -->
          <div class="page-header">
            <div class="header-content">
              <h1>Payroll Hub</h1>
              <p class="description">
                Manage your payroll workflow, review payslips, and process pay runs
              </p>
            </div>
            <div class="company-badge">
              <i class="ph ph-buildings"></i>
              <span><%= company.name %></span>
            </div>
          </div>



          <!-- Dashboard Summary Cards -->
          <div class="dashboard-summary">
            <%
            let totalPendingPayslips = 0;
            let totalFinalizedPayslips = 0;
            let totalNetPayAmount = 0;

            // Use original pending payslips for dashboard stats
            const dashboardPayslips = typeof originalPendingPayslips !== 'undefined' ? originalPendingPayslips : pendingPayslips.filter(p => !p.isFinalized);

            if (dashboardPayslips && dashboardPayslips.length > 0) {
            totalPendingPayslips = dashboardPayslips.length;
            dashboardPayslips.forEach(payslip => { const netPay =
            payslip.calculations?.netPay || 0; totalNetPayAmount += netPay; }); }

            // Count finalized payslips separately
            if (typeof finalizedPayslips !== 'undefined' && finalizedPayslips.length > 0) {
            totalFinalizedPayslips = finalizedPayslips.length;
            finalizedPayslips.forEach(payslip => { const netPay =
            payslip.calculations?.netPay || 0; totalNetPayAmount += netPay; }); }

            const completedPayRuns = allPayRuns.filter(pr => pr.status ===
            'finalized' || pr.status === 'released' ).length; %>

            <!-- Pending Payslips Card -->
            <div class="summary-card">
              <div class="card-icon pending">
                <i class="ph ph-clock"></i>
              </div>
              <div class="card-details">
                <h4>Payslips to Review</h4>
                <div class="card-value"><%= totalPendingPayslips %></div>
                <p>Awaiting finalization and approval</p>
                <div class="card-status">
                  <i class="ph ph-circle"></i>
                  Requires Action
                </div>
              </div>
            </div>

            <!-- Finalized Payslips Card -->
            <div class="summary-card">
              <div class="card-icon finalized">
                <i class="ph ph-check-circle"></i>
              </div>
              <div class="card-details">
                <h4>Finalized Payslips</h4>
                <div class="card-value"><%= totalFinalizedPayslips %></div>
                <p>Ready for pay run creation</p>
                <div class="card-status">
                  <i class="ph ph-check-circle"></i>
                  Ready to Process
                </div>
              </div>
            </div>

            <!-- Total Net Pay Card -->
            <div class="summary-card">
              <div class="card-icon amount">
                <i class="ph ph-currency-circle-dollar"></i>
              </div>
              <div class="card-details">
                <h4>Total Net Pay</h4>
                <div class="card-value">R<%= totalNetPayAmount.toLocaleString('en-ZA', { minimumFractionDigits: 0, maximumFractionDigits: 0 }) %></div>
                <p>Combined payroll value</p>
                <div class="card-status">
                  <i class="ph ph-wallet"></i>
                  Current Period
                </div>
              </div>
            </div>

            <!-- Completed Pay Runs Card -->
            <div class="summary-card">
              <div class="card-icon payruns">
                <i class="ph ph-file-check"></i>
              </div>
              <div class="card-details">
                <h4>Pay Runs</h4>
                <div class="card-value"><%= completedPayRuns %></div>
                <p>Successfully processed</p>
                <div class="card-status">
                  <i class="ph ph-archive"></i>
                  Historical Data
                </div>
              </div>
            </div>
          </div>

          <!-- Pay Run Progress Section - Dashboard Style -->
          <div class="dashboard-section">
            <div class="chart-header">
              <div class="chart-title-section">
                <h3><i class="ph ph-list-checks"></i> Pay Run Progress</h3>
                <div class="chart-legend">
                  <span class="legend-item">
                    <i class="ph ph-calendar"></i> Track and manage your payroll workflow through each step of the process
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div class="progress-trackers">
            <%
            // Simplified: Group all payslips by period regardless of frequency
            const allPeriodGroups = {};
            if (pendingPayslips && pendingPayslips.length > 0) {
              pendingPayslips.forEach(payslip => {
                if (!payslip.startDate || !payslip.endDate) return;
                // Use business date strings for accurate date handling
                const startDateBusiness = payslip.startDateBusiness || moment(payslip.startDate).format('YYYY-MM-DD');
                const endDateBusiness = payslip.endDateBusiness || moment(payslip.endDate).format('YYYY-MM-DD');

                const periodKey = `${payslip.frequency}_${startDateBusiness}_${endDateBusiness}`;
                if (!allPeriodGroups[periodKey]) {
                  allPeriodGroups[periodKey] = {
                    frequency: payslip.frequency,
                    payslips: [],
                    startDate: payslip.startDate,
                    endDate: payslip.endDate,
                    startDateBusiness: startDateBusiness,
                    endDateBusiness: endDateBusiness,
                    periodLabel: payslip.frequency === 'monthly'
                      ? moment(payslip.startDate).format('MMMM YYYY')
                      : `${moment(payslip.startDate).format('DD MMM')} - ${moment(payslip.endDate).format('DD MMM YYYY')}`
                  };
                }
                allPeriodGroups[periodKey].payslips.push(payslip);
              });
            }

            // Sort periods by end date (most recent first)
            const sortedPeriodKeys = Object.keys(allPeriodGroups).sort((a, b) =>
              moment(allPeriodGroups[b].endDate) - moment(allPeriodGroups[a].endDate)
            );

            // Display only active periods (not fully completed)
            sortedPeriodKeys.forEach((periodKey, index) => {
              const periodGroup = allPeriodGroups[periodKey];
              const freq = periodGroup.frequency;
              const periodPayslips = periodGroup.payslips;

              // Check if this period has a completed pay run
              const periodPayRun = (typeof payRunsByFrequency !== 'undefined' &&
                payRunsByFrequency[freq] &&
                moment(payRunsByFrequency[freq].startDate).format('YYYY-MM-DD') === moment(periodGroup.startDate).format('YYYY-MM-DD') &&
                moment(payRunsByFrequency[freq].endDate).format('YYYY-MM-DD') === moment(periodGroup.endDate).format('YYYY-MM-DD'))
                ? payRunsByFrequency[freq] : null;

              const allFinalized = periodPayslips.every(p => p.isFinalized === true);

              // FIXED: Only hide periods when BOTH steps are completed (pay run exists AND is finalized/released)
              // Periods should remain visible during finalization and only hide when pay run is fully completed
              const isPayRunCompleted = periodPayRun && (periodPayRun.status === 'finalized' || periodPayRun.status === 'released');

              // Show periods that haven't completed both steps of the 2-step workflow
              if (!isPayRunCompleted) {
            %>
            <%
            // Simplified 2-step workflow calculation
            const periodId = `${freq}-${periodKey}`;

            let currentStep = 1;
            const hasUnfinalized = periodPayslips.some(p => p.isFinalized === false);

            // Step 1: Review payslips, Step 2: Create pay run
            if (allFinalized) {
              currentStep = 2; // Ready to create pay run
            } else if (hasUnfinalized) {
              currentStep = 1; // Still reviewing payslips
            }
            %>
            <div
              class="frequency-tracker"
              data-period-id="<%= periodId %>"
              data-start-date="<%= moment(periodGroup.startDate).format('YYYY-MM-DD') %>"
              data-end-date="<%= moment(periodGroup.endDate).format('YYYY-MM-DD') %>"
            >
              <div class="tracker-header">
                <div class="tracker-title">
                  <%= periodGroup.periodLabel %> (<%= freq.charAt(0).toUpperCase() + freq.slice(1) %>)
                </div>
                <div class="tracker-status">
                  <% if (allFinalized) { %>
                  <span class="status-badge finalized">
                    <i class="ph ph-check-circle"></i>
                    Ready for Pay Run
                  </span>
                  <% } else { %>
                  <span class="status-badge draft">
                    <i class="ph ph-clock"></i>
                    Pending Review
                  </span>
                  <% } %>
                </div>
              </div>

              <div class="enhanced-tracker-steps">
                <!-- Step 1: Review Payslips -->
                <div
                  class="enhanced-step <%= currentStep === 1 ? 'active' : (currentStep > 1 ? 'completed' : '') %>"
                  data-step="1"
                >
                  <div class="step-indicator">
                    <div class="step-number">
                      <% if (currentStep > 1) { %>
                      <i class="ph ph-check"></i>
                      <% } else { %>
                      1
                      <% } %>
                    </div>
                    <div class="step-connector"></div>
                  </div>
                  <div class="step-content-enhanced">
                    <div class="step-header">
                      <h4 class="step-title">
                        <i class="ph ph-file-text"></i>
                        Review Payslips
                      </h4>
                      <span class="step-status-badge <%= currentStep === 1 ? 'active' : (currentStep > 1 ? 'completed' : 'pending') %>">
                        <% if (currentStep === 1) { %>
                        <i class="ph ph-clock"></i> In Progress
                        <% } else if (currentStep > 1) { %>
                        <i class="ph ph-check-circle"></i> Completed
                        <% } else { %>
                        <i class="ph ph-circle"></i> Pending
                        <% } %>
                      </span>
                    </div>
                    <p class="step-description">
                      Review payslips for accuracy and finalize them
                    </p>
                    <div class="step-details">
                      <div class="detail-item">
                        <i class="ph ph-users"></i>
                        <span><%= periodPayslips.length %> employees</span>
                      </div>
                      <div class="detail-item">
                        <i class="ph ph-calendar"></i>
                        <span><%= periodGroup.periodLabel %></span>
                      </div>
                    </div>
                    <div class="step-actions">
                      <button
                        class="btn btn-primary review-payslips-btn"
                        data-frequency="<%= freq %>"
                        data-start-date="<%= moment(periodGroup.startDate).format('YYYY-MM-DD') %>"
                        data-end-date="<%= moment(periodGroup.endDate).format('YYYY-MM-DD') %>"
                        title="Open payslip review interface"
                        onclick="
                          const frequency = this.getAttribute('data-frequency');
                          const startDate = this.getAttribute('data-start-date');
                          const endDate = this.getAttribute('data-end-date');

                          console.log('Button clicked with:', { frequency, startDate, endDate });

                          if (typeof window.openPayslipReviewModal === 'function') {
                            console.log('Calling openPayslipReviewModal...');
                            window.openPayslipReviewModal(frequency, startDate, endDate);
                          } else {
                            console.error('openPayslipReviewModal function not found');
                          }
                        "
                      >
                        <i class="ph ph-eye"></i>
                        Review Payslips
                      </button>
                    </div>
                  </div>
                </div>

                <!-- Step 2: Create Pay Run -->
                <div
                  class="enhanced-step <%= currentStep === 2 ? 'active' : '' %>"
                  data-step="2"
                >
                  <div class="step-indicator">
                    <div class="step-number">2</div>
                  </div>
                  <div class="step-content-enhanced">
                    <div class="step-header">
                      <h4 class="step-title">
                        <i class="ph ph-plus-circle"></i>
                        Create Pay Run
                      </h4>
                      <span class="step-status-badge <%= currentStep < 2 ? 'pending' : 'active' %>">
                        <% if (currentStep < 2) { %>
                        <i class="ph ph-lock"></i> Locked
                        <% } else { %>
                        <i class="ph ph-play"></i> Ready
                        <% } %>
                      </span>
                    </div>
                    <p class="step-description">
                      Create pay run for finalized payslips
                    </p>
                    <div class="step-details">
                      <% if (currentStep >= 2) { %>
                      <div class="detail-item">
                        <i class="ph ph-check-circle"></i>
                        <span>All payslips finalized</span>
                      </div>
                      <div class="detail-item">
                        <i class="ph ph-users"></i>
                        <span><%= periodPayslips.length %> employees ready</span>
                      </div>
                      <% } else { %>
                      <div class="detail-item">
                        <i class="ph ph-info"></i>
                        <span>Complete Step 1 to unlock</span>
                      </div>
                      <% } %>
                    </div>
                    <div class="step-actions">
                      <% if (allFinalized) { %>
                      <button
                        class="btn btn-success create-payrun-btn"
                        data-frequency="<%= freq %>"
                        data-start-date="<%= periodGroup.startDateBusiness %>"
                        data-end-date="<%= periodGroup.endDateBusiness %>"
                        title="Create new pay run for this period"
                      >
                        <i class="ph ph-plus"></i>
                        Create Pay Run
                      </button>
                      <% } else { %>
                      <button
                        class="btn btn-disabled"
                        disabled
                        title="Complete Step 1 first"
                      >
                        <i class="ph ph-lock"></i>
                        Locked
                      </button>
                      <% } %>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <% } // End if (!periodPayRun) - only show active periods %>
            <% }); %>

            <%
            // Check if any periods were displayed (updated logic to match the display condition)
            const hasActivePeriods = sortedPeriodKeys.some(periodKey => {
              const periodGroup = allPeriodGroups[periodKey];
              const freq = periodGroup.frequency;
              const periodPayRun = (typeof payRunsByFrequency !== 'undefined' &&
                payRunsByFrequency[freq] &&
                moment(payRunsByFrequency[freq].startDate).format('YYYY-MM-DD') === moment(periodGroup.startDate).format('YYYY-MM-DD') &&
                moment(payRunsByFrequency[freq].endDate).format('YYYY-MM-DD') === moment(periodGroup.endDate).format('YYYY-MM-DD'))
                ? payRunsByFrequency[freq] : null;
              // FIXED: Use same logic as display condition - only hide when pay run is completed
              const isPayRunCompleted = periodPayRun && (periodPayRun.status === 'finalized' || periodPayRun.status === 'released');
              return !isPayRunCompleted;
            });

            if (!hasActivePeriods) { %>
            <div class="empty-state-progress">
              <div class="empty-state-content">
                <i class="ph ph-check-circle"></i>
                <h3>All periods completed</h3>
                <p>No active pay periods require attention. Completed pay runs can be found in the Pay Run History section below.</p>
              </div>
            </div>
            <% } %>
          </div>

          <!-- Pay Run History -->
          <div class="section-header">
            <h2>
              <i class="ph ph-clock-clockwise"></i>
              Pay Run History
            </h2>
            <p>View and manage previous pay runs</p>
          </div>

          <div class="pay-runs-history">
            <% if (allPayRuns && allPayRuns.length > 0) { %>
            <div class="table-container">
              <table class="modern-table">
                <thead>
                  <tr>
                    <th>Period</th>
                    <th>Pay Frequency</th>
                    <th>Status</th>
                    <th>Employees</th>
                    <th class="amount">Total Amount</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <% allPayRuns.forEach(payRun => { %>
                  <tr>
                    <td><%= moment(payRun.startDate).format('MMMM YYYY') %></td>
                    <td>
                      <span class="frequency-badge <%= payRun.frequency || 'monthly' %>">
                        <i class="ph ph-calendar"></i>
                        <%= (payRun.frequency || 'monthly').charAt(0).toUpperCase() + (payRun.frequency || 'monthly').slice(1) %>
                      </span>
                    </td>
                    <td>
                      <span class="status-badge <%= payRun.status %>">
                        <%= payRun.status.charAt(0).toUpperCase() +
                        payRun.status.slice(1) %>
                      </span>
                    </td>
                    <td><%= payRun.payslips.length %></td>
                    <td class="amount">
                      R<%= (payRun.totals?.netPay > 0 ? payRun.totals.netPay :
                      payRun.totalAmount > 0 ? payRun.totalAmount :
                      0).toLocaleString('en-ZA', { minimumFractionDigits: 2,
                      maximumFractionDigits: 2 }) %>
                    </td>
                    <td>
                      <div class="action-buttons">
                        <button
                          class="icon-button view-payrun-btn"
                          data-payrun-id="<%= payRun._id %>"
                          title="View Details"
                        >
                          <i class="ph ph-eye"></i>
                        </button>
                        <button
                          class="icon-button download-payslips-btn"
                          data-payrun-id="<%= payRun._id %>"
                          title="Download Payslips"
                        >
                          <i class="ph ph-download"></i>
                        </button>
                        <% if (payRun.status === 'finalized') { %>
                        <button
                          class="icon-button unfinalize-payrun-btn"
                          data-payrun-id="<%= payRun._id %>"
                          title="Unfinalize Pay Run"
                        >
                          <i class="ph ph-arrow-counter-clockwise"></i>
                        </button>
                        <% } %>
                        <% if (payRun.status === 'finalized') { %>
                        <button
                          class="icon-button open-bank-file-modal-btn"
                          data-payrun-id="<%= payRun._id %>"
                          title="Generate Bank File"
                        >
                          <i class="ph ph-file-text"></i>
                        </button>
                        <% } %> <% if (payRun.bankFile && payRun.status ===
                        'finalized') { %>
                        <button
                          class="icon-button upload-to-bank-btn"
                          data-payrun-id="<%= payRun._id %>"
                          title="Upload to Bank"
                        >
                          <i class="ph ph-upload"></i>
                        </button>
                        <% } %> <% if (payRun.status === 'finalized' ||
                        payRun.status === 'released') { %>
                        <button
                          class="icon-button release-to-self-service-btn"
                          data-payrun-id="<%= payRun._id %>"
                          title="Release to Self-Service"
                        >
                          <i class="ph ph-share-network"></i>
                        </button>
                        <% } %>

                        <!-- Xero Integration Button -->
                        <% if (xeroIntegrationActive && (payRun.status ===
                        'finalized' || payRun.status === 'released') &&
                        !payRun.xeroSynced) { %>
                        <button
                          class="icon-button send-to-xero-btn"
                          data-payrun-id="<%= payRun._id %>"
                          title="Send to Xero"
                        >
                          <i class="ph ph-arrow-square-out"></i>
                        </button>
                        <% } %>

                        <!-- QuickBooks Integration Button -->
                        <% if (quickbooksIntegrationActive && (payRun.status ===
                        'finalized' || payRun.status === 'released') &&
                        !payRun.quickbooksSynced) { %>
                        <button
                          class="icon-button send-to-quickbooks-btn"
                          data-payrun-id="<%= payRun._id %>"
                          title="Send to QuickBooks"
                        >
                          <i class="ph ph-arrow-square-out"></i>
                        </button>
                        <% } %>

                        <button
                          class="icon-button view-accounting-info-btn"
                          data-payrun-id="<%= payRun._id %>"
                          title="Accounting Info"
                        >
                          <i class="ph ph-calculator"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                  <% }); %>
                </tbody>
              </table>
            </div>
            <% } else { %>
            <div class="empty-state">
              <i class="ph ph-clipboard-text"></i>
              <h3>No pay run history</h3>
              <p>No pay run history available</p>
            </div>
            <% } %>
            <!-- Pagination -->
            <% if (allPayRuns && allPayRuns.length > 0) { %>
            <div class="pagination-container">
              <div class="pagination-info">
                Showing <%= (pagination.page - 1) * pagination.limit + 1 %>-<%= Math.min(pagination.page * pagination.limit, pagination.totalItems) %> of <%= pagination.totalItems %> pay runs
              </div>
              <div class="pagination-controls">
                <a href="/clients/<%= company.companyCode %>/payrollhub?page=1" class="pagination-link <%= pagination.page === 1 ? 'disabled' : '' %>">
                  <i class="ph ph-caret-double-left"></i>
                </a>
                <a href="/clients/<%= company.companyCode %>/payrollhub?page=<%= pagination.page - 1 %>" class="pagination-link <%= pagination.page === 1 ? 'disabled' : '' %>">
                  <i class="ph ph-caret-left"></i>
                </a>

                <%
                let startPage = Math.max(1, pagination.page - 2);
                let endPage = Math.min(pagination.totalPages, startPage + 4);
                if (endPage - startPage < 4 && pagination.totalPages > 4) {
                  startPage = Math.max(1, endPage - 4);
                }
                %>

                <% for(let i = startPage; i <= endPage; i++) { %>
                  <a href="/clients/<%= company.companyCode %>/payrollhub?page=<%= i %>" class="pagination-link <%= pagination.page === i ? 'active' : '' %>"><%= i %></a>
                <% } %>

                <a href="/clients/<%= company.companyCode %>/payrollhub?page=<%= pagination.page + 1 %>" class="pagination-link <%= pagination.page === pagination.totalPages ? 'disabled' : '' %>">
                  <i class="ph ph-caret-right"></i>
                </a>
                <a href="/clients/<%= company.companyCode %>/payrollhub?page=<%= pagination.totalPages %>" class="pagination-link <%= pagination.page === pagination.totalPages ? 'disabled' : '' %>">
                  <i class="ph ph-caret-double-right"></i>
                </a>
              </div>
            </div>
            <% } %>
            
          </div>
        </main>
      </div>
    </div>

    <!-- Bank File Modal - Moved outside layout structure -->
    <div id="bankFileModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h2>
            <i class="ph ph-file-text"></i>
            🔧 TEST: Generate Bank File Modal - UPDATED CODE
          </h2>
          <button class="close-modal" data-action="close-bank-file">
            <i class="ph ph-x"></i>
          </button>
        </div>
        <div class="modal-body">
          <p>Are you ready to generate the bank file for this pay run?</p>
          <p class="modal-description">
            This will create a file that you can upload to your bank for
            processing payments.
          </p>
        </div>
        <div class="modal-footer">
          <button
            class="btn btn-secondary"
            data-action="close-bank-file"
          >
            <i class="ph ph-x"></i>
            Cancel
          </button>
          <button
            class="btn btn-primary"
            data-action="generate-bank-file"
          >
            <i class="ph ph-file-text"></i>
            Generate
          </button>
        </div>
      </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
      <div class="loading-content">
        <div class="spinner"></div>
        <p id="loadingMessage">Loading...</p>
      </div>
    </div>

    <!-- Notification -->
    <div
      id="notification"
      class="notification"
class="notification-hidden"
    ></div>

    <!-- Payslip Review Modal -->
    <div id="payslipReviewModal" class="modal">
      <div class="modal-content large-modal">
        <div class="modal-header">
          <h2>
            <i class="ph ph-file-text"></i>
            Review Pending Payslips
          </h2>
          <button class="close-modal" data-action="close-payslip-review" title="Close Modal">
            <i class="ph ph-x"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="payslips-stats" id="payslips-stats">
            <div class="stat-item">
              <div class="stat-label">
                <i class="ph ph-users"></i>
                Total Employees
              </div>
              <div class="stat-value" id="stat-total-employees">0</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">
                <i class="ph ph-currency-circle-dollar"></i>
                Total Gross
              </div>
              <div class="stat-value currency" id="stat-total-gross">R0.00</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">
                <i class="ph ph-minus-circle"></i>
                Total Deductions
              </div>
              <div class="stat-value currency" id="stat-total-deductions">
                R0.00
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-label">
                <i class="ph ph-wallet"></i>
                Total Net
              </div>
              <div class="stat-value currency" id="stat-total-net">R0.00</div>
            </div>
          </div>

          <div id="modal-payslips-container">
            <div id="modal-frequency-payslips" class="modal-frequency-payslips">
              <div class="payslips-summary">
                <div class="summary-header">
                  <h3 id="modal-frequency-title">Payslips</h3>
                  <div class="summary-actions">
                    <button
                      id="finalize-selected"
                      class="btn btn-primary btn-hidden"
                      style="display: none;"
                    >
                      <i class="ph ph-check"></i> Finalize Selected
                    </button>
                  </div>
                </div>

                <div class="payslips-table-container">
                  <table class="payslips-table">
                    <thead>
                      <tr>
                        <th>
                          <div class="checkbox-wrapper">
                            <input
                              type="checkbox"
                              id="select-all"
                              class="select-all-checkbox"
                            />
                            <label for="select-all"></label>
                          </div>
                        </th>
                        <th>Employee</th>
                        <th>Pay Period</th>
                        <th class="amount text-right">Gross Amount</th>
                        <th class="amount text-right">Deductions</th>
                        <th class="amount text-right">Net Amount</th>
                        <th>Status</th>
                      </tr>
                    </thead>
                    <tbody id="frequency-payslips-body">
                      <!-- Payslips will be loaded dynamically -->
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>

          <div class="modal-footer">
            <button
              class="btn btn-secondary"
              data-action="close-payslip-review"
            >
              <i class="ph ph-x"></i>
              Close
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- View Payslip Modal -->
    <div id="viewPayslipModal" class="modal">
      <div class="modal-content payslip-modal">
        <div class="modal-header">
          <h2>Payslip Details</h2>
          <span class="close" data-action="close-view-payslip">&times;</span>
        </div>
        <div class="modal-body">
          <div class="frequency-tabs">
            <div class="tab-buttons">
              <% ['weekly', 'biweekly', 'monthly'].forEach((freq, index) => { %>
              <button
                class="tab-button <%= index === 0 ? 'active' : '' %>"
                data-frequency="<%= freq %>"
                data-action="open-modal-frequency"
              >
                <%= freq.charAt(0).toUpperCase() + freq.slice(1) %>
                <span class="badge"
                  ><%= overallStats[freq]?.unfinalizedCount || 0 %></span
                >
              </button>
              <% }); %>
            </div>
          </div>

          <div id="modal-payslips-container">
            <% ['weekly', 'biweekly', 'monthly'].forEach((freq, index) => { %>
            <div
              id="modal-<%= freq %>-payslips"
              class="modal-frequency-payslips <%= index === 0 ? 'show' : 'hide' %>"
            >
              <div class="payslips-summary">
                <div class="summary-header">
                  <h3>
                    <%= freq.charAt(0).toUpperCase() + freq.slice(1) %> Payslips
                  </h3>
                  <div class="summary-actions">
                    <button
                      id="finalize-selected-<%= freq %>"
                      class="btn btn-primary"
                      data-action="finalize-selected"
                      data-frequency="<%= freq %>"
                    >
                      <i class="ph ph-check"></i> Finalize Selected
                    </button>
                  </div>
                </div>

                <div class="payslips-table-container">
                  <table class="payslips-table">
                    <thead>
                      <tr>
                        <th>
                          <div class="checkbox-wrapper">
                            <input
                              type="checkbox"
                              id="select-all-<%= freq %>"
                              class="select-all-checkbox"
                              data-frequency="<%= freq %>"
                            />
                            <label for="select-all-<%= freq %>"></label>
                          </div>
                        </th>
                        <th>Employee</th>
                        <th>Pay Period</th>
                        <th class="amount text-right">Gross Amount</th>
                        <th class="amount text-right">Deductions</th>
                        <th class="amount text-right">Net Amount</th>
                        <th>Status</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody id="<%= freq %>-payslips-body">
                      <% if (payslipsByFrequency && payslipsByFrequency[freq] &&
                      payslipsByFrequency[freq].length > 0) { %> <%
                      payslipsByFrequency[freq].forEach(payslip => { %>
                      <tr data-payslip-id="<%= payslip._id %>">
                        <td>
                          <div class="checkbox-wrapper">
                            <input
                              type="checkbox"
                              id="payslip-<%= payslip._id %>"
                              class="payslip-checkbox"
                              data-payslip-id="<%= payslip._id %>"
                              data-employee-id="<%= payslip.employee._id %>"
                              data-period-end-date="<%= payslip.endDateBusiness || moment.utc(payslip.endDate).format('YYYY-MM-DD') %>"
                              data-frequency="<%= freq %>"
                              <%= payslip.isFinalized ? 'disabled checked' : '' %>
                            >
                            <label for="payslip-<%= payslip._id %>"></label>
                          </div>
                        </td>
                        <td>
                          <%= payslip.employee ? `${payslip.employee.firstName}
                          ${payslip.employee.lastName}` : 'Unknown Employee' %>
                        </td>
                        <td>
                          <%= formatPayPeriod(payslip.payPeriod?.startDate,
                          payslip.payPeriod?.endDate) %>
                        </td>
                        <td class="amount text-right">
                          <% const totalIncome = calculateGrossIncome(payslip);
                          %> <%= formatCurrency ? formatCurrency(totalIncome) :
                          totalIncome.toFixed(2) %>
                        </td>
                        <td class="amount text-right">
                          <% const totalDeductions =
                          calculateDeductions(payslip); %> <%= formatCurrency ?
                          formatCurrency(totalDeductions) :
                          totalDeductions.toFixed(2) %>
                        </td>
                        <td class="amount text-right">
                          <% const netPay = totalIncome - totalDeductions; %>
                          <%= formatCurrency ? formatCurrency(netPay) :
                          netPay.toFixed(2) %>
                        </td>
                        <td><%= payslip.status %></td>
                        <td>
                          <div class="action-buttons">
                            <button
                              class="icon-button"
                              data-action="view-payslip"
                              data-payslip-id="<%= payslip._id %>"
                              title="View Payslip"
                            >
                              <i class="ph ph-eye"></i>
                            </button>
                          </div>
                        </td>
                      </tr>
                      <% }); %> <% } else { %>
                      <tr>
                        <td colspan="8" class="no-data">
                          No pending payslips for <%= freq %> frequency
                        </td>
                      </tr>
                      <% } %>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
            <% }); %>
          </div>

          <div class="modal-footer">
            <button
              class="btn btn-secondary"
              data-action="close-payslip-review"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Surgical Fix: Add Create Pay Run Event Listener -->
    <script>
      // Wait for DOM and consolidated script to load
      document.addEventListener('DOMContentLoaded', function() {
        // Small delay to ensure consolidated script is fully loaded
        setTimeout(function() {
          console.log('🔧 SURGICAL FIX: Adding Create Pay Run event listeners...');

          // Find all create pay run buttons
          const createPayRunButtons = document.querySelectorAll('.create-payrun-btn');
          console.log(`Found ${createPayRunButtons.length} create pay run buttons`);

          // Add event listener to each button
          createPayRunButtons.forEach((button, index) => {
            button.addEventListener('click', function(e) {
              e.preventDefault();

              // Extract data attributes
              const frequency = this.dataset.frequency;
              const startDate = this.dataset.startDate;
              const endDate = this.dataset.endDate;

              console.log(`🎯 Create Pay Run clicked:`, { frequency, startDate, endDate });

              // Validate required data
              if (!frequency || !startDate || !endDate) {
                console.error('❌ Missing required data attributes:', { frequency, startDate, endDate });
                alert('Error: Missing required data for pay run creation');
                return;
              }

              // Debug: Check frontend payslip data before creating pay run
              console.log('🔍 DEBUGGING: Frontend payslip data check...');

              // Get payslips data from the page
              const payslipsDataElement = document.getElementById('payslips-data');
              if (payslipsDataElement) {
                try {
                  const payslipsData = JSON.parse(payslipsDataElement.textContent);
                  const monthlyPayslips = payslipsData.monthly || [];

                  console.log('📊 Frontend payslips data:', {
                    totalMonthlyPayslips: monthlyPayslips.length,
                    payslips: monthlyPayslips.map(p => ({
                      id: p._id,
                      employee: p.employee?.firstName + ' ' + p.employee?.lastName,
                      startDate: p.startDateBusiness || p.startDate,
                      endDate: p.endDateBusiness || p.endDate,
                      isFinalized: p.isFinalized,
                      grossPay: p.grossPay,
                      netPay: p.netPay
                    }))
                  });

                  // Check if any payslips match the date range
                  const matchingPayslips = monthlyPayslips.filter(p => {
                    const pStart = p.startDateBusiness || p.startDate;
                    const pEnd = p.endDateBusiness || p.endDate;
                    return pStart >= startDate && pEnd <= endDate;
                  });

                  console.log('🎯 Payslips matching date range:', {
                    dateRange: `${startDate} to ${endDate}`,
                    matchingCount: matchingPayslips.length,
                    matchingPayslips: matchingPayslips.map(p => ({
                      id: p._id,
                      employee: p.employee?.firstName + ' ' + p.employee?.lastName,
                      dateRange: `${p.startDateBusiness || p.startDate} to ${p.endDateBusiness || p.endDate}`,
                      isFinalized: p.isFinalized
                    }))
                  });

                  // Check finalization status
                  const finalizedCount = matchingPayslips.filter(p => p.isFinalized === true).length;
                  const unfinalizedCount = matchingPayslips.filter(p => p.isFinalized === false).length;

                  console.log('📋 Finalization status:', {
                    totalMatching: matchingPayslips.length,
                    finalized: finalizedCount,
                    unfinalized: unfinalizedCount,
                    allFinalized: finalizedCount === matchingPayslips.length && matchingPayslips.length > 0
                  });

                  // Warning if no finalized payslips
                  if (finalizedCount === 0 && matchingPayslips.length > 0) {
                    console.warn('⚠️ WARNING: No finalized payslips found in frontend data!');
                    console.warn('💡 SOLUTION: Payslips need to be finalized first using "Review Payslips" → "Finalize Selected"');

                    alert('⚠️ Payslips Not Finalized\n\nBefore creating a pay run, you need to:\n1. Click "Review Payslips"\n2. Select the payslips\n3. Click "Finalize Selected"\n4. Then try "Create Pay Run" again');
                    return;
                  }

                } catch (error) {
                  console.error('❌ Error parsing payslips data:', error);
                }
              }

              // Check if createPayRun function is available
              if (typeof window.createPayRun === 'function') {
                console.log('✅ Calling createPayRun function with params:', { frequency, startDate, endDate });
                window.createPayRun(frequency, startDate, endDate);
              } else {
                console.error('❌ createPayRun function not available');
                alert('Error: Pay run creation function not available. Please refresh the page.');
              }
            });

            console.log(`✅ Event listener added to button ${index + 1}`);
          });

          console.log('🎉 SURGICAL FIX: Create Pay Run event listeners added successfully');
        }, 100); // Small delay to ensure consolidated script is loaded
      });
    </script>

    <!-- SURGICAL FIX: Test and verify payrun action buttons -->
    <script>
      // Wait for DOM and consolidated script to load
      document.addEventListener('DOMContentLoaded', function() {
        // Small delay to ensure consolidated script is fully loaded
        setTimeout(function() {
          console.log('🔧 SURGICAL FIX: Testing payrun action button functionality...');

          // Test if functions are available
          console.log('viewPayRun function available:', typeof window.viewPayRun === 'function');
          console.log('downloadPayslips function available:', typeof window.downloadPayslips === 'function');
          console.log('generateBankFile function available:', typeof window.generateBankFile === 'function');
          console.log('uploadToBank function available:', typeof window.uploadToBank === 'function');
          console.log('releaseToSelfService function available:', typeof window.releaseToSelfService === 'function');
          console.log('sendToXero function available:', typeof window.sendToXero === 'function');
          console.log('viewAccountingInfo function available:', typeof window.viewAccountingInfo === 'function');
          console.log('unfinalizePayRun function available:', typeof window.unfinalizePayRun === 'function');

          // Count buttons
          const viewButtons = document.querySelectorAll('.view-payrun-btn');
          const downloadButtons = document.querySelectorAll('.download-payslips-btn');
          const bankFileButtons = document.querySelectorAll('.open-bank-file-modal-btn');
          const uploadButtons = document.querySelectorAll('.upload-to-bank-btn');
          const releaseButtons = document.querySelectorAll('.release-to-self-service-btn');
          const xeroButtons = document.querySelectorAll('.send-to-xero-btn');
          const accountingButtons = document.querySelectorAll('.view-accounting-info-btn');
          const unfinalizeButtons = document.querySelectorAll('.unfinalize-payrun-btn');

          console.log(`Found buttons:`, {
            view: viewButtons.length,
            download: downloadButtons.length,
            bankFile: bankFileButtons.length,
            upload: uploadButtons.length,
            release: releaseButtons.length,
            xero: xeroButtons.length,
            accounting: accountingButtons.length,
            unfinalize: unfinalizeButtons.length
          });

          // Test button click handlers
          viewButtons.forEach((button, index) => {
            console.log(`View button ${index + 1}:`, {
              hasDataAttribute: !!button.getAttribute('data-payrun-id'),
              payrunId: button.getAttribute('data-payrun-id')
            });
          });

          downloadButtons.forEach((button, index) => {
            console.log(`Download button ${index + 1}:`, {
              hasDataAttribute: !!button.getAttribute('data-payrun-id'),
              payrunId: button.getAttribute('data-payrun-id')
            });
          });

          console.log('✅ SURGICAL FIX: Payrun action button test completed');

          // Test Xero integration logic
          console.log('\n🔧 XERO INTEGRATION TEST:');
          console.log('xeroIntegrationActive:', <%= xeroIntegrationActive ? 'true' : 'false' %>);

          <% if (typeof allPayRuns !== 'undefined' && allPayRuns && allPayRuns.length > 0) { %>
          console.log('Pay runs with Xero sync status:');
          <% allPayRuns.forEach((payRun, index) => { %>
          console.log('Pay Run <%= index + 1 %>:', {
            id: '<%= payRun._id %>',
            status: '<%= payRun.status %>',
            xeroSynced: <%= payRun.xeroSynced ? 'true' : 'false' %>,
            shouldShowXeroButton: <%= (xeroIntegrationActive && (payRun.status === 'finalized' || payRun.status === 'released') && !payRun.xeroSynced) ? 'true' : 'false' %>
          });
          <% }); %>
          <% } %>

          console.log('✅ XERO INTEGRATION TEST: Completed');

          // Test EFT Configuration for Bank File Generation
          console.log('\n🏦 EFT CONFIGURATION TEST:');
          <% if (typeof company !== 'undefined' && company) { %>
          console.log('Company ID:', '<%= company._id %>');

          // Check if EFT details are available (this would be passed from backend)
          <% if (typeof eftDetails !== 'undefined' && eftDetails) { %>
          console.log('EFT Configuration found:', {
            format: '<%= eftDetails.eftFormat || "Not configured" %>',
            bank: '<%= eftDetails.bank || "Not set" %>',
            hasAccountNumber: <%= eftDetails.accountNumber ? 'true' : 'false' %>,
            hasBranchCode: <%= eftDetails.branchCode ? 'true' : 'false' %>
          });
          <% } else { %>
          console.log('⚠️ EFT Configuration: Not found - bank file generation may fail');
          <% } %>
          <% } %>

          console.log('✅ EFT CONFIGURATION TEST: Completed');
        }, 200); // Slightly longer delay to ensure everything is loaded
      });
    </script>

  </body>
</html>
