<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/themes/prism.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/plugins/line-numbers/prism-line-numbers.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
        }
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: 280px;
            background: #f8f9fa;
            border-right: 1px solid #dee2e6;
            overflow-y: auto;
            z-index: 1000;
            padding: 20px 0;
        }
        .content {
            margin-left: 280px;
            padding: 20px 40px;
            max-width: calc(100% - 280px);
        }
        .sidebar .nav-link {
            color: #495057;
            padding: 8px 20px;
            border-radius: 0;
            font-size: 14px;
        }
        .sidebar .nav-link:hover {
            background-color: #e9ecef;
            color: #007bff;
        }
        .sidebar .nav-link.active {
            background-color: #007bff;
            color: white;
        }
        .sidebar .nav-section {
            font-weight: 600;
            color: #6c757d;
            padding: 15px 20px 5px;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .endpoint {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin: 20px 0;
            overflow: hidden;
        }
        .endpoint-header {
            background: #e9ecef;
            padding: 15px 20px;
            border-bottom: 1px solid #dee2e6;
        }
        .endpoint-method {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: 600;
            font-size: 12px;
            margin-right: 10px;
        }
        .method-get { background: #d4edda; color: #155724; }
        .method-post { background: #d1ecf1; color: #0c5460; }
        .method-put { background: #fff3cd; color: #856404; }
        .method-delete { background: #f8d7da; color: #721c24; }
        .method-patch { background: #e2e3e5; color: #383d41; }
        .endpoint-url {
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
            color: #495057;
        }
        .endpoint-body {
            padding: 20px;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            overflow-x: auto;
        }
        .code-block pre {
            margin: 0;
            font-size: 13px;
            line-height: 1.4;
        }
        .parameter-table {
            margin: 15px 0;
        }
        .parameter-table th {
            background: #f8f9fa;
            font-weight: 600;
            font-size: 13px;
        }
        .parameter-table td {
            font-size: 13px;
            vertical-align: top;
        }
        .required {
            color: #dc3545;
            font-weight: 600;
        }
        .optional {
            color: #6c757d;
        }
        h1 {
            color: #007bff;
            border-bottom: 3px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
        h2 {
            color: #495057;
            margin-top: 40px;
            margin-bottom: 20px;
            padding-bottom: 8px;
            border-bottom: 2px solid #e9ecef;
        }
        h3 {
            color: #6c757d;
            margin-top: 30px;
            margin-bottom: 15px;
        }
        h4 {
            color: #495057;
            margin-top: 25px;
            margin-bottom: 10px;
            font-size: 18px;
        }
        .alert-info {
            background-color: #e7f3ff;
            border-color: #b3d7ff;
            color: #004085;
        }
        .badge {
            font-size: 11px;
            padding: 4px 8px;
        }
        .toc {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .toc ul {
            margin: 0;
            padding-left: 20px;
        }
        .toc li {
            margin: 5px 0;
        }
        .toc a {
            text-decoration: none;
            color: #007bff;
        }
        .toc a:hover {
            text-decoration: underline;
        }
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }
            .sidebar.show {
                transform: translateX(0);
            }
            .content {
                margin-left: 0;
                max-width: 100%;
                padding: 20px;
            }
            .mobile-toggle {
                display: block;
                position: fixed;
                top: 20px;
                left: 20px;
                z-index: 1001;
                background: #007bff;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
            }
        }
        .mobile-toggle {
            display: none;
        }
        .logo {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #dee2e6;
            margin-bottom: 10px;
        }
        .logo h5 {
            margin: 0;
            color: #007bff;
            font-weight: 600;
        }
        .response-example {
            margin: 15px 0;
        }
        .response-example h6 {
            margin-bottom: 10px;
            color: #495057;
            font-weight: 600;
        }
        .status-code {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: 600;
            margin-right: 8px;
        }
        .status-200 { background: #d4edda; color: #155724; }
        .status-201 { background: #d4edda; color: #155724; }
        .status-400 { background: #f8d7da; color: #721c24; }
        .status-401 { background: #f8d7da; color: #721c24; }
        .status-403 { background: #f8d7da; color: #721c24; }
        .status-404 { background: #f8d7da; color: #721c24; }
        .status-500 { background: #f8d7da; color: #721c24; }

        /* JSON syntax highlighting */
        .json-key { color: #0066cc; font-weight: 600; }
        .json-string { color: #009900; }
        .json-number { color: #cc6600; }
        .json-boolean { color: #990099; font-weight: 600; }
        .json-null { color: #999999; font-style: italic; }

        /* Improved code blocks */
        .code-block {
            position: relative;
            margin: 20px 0;
        }

        .code-block pre {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 20px;
            overflow-x: auto;
            font-size: 14px;
            line-height: 1.5;
        }

        /* Table improvements */
        .table {
            margin: 20px 0;
            font-size: 14px;
        }

        .table th {
            background-color: #f8f9fa;
            border-top: 1px solid #dee2e6;
            font-weight: 600;
            color: #495057;
        }

        /* Endpoint styling improvements */
        .endpoint {
            margin: 30px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .endpoint-body {
            background: white;
        }

        /* Navigation improvements */
        .nav-link {
            transition: all 0.2s ease;
        }

        .nav-link:hover {
            transform: translateX(5px);
        }

        /* Content spacing */
        .content h1 {
            margin-top: 0;
        }

        .content h2 {
            margin-top: 50px;
            margin-bottom: 25px;
        }

        .content h3 {
            margin-top: 35px;
            margin-bottom: 20px;
        }

        .content h4 {
            margin-top: 25px;
            margin-bottom: 15px;
        }

        /* Loading state */
        .loading-spinner {
            text-align: center;
            padding: 50px;
        }

        .spinner-border {
            width: 3rem;
            height: 3rem;
        }
    </style>
</head>
<body>
    <button class="mobile-toggle" onclick="toggleSidebar()">☰</button>
    
    <div class="sidebar" id="sidebar">
        <div class="logo">
            <h5>🐼 PandaPayroll</h5>
            <small class="text-muted">API Documentation</small>
        </div>
        
        <nav class="nav flex-column">
            <div class="nav-section">Overview</div>
            <a class="nav-link" href="#overview">Introduction</a>
            <a class="nav-link" href="#authentication">Authentication</a>
            <a class="nav-link" href="#base-urls">Base URLs</a>
            <a class="nav-link" href="#response-formats">Response Formats</a>
            <a class="nav-link" href="#error-handling">Error Handling</a>
            
            <div class="nav-section">API Endpoints</div>
            <a class="nav-link" href="#auth-apis">Authentication</a>
            <a class="nav-link" href="#employee-apis">Employee Management</a>
            <a class="nav-link" href="#payroll-apis">Payroll Management</a>
            <a class="nav-link" href="#payslip-apis">Payslip Management</a>
            <a class="nav-link" href="#payrun-apis">Pay Run Management</a>
            <a class="nav-link" href="#leave-apis">Leave Management</a>
            <a class="nav-link" href="#eft-apis">EFT & Banking</a>
            <a class="nav-link" href="#calendar-apis">Payroll Calendar</a>
            <a class="nav-link" href="#session-apis">Session Management</a>
            <a class="nav-link" href="#internal-apis">Internal APIs</a>
            
            <div class="nav-section">Reference</div>
            <a class="nav-link" href="#data-models">Data Models</a>
            <a class="nav-link" href="#webhooks">Webhooks</a>
            <a class="nav-link" href="#file-upload">File Upload</a>
            <a class="nav-link" href="#rate-limiting">Rate Limiting</a>
            <a class="nav-link" href="#examples">SDK Examples</a>
        </nav>
    </div>

    <div class="content">
        <div id="api-content">
            <!-- Content will be loaded here via JavaScript -->
            <div class="loading-spinner">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <h4 class="mt-3">Loading API Documentation...</h4>
                <p class="text-muted">Please wait while we load the comprehensive PandaPayroll API documentation.</p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function toggleSidebar() {
            document.getElementById('sidebar').classList.toggle('show');
        }

        // Load and render the markdown content
        async function loadApiDocumentation() {
            try {
                const response = await fetch('/API_DOCUMENTATION.md');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                const markdown = await response.text();

                // Convert markdown to HTML using a simpler approach
                const html = convertMarkdownToHtml(markdown);

                // Process the HTML to add Bootstrap classes and styling
                const processedHtml = processApiHtml(html);

                document.getElementById('api-content').innerHTML = processedHtml;

                // Add smooth scrolling for navigation links
                addSmoothScrolling();

                // Highlight code blocks
                highlightCodeBlocks();

            } catch (error) {
                console.error('Error loading API documentation:', error);
                // Try to load a fallback or show embedded content
                loadFallbackDocumentation();
            }
        }

        // Simple markdown to HTML converter
        function convertMarkdownToHtml(markdown) {
            let html = markdown;

            // Headers
            html = html.replace(/^### (.*$)/gim, '<h3>$1</h3>');
            html = html.replace(/^## (.*$)/gim, '<h2>$1</h2>');
            html = html.replace(/^# (.*$)/gim, '<h1>$1</h1>');
            html = html.replace(/^#### (.*$)/gim, '<h4>$1</h4>');
            html = html.replace(/^##### (.*$)/gim, '<h5>$1</h5>');
            html = html.replace(/^###### (.*$)/gim, '<h6>$1</h6>');

            // Code blocks
            html = html.replace(/```(\w+)?\n([\s\S]*?)```/g, function(match, lang, code) {
                const language = lang || 'text';
                return `<div class="code-block"><pre><code class="language-${language}">${escapeHtml(code.trim())}</code></pre></div>`;
            });

            // Inline code
            html = html.replace(/`([^`]+)`/g, '<code class="bg-light px-1 rounded">$1</code>');

            // Bold
            html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

            // Italic
            html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');

            // Links
            html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>');

            // Lists
            html = html.replace(/^\* (.+)$/gm, '<li>$1</li>');
            html = html.replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>');
            html = html.replace(/^\d+\. (.+)$/gm, '<li>$1</li>');

            // Paragraphs
            html = html.replace(/\n\n/g, '</p><p>');
            html = '<p>' + html + '</p>';

            // Clean up empty paragraphs
            html = html.replace(/<p><\/p>/g, '');
            html = html.replace(/<p>(<h[1-6]>)/g, '$1');
            html = html.replace(/(<\/h[1-6]>)<\/p>/g, '$1');
            html = html.replace(/<p>(<div)/g, '$1');
            html = html.replace(/(<\/div>)<\/p>/g, '$1');
            html = html.replace(/<p>(<ul>)/g, '$1');
            html = html.replace(/(<\/ul>)<\/p>/g, '$1');

            return html;
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function processApiHtml(html) {
            // Add Bootstrap classes to tables
            html = html.replace(/<table>/g, '<table class="table table-striped table-bordered">');

            // Add IDs to headers for navigation
            html = html.replace(/<h([1-6])>([^<]+)<\/h[1-6]>/g, function(match, level, text) {
                const id = text.toLowerCase()
                    .replace(/[^a-z0-9\s&-]/g, '') // Remove special chars except spaces, &, -
                    .replace(/\s+/g, '-') // Replace spaces with hyphens
                    .replace(/&/g, '') // Remove ampersands
                    .replace(/-+/g, '-') // Replace multiple hyphens with single
                    .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
                return `<h${level} id="${id}">${text}</h${level}>`;
            });

            // Add endpoint styling
            html = html.replace(/#### (GET|POST|PUT|DELETE|PATCH) ([^\n]+)/g, function(match, method, endpoint) {
                const methodClass = `method-${method.toLowerCase()}`;
                return `
                    <div class="endpoint">
                        <div class="endpoint-header">
                            <span class="endpoint-method ${methodClass}">${method}</span>
                            <span class="endpoint-url">${endpoint}</span>
                        </div>
                        <div class="endpoint-body">
                `;
            });

            // Close endpoint divs (this is a simple approach)
            html = html.replace(/(<div class="endpoint">[\s\S]*?)<h[1-6]/g, '$1</div></div><h');

            return html;
        }

        function highlightCodeBlocks() {
            document.querySelectorAll('pre code').forEach((block) => {
                // Simple syntax highlighting for JSON
                if (block.className.includes('json') || block.textContent.trim().startsWith('{')) {
                    block.innerHTML = formatJson(block.textContent);
                }
            });
        }

        function formatJson(text) {
            try {
                const obj = JSON.parse(text);
                return JSON.stringify(obj, null, 2)
                    .replace(/(".*?"):/g, '<span class="json-key">$1</span>:')
                    .replace(/: (".*?")/g, ': <span class="json-string">$1</span>')
                    .replace(/: (\d+)/g, ': <span class="json-number">$1</span>')
                    .replace(/: (true|false)/g, ': <span class="json-boolean">$1</span>')
                    .replace(/: (null)/g, ': <span class="json-null">$1</span>');
            } catch (e) {
                return text;
            }
        }

        function addSmoothScrolling() {
            // Map navigation links to actual section IDs
            const navMapping = {
                'overview': 'pandapayroll-api-documentation',
                'authentication': 'authentication',
                'base-urls': 'base-urls',
                'response-formats': 'common-response-formats',
                'error-handling': 'error-handling',
                'auth-apis': 'authentication-apis',
                'employee-apis': 'employee-management-apis',
                'payroll-apis': 'payroll-management-apis',
                'payslip-apis': 'payslip-management-apis',
                'payrun-apis': 'pay-run-management-apis',
                'leave-apis': 'leave-management-apis',
                'eft-apis': 'eft--banking-apis',
                'calendar-apis': 'payroll-calendar-apis',
                'session-apis': 'session-management-apis',
                'internal-apis': 'internal-apis',
                'data-models': 'data-models',
                'webhooks': 'webhooks',
                'file-upload': 'file-upload-apis',
                'rate-limiting': 'rate-limiting',
                'examples': 'sdk-and-integration-examples'
            };

            document.querySelectorAll('.nav-link[href^="#"]').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href').substring(1);
                    const actualId = navMapping[targetId] || targetId;
                    const targetElement = document.getElementById(actualId);

                    if (targetElement) {
                        targetElement.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });

                        // Update active nav link
                        document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
                        this.classList.add('active');

                        // Close sidebar on mobile
                        if (window.innerWidth <= 768) {
                            document.getElementById('sidebar').classList.remove('show');
                        }
                    } else {
                        console.warn(`Target element not found: ${actualId}`);
                        // Try to find any element that contains the text
                        const allHeaders = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
                        const matchingHeader = Array.from(allHeaders).find(h =>
                            h.textContent.toLowerCase().includes(targetId.replace(/-/g, ' '))
                        );
                        if (matchingHeader) {
                            matchingHeader.scrollIntoView({
                                behavior: 'smooth',
                                block: 'start'
                            });
                        }
                    }
                });
            });
        }

        // Fallback documentation content
        function loadFallbackDocumentation() {
            const fallbackContent = `
                <h1 id="pandapayroll-api-documentation">PandaPayroll API Documentation</h1>

                <div class="alert alert-warning">
                    <h4><i class="fas fa-exclamation-triangle"></i> Documentation Loading Issue</h4>
                    <p>We're having trouble loading the full documentation from the markdown file. Here's a basic overview:</p>
                </div>

                <h2 id="overview">Overview</h2>
                <p>PandaPayroll is a comprehensive payroll management system designed for South African businesses. The API provides endpoints for managing employees, calculating payroll, generating payslips, creating pay runs, and handling compliance requirements.</p>

                <h3>Key Features</h3>
                <ul>
                    <li>Employee lifecycle management</li>
                    <li>South African tax calculations (PAYE, UIF, SDL)</li>
                    <li>Payslip generation and finalization</li>
                    <li>Pay run creation and management</li>
                    <li>Leave management</li>
                    <li>EFT file generation</li>
                    <li>Compliance calendar management</li>
                </ul>

                <h2 id="authentication">Authentication</h2>
                <p>PandaPayroll uses JWT (JSON Web Tokens) for API authentication alongside session-based authentication for web interface.</p>

                <h3>JWT Authentication</h3>
                <p>All API endpoints require authentication via JWT tokens passed in the Authorization header:</p>
                <div class="code-block">
                    <pre><code>Authorization: Bearer &lt;jwt_token&gt;</code></pre>
                </div>

                <h2 id="base-urls">Base URLs</h2>
                <ul>
                    <li><strong>Production:</strong> <code>https://payroll.pss-group.co.za</code></li>
                    <li><strong>Development:</strong> <code>http://localhost:3002</code></li>
                    <li><strong>Sandbox:</strong> <code>http://localhost:3002/sandbox</code></li>
                </ul>

                <h2 id="authentication-apis">Authentication APIs</h2>

                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="endpoint-method method-post">POST</span>
                        <span class="endpoint-url">/api/auth/login</span>
                    </div>
                    <div class="endpoint-body">
                        <p>Authenticate user and receive JWT token.</p>
                        <h6>Request Body:</h6>
                        <div class="code-block">
                            <pre><code class="language-json">{
  "email": "<EMAIL>",
  "password": "password123"
}</code></pre>
                        </div>
                        <h6>Response:</h6>
                        <div class="code-block">
                            <pre><code class="language-json">{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "user_id",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe"
  }
}</code></pre>
                        </div>
                    </div>
                </div>

                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="endpoint-method method-get">GET</span>
                        <span class="endpoint-url">/api/auth/test</span>
                    </div>
                    <div class="endpoint-body">
                        <p>Test protected route access.</p>
                        <p><strong>Headers:</strong> <code>Authorization: Bearer &lt;token&gt;</code></p>
                    </div>
                </div>

                <h2 id="employee-management-apis">Employee Management APIs</h2>

                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="endpoint-method method-get">GET</span>
                        <span class="endpoint-url">/api/employee/:employeeId/hourly-status</span>
                    </div>
                    <div class="endpoint-body">
                        <p>Get employee hourly paid status.</p>
                    </div>
                </div>

                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="endpoint-method method-post">POST</span>
                        <span class="endpoint-url">/api/employees/reinstate</span>
                    </div>
                    <div class="endpoint-body">
                        <p>Reinstate terminated employees.</p>
                    </div>
                </div>

                <h2 id="sandbox">Sandbox Environment</h2>
                <div class="alert alert-info">
                    <h4><i class="fas fa-flask"></i> Try the API Sandbox</h4>
                    <p>Test the PandaPayroll API in a safe environment with pre-loaded demo data.</p>
                    <p><strong>Sandbox URL:</strong> <a href="/sandbox" target="_blank">http://localhost:3002/sandbox</a></p>
                    <p><strong>Demo Credentials:</strong></p>
                    <ul>
                        <li>Email: <code><EMAIL></code></li>
                        <li>Password: <code>demo123</code></li>
                        <li>Company Code: <code>DEMO001</code></li>
                    </ul>
                </div>

                <h2 id="support">Support</h2>
                <p>For complete API documentation, please ensure the markdown file is accessible or contact support.</p>
                <p><strong>Troubleshooting:</strong></p>
                <ul>
                    <li>Refresh the page to retry loading</li>
                    <li>Check that the API_DOCUMENTATION.md file exists in the project root</li>
                    <li>Verify the server route for serving the markdown file</li>
                </ul>
            `;

            document.getElementById('api-content').innerHTML = fallbackContent;
            addSmoothScrolling();
            highlightCodeBlocks();
        }

        // Load documentation when page loads
        document.addEventListener('DOMContentLoaded', loadApiDocumentation);
        
        // Handle window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768) {
                document.getElementById('sidebar').classList.remove('show');
            }
        });
    </script>
</body>
</html>
