<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Edit Basic Salary | <%= company.name %></title>

    <!-- Modern Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />

    <!-- Styles -->
    <link rel="stylesheet" href="/css/styles.css" />
    <link rel="stylesheet" href="/css/dashboard.css" />
    <link rel="stylesheet" href="/css/header.css" />
    <link rel="stylesheet" href="/css/mobile-header.css" />
    <link rel="stylesheet" href="/css/mobile-nav.css" />
    <link rel="stylesheet" href="/css/sidebar.css" />
    <link rel="stylesheet" href="/css/addBasicSalary-improved.css" />
    <link rel="stylesheet" href="/css/addBasicSalary.css" />


    <!-- Icons -->
    <script src="https://unpkg.com/@phosphor-icons/web"></script>
  </head>
  <body data-company-code="<%= typeof companyCode !== 'undefined' ? companyCode : '' %>">
    <div class="layout-wrapper">
      <%- include('partials/sidebar', { user: user, company: company }) %>
      <div class="content-wrapper">
        <%- include('partials/header', { user: user }) %>

        <main class="main-container">
          <!-- Main Form Section -->
          <div class="dashboard-card modern">
            <form
              id="basicSalaryForm"
              action="/clients/<%= companyCode %>/employeeProfile/<%= employee._id %>/update-basic-salary"
              method="POST"
              class="modern-form"
              data-redirect-url="/clients/<%= companyCode %>/employeeProfile/<%= employee._id %><%= currentPeriod && currentPeriod.endDate ? '?selectedMonth=' + encodeURIComponent((currentPeriod.endDateBusiness || moment.utc(currentPeriod.endDate).format('YYYY-MM-DD'))) : '' %>"
            >
              <!-- Hidden inputs -->
              <input type="hidden" name="_csrf" value="<%= csrfToken %>" />
              <input type="hidden" name="employeeId" value="<%= employee._id %>" />
              <input type="hidden" name="relevantDate" value="<%= (currentPeriod && currentPeriod.endDate) ? (currentPeriod.endDateBusiness || moment.utc(currentPeriod.endDate).format('YYYY-MM-DD')) : moment.utc().format('YYYY-MM-DD') %>" />
              <input type="hidden" name="companyId" value="<%= (employee.company && employee.company._id) ? employee.company._id : (employee.company || company._id) %>" />
              <input type="hidden" name="companyCode" value="<%= companyCode %>" />

              <!-- Pay Period Info -->
              <div class="form-section">
                <div class="section-header">
                  <h3><i class="ph ph-calendar"></i> Pay Period Information</h3>
                </div>
                <div class="form-group">
                  <label class="form-label">Pay Period</label>
                  <div class="period-display">
                    <% if (currentPeriod && currentPeriod.startDate && currentPeriod.endDate) { %>
                      <%
                      // Use BusinessDate fields for timezone-independent display
                      const periodStartDateBusiness = currentPeriod.startDateBusiness || moment.utc(currentPeriod.startDate).format('YYYY-MM-DD');
                      const periodEndDateBusiness = currentPeriod.endDateBusiness || moment.utc(currentPeriod.endDate).format('YYYY-MM-DD');
                      %>
                      <span class="period-text">
                        <i class="ph ph-calendar-blank"></i>
                        <%= moment(periodStartDateBusiness).format('DD MMMM YYYY') %> - <%= moment(periodEndDateBusiness).format('DD MMMM YYYY') %>
                      </span>
                    <% } else { %>
                      <span class="period-text no-period">
                        <i class="ph ph-calendar-x"></i>
                        No active pay period
                      </span>
                    <% } %>
                  </div>
                </div>
              </div>

              <!-- Salary Configuration -->
              <div class="form-section">
                <div class="section-header">
                  <h3><i class="ph ph-currency-circle-dollar"></i> Salary Configuration</h3>
                </div>

                <div class="switch-container">
                  <label class="switch">
                    <input
                      type="checkbox"
                      id="hourlyPaid"
                      name="hourlyPaid"
                      <%= (employee.regularHours && employee.regularHours.hourlyPaid) ? 'checked' : '' %>
                    >
                    <span class="slider round"></span>
                  </label>
                  <span class="switch-label">Hourly Paid</span>
                </div>

                <div class="form-group">
                  <label for="basicSalary" id="rateLabel" class="form-label">
                    <%= (employee.regularHours && employee.regularHours.hourlyPaid) ? 'Hourly Rate' : 'Basic Salary' %>
                  </label>
                  <div class="input-with-icon">
                    <i class="ph ph-currency-zar"></i>
                    <input
                      type="number"
                      name="basicSalary"
                      id="basicSalary"
                      value="<%= (currentPeriod && currentPeriod.basicSalary !== undefined) ? currentPeriod.basicSalary : 0 %>"
                      required
                      placeholder="<%= (employee.regularHours && employee.regularHours.hourlyPaid) ? 'Enter hourly rate' : 'Enter monthly salary' %>"
                      onclick="this.select();"
                      class="form-input"
                    />
                  </div>
                </div>

                <!-- Hourly Pay Options -->
                <div id="dontAutoPay" class="checkbox-option" style="display: <%= (employee.regularHours && employee.regularHours.hourlyPaid) ? 'block' : 'none' %>">
                  <label class="checkbox-container">
                    <input 
                      type="checkbox" 
                      id="dontAutoPayPublicHolidays" 
                      name="dontAutoPayPublicHolidays"
                      <%= payroll && payroll.dontAutoPayPublicHolidays ? 'checked' : '' %>
                    >
                    <span class="checkmark"></span>
                    <span>Don't Auto-Pay Public Holidays</span>
                  </label>
                </div>

                <!-- Additional Hours Options -->
                <div id="additionalHours" class="checkbox-option" style="display: <%= !(employee.regularHours && employee.regularHours.hourlyPaid) ? 'block' : 'none' %>">
                  <label class="checkbox-container">
                    <input 
                      type="checkbox" 
                      id="paidForAdditionalHours" 
                      name="paidForAdditionalHours"
                      onchange="handleAdditionalHoursCheckbox()"
                      <%= (payroll && payroll.additionalHours) ? 'checked' : '' %>
                    >
                    <span class="checkmark"></span>
                    <span>Paid for Additional Hours</span>
                  </label>

                  <div id="overrideRateContainer" class="nested-options" style="display: none;">
                    <label class="checkbox-container">
                      <input 
                        type="checkbox" 
                        id="overrideCalculatedHourlyRate" 
                        name="overrideCalculatedHourlyRate"
                        onchange="handleOverrideRateCheckbox()"
                      >
                      <span class="checkmark"></span>
                      <span>Override Calculated Hourly Rate</span>
                    </label>

                    <div id="rateOverrideDiv" class="rate-override" style="display: none;">
                      <div class="input-group">
                        <label for="hourlyRateOverride">Override Rate</label>
                        <div class="input-with-icon">
                          <i class="ph ph-currency-zar"></i>
                          <input
                            type="number"
                            id="hourlyRateOverride"
                            name="hourlyRateOverride"
                            step="0.01"
                            placeholder="Enter override rate"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Form Actions -->
              <div class="form-actions">
                <a href="/clients/<%= companyCode %>/employeeProfile/<%= employee._id %><%= currentPeriod && currentPeriod.endDate ? '?selectedMonth=' + encodeURIComponent((currentPeriod.endDateBusiness || moment.utc(currentPeriod.endDate).format('YYYY-MM-DD'))) : '' %>" class="btn btn-secondary">
                  <i class="ph ph-x"></i>
                  Cancel
                </a>
                <button type="submit" class="btn btn-primary button primary">
                  <i class="ph ph-check"></i>
                  Save Changes
                </button>
              </div>
            </form>
          </div>
        </main>
      </div>
    </div>

    <!-- Load the enhanced form handler with animations -->
    <script src="/js/addBasicSalary.js"></script>

    <script>
      function handleHourlyPaidCheckbox() {
        const hourlyPaidCheckbox = document.getElementById("hourlyPaid");
        const additionalHoursCheckbox = document.getElementById("additionalHours");
        const rateLabel = document.getElementById("rateLabel");
        const autoPayCheckbox = document.getElementById("dontAutoPay");

        if (hourlyPaidCheckbox.checked) {
          rateLabel.textContent = "Hourly Rate";
          autoPayCheckbox.style.display = "block";
          additionalHoursCheckbox.style.display = "none";
        } else {
          rateLabel.textContent = "Basic Salary";
          autoPayCheckbox.style.display = "none";
          additionalHoursCheckbox.style.display = "block";
        }
      }

      function handleAdditionalHoursCheckbox() {
        const additionalHoursCheckbox = document.getElementById("paidForAdditionalHours");
        const overrideRateContainer = document.getElementById("overrideRateContainer");

        overrideRateContainer.style.display = additionalHoursCheckbox.checked ? "block" : "none";
        if (!additionalHoursCheckbox.checked) {
          document.getElementById("overrideCalculatedHourlyRate").checked = false;
          document.getElementById("rateOverrideDiv").style.display = "none";
        }
      }

      function handleOverrideRateCheckbox() {
        const overrideRateCheckbox = document.getElementById("overrideCalculatedHourlyRate");
        const rateOverrideDiv = document.getElementById("rateOverrideDiv");

        rateOverrideDiv.style.display = overrideRateCheckbox.checked ? "block" : "none";
      }
    </script>
    <script src="/js/addBasicSalary.js"></script>
  </body>
</html>
