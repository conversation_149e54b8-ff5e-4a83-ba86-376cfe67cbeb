<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Add New Employee | <%= company.name %></title>

    <!-- Critical CSS - Inline for Dashboard Design Consistency -->
    <style>
      /* Clean Purple & Orange Color System */
      :root {
        --primary-color: #8b5cf6;        /* Purple */
        --primary-light: #c4b5fd;        /* Light purple */
        --primary-lighter: #f3e8ff;      /* Very light purple */
        --accent-color: #f59e0b;         /* Orange */
        --accent-light: #fbbf24;         /* Light orange */
        --accent-lighter: #fef3c7;       /* Very light orange */
        --background-color: #f9fafb;
        --surface-color: #ffffff;
        --text-primary: #111827;
        --text-secondary: #6b7280;
        --border-color: #e5e7eb;
        --success-color: #10b981;
        --warning-color: #f59e0b;
        --space-4: 1rem;
        --space-6: 1.5rem;
        --radius-lg: 0.75rem;
      }

      /* Base Styles */
      *, *::before, *::after {
        box-sizing: border-box;
      }

      body {
        margin: 0;
        padding: 0;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        background: var(--background-color);
        color: var(--text-primary);
        line-height: 1.6;
      }

      /* Layout Structure */
      .main-container {
        flex: 1;
        padding: var(--space-6);
        max-width: 100%;
        overflow-x: hidden;
        margin-top: 2rem; /* Prevent header overlap */
      }

      /* Employee Container - Dashboard Card Style */
      .employee-container {
        background: var(--surface-color);
        border-radius: var(--radius-lg);
        border: 1px solid var(--border-color);
        overflow: hidden;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
      }

      /* Action Tabs - Dashboard Style */
      .action-tabs {
        display: flex;
        background: var(--surface-color);
        border-bottom: 1px solid var(--border-color);
        padding: 0;
        overflow-x: auto;
      }

      .tab-button {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: var(--space-4) var(--space-6);
        border: none;
        background: transparent;
        color: var(--text-secondary);
        font-family: inherit;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        border-bottom: 2px solid transparent;
        white-space: nowrap;
      }

      .tab-button:hover {
        color: var(--primary-color);
        background: var(--primary-lighter);
      }

      .tab-button.active {
        color: var(--primary-color);
        border-bottom-color: var(--primary-color);
        background: var(--primary-lighter);
      }

      .tab-button i {
        font-size: 16px;
      }
    </style>

    <!-- Modern Font - Dashboard Consistency -->
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <!-- Phosphor Icons -->
    <script src="https://unpkg.com/@phosphor-icons/web"></script>

    <!-- Styles -->
    <link rel="stylesheet" href="/css/styles.css" />
    <link rel="stylesheet" href="/css/onboarding.css" />
    <link rel="stylesheet" href="/css/add-employee-details.css" />
    <link rel="stylesheet" href="/css/header.css" />
    <link rel="stylesheet" href="/css/mobile-header.css" />
    <link rel="stylesheet" href="/css/mobile-nav.css" />
    <link rel="stylesheet" href="/css/mobile-add-employee-details.css" />
    <link rel="stylesheet" href="/css/sidebar.css" />
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css"
    />
    <link rel="stylesheet" href="/css/toggle-switch.css" />

    <meta name="csrf-token" content="<%= csrfToken %>" />
    <!-- Additional Dashboard-Style CSS -->
    <style>
      /* Form Sections - Dashboard Card Style */
      .employee-sections-grid {
        padding: var(--space-6);
      }

      .employee-section {
        background: var(--surface-color);
        border-radius: var(--radius-lg);
        border: 1px solid var(--border-color);
        overflow: hidden;
      }

      /* Step Indicator - Clean Solid Colors */
      .step-indicator {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: var(--space-4);
        padding: var(--space-6);
        background: var(--primary-lighter);
        border-bottom: 1px solid var(--border-color);
        margin-bottom: 0;
      }

      .step-number {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;
        padding: var(--space-4);
        border-radius: var(--radius-lg);
        background: var(--surface-color);
        border: 2px solid var(--border-color);
        color: var(--text-secondary);
        font-weight: 600;
        font-size: 16px;
        min-width: 120px;
        text-align: center;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      }

      .step-number.active {
        border-color: var(--primary-color);
        background: var(--primary-color);
        color: white;
        box-shadow: 0 4px 12px rgba(139, 92, 246, 0.25);
        transform: translateY(-2px);
      }

      .step-label {
        font-size: 12px;
        font-weight: 500;
        line-height: 1.2;
      }

      /* Form Sections */
      .form-section {
        padding: var(--space-6);
        border-bottom: 1px solid var(--border-color);
      }

      .form-section:last-child {
        border-bottom: none;
      }

      .section-title {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: var(--space-6);
        padding-bottom: var(--space-4);
        border-bottom: 1px solid var(--border-color);
      }

      .section-title i {
        font-size: 20px;
        color: var(--primary-color);
      }

      .section-title h3 {
        font-size: 18px;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
      }

      /* Form Grid - Dashboard Style */
      .form-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: var(--space-6);
        margin-top: var(--space-4);
      }

      /* Form Groups */
      .form-group {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }

      .form-group label {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        font-weight: 500;
        color: var(--text-primary);
        margin-bottom: 4px;
      }

      .form-group label i {
        font-size: 16px;
        color: var(--primary-color);
      }

      /* Input Wrapper */
      .input-wrapper {
        position: relative;
      }

      .input-wrapper select,
      .input-wrapper input {
        width: 100%;
        padding: 12px 16px;
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        font-size: 14px;
        font-family: inherit;
        background: var(--surface-color);
        color: var(--text-primary);
        transition: all 0.2s ease;
      }

      .input-wrapper select:focus,
      .input-wrapper input:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
      }

      /* Toggle Container - Clean Solid Style */
      .toggle-container {
        display: flex;
        align-items: center;
        gap: var(--space-4);
        padding: var(--space-4);
        background: var(--accent-lighter);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        margin-bottom: var(--space-4);
      }

      .toggle-text {
        flex: 1;
        font-size: 14px;
        font-weight: 500;
        color: var(--text-primary);
      }

      .settings-link {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        border-radius: 6px;
        background: var(--surface-color);
        border: 1px solid var(--border-color);
        color: var(--text-secondary);
        text-decoration: none;
        transition: all 0.2s ease;
      }

      .settings-link:hover {
        color: var(--primary-color);
        border-color: var(--primary-color);
        background: var(--primary-lighter);
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(139, 92, 246, 0.15);
      }

      /* Modern Checkbox - Clean Solid Style */
      .modern-checkbox {
        padding: var(--space-4);
        background: var(--primary-lighter);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
      }

      .checkbox-wrapper {
        display: flex;
        align-items: center;
        gap: 12px;
        cursor: pointer;
        position: relative;
      }

      .checkbox-label {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        font-weight: 500;
        color: var(--text-primary);
        flex: 1;
      }

      .checkbox-label i {
        font-size: 16px;
        color: var(--primary-color);
      }

      /* Responsive Design */
      @media (max-width: 768px) {
        .main-container {
          padding: var(--space-4);
        }

        .form-grid {
          grid-template-columns: 1fr;
          gap: var(--space-4);
        }

        .step-indicator {
          flex-direction: column;
          gap: var(--space-4);
        }

        .step-number {
          min-width: 100px;
        }

        .action-tabs {
          flex-wrap: wrap;
        }

        .tab-button {
          flex: 1;
          min-width: 120px;
        }
      }
    </style>
  </head>

  <body>
    <%- include('partials/header', { user: user, company: company, currentPage: 'employees' }) %>
    <nav><%- include('partials/sidebar') %></nav>

    <main class="main-container">
      <div class="employee-container">
        <div class="action-tabs">
          <button
            class="tab-button"
            onclick="window.location.href='/clients/<%= companyCode %>/employeeManagement'"
          >
            <i class="ph ph-users-three"></i>
            Employee List
          </button>
          <button class="tab-button active">
            <i class="ph ph-user-plus"></i>
            Add New Employee
          </button>
          <button class="tab-button" onclick="redirectToLeaveOverview()">
            <i class="ph ph-calendar"></i>
            Leave Overview
          </button>
          <button
            class="tab-button"
            onclick="window.location.href='/clients/<%= companyCode %>/employeeManagement/selfService'"
          >
            <i class="ph ph-gear"></i>
            Self Service
          </button>
          <button
            class="tab-button"
            onclick="window.location.href='/clients/<%= companyCode %>/employeeManagement/bulkActions'"
          >
            <i class="ph ph-list-plus"></i>
            Bulk Actions
          </button>
        </div>

        <div class="employee-sections-grid">
          <!-- Form Section -->
          <div class="employee-section">
            <div class="step-indicator" data-current-step="1">
              <div class="step-number active" data-step="1">
                1
                <span class="step-label">Employment Type</span>
              </div>
              <div class="step-number" data-step="2">
                2
                <span class="step-label">Personal Details</span>
              </div>
              <div class="step-number" data-step="3">
                3
                <span class="step-label">Work Schedule</span>
              </div>
            </div>

            <form
              id="addEmployeeForm"
              method="POST"
              action="/clients/<%= companyCode %>/employeeManagement/addNew/submit"
            >
              <!-- Add CSRF token input with proper error handling -->
              <% if (typeof csrfToken !== 'undefined') { %>
              <input type="hidden" name="_csrf" value="<%= csrfToken %>" />
              <% } %>
              <!-- Rest of your form content -->
              <!-- Step 1: Employment Type -->
              <div class="form-step active" data-step="1">
                <!-- Employment Type Section -->
                <div class="form-section">
                  <div class="section-title">
                    <i class="ph ph-briefcase"></i>
                    <h3>Employment Type</h3>
                  </div>

                  <div class="form-grid">
                    <!-- Working Hours -->
                    <div class="form-group">
                      <label for="workingHours">
                        <i class="ph ph-clock"></i>
                        Working Hours
                      </label>
                      <div class="input-wrapper">
                        <select id="workingHours" name="workingHours" required>
                          <option value="">Select working hours</option>
                          <option value="Full Time">Full Time</option>
                          <option value="Part Time">
                            Less Than 22 Hours a Week
                          </option>
                        </select>
                      </div>
                    </div>

                    <!-- Director Checkbox -->
                    <div class="form-group">
                      <div class="modern-checkbox">
                        <label class="checkbox-wrapper">
                          <input
                            type="checkbox"
                            id="directorCheckbox"
                            name="isDirector"
                          />
                          <span class="checkbox-label">
                            <i class="ph ph-user-circle-gear"></i>
                            Director
                          </span>
                          <span class="checkmark"></span>
                        </label>
                      </div>
                    </div>

                    <!-- Director Type -->
                    <div
                      class="form-group"
                      id="directorTypeGroup"
                      style="display: none"
                    >
                      <label for="typeOfDirector">
                        <i class="ph ph-identification-card"></i>
                        Type of Director
                      </label>
                      <div class="input-wrapper">
                        <select id="typeOfDirector" name="typeOfDirector">
                          <option value="">Select director type</option>
                          <option value="Executive Director/ Member of a CC">
                            Executive Director/ Member of a CC
                          </option>
                          <option value="RSA Resident Non-Executive Director">
                            RSA Resident Non-Executive Director
                          </option>
                          <option value="Non Resident Non-Executive Director">
                            Non Resident Non-Executive Director
                          </option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Employee Number Section -->
                <div class="form-section" id="employeeNumberContainer">
                  <div class="section-title">
                    <i class="ph ph-identification-badge"></i>
                    <h3>Employee Number</h3>
                  </div>

                  <div class="toggle-container">
                    <span class="toggle-text">Auto-generate employee numbers</span>
                    <label class="toggle-switch" data-tooltip="Configure employee number settings in Company Settings">
                      <input
                        type="checkbox"
                        id="employeeNumberMode"
                        <% if (typeof employeeNumberSettings === 'undefined' || !employeeNumberSettings || employeeNumberSettings.mode !== 'manual') { %>checked<% } %>
                      >
                      <span class="toggle-slider"></span>
                    </label>
                    <a href="/clients/<%= companyCode %>/settings/employee/employee-numbers" class="settings-link" title="Configure employee number settings">
                      <i class="ph ph-gear"></i>
                    </a>
                  </div>

                  <% if (typeof employeeNumberSettings !== 'undefined' && employeeNumberSettings && employeeNumberSettings.mode === 'manual') { %>
                    <div class="form-group">
                      <label for="companyEmployeeNumber">
                        <i class="ph ph-hash"></i>
                        Employee Number *
                      </label>
                      <div class="input-wrapper">
                        <input
                          type="text"
                          id="companyEmployeeNumber"
                          name="companyEmployeeNumber"
                          placeholder="Enter employee number"
                          pattern="[A-Za-z0-9\-]+"
                          title="Can contain letters, numbers and hyphens"
                          required
                        />
                      </div>
                    </div>
                  <% } %>
                  <div id="autoGenerateInfo" class="info-message" style="display: <%= (typeof employeeNumberSettings === 'undefined' || !employeeNumberSettings || employeeNumberSettings.mode !== 'manual') ? 'block' : 'none' %>">
                    <i class="ph ph-info"></i>
                    <span>Employee number will be automatically generated upon submission</span>
                  </div>
                </div>

                <!-- Navigation Buttons -->
                <div class="form-actions">
                  <div>
                    <button type="button" class="btn btn-primary next-step">
                      Next
                      <i class="ph ph-arrow-right"></i>
                    </button>
                  </div>
                </div>
              </div>

              <!-- Step 2: Personal Information -->
              <div class="form-step" data-step="2">
                <h3 class="section-header">
                  <i class="ph ph-user"></i>
                  Personal Information
                </h3>

                <!-- Basic Information -->
                <div class="form-section">
                  <h4 class="subsection-header">Basic Details</h4>
                  <div class="form-grid">
                    <div class="form-group">
                      <label for="firstName">First Names</label>
                      <input
                        type="text"
                        id="firstName"
                        name="firstName"
                        required
                        placeholder="Enter first names"
                      />
                    </div>

                    <div class="form-group">
                      <label for="lastName">Last Name</label>
                      <input
                        type="text"
                        id="lastName"
                        name="lastName"
                        required
                        placeholder="Enter last name"
                      />
                    </div>

                    <div class="form-group">
                      <label for="dob">Date of Birth</label>
                      <input
                        type="date"
                        id="dob"
                        name="dob"
                        class="flatpickr"
                        value=""
                        placeholder="Select date"
                      />
                    </div>
                  </div>
                </div>

                <!-- Contact Information -->
                <div class="form-section">
                  <h4 class="subsection-header">Contact Details</h4>
                  <div class="form-grid">
                    <div class="form-group">
                      <label for="email">Email Address</label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        required
                        placeholder="Enter email address"
                      />
                    </div>

                    <div class="form-group">
                      <label for="personalDetails.mobileNumber">Mobile Number</label>
                      <div class="input-icon">
                        <i class="ph ph-phone"></i>
                        <input
                          type="tel"
                          id="personalDetails.mobileNumber"
                          name="personalDetails.mobileNumber"
                          placeholder="Enter mobile number"
                          pattern="[0-9]{10,15}"
                          title="Please enter a valid phone number (10-15 digits)"
                          required
                        />
                      </div>
                      <small class="form-hint">Format: 10-15 digits (e.g., 0831234567)</small>
                    </div>
                  </div>
                </div>

                <!-- Identification - Modernized -->
                <div class="form-section identification-section">
                  <div class="section-title">
                    <i class="ph ph-identification-card"></i>
                    <h4>Identification</h4>
                  </div>

                  <div class="identification-container">
                    <!-- ID Type Selection -->
                    <div class="id-type-group">
                      <div class="form-group">
                        <label for="idType">
                          <i class="ph ph-cards"></i>
                          Identification Type *
                        </label>
                        <div class="select-wrapper">
                          <select id="idType" name="idType" required>
                            <option value="">Select ID Type</option>
                            <option value="rsa">South African ID Document</option>
                            <option value="passport">International Passport</option>
                            <option value="other">No Identification Document</option>
                          </select>
                          <i class="ph ph-caret-down"></i>
                        </div>
                        <small class="form-hint">Choose the type of identification document for this employee</small>
                      </div>
                    </div>

                    <!-- Dynamic ID Fields -->
                    <div class="id-fields-container">
                      <div id="idNumberContainer" class="form-group id-field-group" style="display: none;">
                        <label for="idNumber">
                          <i class="ph ph-identification-card"></i>
                          South African ID Number *
                        </label>
                        <div class="input-icon">
                          <i class="ph ph-identification-card"></i>
                          <input
                            type="text"
                            id="idNumber"
                            name="idNumber"
                            placeholder="Enter 13-digit ID number"
                            pattern="[0-9]{13}"
                            title="South African ID number must be 13 digits"
                            maxlength="13"
                          />
                        </div>
                        <small class="form-hint">Enter the 13-digit South African identity number (format: YYMMDDGGGGSAZ)</small>
                      </div>

                      <div id="passportNumberContainer" class="form-group id-field-group" style="display: none;">
                        <label for="passportNumber">
                          <i class="ph ph-passport"></i>
                          Passport Number *
                        </label>
                        <div class="input-icon">
                          <i class="ph ph-passport"></i>
                          <input
                            type="text"
                            id="passportNumber"
                            name="passportNumber"
                            placeholder="Enter passport number"
                            pattern="[A-Za-z0-9]{6,12}"
                            title="Passport number should be 6-12 characters"
                            maxlength="12"
                          />
                        </div>
                        <small class="form-hint">Enter the passport number as shown on the document</small>
                      </div>
                    </div>

                    <!-- Tax Number -->
                    <div class="tax-number-group">
                      <div class="form-group">
                        <label for="incomeTaxNumber">
                          <i class="ph ph-receipt"></i>
                          Income Tax Number
                        </label>
                        <div class="input-icon">
                          <i class="ph ph-receipt"></i>
                          <input
                            type="text"
                            id="incomeTaxNumber"
                            name="incomeTaxNumber"
                            placeholder="Enter tax number (optional)"
                            pattern="[0-9]{10}"
                            title="Tax number should be 10 digits"
                            maxlength="10"
                          />
                        </div>
                        <small class="form-hint">Optional: Enter the employee's SARS income tax number if available</small>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Address Information -->
                <div class="form-section">
                  <h4 class="subsection-header">Residential Address</h4>
                  <div class="form-grid">
                    <div class="form-group">
                      <label for="streetAddress">Street Address</label>
                      <input
                        type="text"
                        id="streetAddress"
                        name="streetAddress"
                        placeholder="Enter street address"
                      />
                    </div>

                    <div class="form-group">
                      <label for="suburb">Suburb</label>
                      <input
                        type="text"
                        id="suburb"
                        name="suburb"
                        placeholder="Enter suburb"
                      />
                    </div>

                    <div class="form-group">
                      <label for="city">City</label>
                      <input
                        type="text"
                        id="city"
                        name="city"
                        placeholder="Enter city"
                      />
                    </div>

                    <div class="form-group">
                      <label for="postalCode">Postal Code</label>
                      <input
                        type="text"
                        id="postalCode"
                        name="postalCode"
                        placeholder="Enter postal code"
                      />
                    </div>
                  </div>
                </div>

                <!-- Banking Details Section - Reorganized -->
                <div class="form-section banking-details-section">
                  <div class="section-title">
                    <i class="ph ph-bank"></i>
                    <h4>Banking Details</h4>
                  </div>

                  <!-- Payment Method Selection -->
                  <div class="payment-method-group">
                    <div class="form-group">
                      <label for="paymentMethod">
                        <i class="ph ph-credit-card"></i>
                        Payment Method *
                      </label>
                      <div class="select-wrapper">
                        <select id="paymentMethod" name="paymentMethod" required>
                          <option value="">Select Payment Method</option>
                          <option value="eft">Electronic Funds Transfer (EFT)</option>
                          <option value="cash">Cash Payment</option>
                          <option value="cheque">Cheque Payment</option>
                        </select>
                        <i class="ph ph-caret-down"></i>
                      </div>
                      <small class="form-hint">Choose how this employee will receive their salary payments</small>
                    </div>
                  </div>

                  <!-- Banking Information (shown when EFT is selected) -->
                  <div id="bankingDetailsFields" class="banking-details-container">
                    <div class="banking-info-header">
                      <h5>Bank Account Information</h5>
                      <p class="info-text">Please provide the employee's banking details for salary payments</p>
                    </div>

                    <!-- Bank Selection Group -->
                    <div class="bank-selection-group">
                      <div class="form-group">
                        <label for="bankName">
                          <i class="ph ph-bank"></i>
                          Bank Name *
                        </label>
                        <div class="select-wrapper">
                          <select id="bankName" name="bankName">
                            <option value="">Select Bank</option>
                            <option value="ABSA Bank">ABSA Bank</option>
                            <option value="Capitec Bank">Capitec Bank</option>
                            <option value="FNB">First National Bank (FNB)</option>
                            <option value="Nedbank">Nedbank</option>
                            <option value="Standard Bank">Standard Bank</option>
                            <option value="African Bank">African Bank</option>
                            <option value="Bidvest Bank">Bidvest Bank</option>
                            <option value="Discovery Bank">Discovery Bank</option>
                            <option value="TymeBank">TymeBank</option>
                            <option value="Other">Other Bank</option>
                          </select>
                          <i class="ph ph-caret-down"></i>
                        </div>
                      </div>

                      <div class="form-group">
                        <label for="accountType">
                          <i class="ph ph-folder"></i>
                          Account Type *
                        </label>
                        <div class="select-wrapper">
                          <select id="accountType" name="accountType">
                            <option value="">Select Account Type</option>
                            <option value="Current">Current Account</option>
                            <option value="Savings">Savings Account</option>
                            <option value="Transmission">Transmission Account</option>
                          </select>
                          <i class="ph ph-caret-down"></i>
                        </div>
                      </div>
                    </div>

                    <!-- Account Details Group -->
                    <div class="account-details-group">
                      <div class="form-group">
                        <label for="accountNumber">
                          <i class="ph ph-hash"></i>
                          Account Number *
                        </label>
                        <div class="input-wrapper">
                          <input
                            type="text"
                            id="accountNumber"
                            name="accountNumber"
                            pattern="[0-9]{5,16}"
                            title="Account number must be between 5 and 16 digits"
                            placeholder="Enter account number"
                          />
                        </div>
                        <small class="form-hint">Enter the full account number (5-16 digits)</small>
                      </div>

                      <div class="form-group">
                        <label for="branchCode">
                          <i class="ph ph-map-pin"></i>
                          Branch Code
                        </label>
                        <div class="input-wrapper">
                          <input
                            type="text"
                            id="branchCode"
                            name="branchCode"
                            pattern="[0-9]{6}"
                            title="Branch code must be 6 digits"
                            class="auto-populated"
                            placeholder="Auto-populated"
                            readonly
                          />
                        </div>
                        <small class="form-hint auto-hint">Branch code will be automatically populated based on the selected bank</small>
                      </div>
                    </div>

                    <!-- Account Holder Group -->
                    <div class="account-holder-group">
                      <div class="form-group">
                        <label for="accountHolder">
                          <i class="ph ph-user-circle"></i>
                          Account Holder Type *
                        </label>
                        <div class="select-wrapper">
                          <select id="accountHolder" name="accountHolder">
                            <option value="">Select Account Holder Type</option>
                            <option value="Own">Own Account (Employee's personal account)</option>
                            <option value="Joint">Joint Account (Shared with spouse/partner)</option>
                            <option value="Third Party">Third Party Account (Different account holder)</option>
                          </select>
                          <i class="ph ph-caret-down"></i>
                        </div>
                        <small class="form-hint">Specify who owns the bank account for compliance purposes</small>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Form Actions -->
                <div class="form-actions">
                  <div>
                    <button type="button" class="btn btn-secondary prev-step">
                      <i class="ph ph-arrow-left"></i>
                      Previous
                    </button>
                    <button type="button" class="btn btn-primary next-step">
                      <i class="ph ph-arrow-right"></i>
                      Next
                    </button>
                  </div>
                </div>
              </div>

              <!-- Step 3: Employment Details -->
              <div class="form-step" data-step="3">
                <h3 class="section-header">
                  <i class="fas fa-briefcase header-icon"></i>
                  Employment Details
                </h3>

                <!-- Position Details - Modernized -->
                <div class="form-section position-details-section">
                  <div class="section-title">
                    <i class="ph ph-briefcase"></i>
                    <h4>Position Details</h4>
                  </div>

                  <div class="position-details-container">
                    <!-- Job Title Group -->
                    <div class="job-title-group">
                      <div class="form-group">
                        <label for="jobTitle">
                          <i class="ph ph-user-focus"></i>
                          Job Title *
                        </label>
                        <div class="input-icon">
                          <i class="ph ph-user-focus"></i>
                          <input
                            type="text"
                            id="jobTitle"
                            name="jobTitle"
                            required
                            placeholder="Enter employee's job title"
                          />
                        </div>
                        <small class="form-hint">The official job title or position name for this employee</small>
                      </div>
                    </div>

                    <!-- Department and Cost Centre Group -->
                    <div class="department-group">
                      <div class="form-group">
                        <label for="department">
                          <i class="ph ph-buildings"></i>
                          Department
                        </label>
                        <div class="input-icon">
                          <i class="ph ph-buildings"></i>
                          <input
                            type="text"
                            id="department"
                            name="department"
                            placeholder="Enter department name"
                          />
                        </div>
                        <small class="form-hint">The department or division where this employee will work</small>
                      </div>

                      <div class="form-group">
                        <label for="costCentre">
                          <i class="ph ph-chart-pie-slice"></i>
                          Cost Centre
                        </label>
                        <div class="input-icon">
                          <i class="ph ph-chart-pie-slice"></i>
                          <input
                            type="text"
                            id="costCentre"
                            name="costCentre"
                            placeholder="Enter cost centre code"
                          />
                        </div>
                        <small class="form-hint">Optional: Cost centre code for budget allocation and reporting purposes</small>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Pay Information - Modernized -->
                <div class="form-section pay-details-section">
                  <div class="section-title">
                    <i class="ph ph-money"></i>
                    <h4>Pay Details</h4>
                  </div>

                  <div class="pay-details-container">
                    <!-- Pay Frequency Group -->
                    <div class="pay-frequency-group">
                      <div class="form-group">
                        <label for="payFrequency">
                          <i class="ph ph-calendar-check"></i>
                          Pay Frequency *
                        </label>
                        <div class="select-wrapper" data-tooltip="Configure pay frequencies in Company Settings">
                          <select id="payFrequency" name="payFrequency" required>
                            <option value="">Select Pay Frequency</option>
                            <% if (payFrequencies && payFrequencies.length > 0) {
                            %> <% payFrequencies.forEach(function(pf) { %>
                            <option
                              value="<%= pf._id %>"
                              data-frequency="<%= pf.frequency %>"
                              data-last-day="<%= pf.lastDayOfPeriod %>"
                            >
                              <%= pf.name %>
                            </option>
                            <% }); %> <% } else { %>
                            <option value="" disabled>
                              No pay frequencies available
                            </option>
                            <% } %>
                          </select>
                          <div class="select-icons">
                            <a href="/clients/<%= companyCode %>/settings/payroll/pay-frequencies" class="settings-link" title="Configure pay frequencies">
                              <i class="ph ph-gear"></i>
                            </a>
                          </div>
                        </div>
                        <small class="form-hint">This determines how often the employee will be paid and when payroll periods are generated</small>
                      </div>
                    </div>

                    <!-- Appointment Date Group -->
                    <div class="appointment-date-group">
                      <div class="form-group">
                        <label for="doa">
                          <i class="ph ph-calendar-plus"></i>
                          Date of Appointment *
                        </label>
                        <div class="input-icon">
                          <i class="ph ph-calendar-plus"></i>
                          <input
                            type="date"
                            id="doa"
                            name="doa"
                            class="flatpickr"
                            required
                            placeholder="Select appointment date"
                          />
                        </div>
                        <small class="form-hint">The official start date for this employee's employment</small>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Regular Hours Section - Modernized -->
                <div class="form-section regular-hours-section">
                  <div class="section-title">
                    <i class="ph ph-clock"></i>
                    <h4>Regular Hours</h4>
                  </div>

                  <div class="regular-hours-container">
                    <!-- Payment Type Group -->
                    <div class="payment-type-group">
                      <div class="form-group">
                        <label class="checkbox-label">
                          <i class="ph ph-clock-clockwise"></i>
                          Payment Structure
                        </label>
                        <div class="modern-checkbox">
                          <div class="checkbox-wrapper">
                            <input
                              type="checkbox"
                              id="hourlyPaid"
                              name="hourlyPaid"
                            />
                            <label for="hourlyPaid" class="checkbox-label">
                              <i class="ph ph-timer"></i>
                              Hourly paid employee
                            </label>
                            <span class="checkmark"></span>
                          </div>
                        </div>
                        <small class="form-hint">Check this if the employee is paid based on hours worked rather than a fixed salary</small>
                      </div>
                    </div>

                    <!-- Hours Configuration Group -->
                    <div class="hours-config-group">
                      <div class="form-group">
                        <label for="hoursPerDay">
                          <i class="ph ph-clock"></i>
                          Hours per Day
                        </label>
                        <div class="input-icon">
                          <i class="ph ph-clock"></i>
                          <input
                            type="number"
                            id="hoursPerDay"
                            name="hoursPerDay"
                            step="0.1"
                            value="8.0"
                            min="0"
                            max="24"
                            placeholder="8.0"
                          />
                        </div>
                        <small class="form-hint warning-hint">
                          <i class="ph ph-warning-circle"></i>
                          Zero regular hours will result in a zero hourly rate for unpaid leave deductions
                        </small>
                      </div>

                      <div class="form-group">
                        <label for="schedule">
                          <i class="ph ph-calendar-dots"></i>
                          Work Schedule *
                        </label>
                        <div class="select-wrapper">
                          <select id="schedule" name="schedule" required>
                            <option value="">Select Schedule Type</option>
                            <option value="Fixed">Fixed Schedule</option>
                            <option value="Casual/Temp">Casual/Temporary</option>
                          </select>
                          <i class="ph ph-caret-down"></i>
                        </div>
                        <small class="form-hint">Choose whether this employee works a fixed schedule or casual/temporary hours</small>
                      </div>
                    </div>
                  </div>

                  <!-- Working Days Configuration -->
                  <div class="working-days-section">
                    <div class="working-days-header">
                      <label>
                        <i class="ph ph-calendar-check"></i>
                        Working Days
                      </label>
                      <p class="section-description">Select the days this employee typically works</p>
                    </div>

                    <div class="working-days-grid">
                      <% ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].forEach(day => { %>
                      <div class="day-row">
                        <div class="modern-checkbox">
                          <div class="checkbox-wrapper">
                            <input
                              type="checkbox"
                              name="workingDays[]"
                              value="<%= day %>"
                              id="day<%= day %>"
                              <%= ['Mon', 'Tue', 'Wed', 'Thu', 'Fri'].includes(day) ? 'checked' : '' %>
                            >
                            <label for="day<%= day %>" class="checkbox-label">
                              <%= day === 'Mon' ? 'Monday' : day === 'Tue' ? 'Tuesday' : day === 'Wed' ? 'Wednesday' : day === 'Thu' ? 'Thursday' : day === 'Fri' ? 'Friday' : day === 'Sat' ? 'Saturday' : 'Sunday' %>
                            </label>
                            <span class="checkmark"></span>
                          </div>
                        </div>
                      </div>
                      <% }); %>
                    </div>
                  </div>

                  <!-- Days Per Week Calculation -->
                  <div class="days-calculation-section">
                    <div class="form-group">
                      <label>
                        <i class="ph ph-calculator"></i>
                        Full Days per Week
                      </label>
                      <div class="days-per-week-display">
                        <div class="calculated-days">
                          <span id="fullDaysPerWeek">5.0</span>
                          <small>(Automatically calculated from selected working days)</small>
                        </div>
                        <button
                          type="button"
                          id="overrideButton"
                          class="btn-override"
                        >
                          <i class="ph ph-pencil-simple"></i> Override
                        </button>
                        <div
                          id="overrideInput"
                          class="override-input"
                          style="display: none"
                        >
                          <input
                            type="number"
                            id="fullDaysPerWeekOverride"
                            name="fullDaysPerWeek"
                            step="0.5"
                            min="0"
                            max="7"
                            value="5.0"
                            placeholder="5.0"
                          />
                          <button type="button" class="btn-cancel-override">
                            <i class="ph ph-x"></i>
                          </button>
                        </div>
                      </div>
                      <small class="form-hint">This value is used for leave calculations and payroll processing</small>
                    </div>
                  </div>
                </div>

                <!-- Form Actions -->
                <div class="form-actions">
                  <div>
                    <button type="button" class="btn btn-secondary prev-step">
                      <i class="ph ph-arrow-left"></i>
                      Previous
                    </button>
                    <button type="submit" class="btn btn-primary">
                      Submit
                      <i class="ph ph-check"></i>
                    </button>
                  </div>
                </div>
              </div>

              <!-- Step 4: Submit -->
              <div class="form-step" data-step="4">
                <h3 class="section-header">
                  <i class="fas fa-check header-icon"></i>
                  Submit
                </h3>

                <!-- Form Actions -->
                <div class="form-actions">
                  <div>
                    <button type="button" class="btn btn-secondary prev-step">
                      <i class="ph ph-arrow-left"></i>
                      Previous
                    </button>
                    <button type="submit" class="btn btn-primary">
                      Submit
                      <i class="ph ph-check"></i>
                    </button>
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </main>

    <%- include('partials/mobile-bottom-nav', { req: { path: `/clients/${company.companyCode}/employeeManagement` }, company: company }) %>

    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>

    <!-- Block browser alerts on this page for better UX -->
    <script>
      // Override alert function to prevent disruptive browser notifications
      const originalAlert = window.alert;
      window.alert = function(message) {
        console.log('Blocked browser alert:', message);
        // Convert to toast notification instead
        showEmployeeFormToast(message);
      };

      // Toast notification function for employee form
      function showEmployeeFormToast(message) {
        // Create toast container if it doesn't exist
        let toastContainer = document.querySelector('.employee-form-toast-container');
        if (!toastContainer) {
          toastContainer = document.createElement('div');
          toastContainer.className = 'employee-form-toast-container';
          toastContainer.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            display: flex;
            flex-direction: column;
            gap: 10px;
          `;
          document.body.appendChild(toastContainer);
        }

        // Create toast element
        const toast = document.createElement('div');
        toast.className = 'employee-form-toast';
        toast.style.cssText = `
          background: #10b981;
          color: white;
          border-radius: 8px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          padding: 16px 20px;
          min-width: 300px;
          max-width: 400px;
          display: flex;
          align-items: center;
          gap: 12px;
          transform: translateX(120%);
          transition: all 0.3s ease-in-out;
          font-family: 'Inter', sans-serif;
          font-size: 14px;
          line-height: 1.4;
        `;

        toast.innerHTML = `
          <i class="ph ph-check-circle" style="font-size: 20px; flex-shrink: 0;"></i>
          <span>${message}</span>
        `;

        // Add toast to container
        toastContainer.appendChild(toast);

        // Show with animation
        setTimeout(() => {
          toast.style.transform = 'translateX(0)';
        }, 10);

        // Remove the toast after 3 seconds
        setTimeout(() => {
          toast.style.transform = 'translateX(120%)';
          setTimeout(() => {
            toast.remove();
            if (toastContainer.children.length === 0) {
              toastContainer.remove();
            }
          }, 300);
        }, 3000);
      }
    </script>

    <script src="/js/add-employee-details.js"></script>
    <script src="/js/employee-number-toggle.js"></script>
    <script>
      window.payFrequencies = <%- JSON.stringify(locals.payFrequencies || []) %>;
      window.companyCode = "<%= companyCode %>";

      document.addEventListener('DOMContentLoaded', function() {
        const idTypeSelect = document.getElementById('idType');
        const idNumberContainer = document.getElementById('idNumberContainer');
        const passportNumberContainer = document.getElementById('passportNumberContainer');

        function updateIdFields() {
            const selectedType = idTypeSelect.value;
            idNumberContainer.style.display = selectedType === 'rsa' ? 'block' : 'none';
            passportNumberContainer.style.display = selectedType === 'passport' ? 'block' : 'none';
        }

        idTypeSelect.addEventListener('change', updateIdFields);
        // Call initially to set correct state
        updateIdFields();
      });
    </script>
    <script>
      document.addEventListener("DOMContentLoaded", () => {
        console.log("Form structure check");
        const formSteps = document.querySelectorAll(".form-step");
        console.log("Number of form steps found:", formSteps.length);
        formSteps.forEach((step, index) => {
          console.log(`Step ${index + 1} initial classes:`, step.className);
          console.log(
            `Step ${index + 1} initial display:`,
            getComputedStyle(step).display
          );
        });
      });
    </script>
    <script>
      function redirectToLeaveOverview() {
        window.location.href = `/clients/<%= companyCode %>/employeeManagement/leaveOverview`;
      }
    </script>
  </body>
</html>
