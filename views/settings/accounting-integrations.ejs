<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="csrf-token" content="<%= csrfToken %>" />
    <meta name="company-code" content="<%= company ? company.companyCode : '' %>" />
    <meta name="xero-status" content="<%= xeroIntegration?.status === 'active' ? 'active' : 'inactive' %>" />
    <title>Settings - <%= company ? company.name : 'Company' %></title>

    <!-- Modern Font -->
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <!-- Phosphor Icons -->
    <script src="https://unpkg.com/@phosphor-icons/web"></script>

    <!-- Styles -->
    <link rel="stylesheet" href="/css/styles.css" />
    <link rel="stylesheet" href="/css/onboarding.css" />
    <link rel="stylesheet" href="/css/employeeManagement.css" />
    <link rel="stylesheet" href="/css/header.css" />
    <link rel="stylesheet" href="/css/sidebar.css" />
    <link rel="stylesheet" href="/css/settings.css" />
    <link rel="stylesheet" href="/css/accounting-settings.css" />
    <link rel="stylesheet" href="/css/xero-mapping.css" />
    <link rel="stylesheet" href="/css/dark-mode.css" />
    
    <!-- Toast notification for token refresh -->
    <style>
      .toast {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 4px;
        color: white;
        font-weight: 500;
        z-index: 1000;
        display: none;
        animation: slideIn 0.3s ease-out;
      }
      
      .toast.success {
        background-color: #4CAF50;
      }
      
      .toast.error {
        background-color: #f44336;
      }
      
      @keyframes slideIn {
        from {
          transform: translateX(100%);
          opacity: 0;
        }
        to {
          transform: translateX(0);
          opacity: 1;
        }
      }
    </style>
  </head>

  <body>
    <!-- Toast notification container -->
    <div id="tokenRefreshToast" class="toast"></div>

    <div class="layout-wrapper">
      <%- include('../partials/sidebar', { user: user, company: company }) %>
      <div class="content-wrapper">
        <%- include('../partials/header', { user: user, company: company, currentPage: 'settings' }) %>

        <main class="main-container">
          <!-- Tabs Section -->
          <div id="tabs-section" style="margin-top: 6.5rem;">
        <div class="tab-row main-tabs">
          <a
            href="/clients/<%= company ? company.companyCode : '' %>/settings/accounting"
            class="tab-button <%= activeTab === 'accounting' ? 'active' : '' %>"
          >
            <i class="ph ph-calculator"></i>
            <span>Accounting</span>
          </a>
          <a
            href="/clients/<%= company ? company.companyCode : '' %>/settings/employee"
            class="tab-button"
          >
            <i class="ph ph-users"></i>
            Employee
          </a>
          <a
            href="/clients/<%= company ? company.companyCode : '' %>/settings/payroll"
            class="tab-button"
          >
            <i class="ph ph-money"></i>
            Payroll
          </a>
          <a
            href="/clients/<%= company ? company.companyCode : '' %>/settings/other"
            class="tab-button"
          >
            <i class="ph ph-gear"></i>
            Other
          </a>
        </div>
      </div>

      <!-- Content Section -->
      <div class="content-section">
        <div class="section-header">
          <h2>Accounting Integrations</h2>
          <p>Connect your payroll system with popular accounting software to automate data synchronization and streamline your financial workflows.</p>
        </div>

        <div class="integration-grid">
          <% // Safely check integration status with default values const
          integration = typeof xeroIntegration !== 'undefined' ? xeroIntegration
          : { status: 'inactive' }; const connected = integration.status ===
          'active'; %>

          <!-- Xero Integration -->
          <div class="integration-card xero-integration">
            <div class="integration-header">
              <img src="/xero.svg" alt="Xero" class="integration-logo" />
              <span
                class="status-badge <%= xeroIntegration?.status === 'active' ? 'connected' : 'disconnected' %>"
              >
                <%= xeroIntegration?.status === 'active' ? 'Connected' :
                'Disconnected' %>
              </span>
            </div>
            <div class="integration-content">
              <h3>Xero Integration</h3>
              <p>Seamlessly sync your payroll data with Xero accounting software. Automatically create journal entries for salaries, taxes, and benefits.</p>

              <div class="integration-features">
                <div class="feature-item">
                  <i class="ph ph-check-circle"></i>
                  <span>Automatic journal entry creation</span>
                </div>
                <div class="feature-item">
                  <i class="ph ph-check-circle"></i>
                  <span>Customizable account mapping</span>
                </div>
                <div class="feature-item">
                  <i class="ph ph-check-circle"></i>
                  <span>Real-time synchronization</span>
                </div>
              </div>
            </div>

            <div class="integration-actions">
              <% if (xeroIntegration?.status === 'active') { %>
              <button
                class="secondary-button"
                id="disconnectButton"
                data-company-code="<%= company.companyCode %>"
              >
                <i class="ph ph-plug-x"></i>
                Disconnect
              </button>
              <% } else { %>
              <a
                href="/clients/<%= company.companyCode %>/settings/accounting/xero/connect"
                class="primary-button"
              >
                <i class="ph ph-plug"></i>
                Connect to Xero
              </a>
              <% } %>
            </div>
          </div>

          <!-- QuickBooks Integration -->
          <div class="integration-card quickbooks-integration">
            <div class="integration-header">
              <img
                src="/qb.png"
                alt="QuickBooks"
                class="integration-logo"
                style="max-height: 32px; width: auto"
              />
              <span
                class="status-badge <%= quickbooksIntegration?.status === 'active' ? 'connected' : 'disconnected' %>"
              >
                <%= quickbooksIntegration?.status === 'active' ? 'Connected' :
                'Disconnected' %>
              </span>
            </div>
            <div class="integration-content">
              <h3>QuickBooks Integration</h3>
              <p>Connect with QuickBooks Online to automatically sync payroll transactions. Streamline your accounting workflow with seamless data integration.</p>

              <div class="integration-features">
                <div class="feature-item">
                  <i class="ph ph-check-circle"></i>
                  <span>Automated payroll entries</span>
                </div>
                <div class="feature-item">
                  <i class="ph ph-check-circle"></i>
                  <span>Chart of accounts mapping</span>
                </div>
                <div class="feature-item">
                  <i class="ph ph-check-circle"></i>
                  <span>OAuth 2.0 secure connection</span>
                </div>
              </div>
            </div>

            <div class="integration-actions">
              <% if (quickbooksIntegration?.status === 'active') { %>
              <button
                class="secondary-button"
                id="disconnectQuickBooksButton"
                data-company-code="<%= company.companyCode %>"
              >
                <i class="ph ph-plug-x"></i>
                Disconnect
              </button>
              <% } else { %>
              <a
                href="/quickbooks/connect/<%= company.companyCode %>"
                class="primary-button"
              >
                <i class="ph ph-plug"></i>
                Connect to QuickBooks
              </a>
              <% } %>
            </div>
          </div>
        </div>

        <% if (xeroIntegration?.status === 'active') { %>
        <!-- Xero Account Mapping Section -->
        <div class="section-header mapping-section-header">
          <h2>Account Mapping</h2>
          <p>Configure how your payroll items map to Xero accounts</p>
        </div>

        <div class="mapping-section">
          <div class="mapping-header">
            <div class="mapping-help">
              <h4>How to Map Accounts</h4>
              <ul>
                <li>Select a Xero account for each payroll item below</li>
                <li>
                  Mapped accounts will be used when syncing payroll data to Xero
                </li>
                <li>Required mappings are marked with an asterisk (*)</li>
              </ul>
            </div>
            <button class="save-mappings" id="saveXeroMappings" disabled>
              <i class="ph ph-floppy-disk"></i>
              Save Mappings
            </button>
          </div>

          <div class="mapping-grid" id="xeroMappingGrid">
            <!-- Mapping cards will be populated by JavaScript -->
            <div class="mapping-card skeleton">
              <h3>Loading...</h3>
              <select disabled>
                <option>Loading accounts...</option>
              </select>
            </div>
          </div>
        </div>
        <% } %>

        <!-- Integration Benefits Section -->
        <div class="benefits-section">
          <div class="section-header">
            <h2>Integration Benefits</h2>
            <p>Streamline your accounting workflow with automated data synchronization</p>
          </div>

          <div class="benefits-grid">
            <div class="benefit-card">
              <div class="benefit-icon">
                <i class="ph ph-clock"></i>
              </div>
              <h3>Save Time</h3>
              <p>Eliminate manual data entry with automatic payroll synchronization to your accounting system.</p>
            </div>

            <div class="benefit-card">
              <div class="benefit-icon">
                <i class="ph ph-shield-check"></i>
              </div>
              <h3>Reduce Errors</h3>
              <p>Minimize human error with direct data transfer between PandaPayroll and your accounting software.</p>
            </div>

            <div class="benefit-card">
              <div class="benefit-icon">
                <i class="ph ph-chart-line"></i>
              </div>
              <h3>Real-time Insights</h3>
              <p>Get up-to-date financial reports with synchronized payroll data in your accounting system.</p>
            </div>

            <div class="benefit-card">
              <div class="benefit-icon">
                <i class="ph ph-lock"></i>
              </div>
              <h3>Secure & Compliant</h3>
              <p>Bank-level security with OAuth 2.0 authentication ensures your data remains protected.</p>
            </div>
          </div>
        </div>
      </div>
        </main>
      </div>
    </div>

    <%- include('../partials/disconnect-modal') %>

    <script>
      // Make company code available to JavaScript
      var companyCode = '<%= company.companyCode %>';
      var isXeroConnected = <%= xeroIntegration && xeroIntegration.status === 'active' ? 'true' : 'false' %>;
      var isQuickBooksConnected = <%= quickbooksIntegration && quickbooksIntegration.status === 'active' ? 'true' : 'false' %>;
      var xeroInitialized = false; // Flag to prevent multiple initializations
      
      // Toast notification function
      function showToast(message, type) {
        const toast = document.getElementById('tokenRefreshToast');
        if (!toast) return;
        
        toast.textContent = message;
        toast.className = 'toast ' + (type || 'success');
        toast.style.display = 'block';
        
        setTimeout(function() {
          toast.style.display = 'none';
        }, 5000);
      }
      
      // Error handler for token refresh
      window.handleTokenRefreshError = function(error) {
        console.error('[XERO-DEBUG] Token refresh error:', error);
        showToast('Error refreshing Xero connection. Please try reconnecting.', 'error');
      };
      
      // Success handler for token refresh
      window.handleTokenRefreshSuccess = function() {
        console.log('[XERO-DEBUG] Token refreshed successfully');
        showToast('Xero connection refreshed successfully', 'success');
      };
      
      console.log('Company Code:', companyCode);
      console.log('Xero Integration Status:', '<%= xeroIntegration ? xeroIntegration.status : "undefined" %>');
      console.log('Is Xero Connected:', isXeroConnected);
      console.log('QuickBooks Integration Status:', '<%= quickbooksIntegration ? quickbooksIntegration.status : "undefined" %>');
      console.log('Is QuickBooks Connected:', isQuickBooksConnected);

      // QuickBooks disconnect functionality with modal
      document.addEventListener('DOMContentLoaded', function() {
        const disconnectQuickBooksButton = document.getElementById('disconnectQuickBooksButton');
        const modal = document.getElementById('quickbooksDisconnectModal');
        const closeModalBtn = document.getElementById('closeQuickBooksModal');
        const cancelBtn = document.getElementById('cancelQuickBooksDisconnect');
        const confirmBtn = document.getElementById('confirmQuickBooksDisconnect');

        // Show modal when disconnect button is clicked
        if (disconnectQuickBooksButton) {
          disconnectQuickBooksButton.addEventListener('click', function() {
            const companyCode = this.getAttribute('data-company-code');
            confirmBtn.setAttribute('data-company-code', companyCode);
            showModal();
          });
        }

        // Modal control functions
        function showModal() {
          modal.classList.add('active');
          document.body.style.overflow = 'hidden';
          // Focus trap for accessibility
          confirmBtn.focus();
        }

        function hideModal() {
          modal.classList.remove('active');
          document.body.style.overflow = '';
          // Reset button state
          resetConfirmButton();
        }

        function resetConfirmButton() {
          confirmBtn.disabled = false;
          confirmBtn.innerHTML = '<i class="ph ph-plug-x"></i> Disconnect QuickBooks';
        }

        // Close modal event listeners
        if (closeModalBtn) {
          closeModalBtn.addEventListener('click', hideModal);
        }

        if (cancelBtn) {
          cancelBtn.addEventListener('click', hideModal);
        }

        // Close modal when clicking outside
        modal.addEventListener('click', function(e) {
          if (e.target === modal) {
            hideModal();
          }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
          if (e.key === 'Escape' && modal.classList.contains('active')) {
            hideModal();
          }
        });

        // Confirm disconnect
        if (confirmBtn) {
          confirmBtn.addEventListener('click', async function() {
            const companyCode = this.getAttribute('data-company-code');

            try {
              // Update button state
              this.disabled = true;
              this.innerHTML = '<i class="ph ph-spinner ph-spin"></i> Disconnecting...';

              const response = await fetch(`/quickbooks/disconnect/${companyCode}`, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
                }
              });

              const result = await response.json();

              if (result.success) {
                // Update button to show success
                this.innerHTML = '<i class="ph ph-check"></i> Disconnected';
                showToast('QuickBooks disconnected successfully', 'success');

                setTimeout(() => {
                  hideModal();
                  window.location.reload();
                }, 1500);
              } else {
                throw new Error(result.error || 'Failed to disconnect QuickBooks');
              }
            } catch (error) {
              console.error('QuickBooks disconnect error:', error);
              showToast('Failed to disconnect QuickBooks: ' + error.message, 'error');
              resetConfirmButton();
            }
          });
        }
      });

      if (isXeroConnected) {
        document.addEventListener('DOMContentLoaded', function() {
          if (!xeroInitialized) {
            xeroInitialized = true;
            console.log('[XERO-DEBUG] Initializing from main script tag', new Date().toISOString());
            initializeXeroMapping(companyCode);
          } else {
            console.log('[XERO-DEBUG] Initialization already done, skipping', new Date().toISOString());
          }
        });
      }
    </script>

    <script src="/js/xero-integration.js"></script>
    <script src="/include.js"></script>
    <script src="/script.js"></script>
    <script src="/main.js"></script>
    <script src="/tab.js"></script>

    <!-- QuickBooks Disconnect Confirmation Modal -->
    <div id="quickbooksDisconnectModal" class="qb-disconnect-modal-overlay">
      <div class="qb-disconnect-modal-container">
        <div class="qb-disconnect-modal-header">
          <h3 class="qb-disconnect-modal-title">
            <i class="ph ph-warning-circle"></i>
            Disconnect QuickBooks
          </h3>
          <button class="qb-disconnect-modal-close" id="closeQuickBooksModal" aria-label="Close modal">
            <i class="ph ph-x"></i>
          </button>
        </div>

        <div class="qb-disconnect-modal-body">
          <div class="qb-disconnect-warning-content">
            <div class="qb-disconnect-warning-icon">
              <i class="ph ph-warning-diamond"></i>
            </div>
            <div class="qb-disconnect-warning-text">
              <p class="qb-disconnect-warning-message">
                Are you sure you want to disconnect QuickBooks? This will stop all data synchronization.
              </p>
              <div class="qb-disconnect-warning-details">
                <ul>
                  <li>Payroll data will no longer sync to QuickBooks</li>
                  <li>Existing synced data will remain in QuickBooks</li>
                  <li>You can reconnect at any time</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <div class="qb-disconnect-modal-footer">
          <button class="qb-disconnect-btn-secondary" id="cancelQuickBooksDisconnect">
            <i class="ph ph-x"></i>
            Cancel
          </button>
          <button class="qb-disconnect-btn-danger" id="confirmQuickBooksDisconnect">
            <i class="ph ph-plug-x"></i>
            Disconnect QuickBooks
          </button>
        </div>
      </div>
    </div>
  </body>
</html>
