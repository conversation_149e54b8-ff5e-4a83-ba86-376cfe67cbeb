<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
    <meta name="refresh-timestamp" content="<%= typeof refreshTimestamp !== 'undefined' ? refreshTimestamp : Date.now() %>" />
    <title>Employee Management | <%= company.name %></title>

    <!-- Modern Font -->
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <!-- Existing Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>

    <!-- Styles -->
    <link rel="stylesheet" href="/css/styles.css" />
    <link rel="stylesheet" href="/css/onboarding.css" />
    <link rel="stylesheet" href="/css/employeeManagement.css" />
    <link rel="stylesheet" href="/css/header.css" />
    <link rel="stylesheet" href="/css/mobile-header.css" />
    <link rel="stylesheet" href="/css/mobile-nav.css" />
    <link rel="stylesheet" href="/css/mobile-employee.css" />
    <link rel="stylesheet" href="/css/sidebar.css" />

    <!-- Icons -->
    <script type="module" src="https://unpkg.com/@phosphor-icons/web@2.1.5"></script>
    <style>
      /* Ensure Phosphor icons are properly sized and aligned */
      .ph {
        display: inline-block;
        font-size: 1.2em;
        line-height: 1;
        vertical-align: middle;
      }
      
      /* Add a small margin to icons when they're next to text */
      button .ph,
      .filter-group .ph,
      .company-badge .ph {
        margin-right: 0.5em;
      }

      /* Fix z-index layering issue: Ensure profile dropdown appears above action tabs */
      .sub-menu-wrap {
        z-index: 999999 !important; /* Highest priority for profile dropdown */
      }

      /* Ensure action tabs don't interfere with header dropdown */
      .action-tabs {
        z-index: 100 !important; /* Lower than header elements */
      }

      /* Ensure other page elements don't interfere */
      .main-container {
        position: relative;
        z-index: 1; /* Base layer for main content */
      }

      /* Responsive z-index fixes */
      @media (max-width: 768px) {
        .sub-menu-wrap {
          z-index: 999999 !important; /* Maintain highest priority on mobile */
        }

        .action-tabs {
          z-index: 50 !important; /* Even lower on mobile to prevent conflicts */
        }
      }
    </style>
  </head>
  <body>
    <div class="layout-wrapper">
      <%- include('partials/sidebar', { user: user, company: company }) %>
      <div class="content-wrapper">
        <%- include('partials/header', { user: user, company: company, currentPage: 'employees' }) %>

        <main class="main-container">


          <!-- Action Tabs -->
          <div class="action-tabs">
            <button class="tab-button active" onclick="showTab('employeeList')">
              <i class="ph ph-users-three"></i>
              Employee List
            </button>
            <button
              class="tab-button"
              onclick="redirectToAddNewEmployeeDetails('addEmployee')"
            >
              <i class="ph ph-user-plus"></i>
              Add New Employee
            </button>
            <button class="tab-button" onclick="redirectToLeaveOverview()">
              <i class="ph ph-calendar"></i>
              Leave Overview
            </button>
            <button class="tab-button" onclick="redirectToSelfService()">
              <i class="ph ph-gear"></i>
              Self Service
            </button>
            <button class="tab-button" onclick="redirectToBulkActions()">
              <i class="ph ph-list-plus"></i>
              Bulk Actions
            </button>
          </div>

          <!-- Employee List Section -->
          <div id="employeeList" class="tab-content">
            <!-- Filters -->
            <div class="filters-section">
              <div class="filter-group">
                <label for="statusFilter">
                  <i class="ph ph-funnel"></i>
                  Status
                </label>
                <select id="statusFilter" class="filter-select">
                  <option value="all">All Employees</option>
                  <option value="active" selected>Active Employees</option>
                  <option value="inactive">Inactive Employees</option>
                </select>
              </div>

              <div class="filter-group">
                <label for="searchInputEmployee">
                  <i class="ph ph-magnifying-glass"></i>
                  Search
                </label>
                <input
                  type="text"
                  id="searchInputEmployee"
                  class="search-input"
                  placeholder="Search by name, ID, or department..."
                />
              </div>
            </div>

            <!-- Employee Table -->
            <div class="table-container">
              <table id="employeeTable" class="modern-table">
                <thead>
                  <tr>
                    <th>Employee Number</th>
                    <th>Name</th>
                    <th>Cost Centre</th>
                    <th>Pay Frequency</th>
                    <th>Status</th>
                  </tr>
                </thead>
                <tbody id="employeeTableBody">
                  <% employees.forEach(function(employee) { %>
                  <tr data-employee-id="<%= employee._id %>">
                    <td><%= employee.companyEmployeeNumber %></td>
                    <td>
                      <div class="employee-name">
                        <%= employee.firstName %> <%= employee.lastName %>
                      </div>
                    </td>
                    <td><%= employee.costCentre %></td>
                    <td>
                      <%
                        // Get frequency from populated payFrequency object
                        let frequency = 'monthly'; // default fallback
                        if (employee.payFrequency && employee.payFrequency.frequency) {
                          frequency = employee.payFrequency.frequency;
                        }
                        // Handle bi-weekly vs biweekly naming
                        const displayFrequency = frequency === 'bi-weekly' ? 'biweekly' : frequency;
                        const badgeClass = displayFrequency.toLowerCase();
                        const displayName = displayFrequency.charAt(0).toUpperCase() + displayFrequency.slice(1);
                      %>
                      <span class="frequency-badge <%= badgeClass %>">
                        <i class="ph ph-calendar"></i>
                        <%= displayName %>
                      </span>
                    </td>
                    <td>
                      <span
                        class="status-badge <%= employee.status.toLowerCase() %>"
                      >
                        <%= employee.status %>
                      </span>
                    </td>
                  </tr>
                  <% }); %>
                </tbody>
              </table>
            </div>

            <!-- Pagination -->
            <div class="pagination-container">
              <div id="paginationInfo" class="pagination-info"></div>
              <div class="pagination-buttons">
                <button
                  id="prevButton"
                  class="pagination-button"
                  onclick="prevPage()"
                >
                  <i class="ph ph-caret-left"></i> Previous
                </button>
                <button
                  id="nextButton"
                  class="pagination-button"
                  onclick="nextPage()"
                >
                  Next <i class="ph ph-caret-right"></i>
                </button>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>

    <!-- Floating Action Button for Mobile -->
    <button class="fab-add-employee" onclick="redirectToAddNewEmployeeDetails()" aria-label="Add New Employee">
      <i class="ph ph-plus"></i>
    </button>

    <%- include('partials/mobile-bottom-nav', { req: { path: `/clients/${company.companyCode}/employeeManagement` }, company: company }) %>

    <!-- Keep existing scripts -->
    <script src="/validate.js"></script>
    <script src="/responsive-script.js"></script>
    <script src="/include.js"></script>
    <script src="/script.js"></script>
    <script src="/main.js"></script>
    <script src="/tab.js"></script>

    <!-- Navigation Functions -->
    <script>
      function redirectToAddNewEmployeeDetails() {
        window.location.href = `/clients/<%= company.companyCode %>/employeeManagement/addNewEmployeeDetails`;
      }

      function redirectToSelfService() {
        window.location.href = `/clients/<%= company.companyCode %>/employeeManagement/selfService`;
      }

      function redirectToBulkActions() {
        window.location.href = `/clients/<%= company.companyCode %>/employeeManagement/bulkActions`;
      }

      function redirectToLeaveOverview() {
        window.location.href = `/clients/<%= company.companyCode %>/employeeManagement/leaveOverview`;
      }
    </script>

    <!-- Employee Management Filtering Script -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        // Prevent multiple initializations
        if (window.employeeManagementInitialized) {
          return;
        }
        window.employeeManagementInitialized = true;

        const statusFilter = document.getElementById("statusFilter");
        const searchInput = document.getElementById("searchInputEmployee");
        const tableBody = document.getElementById("employeeTableBody");

        // Skip if elements don't exist
        if (!statusFilter || !searchInput || !tableBody) {
          return;
        }

        const rows = Array.from(tableBody.getElementsByTagName("tr"));

        // Store original rows for reset
        const originalRows = [...rows];

        // Pagination Variables
        const rowsPerPage = 10;
        let currentPage = 1;
        let filteredRows = [...rows];

        function filterTable() {
          const searchTerm = searchInput.value.toLowerCase().trim();
          const selectedStatus = statusFilter.value.toLowerCase().trim();

          // Reset filteredRows to original rows before filtering
          filteredRows = originalRows.filter((row) => {
            const name = row.querySelector(".employee-name")?.textContent.toLowerCase() || "";
            const employeeNumber = row.cells[0]?.textContent.toLowerCase() || "";
            const costCentre = row.cells[2]?.textContent.toLowerCase() || "";
            const statusElement = row.querySelector(".status-badge");
            const status = statusElement ? statusElement.textContent.trim().toLowerCase() : "";

            const matchesSearch = !searchTerm ||
              name.includes(searchTerm) ||
              employeeNumber.includes(searchTerm) ||
              costCentre.includes(searchTerm);

            let matchesStatus = false;
            if (selectedStatus === "all") {
              matchesStatus = true;
            } else if (selectedStatus === "active") {
              // Match both "Active" and "Serving Notice" for active filter
              matchesStatus = status === "active" || status === "serving notice";
            } else if (selectedStatus === "inactive") {
              // Match only "Inactive" for inactive filter
              matchesStatus = status === "inactive";
            } else {
              // Exact match for any other status values
              matchesStatus = status === selectedStatus;
            }

            return matchesSearch && matchesStatus;
          });

          // Reset to first page when filtering
          currentPage = 1;
          updatePagination();
        }

        function updatePagination() {
          const totalPages = Math.ceil(filteredRows.length / rowsPerPage);
          const start = (currentPage - 1) * rowsPerPage;
          const end = start + rowsPerPage;

          // Hide all rows first
          originalRows.forEach(row => row.style.display = "none");

          // Show only the rows for current page
          filteredRows.slice(start, end).forEach(row => row.style.display = "");

          // Update pagination info
          const paginationInfo = document.getElementById("paginationInfo");
          if (paginationInfo) {
            const showing = Math.min(filteredRows.length, end - start);
            paginationInfo.textContent = `Showing ${showing} of ${filteredRows.length} entries`;
          }

          // Update button states
          const prevButton = document.getElementById("prevButton");
          const nextButton = document.getElementById("nextButton");
          if (prevButton) prevButton.disabled = currentPage === 1;
          if (nextButton) nextButton.disabled = currentPage >= totalPages || totalPages === 0;
        }

        // Status Filter Event Listener
        statusFilter.addEventListener("change", filterTable);

        // Search Input Event Listener with Debounce
        let searchTimeout;
        searchInput.addEventListener("input", function () {
          clearTimeout(searchTimeout);
          searchTimeout = setTimeout(filterTable, 300);
        });

        // Pagination Button Event Listeners
        window.prevPage = function () {
          if (currentPage > 1) {
            currentPage--;
            updatePagination();
          }
        };

        window.nextPage = function () {
          const totalPages = Math.ceil(filteredRows.length / rowsPerPage);
          if (currentPage < totalPages) {
            currentPage++;
            updatePagination();
          }
        };

        // Initialize table
        filterTable();

        // Make table rows clickable
        tableBody.addEventListener("click", function (e) {
          const row = e.target.closest("tr");
          if (row) {
            const employeeId = row.dataset.employeeId;
            if (employeeId) {
              window.location.href = `/clients/<%= company.companyCode %>/employeeProfile/${employeeId}`;
            }
          }
        });

        // Add hover effect to table rows
        originalRows.forEach((row) => {
          row.addEventListener("mouseenter", function () {
            this.style.cursor = "pointer";
            this.style.backgroundColor = "rgba(99, 102, 241, 0.05)";
          });

          row.addEventListener("mouseleave", function () {
            this.style.backgroundColor = "";
          });
        });
      });

      // Enhanced cache management and refresh detection
      document.addEventListener('DOMContentLoaded', function() {
        const urlParams = new URLSearchParams(window.location.search);
        const refreshParam = urlParams.get('refresh');
        const actionParam = urlParams.get('action');
        const isRefresh = <%= typeof isRefresh !== 'undefined' ? isRefresh : false %>;
        const refreshTimestamp = '<%= typeof refreshTimestamp !== 'undefined' ? refreshTimestamp : Date.now() %>';

        // Log refresh information for debugging
        if (refreshParam || isRefresh) {
          console.log('Employee management page refreshed:', {
            refreshParam: refreshParam,
            actionParam: actionParam,
            isRefresh: isRefresh,
            timestamp: refreshTimestamp,
            employeeCount: <%= employees.length %>,
            loadTime: new Date().toISOString()
          });
        }

        // Clean up URL parameters after a delay
        if (refreshParam && window.history && window.history.replaceState) {
          setTimeout(() => {
            const cleanUrl = window.location.pathname;
            window.history.replaceState({}, document.title, cleanUrl);
            console.log('URL cleaned up, parameters removed');
          }, 2000);
        }

        // Force refresh if page was loaded from browser cache
        if (window.performance && window.performance.navigation.type === 2) {
          console.log('Page loaded from cache, forcing refresh...');
          const timestamp = Date.now();
          window.location.href = window.location.pathname + '?refresh=' + timestamp + '&action=cache-refresh';
        }

        // Add cache-busting to internal navigation links
        const internalLinks = document.querySelectorAll('a[href*="/employeeManagement"], a[href*="/employeeProfile"]');
        internalLinks.forEach(link => {
          link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            if (href && !href.includes('refresh=') && !href.includes('#')) {
              const separator = href.includes('?') ? '&' : '?';
              this.setAttribute('href', href + separator + 'refresh=' + Date.now());
            }
          });
        });

        // Show success message if this is a refresh after an operation
        if (actionParam) {
          let message = '';
          switch(actionParam) {
            case 'add':
              message = 'Employee added successfully and data refreshed';
              break;
            case 'edit':
              message = 'Employee updated successfully and data refreshed';
              break;
            case 'delete':
              message = 'Employee deleted successfully and data refreshed';
              break;
          }

          if (message) {
            console.log(message);
            // Optional: Show a subtle notification
            setTimeout(() => {
              const notification = document.createElement('div');
              notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #10b981;
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 1000;
                font-size: 14px;
                opacity: 0;
                transition: opacity 0.3s ease;
              `;
              notification.textContent = message;
              document.body.appendChild(notification);

              // Fade in
              setTimeout(() => notification.style.opacity = '1', 100);

              // Fade out and remove
              setTimeout(() => {
                notification.style.opacity = '0';
                setTimeout(() => notification.remove(), 300);
              }, 3000);
            }, 500);
          }
        }
      });
    </script>

    <!-- TEMPORARY: Cache clearing script for testing data refresh issues -->
    <script src="/js/clear-cache-for-testing.js"></script>
  </body>
</html>
