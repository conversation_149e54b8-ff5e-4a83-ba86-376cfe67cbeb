<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="csrf-token" content="<%= csrfToken %>" />
    <meta name="company-code" content="<%= company.companyCode %>" />
    <meta name="description" content="Pay Run Details - View and manage payroll details for <%= company.name %>" />
    <title>Pay Run Details | <%= company.name %></title>

    <!-- Consolidated Payroll Hub Styles (matching payrollhub.ejs design) -->
    <link rel="stylesheet" href="/css/payrollhub-desktop.css" />
    <link rel="stylesheet" href="/css/payrollhub-mobile.css" media="screen and (max-width: 768px)" />

    <!-- Preload Critical Resources for Speed Index -->
    <link rel="preload" href="/css/payrollhub-desktop.css" as="style" />
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" as="style" />

    <!-- Additional CSS for specific functionality -->
    <link rel="stylesheet" href="/css/header.css" media="print" onload="this.media='all'" />
    <link rel="stylesheet" href="/css/sidebar.css" media="print" onload="this.media='all'" />
    <link rel="stylesheet" href="/css/dashboard.css" media="print" onload="this.media='all'" />
    <link rel="stylesheet" href="/css/toast.css" media="print" onload="this.media='all'" />

    <!-- Fallback for browsers without JS -->
    <noscript>
      <link rel="stylesheet" href="/css/payrollhub-desktop.css" />
      <link rel="stylesheet" href="/css/payrollhub-mobile.css" />
      <link rel="stylesheet" href="/css/header.css" />
      <link rel="stylesheet" href="/css/sidebar.css" />
      <link rel="stylesheet" href="/css/dashboard.css" />
    </noscript>

    <!-- Resource Hints for Performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link rel="preconnect" href="https://cdnjs.cloudflare.com" />
    <link rel="dns-prefetch" href="https://unpkg.com" />

    <!-- Optimized Font Loading -->
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap"
      rel="stylesheet"
      media="print"
      onload="this.media='all'"
    />

    <!-- Defer Non-Critical Scripts with low priority -->
    <script defer src="https://unpkg.com/@phosphor-icons/web@2.0.3/dist/index.umd.js" fetchpriority="low"></script>
    <script defer src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js" fetchpriority="low"></script>

    <!-- Pay Run Details Specific Styles -->
    <style>
      /* Pay Run Details specific overrides and additions */
      .summary-cards-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
      }

      .pay-run-actions {
        display: flex;
        gap: 1rem;
        margin-bottom: 2rem;
        flex-wrap: wrap;
      }

      .pay-run-actions .btn {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.2s ease;
        border: none;
        cursor: pointer;
        text-decoration: none;
      }

      .pay-run-actions .btn-primary {
        background: #3b82f6;
        color: white;
      }

      .pay-run-actions .btn-primary:hover {
        background: #2563eb;
        transform: translateY(-1px);
      }

      .pay-run-actions .btn-success {
        background: #22c55e;
        color: white;
      }

      .pay-run-actions .btn-success:hover {
        background: #16a34a;
        transform: translateY(-1px);
      }

      .payslips-table-section {
        margin-top: 2rem;
      }

      .payslips-table-section h2 {
        font-size: 1.25rem;
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      /* Mobile responsiveness */
      @media (max-width: 768px) {
        .pay-run-actions {
          flex-direction: column;
        }

        .pay-run-actions .btn {
          width: 100%;
          justify-content: center;
        }

        .summary-cards-grid {
          grid-template-columns: 1fr;
        }
    </style>
  </head>

  <body data-company-code="<%= company.companyCode %>">
    <div class="layout-wrapper">
      <%- include('partials/sidebar', { user: user, company: company, activePage: 'payroll' }) %>
      <div class="content-wrapper">
        <%- include('partials/header', { user: user, company: company, currentPage: 'payroll' }) %>

        <main class="main-container">
          <!-- Page Header -->
          <div class="page-header">
            <div class="header-content">
              <h1>Pay Run Details</h1>
              <p class="description">
                View and manage payroll details for the period <%= moment(payRun.startDate).format('DD MMM YYYY') %> - <%= moment(payRun.endDate).format('DD MMM YYYY') %>
              </p>
            </div>
            <div class="company-badge">
              <i class="ph ph-buildings"></i>
              <span><%= company.name %></span>
            </div>
          </div>

          <!-- Pay Run Summary Cards -->
          <div class="summary-cards-grid">
            <!-- Total Payslips Card -->
            <div class="summary-card">
              <div class="card-icon pending">
                <i class="ph ph-users"></i>
              </div>
              <div class="card-details">
                <h4>Total Payslips</h4>
                <div class="card-value"><%= payRun.payslips && payRun.payslips.length ? payRun.payslips.length : (payRun.payrollPeriods && payRun.payrollPeriods.length ? payRun.payrollPeriods.length : 0) %></div>
                <p>Employees in this pay run</p>
                <div class="card-status">
                  <i class="ph ph-check-circle"></i>
                  Pay Run Details
                </div>
              </div>
            </div>

            <!-- Total Gross Pay Card -->
            <div class="summary-card">
              <div class="card-icon amount">
                <i class="ph ph-currency-circle-dollar"></i>
              </div>
              <div class="card-details">
                <h4>Total Gross Pay</h4>
                <div class="card-value">R<%= totalGrossPay.toLocaleString('en-ZA', {minimumFractionDigits: 0, maximumFractionDigits: 0}) %></div>
                <p>Before deductions</p>
                <div class="card-status">
                  <i class="ph ph-wallet"></i>
                  Gross Amount
                </div>
              </div>
            </div>

            <!-- Total Net Pay Card -->
            <div class="summary-card">
              <div class="card-icon finalized">
                <i class="ph ph-money"></i>
              </div>
              <div class="card-details">
                <h4>Total Net Pay</h4>
                <div class="card-value">R<%= totalNetPay.toLocaleString('en-ZA', {minimumFractionDigits: 0, maximumFractionDigits: 0}) %></div>
                <p>After all deductions</p>
                <div class="card-status">
                  <i class="ph ph-check-circle"></i>
                  Final Amount
                </div>
              </div>
            </div>

            <!-- Total PAYE Card -->
            <div class="summary-card">
              <div class="card-icon payruns">
                <i class="ph ph-bank"></i>
              </div>
              <div class="card-details">
                <h4>Total PAYE</h4>
                <div class="card-value">R<%= totalPAYE.toLocaleString('en-ZA', {minimumFractionDigits: 0, maximumFractionDigits: 0}) %></div>
                <p>Income tax deductions</p>
                <div class="card-status">
                  <i class="ph ph-receipt"></i>
                  Tax Amount
                </div>
              </div>
            </div>

            <!-- Total SDL Card -->
            <div class="summary-card">
              <div class="card-icon amount">
                <i class="ph ph-chart-pie"></i>
              </div>
              <div class="card-details">
                <h4>Total SDL</h4>
                <div class="card-value">R<%= totalSDL.toLocaleString('en-ZA', {minimumFractionDigits: 0, maximumFractionDigits: 0}) %></div>
                <p>Skills development levy</p>
                <div class="card-status">
                  <i class="ph ph-graduation-cap"></i>
                  Employer Contribution
                </div>
              </div>
            </div>

            <!-- Total UIF Card -->
            <div class="summary-card">
              <div class="card-icon pending">
                <i class="ph ph-percent"></i>
              </div>
              <div class="card-details">
                <h4>Total UIF</h4>
                <div class="card-value">R<%= totalUIF.toLocaleString('en-ZA', {minimumFractionDigits: 0, maximumFractionDigits: 0}) %></div>
                <p>Unemployment insurance</p>
                <div class="card-status">
                  <i class="ph ph-shield-check"></i>
                  Insurance Fund
                </div>
              </div>
            </div>
          </div>

          <!-- Pay Run Action Buttons -->
          <div class="pay-run-actions">
            <button class="btn btn-primary" onclick="downloadPDF()">
              <i class="ph ph-file-pdf"></i>
              Download PDF Report
            </button>
            <button class="btn btn-success" onclick="downloadExcel()">
              <i class="ph ph-file-xls"></i>
              Download Excel Report
            </button>
          </div>

          <!-- Payslips Table Section -->
          <div class="payslips-table-section">
            <h2>
              <i class="ph ph-list-checks"></i>
              Payslips in this Pay Run
            </h2>

            <% if (payRun.payslips && payRun.payslips.length > 0) { %>
              <div class="table-container">
                <table class="modern-table">
                  <thead>
                    <tr>
                      <th>Employee #</th>
                      <th>Name</th>
                      <th>Gross Pay</th>
                      <th>Deductions</th>
                      <th>Net Pay</th>
                      <th>Payment Method</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    <% payRun.payslips.forEach(function(payslip) { %>
                    <tr class="payslip-row" data-payslip-id="<%= payslip._id %>">
                      <td><%= payslip.employee.companyEmployeeNumber || 'N/A' %></td>
                      <td class="employee-name">
                        <%= payslip.employee.firstName %> <%= payslip.employee.lastName %>
                      </td>
                      <td class="amount">R<%= (payslip.grossPay || 0).toLocaleString('en-ZA', {minimumFractionDigits: 2, maximumFractionDigits: 2}) %></td>
                      <td class="amount">R<%= (payslip.totalDeductions || 0).toLocaleString('en-ZA', {minimumFractionDigits: 2, maximumFractionDigits: 2}) %></td>
                      <td class="amount">R<%= (payslip.netPay || 0).toLocaleString('en-ZA', {minimumFractionDigits: 2, maximumFractionDigits: 2}) %></td>
                      <td>
                        <span class="status-badge success">
                          <%= payslip.employee.paymentMethod || 'EFT' %>
                        </span>
                      </td>
                      <td>
                        <button
                          class="icon-button"
                          onclick="togglePayslipDetails('<%= payslip._id %>')"
                          title="View Details"
                        >
                          <i class="ph ph-eye"></i>
                        </button>
                      </td>
                    </tr>
                    <!-- Detailed Breakdown Row (Hidden by Default) -->
                    <tr class="payslip-details" id="details-<%= payslip._id %>" style="display: none;">
                      <td colspan="7">
                        <div class="payslip-details-container">
                          <!-- Employee details content -->
                        </div>
                      </td>
                    </tr>
                    <% }); %>
                  </tbody>
                </table>
              </div>
            <% } else if (payRun.payrollPeriods && payRun.payrollPeriods.length > 0) { %>
              <div class="table-container">
                <table class="modern-table">
                  <thead>
                    <tr>
                      <th>Employee #</th>
                      <th>Name</th>
                      <th>Basic Salary</th>
                      <th>Gross Pay</th>
                      <th>Deductions</th>
                      <th>Net Pay</th>
                    </tr>
                  </thead>
                  <tbody>
                    <% payRun.payrollPeriods.forEach(function(period) { %>
                    <tr>
                      <td><%= period.employee.companyEmployeeNumber || 'N/A' %></td>
                      <td class="employee-name">
                        <%= period.employee.firstName %> <%= period.employee.lastName %>
                      </td>
                      <td class="amount">R<%= (period.basicSalary || 0).toLocaleString('en-ZA', {minimumFractionDigits: 2, maximumFractionDigits: 2}) %></td>
                      <td class="amount">R<%= (period.grossPay || period.basicSalary || 0).toLocaleString('en-ZA', {minimumFractionDigits: 2, maximumFractionDigits: 2}) %></td>
                      <td class="amount">R<%= (period.totalDeductions || 0).toLocaleString('en-ZA', {minimumFractionDigits: 2, maximumFractionDigits: 2}) %></td>
                      <td class="amount">R<%= (period.netPay || 0).toLocaleString('en-ZA', {minimumFractionDigits: 2, maximumFractionDigits: 2}) %></td>
                    </tr>
                    <% }); %>
                  </tbody>
                </table>
              </div>
            <% } else { %>
            <div class="empty-state">
              <div class="empty-state-icon">
                <i class="ph ph-file-x"></i>
              </div>
              <h3>No Payslips Available</h3>
              <p>This pay run does not contain any payslips yet.</p>
              <p class="status-info">Status: <span class="status-badge"><%= payRun.status %></span></p>
              <% if (payRun.status === 'draft' || payRun.status === 'processing') { %>
              <p>Payslips will appear here once the pay run is finalized.</p>
              <% } %>
            </div>
            <% } %>
            </div>
        </main>
      </div>
    </div>



    <script>
    // Add logging to existing functions
    async function releaseLateStarter(payslipId) {
      console.group('Release Late Starter');
      console.log('Payslip ID:', payslipId);
      try {
        console.log('Sending request to release late starter...');
        const response = await fetch(`/api/payslips/${payslipId}/release-late-starter`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'CSRF-Token': '<%= csrfToken %>'
          }
        });
        
        console.log('Response status:', response.status);
        if (!response.ok) {
          throw new Error('Failed to release late starter');
        }
        
        console.log('Successfully released late starter, redirecting...');
        window.location.href = `/clients/${window.location.pathname.split('/')[2]}/payruns/<%= payRun._id %>`;
      } catch (error) {
        console.error('Error releasing late starter:', error);
        alert('Failed to release late starter. Please try again.');
      }
      console.groupEnd();
    }

    function downloadPDF() {
      console.group('Download PDF');
      const companyCode = window.location.pathname.split('/')[2];
      console.log('Company Code:', companyCode);
      console.log('Pay Run ID:', '<%= payRun._id %>');
      console.log('CSRF Token:', '<%= csrfToken %>');
      const url = `/clients/${companyCode}/payruns/<%= payRun._id %>/download?format=pdf&_csrf=<%= csrfToken %>`;
      console.log('Download URL:', url);
      window.location.href = url;
      console.groupEnd();
    }

    function downloadExcel() {
      console.group('Download Excel');
      const companyCode = window.location.pathname.split('/')[2];
      console.log('Company Code:', companyCode);
      console.log('Pay Run ID:', '<%= payRun._id %>');
      console.log('CSRF Token:', '<%= csrfToken %>');
      const url = `/clients/${companyCode}/payruns/<%= payRun._id %>/download?format=excel&_csrf=<%= csrfToken %>`;
      console.log('Download URL:', url);
      window.location.href = url;
      console.groupEnd();
    }

    // Toggle payslip details visibility
    function togglePayslipDetails(payslipId) {
      const detailsRow = document.getElementById(`details-${payslipId}`);
      if (detailsRow.style.display === 'none') {
        detailsRow.style.display = 'table-row';
      } else {
        detailsRow.style.display = 'none';
      }
    }

    // Helper function to get value from either payslip or payrollPeriod
    function getValue(payslip, field) {
      if (payslip[field] !== undefined && payslip[field] !== null) {
        return payslip[field];
      } else if (payslip.payrollPeriod && payslip.payrollPeriod[field] !== undefined && payslip.payrollPeriod[field] !== null) {
        return payslip.payrollPeriod[field];
      }
      return 0;
    }

    // Log when page loads
    window.addEventListener('load', () => {
      console.log('Pay Run Details page loaded successfully');
      });

      // Toggle payslip details
      const toggleDetailsButtons = document.querySelectorAll('.toggle-details');
      toggleDetailsButtons.forEach(button => {
        button.addEventListener('click', () => {
          const payslipId = button.getAttribute('data-payslip-id');
          const detailsRow = document.getElementById(`details-${payslipId}`);
          if (detailsRow.style.display === 'none') {
            detailsRow.style.display = 'table-row';
          } else {
            detailsRow.style.display = 'none';
          }
        });
      });

      function finalizePayRun(payRunId) {
        const button = document.getElementById('finalizePayRunBtn');
        button.disabled = true;
        button.innerHTML = '<i class="ph ph-spinner ph-spin"></i> Finalizing...';
        
        fetch(`/clients/<%= company.companyCode %>/payruns/${payRunId}/finalize`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'CSRF-Token': '<%= csrfToken %>'
          }
        })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            // Reload the page to show the finalized pay run
            window.location.reload();
          } else {
            alert(`Failed to finalize pay run: ${data.message || 'Unknown error'}`);
            button.disabled = false;
            button.innerHTML = '<i class="ph ph-check-circle"></i> Retry Finalization';
          }
        })
        .catch(error => {
          console.error('Error finalizing pay run:', error);
          alert('Failed to finalize pay run. Please try again later.');
          button.disabled = false;
          button.innerHTML = '<i class="ph ph-check-circle"></i> Retry Finalization';
        });
      }
    </script>

    <!-- Scripts -->
    <script src="/js/sidebar.js"></script>
    <script src="/responsive-script.js"></script>
  </body>
</html>
