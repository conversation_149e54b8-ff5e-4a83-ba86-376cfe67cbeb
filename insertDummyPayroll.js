// insertDummyPayroll.js

const mongoose = require("mongoose");
const Payroll = require("./models/Payroll"); // Adjust the path based on your project structure

async function insertDummyPayroll() {
  try {
    // Connect to MongoDB
    await mongoose.connect("mongodb://localhost:27017/payrollDB", {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    // Dummy data
    const dummyPayslip = new Payroll({
      month: new Date(Date.UTC(2024, 4, 31)), // May 31, 2024 in UTC
      employee: new mongoose.Types.ObjectId("6660d6edca6cb5675005f01a"), // Correctly instantiate ObjectId
      finalised: false,
      payComponents: [
        { componentType: "garnishee", amount: 900 },
        { componentType: "garnishee", amount: 900 },
        { componentType: "maintenance_order", amount: 500 },
      ],
      garnishee: 900,
      maintenanceOrder: 500,
    });

    // Save the dummy payslip
    await dummyPayslip.save();

    console.log("Dummy payslip saved successfully");

    // Disconnect from MongoDB
    await mongoose.connection.close();
  } catch (error) {
    console.error("Error inserting dummy payslip:", error);

    // Close the connection in case of error
    await mongoose.connection.close();
  }
}

// Call the function to insert dummy payroll data
insertDummyPayroll();
