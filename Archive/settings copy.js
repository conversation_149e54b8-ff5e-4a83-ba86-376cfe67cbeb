const express = require("express");
const router = express.Router();
const EmployeeNumberSettings = require("../models/employeeNumberSettings");
const Employee = require("../models/Employee");
const EmployerDetails = require("../models/employerDetails");
const EFTDetails = require("../models/eftDetails");
const multer = require("multer");
const upload = multer({ dest: "uploads/" });
const PayslipSettings = require("../models/payslipSettings");
const path = require("path");
const fs = require("fs");
const { ensureAuthenticated } = require("../config/ensureAuthenticated");
const Company = require("../models/Company");
const checkCompanyMiddleware = require("../middleware/checkCompany");

// Apply middleware to all routes
router.use(ensureAuthenticated);
router.use(checkCompanyMiddleware);

router.get("/", async (req, res) => {
  try {
    const activeTab = req.query.tab || "employerDetails";
    const isNewCompany = req.query.newCompany === "true";

    let company;
    let employerDetails;

    if (isNewCompany) {
      company = { name: "New Company" };
      employerDetails = {};
    } else {
      company = await Company.findById(req.user.currentCompany);
      employerDetails = await EmployerDetails.findOne({
        company: req.user.currentCompany,
      });
    }

    const employeeNumberSettings = await EmployeeNumberSettings.findOne({
      company: req.user.currentCompany,
    });

    const eftDetails = await EFTDetails.findOne({
      company: req.user.currentCompany,
    });

    const payslipSettings = await PayslipSettings.findOne({
      company: req.user.currentCompany,
    });

    res.render("settings", {
      activeTab: activeTab,
      user: req.user,
      employerDetails: employerDetails || {},
      companyContext: req.companyContext,
      isNewCompany: isNewCompany,
      company: company,
      employeeNumberSettings: employeeNumberSettings || {},
      eftDetails: eftDetails || {},
      payslipSettings: payslipSettings || {},
    });
  } catch (error) {
    console.error("Error in settings route:", error);
    res.status(500).render("error", { message: "Error loading settings page" });
  }
});

// Existing settings routes (if any)

// New route for the new-template page
router.get("/new-template", (req, res) => {
  res.render("new-template");
});

// Handle form submission for new template
router.post("/new-template", (req, res) => {
  const { templateName } = req.body;
  // TODO: Save the new template to your database
  console.log("New template created:", templateName);
  res.redirect("/settings#templates"); // Redirect back to the templates tab in settings
});

// Handle employee number settings
router.post("/employee-numbers", async (req, res) => {
  try {
    const { employeeNumberMode, firstEmployeeNumber, sortEmployeesByNumber } =
      req.body;

    let settings = await EmployeeNumberSettings.findOne({
      company: req.user.currentCompany,
    });
    if (!settings) {
      settings = new EmployeeNumberSettings({
        company: req.user.currentCompany,
      });
    }

    settings.mode = employeeNumberMode;
    settings.firstEmployeeNumber = firstEmployeeNumber;
    settings.lastGeneratedNumber = firstEmployeeNumber;
    settings.sortEmployeesByNumber = sortEmployeesByNumber === "on";

    await settings.save();

    req.flash("success", "Employee number settings saved successfully");
    res.redirect("/settings#employeeNumbers");
  } catch (error) {
    console.error("Error saving employee number settings:", error);
    req.flash("error", "Failed to save employee number settings");
    res.redirect("/settings#employeeNumbers");
  }
});

function incrementEmployeeNumber(baseNumber, increment) {
  const numericPart = parseInt(baseNumber.replace(/^\D+/g, "")) + increment;
  const prefix = baseNumber.replace(/\d+$/g, "");
  return (
    prefix +
    numericPart.toString().padStart(baseNumber.length - prefix.length, "0")
  );
}

// New route to fetch employee number settings
router.get("/employee-number-settings", async (req, res) => {
  try {
    const settings = await EmployeeNumberSettings.findOne();
    res.json(settings || { mode: "manual" });
  } catch (error) {
    console.error("Error fetching employee number settings:", error);
    res
      .status(500)
      .json({ message: "Error fetching employee number settings" });
  }
});

// New routes for beneficiaries
router.get("/beneficiaries-add-garnishee", (req, res) => {
  res.render("beneficiaries-add-garnishee");
});

router.get("/beneficiaries-add-maintenance-order", (req, res) => {
  res.render("beneficiaries-add-maintenance-order");
});

router.get("/beneficiaries-add-medical-aid", (req, res) => {
  res.render("beneficiaries-add-medical-aid");
});

router.get("/beneficiaries-add-pension-fund", (req, res) => {
  res.render("beneficiaries-add-pension-fund");
});

router.get("/beneficiaries-add-provident-fund", (req, res) => {
  res.render("beneficiaries-add-provident-fund");
});

router.get("/beneficiaries-add-retirement-annuity-fund", (req, res) => {
  res.render("beneficiaries-add-retirement-annuity-fund");
});

// New route for adding a custom item
router.get("/add-new-custom-item", (req, res) => {
  res.render("add-new-custom-item");
});

// Handle custom item form submission
router.post("/add-new-custom-item", (req, res) => {
  // TODO: Implement logic to save the new custom item to your database
  console.log("New custom item:", req.body);
  res.redirect("/settings#customItems");
});

// New routes for custom items
router.get("/new-custom-income", (req, res) => {
  res.render("new-custom-income");
});

router.get("/new-custom-deduction", (req, res) => {
  res.render("new-custom-deduction");
});

router.get("/new-custom-allowance", (req, res) => {
  res.render("new-custom-allowance");
});

router.get("/new-custom-benefit", (req, res) => {
  res.render("new-custom-benefit");
});

router.get("/new-custom-erContribution", (req, res) => {
  res.render("new-custom-erContribution");
});

router.get("/new-custom-reimbursement", (req, res) => {
  res.render("new-custom-reimbursement");
});

router.get("/new-custom-existing-item", (req, res) => {
  res.render("new-custom-existing-item");
});

// Update the save-employer-details route
router.post(
  "/save-employer-details",
  checkCompanyMiddleware,
  upload.single("logo"),
  async (req, res) => {
    try {
      console.log("Received employer data:", req.body);
      console.log("Received file:", req.file);

      if (!req.user || !req.user.currentCompany) {
        throw new Error("Current company not set for user");
      }

      let employerDetails = await EmployerDetails.findOne({
        company: req.user.currentCompany,
      });
      if (!employerDetails) {
        employerDetails = new EmployerDetails({
          company: req.user.currentCompany,
        });
      }

      // Update all fields from the form
      employerDetails.tradingName = req.body.tradingName;
      employerDetails.physicalAddress = {
        unitNumber: req.body.unitNumber,
        complex: req.body.complex,
        streetNumber: req.body.streetNumber,
        street: req.body.street,
        suburbDistrict: req.body.suburbDistrict,
        cityTown: req.body.cityTown,
        code: req.body.code,
      };
      employerDetails.postalAddress = {
        line1: req.body.postalLine1,
        line2: req.body.postalLine2,
        line3: req.body.postalLine3,
        code: req.body.postalCode,
      };
      employerDetails.payeNumber = req.body.payeNumber;
      employerDetails.tradeClassificationGroup =
        req.body.tradeClassificationGroup;
      employerDetails.tradeClassification = req.body.tradeClassification;
      employerDetails.diplomaticIndemnity =
        req.body.diplomaticIndemnity === "on";
      employerDetails.sicMainGroup = req.body.sicMainGroup;
      employerDetails.sicLevel2 = req.body.sicLevel2;
      employerDetails.sicLevel3 = req.body.sicLevel3;
      employerDetails.sicLevel4 = req.body.sicLevel4;
      employerDetails.sicCode = req.body.sicCode;
      employerDetails.telephoneNumber = req.body.telephoneNumber;
      employerDetails.sarsContactName = req.body.sarsContactName;
      employerDetails.sarsContactSurname = req.body.sarsContactSurname;
      employerDetails.sarsContactPosition = req.body.sarsContactPosition;
      employerDetails.sarsContactBusTelNo = req.body.sarsContactBusTelNo;
      employerDetails.sarsContactCellNo = req.body.sarsContactCellNo;
      employerDetails.sarsContactEmail = req.body.sarsContactEmail;
      employerDetails.reportEmployeeNumbers =
        req.body.reportEmployeeNumbers === "on";
      employerDetails.uifNumber = req.body.uifNumber;
      employerDetails.companyRegistrationNo = req.body.companyRegistrationNo;

      // Handle logo upload
      if (req.file) {
        const oldLogoPath = employerDetails.logo;
        employerDetails.logo = req.file.path;

        // Delete old logo file if it exists
        if (oldLogoPath) {
          fs.unlink(oldLogoPath, (err) => {
            if (err) console.error("Error deleting old logo:", err);
          });
        }
      }

      await employerDetails.save();

      console.log("Saved employer details:", employerDetails);

      res.json({
        success: true,
        message: "Employer details saved successfully",
      });
    } catch (error) {
      console.error("Error saving employer details:", error);
      res.status(500).json({
        success: false,
        message: "Failed to save employer details",
        error: error.message,
      });
    }
  }
);

// // Get employer details
// router.get("/employer-details", async (req, res) => {
//   try {
//     const employerDetails = await EmployerDetails.findOne();
//     res.status(200).json(employerDetails || {});
//   } catch (error) {
//     console.error("Error fetching employer details:", error);
//     res.status(500).json({ message: "Error fetching employer details" });
//   }
// });

// Handle form submission
router.post("/employerDetails", async (req, res) => {
  try {
    // Process form data here
    // Save to database, etc.

    // Redirect to next step or back to settings
    res.redirect("/settings?tab=payFrequencies");
  } catch (error) {
    console.error(error);
    res.status(500).render("settings", {
      activeTab: "employerDetails",
      error: "An error occurred while saving the data.",
    });
  }
});

// Save EFT details
router.post("/eft-details", async (req, res) => {
  try {
    console.log("Received EFT details:", req.body);

    let eftDetails = await EFTDetails.findOne();
    if (!eftDetails) {
      eftDetails = new EFTDetails();
    }

    // Update the fields
    eftDetails.eftFormat = req.body.eftFormat;
    eftDetails.bank = req.body.bank;
    eftDetails.accountNumber = req.body.accountNumber;
    eftDetails.branchCode = req.body.branchCode;
    eftDetails.accountType = req.body.accountType;

    await eftDetails.save();
    console.log("EFT details saved:", eftDetails);
    res.status(200).json({ message: "EFT details saved successfully" });
  } catch (error) {
    console.error("Error saving EFT details:", error);
    res
      .status(500)
      .json({ message: "Error saving EFT details", error: error.message });
  }
});

// Get EFT details
router.get("/eft-details", async (req, res) => {
  try {
    const eftDetails = await EFTDetails.findOne();
    res.status(200).json(eftDetails || {});
  } catch (error) {
    console.error("Error fetching EFT details:", error);
    res.status(500).json({ message: "Error fetching EFT details" });
  }
});

// Add additional bank account
router.post("/add-additional-bank-account", async (req, res) => {
  try {
    let eftDetails = await EFTDetails.findOne();
    if (!eftDetails) {
      eftDetails = new EFTDetails();
    }

    eftDetails.additionalBankAccounts.push(req.body);
    await eftDetails.save();

    res
      .status(200)
      .json({ message: "Additional bank account added successfully" });
  } catch (error) {
    console.error("Error adding additional bank account:", error);
    res.status(500).json({
      message: "Error adding additional bank account",
      error: error.message,
    });
  }
});

// Add Job Grade page route
router.get("/add-job-grade", (req, res) => {
  res.render("add-job-grade");
});

// Handle Job Grade form submission
router.post("/add-job-grade", async (req, res) => {
  try {
    // TODO: Implement logic to save the new job grade
    console.log("New job grade:", req.body);
    res.redirect("/settings#jobGrades");
  } catch (error) {
    console.error("Error adding job grade:", error);
    res.status(500).json({ message: "Error adding job grade" });
  }
});

// Add Pay Point page route
router.get("/add-pay-point", (req, res) => {
  res.render("add-pay-point");
});

// Handle Pay Point form submission
router.post("/add-pay-point", async (req, res) => {
  try {
    // TODO: Implement logic to save the new pay point
    console.log("New pay point:", req.body);
    res.redirect("/settings#payPoints");
  } catch (error) {
    console.error("Error adding pay point:", error);
    res.status(500).json({ message: "Error adding pay point" });
  }
});

// Get payslip settings
router.get("/payslip-settings", async (req, res) => {
  try {
    const payslipSettings = await PayslipSettings.findOne();
    res.status(200).json(payslipSettings || {});
  } catch (error) {
    console.error("Error fetching payslip settings:", error);
    res.status(500).json({ message: "Error fetching payslip settings" });
  }
});

// Save payslip settings
router.post("/payslip-settings", async (req, res) => {
  try {
    console.log("Received payslip settings:", req.body);

    let payslipSettings = await PayslipSettings.findOne();
    if (!payslipSettings) {
      payslipSettings = new PayslipSettings(req.body);
    } else {
      payslipSettings.set(req.body);
    }

    await payslipSettings.save();
    console.log("Payslip settings saved:", payslipSettings);
    res.status(200).json({ message: "Payslip settings saved successfully" });
  } catch (error) {
    console.error("Error saving payslip settings:", error);
    res
      .status(500)
      .json({ message: "Error saving payslip settings", error: error.message });
  }
});

// New route for the new-pay-frequencies page
router.get("/new-pay-frequency", (req, res) => {
  res.render("new-pay-frequencies");
});

// Payroll Calculations routes
router.get("/basic-pay", (req, res) => {
  res.render("settings-basic-pay");
});

router.get("/bcea", (req, res) => {
  res.render("settings-bcea");
});

router.get("/eti", (req, res) => {
  res.render("settings-eti");
});

router.get("/additional-eti", (req, res) => {
  res.render("settings-add-eti");
});

router.get("/garnishee-calc", (req, res) => {
  res.render("settings-garnishee-calc");
});

router.get("/sdl", (req, res) => {
  res.render("settings-sdl");
});

router.get("/prorata", (req, res) => {
  res.render("settings-prorata");
});

router.post("/employer-details", upload.single("logo"), async (req, res) => {
  try {
    const isNewCompany = req.body.isNewCompany === "true";
    let company;
    let employerDetails;

    if (isNewCompany) {
      // Create a new Company
      company = new Company({
        name: req.body.tradingName,
        owner: req.user._id,
      });

      // Create a new EmployerDetails for the new company
      employerDetails = new EmployerDetails({
        company: company._id,
        tradingName: req.body.tradingName,
      });

      // Link the EmployerDetails to the Company
      company.employerDetails = employerDetails._id;

      await company.save();
      await employerDetails.save();

      // Add the new company to the user's companies array
      req.user.companies.push(company._id);
      await req.user.save();
    } else {
      // For existing company, fetch the company and its employer details
      company = await Company.findById(req.user.currentCompany);
      employerDetails = await EmployerDetails.findOne({ company: company._id });

      if (!employerDetails) {
        // If no EmployerDetails exist for this company, create a new one
        employerDetails = new EmployerDetails({ company: company._id });
        company.employerDetails = employerDetails._id;
        await company.save();
      }
    }

    // Update company name
    company.name = req.body.tradingName;
    await company.save();

    // Update employerDetails fields
    employerDetails.tradingName = req.body.tradingName;
    employerDetails.physicalAddress = {
      unitNumber: req.body.unitNumber,
      complex: req.body.complex,
      streetNumber: req.body.streetNumber,
      street: req.body.street,
      suburbDistrict: req.body.suburbDistrict,
      cityTown: req.body.cityTown,
      code: req.body.code,
    };
    employerDetails.postalAddress = {
      line1: req.body.postalLine1,
      line2: req.body.postalLine2,
      line3: req.body.postalLine3,
      code: req.body.postalCode,
    };
    // ... update other fields as necessary ...

    // Handle logo upload
    if (req.file) {
      const oldLogoPath = employerDetails.logo;
      employerDetails.logo = req.file.path;

      // Delete old logo file if it exists
      if (oldLogoPath) {
        fs.unlink(oldLogoPath, (err) => {
          if (err) console.error("Error deleting old logo:", err);
        });
      }
    }

    await employerDetails.save();

    res.json({
      success: true,
      message: isNewCompany
        ? "New company created successfully"
        : "Company details updated successfully",
    });
  } catch (error) {
    console.error("Error saving employer details:", error);
    res.status(500).json({
      success: false,
      message: "Failed to save employer details",
      error: error.message,
    });
  }
});

module.exports = router;
