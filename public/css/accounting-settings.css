/* Base styles */
:root {
  --primary-color: #6366f1;
  --secondary-color: #818cf8;
  --background-color: #f8fafc;
  --card-background: #ffffff;
  --text-primary: #0f172a;
  --text-secondary: #64748b;
  --text-muted: #94a3b8;
  --border-color: #e2e8f0;
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Main container */
main {
  padding: 2rem;
  background: var(--background-color);
  min-height: calc(100vh - 60px);
}

/* Title section styling */
.title-section {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.03), rgba(129, 140, 248, 0.05));
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.03);
  border: 1px solid rgba(99, 102, 241, 0.1);
}

.banner-content {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.banner-icon {
  background: rgba(99, 102, 241, 0.1);
  padding: 12px;
  border-radius: 12px;
  color: var(--primary-color);
}

.banner-text h1 {
  font-size: 1.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.banner-subtitle {
  color: var(--text-secondary);
  font-size: 0.975rem;
}

/* Page Header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
}

.header-content h1 {
  font-size: 1.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.description {
  color: var(--text-secondary);
  font-size: 0.975rem;
  max-width: 600px;
}

.company-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(99, 102, 241, 0.1);
  border-radius: 9999px;
  color: var(--primary-color);
  font-size: 0.875rem;
}

/* Tabs Section */
.tabs-container {
  background: var(--card-background);
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.tabs-section {
  border-bottom: 1px solid var(--border-color);
  padding: 4px;
}

.tab-row {
  display: flex;
  gap: 0.5rem;
  padding: 0.5rem;
}

.tab-group {
  display: flex;
  gap: 8px;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  color: var(--text-secondary);
  text-decoration: none;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  white-space: nowrap;
  transition: all 0.2s ease;
}

.tab-button:hover {
  color: var(--primary-color);
  background: rgba(99, 102, 241, 0.08);
}

.tab-button.active {
  background: rgba(99, 102, 241, 0.1);
  color: var(--primary-color);
}

.tab-button i {
  font-size: 1.25rem;
}

/* Settings Grid */
.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  padding: 1.5rem;
}

.settings-card {
  background: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  padding: 1.5rem;
  text-decoration: none;
  transition: all 0.2s ease;
}

.settings-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border-color: var(--primary-color);
}

.settings-card h3 {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.75rem;
}

.settings-card p {
  color: var(--text-secondary);
  font-size: 0.875rem;
  line-height: 1.5;
}

.settings-card i {
  color: var(--primary-color);
  font-size: 1.25rem;
}

/* Xero Integration UI */
.integration-content {
  padding: 2rem;
}

.integration-header {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
}

.integration-logo {
  width: 40px;
  height: 40px;
  margin-right: 1rem;
}

.integration-status {
  margin-top: 1.5rem;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 500;
  margin-bottom: 1rem;
}

.status-badge i {
  margin-right: 0.5rem;
}

.status-badge.connected {
  background-color: #e6f4ea;
  color: #1e7e34;
}

.status-badge.disconnected {
  background-color: #feeced;
  color: #dc3545;
}

.integration-description {
  color: #666;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.integration-features {
  list-style: none;
  padding: 0;
  margin: 0 0 2rem 0;
}

.integration-features li {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
  color: #444;
}

.integration-features li i {
  color: #28a745;
  margin-right: 0.75rem;
}

.integration-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
}

.integration-details {
  background-color: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  margin: 1rem 0;
}

.integration-details p {
  margin: 0.5rem 0;
  color: #444;
}

.error-message {
  color: #dc3545;
}

.alert {
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.alert i {
  margin-right: 0.75rem;
}

.alert-danger {
  background-color: #feeced;
  color: #dc3545;
}

.alert-success {
  background-color: #e6f4ea;
  color: #1e7e34;
}

/* Modern Button Styles */
.primary-button {
  background: linear-gradient(135deg, #4F46E5 0%, #4338CA 100%);
  color: white;
  border: none;
  padding: 14px 28px;
  border-radius: 12px;
  font-size: 0.9375rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15);
  font-family: var(--font-family);
  letter-spacing: -0.025em;
  min-width: 160px;
  justify-content: center;
}

.primary-button:hover {
  background: linear-gradient(135deg, #4338CA 0%, #3730A3 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(79, 70, 229, 0.2);
}

.primary-button:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(79, 70, 229, 0.2);
}

.primary-button:disabled {
  background: linear-gradient(135deg, #E5E7EB 0%, #D1D5DB 100%);
  color: #9CA3AF;
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

.primary-button i {
  font-size: 1rem;
}

.secondary-button {
  background: white;
  color: #4F46E5;
  border: 2px solid #4F46E5;
  padding: 12px 26px;
  border-radius: 12px;
  font-size: 0.9375rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  box-shadow: 0 2px 8px rgba(79, 70, 229, 0.1);
  font-family: var(--font-family);
  letter-spacing: -0.025em;
  min-width: 160px;
  justify-content: center;
}

.secondary-button:hover {
  background: #F5F3FF;
  border-color: #4338CA;
  color: #4338CA;
  transform: translateY(-1px);
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.08);
}

.secondary-button:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.secondary-button i {
  font-size: 1rem;
}

.save-mappings {
  background: linear-gradient(135deg, #4F46E5 0%, #4338CA 100%);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(79, 70, 229, 0.1);
}

.save-mappings:hover {
  background: linear-gradient(135deg, #4338CA 0%, #3730A3 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(79, 70, 229, 0.2);
}

.save-mappings:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(79, 70, 229, 0.2);
}

.save-mappings:disabled {
  background: linear-gradient(135deg, #E5E7EB 0%, #D1D5DB 100%);
  color: #9CA3AF;
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

.save-mappings i {
  font-size: 1rem;
}

/* Employee Management Page Style */
.content-section {
  background: #fff;
  border-radius: 20px;
  padding: 48px 40px;
  margin: 32px 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #f1f5f9;
  max-width: none;
  width: 100%;
}

.section-header {
  margin-bottom: 32px;
  text-align: center;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.section-header h2 {
  font-size: 2rem;
  font-weight: 800;
  color: var(--text-primary);
  margin-bottom: 12px;
  font-family: var(--font-family);
  letter-spacing: -0.025em;
  line-height: 1.2;
}

.section-header p {
  color: var(--text-secondary);
  font-size: 1rem;
  line-height: 1.6;
  font-family: var(--font-family);
  font-weight: 400;
}

.integration-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 32px;
  margin-bottom: 48px;
  max-width: none;
  width: 100%;
}

.integration-card {
  background: #fff;
  border: 1px solid #E5E7EB;
  border-radius: 16px;
  padding: 32px;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  overflow: hidden;
  min-height: 320px;
  font-family: var(--font-family);
}

.integration-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #6366f1, #8b5cf6);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.integration-card:hover {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  transform: translateY(-2px);
  border-color: #6366f1;
}

.integration-card:hover::before {
  opacity: 1;
}

.integration-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f1f5f9;
}

.integration-logo {
  height: 40px;
  width: auto;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.status-badge {
  font-size: 0.75rem;
  font-weight: 700;
  padding: 8px 16px;
  border-radius: 9999px;
  background: #F3F4F6;
  color: #6B7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-family: var(--font-family);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.status-badge.connected {
  background: linear-gradient(135deg, #D1FAE5, #A7F3D0);
  color: #065F46;
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
}

.status-badge.disconnected {
  background: linear-gradient(135deg, #FEE2E2, #FECACA);
  color: #DC2626;
  box-shadow: 0 2px 4px rgba(220, 38, 38, 0.2);
}

.integration-content {
  flex: 1;
  margin-bottom: 24px;
}

.integration-content h3 {
  font-size: 1.375rem;
  font-weight: 800;
  color: var(--text-primary);
  margin-bottom: 16px;
  font-family: var(--font-family);
  letter-spacing: -0.025em;
  line-height: 1.2;
}

.integration-content p {
  color: var(--text-secondary);
  font-size: 0.9375rem;
  line-height: 1.7;
  margin-bottom: 24px;
  font-family: var(--font-family);
  font-weight: 400;
}

.integration-features {
  display: flex;
  flex-direction: column;
  gap: 14px;
  margin-bottom: 28px;
  padding: 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 0.9375rem;
  color: var(--text-primary);
  font-family: var(--font-family);
  font-weight: 500;
}

.feature-item i {
  color: #10B981;
  font-size: 1.125rem;
  flex-shrink: 0;
  background: rgba(16, 185, 129, 0.1);
  padding: 4px;
  border-radius: 6px;
}

.integration-actions {
  display: flex;
  gap: 16px;
  margin-top: auto;
  padding-top: 20px;
  border-top: 1px solid #f1f5f9;
}

.mapping-section {
  background: #fff;
  border: 1px solid #E5E7EB;
  border-radius: 8px;
  padding: 24px;
  margin-top: 16px;
}

.mapping-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.mapping-help {
  max-width: 600px;
}

.mapping-help h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 12px;
}

.mapping-help ul {
  list-style-type: disc;
  margin-left: 20px;
  color: #6B7280;
  font-size: 0.875rem;
}

.mapping-help li {
  margin-bottom: 4px;
}

/* Benefits Section */
.benefits-section {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 20px;
  padding: 48px 40px;
  margin-top: 48px;
  border: 1px solid #e2e8f0;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 32px;
  margin-top: 40px;
  max-width: none;
  width: 100%;
}

.benefit-card {
  background: white;
  border-radius: 16px;
  padding: 32px 28px;
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid #e2e8f0;
  position: relative;
  overflow: hidden;
  min-height: 240px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.benefit-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #6366f1, #8b5cf6);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.benefit-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 28px -8px rgba(0, 0, 0, 0.12);
  border-color: #6366f1;
}

.benefit-card:hover::before {
  transform: scaleX(1);
}

.benefit-icon {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  transition: all 0.3s ease;
}

.benefit-icon i {
  font-size: 1.75rem;
  color: white;
}

.benefit-card:hover .benefit-icon {
  transform: scale(1.1);
  box-shadow: 0 8px 20px rgba(99, 102, 241, 0.3);
}

.benefit-card h3 {
  font-size: 1.25rem;
  font-weight: 800;
  color: var(--text-primary);
  margin-bottom: 16px;
  font-family: var(--font-family);
  letter-spacing: -0.025em;
}

.benefit-card p {
  color: var(--text-secondary);
  font-size: 0.9375rem;
  line-height: 1.7;
  margin: 0;
  font-family: var(--font-family);
  font-weight: 400;
}

/* QuickBooks Disconnect Modal - Unique namespace to avoid conflicts */
.qb-disconnect-modal-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  background: rgba(0, 0, 0, 0.6) !important;
  backdrop-filter: blur(4px) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  z-index: 99999 !important;
  opacity: 0 !important;
  visibility: hidden !important;
  transition: all 0.3s ease !important;
  margin: 0 !important;
  padding: 0 !important;
  box-sizing: border-box !important;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

.qb-disconnect-modal-overlay.active {
  opacity: 1 !important;
  visibility: visible !important;
}

.qb-disconnect-modal-container {
  background: white !important;
  border-radius: 16px !important;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
  max-width: 500px !important;
  width: 90% !important;
  max-height: 90vh !important;
  overflow: hidden !important;
  transform: scale(0.9) translateY(20px) !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  z-index: 100000 !important;
  margin: 0 !important;
  padding: 0 !important;
}

.qb-disconnect-modal-overlay.active .qb-disconnect-modal-container {
  transform: scale(1) translateY(0) !important;
}

.qb-disconnect-modal-header {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  padding: 24px 28px !important;
  border-bottom: 1px solid #f1f5f9 !important;
  background: linear-gradient(135deg, #fef2f2 0%, #fef7f7 100%) !important;
  margin: 0 !important;
}

.qb-disconnect-modal-title {
  display: flex !important;
  align-items: center !important;
  gap: 12px !important;
  font-size: 1.25rem !important;
  font-weight: 700 !important;
  color: #dc2626 !important;
  margin: 0 !important;
  letter-spacing: -0.025em !important;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

.qb-disconnect-modal-title i {
  font-size: 1.375rem !important;
  color: #dc2626 !important;
}

.qb-disconnect-modal-close {
  background: none !important;
  border: none !important;
  padding: 8px !important;
  border-radius: 8px !important;
  cursor: pointer !important;
  color: #6b7280 !important;
  transition: all 0.2s ease !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.qb-disconnect-modal-close:hover {
  background: rgba(0, 0, 0, 0.05) !important;
  color: #374151 !important;
}

.qb-disconnect-modal-close i {
  font-size: 1.25rem !important;
}

.qb-disconnect-modal-body {
  padding: 28px !important;
  margin: 0 !important;
}

.qb-disconnect-warning-content {
  display: flex !important;
  gap: 20px !important;
  align-items: flex-start !important;
}

.qb-disconnect-warning-icon {
  flex-shrink: 0 !important;
  width: 48px !important;
  height: 48px !important;
  background: linear-gradient(135deg, #fef2f2, #fee2e2) !important;
  border-radius: 12px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border: 1px solid #fecaca !important;
}

.qb-disconnect-warning-icon i {
  font-size: 1.5rem !important;
  color: #dc2626 !important;
}

.qb-disconnect-warning-text {
  flex: 1 !important;
}

.qb-disconnect-warning-message {
  font-size: 1rem !important;
  font-weight: 600 !important;
  color: #0f172a !important;
  margin: 0 0 16px 0 !important;
  line-height: 1.6 !important;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

.qb-disconnect-warning-details {
  background: #f8fafc !important;
  border-radius: 8px !important;
  padding: 16px !important;
  border: 1px solid #e2e8f0 !important;
}

.qb-disconnect-warning-details ul {
  margin: 0 !important;
  padding: 0 !important;
  list-style: none !important;
}

.qb-disconnect-warning-details li {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  font-size: 0.875rem !important;
  color: #64748b !important;
  margin-bottom: 8px !important;
  line-height: 1.5 !important;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

.qb-disconnect-warning-details li:last-child {
  margin-bottom: 0 !important;
}

.qb-disconnect-warning-details li::before {
  content: '•' !important;
  color: #6366f1 !important;
  font-weight: bold !important;
  font-size: 1rem !important;
}

.qb-disconnect-modal-footer {
  display: flex !important;
  gap: 12px !important;
  justify-content: flex-end !important;
  padding: 20px 28px 28px !important;
  background: #f8fafc !important;
  border-top: 1px solid #f1f5f9 !important;
  margin: 0 !important;
}

.qb-disconnect-btn-secondary,
.qb-disconnect-btn-danger {
  display: inline-flex !important;
  align-items: center !important;
  gap: 8px !important;
  padding: 12px 20px !important;
  border-radius: 8px !important;
  font-size: 0.875rem !important;
  font-weight: 600 !important;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  border: none !important;
  text-decoration: none !important;
  min-width: 120px !important;
  justify-content: center !important;
}

.qb-disconnect-btn-secondary {
  background: white !important;
  color: #6b7280 !important;
  border: 1px solid #d1d5db !important;
}

.qb-disconnect-btn-secondary:hover {
  background: #f9fafb !important;
  color: #374151 !important;
  border-color: #9ca3af !important;
}

.qb-disconnect-btn-danger {
  background: linear-gradient(135deg, #dc2626, #b91c1c) !important;
  color: white !important;
  box-shadow: 0 2px 4px rgba(220, 38, 38, 0.2) !important;
}

.qb-disconnect-btn-danger:hover {
  background: linear-gradient(135deg, #b91c1c, #991b1b) !important;
  box-shadow: 0 4px 8px rgba(220, 38, 38, 0.3) !important;
  transform: translateY(-1px) !important;
}

.qb-disconnect-btn-danger:disabled {
  opacity: 0.6 !important;
  cursor: not-allowed !important;
  transform: none !important;
}

/* Loading spinner animation */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.ph-spin {
  animation: spin 1s linear infinite;
}

/* Focus styles for accessibility */
.modal-close:focus,
.btn-secondary:focus,
.btn-danger:focus {
  outline: 2px solid #6366f1;
  outline-offset: 2px;
}

/* Smooth transitions for all interactive elements */
.modal-overlay * {
  transition: all 0.2s ease;
}

/* Responsive Adjustments */
@media (max-width: 1024px) {
  .integration-grid {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .benefits-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
  }
}

@media (max-width: 768px) {
  main {
    padding: 1rem;
  }

  .page-header {
    flex-direction: column;
    gap: 1rem;
  }

  .settings-grid {
    grid-template-columns: 1fr;
    padding: 1rem;
  }

  .tab-button {
    padding: 0.5rem 0.75rem;
  }

  .integration-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .mapping-grid {
    grid-template-columns: 1fr;
  }

  .benefits-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .mapping-header {
    flex-direction: column;
    gap: 16px;
  }

  .save-mappings {
    width: 100%;
    justify-content: center;
  }

  .benefits-section {
    padding: 32px 24px;
  }

  .benefit-card {
    padding: 24px 20px;
    min-height: 200px;
  }

  .integration-card {
    padding: 24px 20px;
    min-height: 280px;
  }

  .section-header h2 {
    font-size: 1.75rem;
  }

  .section-header p {
    font-size: 0.9375rem;
  }

  .integration-content h3 {
    font-size: 1.25rem;
  }

  .primary-button,
  .secondary-button {
    width: 100%;
    min-width: auto;
  }

  .integration-actions {
    flex-direction: column;
    gap: 12px;
  }

  /* QuickBooks Modal responsive styles */
  .qb-disconnect-modal-container {
    width: 95% !important;
    margin: 20px !important;
  }

  .qb-disconnect-modal-header {
    padding: 20px !important;
  }

  .qb-disconnect-modal-body {
    padding: 20px !important;
  }

  .qb-disconnect-warning-content {
    flex-direction: column !important;
    gap: 16px !important;
    text-align: center !important;
  }

  .qb-disconnect-warning-icon {
    align-self: center !important;
  }

  .qb-disconnect-modal-footer {
    flex-direction: column !important;
    padding: 16px 20px 20px !important;
  }

  .qb-disconnect-btn-secondary,
  .qb-disconnect-btn-danger {
    width: 100% !important;
    min-width: auto !important;
  }
}
