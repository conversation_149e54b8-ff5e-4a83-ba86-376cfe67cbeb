/* Add Basic Salary - Improved Design Following Dashboard Patterns */

/* Use dashboard design system variables */
:root {
  /* Inherit from dashboard.css variables */
  --form-spacing: var(--space-6);
  --form-border-radius: var(--radius-lg);
  --form-shadow: var(--shadow-sm);
}

/* Main container following dashboard layout */
.main-container {
  padding: var(--content-padding);
  padding-top: calc(var(--header-height) + var(--space-6)); /* Add top padding to prevent header overlap */
  max-width: 800px;
  margin: 0 auto;
}

/* Dashboard card styling for the form */
.dashboard-card.modern {
  background: var(--surface-color);
  border-radius: var(--form-border-radius);
  border: 1px solid var(--border-color);
  box-shadow: var(--form-shadow);
  padding: var(--form-spacing);
  margin-bottom: var(--form-spacing);
  contain: layout style;
  transform: translateZ(0);
}

/* Form sections following dashboard card patterns */
.form-section {
  margin-bottom: var(--space-8);
}

.form-section:last-of-type {
  margin-bottom: 0;
}

/* Section headers matching dashboard chart headers */
.section-header {
  margin-bottom: var(--space-4);
}

.section-header h3 {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  line-height: 1.2;
}

.section-header h3 i {
  color: var(--primary-color);
  font-size: var(--text-xl);
}

/* Form groups following dashboard spacing */
.form-group {
  margin-bottom: var(--space-4);
}

/* Form labels matching dashboard typography */
.form-label {
  display: block;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  margin-bottom: var(--space-2);
  line-height: 1.4;
}

/* Period display following dashboard info display patterns */
.period-display {
  background: var(--gray-50);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  padding: var(--space-3) var(--space-4);
}

.period-text {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--text-sm);
  color: var(--text-secondary);
  font-weight: var(--font-medium);
}

.period-text i {
  color: var(--primary-color);
}

.period-text.no-period {
  color: var(--text-muted);
}

.period-text.no-period i {
  color: var(--text-muted);
}

/* Switch container following dashboard toggle patterns */
.switch-container {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-4);
  padding: var(--space-3) var(--space-4);
  background: var(--gray-50);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
}

/* Switch styling matching dashboard toggles */
.switch {
  position: relative;
  display: inline-block;
  width: 48px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--gray-300);
  transition: var(--transition-base);
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: var(--white);
  transition: var(--transition-base);
  border-radius: 50%;
  box-shadow: var(--shadow-sm);
}

input:checked + .slider {
  background-color: var(--primary-color);
}

input:checked + .slider:before {
  transform: translateX(24px);
}

.switch-label {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
}

/* Input styling following dashboard form patterns */
.input-with-icon {
  position: relative;
}

.input-with-icon i {
  position: absolute;
  left: var(--space-3);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  font-size: var(--text-base);
}

.form-input,
.input-with-icon input {
  width: 100%;
  padding: var(--space-3) var(--space-3) var(--space-3) calc(var(--space-8) + var(--space-2));
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: var(--font-normal);
  color: var(--text-primary);
  background: var(--white);
  transition: var(--transition-fast);
  box-sizing: border-box;
}

.form-input:focus,
.input-with-icon input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input::placeholder,
.input-with-icon input::placeholder {
  color: var(--text-muted);
}

/* Checkbox styling following dashboard patterns */
.checkbox-option {
  margin-bottom: var(--space-4);
}

.checkbox-container {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  cursor: pointer;
  user-select: none;
  padding: var(--space-2);
  border-radius: var(--radius-sm);
  transition: var(--transition-fast);
}

.checkbox-container:hover {
  background: var(--gray-50);
}

.checkbox-container input {
  display: none;
}

.checkmark {
  width: 18px;
  height: 18px;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-sm);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-fast);
  background: var(--white);
}

.checkbox-container input:checked + .checkmark {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.checkbox-container input:checked + .checkmark::after {
  content: "";
  width: 6px;
  height: 10px;
  border: solid var(--white);
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
  margin-bottom: 2px;
}

.checkbox-container span:not(.checkmark) {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
}

/* Nested options styling */
.nested-options {
  margin-left: var(--space-6);
  margin-top: var(--space-3);
  padding-left: var(--space-3);
  border-left: 2px solid var(--border-light);
}

/* Form actions following dashboard button patterns */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--space-3);
  margin-top: var(--space-8);
  padding-top: var(--space-6);
  border-top: 1px solid var(--border-light);
}

/* Button styling matching dashboard buttons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-5);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: var(--transition-fast);
  text-decoration: none;
  border: 1px solid transparent;
  line-height: 1.4;
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--white);
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background-color: var(--primary-600);
  border-color: var(--primary-600);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background-color: var(--gray-100);
  color: var(--text-secondary);
  border-color: var(--border-color);
}

.btn-secondary:hover {
  background-color: var(--gray-200);
  color: var(--text-primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

/* Responsive design following dashboard patterns */
@media (max-width: 768px) {
  .main-container {
    padding: var(--space-4);
    padding-top: calc(var(--header-height) + var(--space-4)); /* Maintain header clearance on mobile */
  }
  
  .dashboard-card.modern {
    padding: var(--space-4);
  }
  
  .form-actions {
    flex-direction: column-reverse;
  }
  
  .btn {
    justify-content: center;
  }
}
