/* ========================================
   PAYROLL HUB - DESKTOP CSS CONSOLIDATION
   ======================================== */

/* Design System Variables */
:root {
  --primary-color: #3b82f6;
  --secondary-color: #2563eb;
  --background-color: #f9fafb;
  --surface-color: #ffffff;
  --text-primary: #111827;
  --text-secondary: #4b5563;
  --border-color: #e5e7eb;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --space-4: 1rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --radius-lg: 0.75rem;
}

/* ========================================
   GLOBAL STYLES
   ======================================== */

*, *::before, *::after {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background: var(--background-color);
  color: var(--text-primary);
  line-height: 1.6;
}

/* Font fallback to prevent layout shift */
.font-loaded body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* ========================================
   HIDE MOBILE ELEMENTS ON DESKTOP
   ======================================== */

/* Hide mobile header elements on desktop */
.mobile-menu-toggle,
.mobile-nav-menu,
.mobile-nav-content,
.mobile-nav-overlay,
.mobile-nav-header,
.mobile-nav-close,
.mobile-nav-links,
.mobile-nav-link,
.mobile-bottom-nav,
.mobile-bottom-navigation,
.bottom-nav,
.bottom-navigation,
.mobile-only,
nav[class*="mobile"],
nav[class*="bottom"] {
  display: none !important;
}

/* Ensure mobile navigation is hidden */
@media screen and (min-width: 769px) {
  .mobile-menu-toggle,
  .mobile-nav-menu,
  .mobile-nav-content,
  .mobile-nav-overlay,
  .mobile-nav-header,
  .mobile-nav-close,
  .mobile-nav-links,
  .mobile-nav-link,
  .mobile-bottom-nav,
  .mobile-bottom-navigation,
  .bottom-nav,
  .bottom-navigation,
  .mobile-header,
  .mobile-only,
  nav[class*="mobile"],
  nav[class*="bottom"],
  [class*="mobile-nav"],
  [class*="bottom-nav"] {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
  }
}

/* ========================================
   LAYOUT STRUCTURE
   ======================================== */

.layout-wrapper {
  display: flex;
  min-height: 100vh;
  background: var(--background-color);
}

.content-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-left: 280px;
}

.main-container {
  flex: 1;
  padding: var(--space-6);
  max-width: 100%;
  overflow-x: hidden;
  contain: layout style;
}

.sidebar {
  position: fixed;
  left: 0;
  top: 0;
  width: 280px;
  height: 100vh;
  background: var(--surface-color);
  border-right: 1px solid var(--border-color);
  z-index: 1000;
  transform: translateZ(0);
}

.header {
  position: sticky;
  top: 0;
  background: var(--surface-color);
  border-bottom: 1px solid var(--border-color);
  padding: var(--space-4) var(--space-6);
  z-index: 100;
  transform: translateZ(0);
}

/* ========================================
   PAYROLL CONTAINER
   ======================================== */

.payroll-container {
  max-width: 1200px;
  margin: 0 auto;
}

/* ========================================
   PAGE HEADER
   ======================================== */

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-8);
  padding-top: var(--space-16);
  padding-bottom: var(--space-6);
  border-bottom: 1px solid var(--border-color);
}

.header-content h1 {
  font-size: 28px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.header-content .description {
  color: var(--text-secondary);
  margin: 0;
  font-size: 16px;
  line-height: 1.4;
}

.company-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: 8px 16px;
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 500;
}

.company-badge i {
  font-size: 16px;
  color: var(--primary-color);
}

/* ========================================
   DASHBOARD SUMMARY CARDS
   ======================================== */

.dashboard-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

.summary-card {
  background: var(--surface-color);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  border: 1px solid var(--border-color);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  display: flex;
  align-items: flex-start;
  gap: var(--space-4);
  contain: layout style;
  transform: translateZ(0);
}

.summary-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  flex-shrink: 0;
}

.card-icon.pending {
  background: linear-gradient(135deg, var(--warning-color) 0%, #d97706 100%);
}

.card-icon.finalized {
  background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
}

.card-icon.amount {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.card-icon.payruns {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
}

.card-details {
  flex: 1;
  min-width: 0;
}

.card-details h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.card-details p {
  color: var(--text-secondary);
  margin: 0 0 4px 0;
  font-size: 14px;
  line-height: 1.4;
}

.card-value {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 4px 0;
  line-height: 1;
}

.card-status {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: var(--text-secondary);
  margin-top: 8px;
}

.card-status i {
  font-size: 14px;
}

/* ========================================
   SECTION HEADERS
   ======================================== */

.dashboard-section {
  margin-bottom: var(--space-8);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-6);
}

.chart-title-section h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.chart-title-section h3 i {
  font-size: 18px;
  color: var(--primary-color);
}

.chart-legend {
  margin-top: 4px;
}

.legend-item {
  color: var(--text-secondary);
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.legend-item i {
  font-size: 12px;
  color: var(--primary-color);
}

/* ========================================
   PROGRESS TRACKERS
   ======================================== */

.progress-trackers {
  margin-bottom: var(--space-8);
}

.frequency-tracker {
  background: var(--surface-color);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: var(--space-6);
  contain: layout style;
  transform: translateZ(0);
}

.tracker-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tracker-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 12px;
  border-radius: 9999px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-badge.draft {
  background: rgba(245, 158, 11, 0.1);
  color: #92400e;
}

.status-badge.finalized {
  background: rgba(16, 185, 129, 0.1);
  color: #065f46;
}

.status-badge.released {
  background: rgba(59, 130, 246, 0.1);
  color: #1e40af;
}

/* Frequency Badge Styles */
.frequency-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 12px;
  border-radius: 9999px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
  letter-spacing: 0.05em;
}

.frequency-badge.monthly {
  background: rgba(99, 102, 241, 0.1);
  color: #4338ca;
}

.frequency-badge.weekly {
  background: rgba(16, 185, 129, 0.1);
  color: #065f46;
}

.frequency-badge.biweekly {
  background: rgba(245, 158, 11, 0.1);
  color: #92400e;
}

.frequency-badge i {
  font-size: 14px;
}

/* Enhanced Steps */
.enhanced-tracker-steps {
  padding: var(--space-6);
  display: flex;
  gap: var(--space-8);
}

.enhanced-step {
  flex: 1;
  display: flex;
  gap: var(--space-4);
  opacity: 0.5;
  transition: all 0.3s ease;
}

.enhanced-step.active,
.enhanced-step.completed {
  opacity: 1;
}

.step-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex-shrink: 0;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  border: 2px solid var(--border-color);
  background: var(--surface-color);
  color: var(--text-secondary);
  transition: all 0.3s ease;
}

.enhanced-step.active .step-number {
  border-color: var(--primary-color);
  background: var(--primary-color);
  color: white;
}

.enhanced-step.completed .step-number {
  border-color: var(--success-color);
  background: var(--success-color);
  color: white;
}

.step-connector {
  width: 2px;
  height: 60px;
  background: var(--border-color);
  margin-top: 8px;
  transition: all 0.3s ease;
}

.enhanced-step.completed .step-connector {
  background: var(--success-color);
}

.step-content-enhanced {
  flex: 1;
  min-width: 0;
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.step-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.step-title i {
  font-size: 16px;
  color: var(--primary-color);
}

.step-status-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.step-status-badge.pending {
  background: rgba(107, 114, 128, 0.1);
  color: #374151;
}

.step-status-badge.active {
  background: rgba(59, 130, 246, 0.1);
  color: #1e40af;
}

.step-status-badge.completed {
  background: rgba(16, 185, 129, 0.1);
  color: #065f46;
}

.step-description {
  color: var(--text-secondary);
  margin: 0 0 12px 0;
  font-size: 14px;
  line-height: 1.4;
}

.step-details {
  display: flex;
  gap: var(--space-4);
  margin-bottom: 16px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
  color: var(--text-secondary);
}

.detail-item i {
  font-size: 14px;
  color: var(--primary-color);
}

/* ========================================
   BUTTON SYSTEM
   ======================================== */

.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: var(--radius-lg);
  font-weight: 500;
  font-size: 14px;
  transition: all 0.2s ease;
  cursor: pointer;
  border: none;
  text-decoration: none;
  justify-content: center;
  min-height: 36px;
}

.btn i {
  font-size: 14px;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: var(--secondary-color);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-success {
  background: var(--success-color);
  color: white;
}

.btn-success:hover:not(:disabled) {
  background: #059669;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.btn-secondary {
  background: var(--background-color);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover:not(:disabled) {
  background: #e5e7eb;
  transform: translateY(-1px);
}

.btn-disabled,
.btn:disabled {
  background: var(--background-color);
  color: var(--text-secondary);
  cursor: not-allowed;
  opacity: 0.5;
}

.btn:disabled:hover {
  transform: none;
  box-shadow: none;
}

/* ========================================
   TABLE STYLES
   ======================================== */

.modern-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background: var(--surface-color);
  border-radius: var(--radius-lg);
  overflow: hidden;
  border: 1px solid var(--border-color);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.modern-table thead {
  background: var(--background-color);
}

.modern-table th {
  padding: 12px 16px;
  text-align: left;
  font-size: 12px;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-bottom: 1px solid var(--border-color);
}

.modern-table td {
  padding: 16px;
  font-size: 14px;
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-color);
}

.modern-table tbody tr:last-child td {
  border-bottom: none;
}

.modern-table tbody tr:hover {
  background: var(--background-color);
}

.modern-table .amount {
  text-align: right;
  font-weight: 500;
}

/* ========================================
   ACTION BUTTONS
   ======================================== */

.action-buttons {
  display: flex;
  gap: 8px;
}

.icon-button {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  border: 1px solid var(--border-color);
  background: var(--surface-color);
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.icon-button:hover {
  background: var(--background-color);
  color: var(--primary-color);
  border-color: var(--primary-color);
  transform: translateY(-1px);
}

/* Integration-specific button styling */
.send-to-xero-btn {
  border-color: #13B5EA;
  color: #13B5EA;
}

.send-to-xero-btn:hover {
  background: #13B5EA;
  color: white;
  border-color: #13B5EA;
}

.send-to-quickbooks-btn {
  border-color: #0077C5;
  color: #0077C5;
}

.send-to-quickbooks-btn:hover {
  background: #0077C5;
  color: white;
  border-color: #0077C5;
}

/* ========================================
   PAGINATION
   ======================================== */

.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--space-6);
  padding-top: var(--space-6);
  border-top: 1px solid var(--border-color);
}

.pagination-info {
  font-size: 14px;
  color: var(--text-secondary);
}

.pagination-controls {
  display: flex;
  gap: 4px;
}

.pagination-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  border: 1px solid var(--border-color);
  background: var(--surface-color);
  color: var(--text-secondary);
  text-decoration: none;
  font-size: 14px;
  transition: all 0.2s ease;
}

.pagination-link:hover:not(.disabled) {
  background: var(--background-color);
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.pagination-link.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.pagination-link.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* ========================================
   MODAL STYLES
   ======================================== */

.modal {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
  backdrop-filter: blur(4px) !important;
  display: none !important;
  align-items: center !important;
  justify-content: center !important;
  z-index: 99999 !important;
  opacity: 0 !important;
  visibility: hidden !important;
  transition: opacity 0.3s ease, visibility 0.3s ease !important;
}

.modal.show {
  display: flex !important;
  opacity: 1 !important;
  visibility: visible !important;
}

.modal-content {
  background: var(--surface-color);
  border-radius: var(--radius-lg);
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  margin: 0;
  position: relative;
  transform: scale(0.9);
  opacity: 0;
  transition: all 0.3s ease;
}

.modal.show .modal-content,
.modal[style*="display: flex"] .modal-content {
  transform: scale(1);
  opacity: 1;
}

.modal-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h2 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.close-modal {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  border: 1px solid var(--border-color);
  background: var(--surface-color);
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 16px;
}

.close-modal:hover {
  background: var(--background-color);
  color: var(--danger-color);
  border-color: var(--danger-color);
}

.modal-body {
  padding: var(--space-6);
  max-height: 60vh;
  overflow-y: auto;
}

.modal-footer {
  padding: var(--space-6);
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
  gap: var(--space-4);
}

/* Large Modal */
.large-modal {
  max-width: 1000px;
  width: 95%;
}

.large-modal .modal-content {
  max-width: 1000px;
  width: 100%;
}

/* ========================================
   PAYSLIP MODAL SPECIFIC STYLES
   ======================================== */

.payslips-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
  margin-bottom: var(--space-6);
  padding: var(--space-4);
  background: var(--background-color);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
}

.stat-item {
  text-align: center;
}

.stat-label {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
}

.stat-label i {
  font-size: 14px;
  color: var(--primary-color);
}

.stat-value {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
}

.stat-value.currency {
  color: var(--success-color);
}

/* Payslips Table */
.payslips-table-container {
  overflow-x: auto;
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
}

.payslips-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background: var(--surface-color);
  margin: 0;
}

.payslips-table th {
  padding: 12px 16px;
  text-align: left;
  font-size: 12px;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-bottom: 1px solid var(--border-color);
  background: var(--background-color);
}

.payslips-table td {
  padding: 12px 16px;
  font-size: 14px;
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-color);
}

.payslips-table tbody tr:last-child td {
  border-bottom: none;
}

.payslips-table tbody tr:hover {
  background: var(--background-color);
}

.payslips-table .text-right {
  text-align: right;
}

.payslips-table .amount {
  font-weight: 500;
}

/* ========================================
   CHECKBOX STYLES
   ======================================== */

.checkbox-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}

.checkbox-wrapper input[type="checkbox"] {
  width: 16px;
  height: 16px;
  border: 2px solid var(--border-color);
  border-radius: 4px;
  background: var(--surface-color);
  cursor: pointer;
  position: relative;
  appearance: none;
}

.checkbox-wrapper input[type="checkbox"]:checked {
  background: var(--primary-color);
  border-color: var(--primary-color);
}

.checkbox-wrapper input[type="checkbox"]:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.checkbox-wrapper input[type="checkbox"]:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.checkbox-wrapper label {
  margin: 0;
  cursor: pointer;
}

/* ========================================
   FREQUENCY SECTIONS
   ======================================== */

.frequency-section {
  margin-bottom: 2rem;
  background: var(--surface-color);
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid var(--border-color);
}

.frequency-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
  padding: 1rem;
  background-color: var(--background-color);
  border-radius: 4px;
}

.frequency-header h4 {
  margin: 0;
  font-size: 1.2rem;
  color: var(--text-primary);
}

.frequency-header .review-btn {
  margin-left: 1rem;
}

.frequency-header h3 {
  font-size: 1.25rem;
  color: var(--text-primary);
  margin: 0;
}

.frequency-stats {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.frequency-stats .count {
  font-weight: 600;
  color: var(--text-secondary);
}

.frequency-stats .total-amount {
  color: var(--success-color);
  font-weight: 600;
}

.period-section {
  background: var(--surface-color);
  border-radius: 6px;
  margin: 1rem 0;
  padding: 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border-color);
}

.period-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.period-header h4 {
  font-size: 1.1rem;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.period-stats {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.period-actions {
  display: flex;
  gap: 0.5rem;
}

/* Summary Stats Styles */
.period-summary {
  background: var(--background-color);
  border-radius: 6px;
  padding: 1rem;
  margin: 0.75rem 0;
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.summary-stats .stat-item {
  background: var(--surface-color);
  padding: 0.75rem;
  border-radius: 4px;
  transition: transform 0.2s ease;
  border: 1px solid var(--border-color);
}

.summary-stats .stat-item:hover {
  transform: translateY(-2px);
}

/* Employee List Styles */
.employee-list-container {
  border-top: 1px solid var(--border-color);
  margin-top: 1rem;
  padding-top: 1rem;
}

.employee-list-container .payslips-list {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-in-out;
}

.employee-list-container[style*="display: block"] .payslips-list {
  max-height: 1000px;
}

/* ========================================
   LOADING STATES
   ======================================== */

.loading-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  backdrop-filter: blur(4px);
}

.loading-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: white;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ========================================
   NOTIFICATIONS
   ======================================== */

.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 16px 24px;
  border-radius: var(--radius-lg);
  color: white;
  font-weight: 500;
  font-size: 14px;
  z-index: 9999;
  display: none;
  align-items: center;
  gap: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  min-width: 300px;
  animation: slideIn 0.5s ease-out;
}

.notification.success {
  background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
}

.notification.error {
  background: linear-gradient(135deg, var(--danger-color) 0%, #dc2626 100%);
}

.notification.info {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.notification.warning {
  background: linear-gradient(135deg, var(--warning-color) 0%, #d97706 100%);
}

.notification i {
  font-size: 18px;
  flex-shrink: 0;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* ========================================
   EMPTY STATES
   ======================================== */

.empty-state,
.empty-state-progress {
  text-align: center;
  padding: var(--space-8);
  color: var(--text-secondary);
}

.empty-state i,
.empty-state-progress i {
  font-size: 48px;
  margin-bottom: var(--space-4);
  color: var(--border-color);
}

.empty-state h3,
.empty-state-progress h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 8px 0;
}

.empty-state p,
.empty-state-progress p {
  margin: 0;
  font-size: 14px;
  line-height: 1.4;
}

.no-data {
  text-align: center;
  padding: var(--space-8);
  color: var(--text-secondary);
  font-style: italic;
}

/* ========================================
   SUMMARY ACTIONS
   ======================================== */

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-4);
}

.summary-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.summary-actions {
  display: flex;
  gap: var(--space-4);
}

/* ========================================
   TABLE CONTAINER
   ======================================== */

.table-container {
  overflow-x: auto;
  border-radius: var(--radius-lg);
}

/* ========================================
   MODAL SPECIFIC OVERRIDES
   ======================================== */

/* Prevent body scroll when modal is open */
body.modal-open {
  overflow: hidden;
}

/* Bank file modal specific sizing */
#bankFileModal .modal-content {
  max-width: 500px;
  width: 90%;
}

/* Modal Base Styles - Allow JavaScript Control */
#bankFileModal,
#payslipReviewModal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 99999;
  background-color: rgba(0, 0, 0, 0.5);
  align-items: center;
  justify-content: center;
  display: none; /* Default hidden, controlled by JavaScript */
}

/* Modal Show State - Controlled by JavaScript */
#bankFileModal.show,
#payslipReviewModal.show {
  display: flex !important;
  opacity: 1 !important;
  visibility: visible !important;
  pointer-events: auto !important;
}

/* ========================================
   SELECTIVE MODAL RESET
   ======================================== */

.xpay-modal,
#xpay-bank-file-modal,
#missingBankDetailsModal {
  display: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
  pointer-events: none !important;
}

/* ========================================
   UTILITY CLASSES FOR INLINE STYLE REPLACEMENTS
   ======================================== */

.modal-description {
  color: var(--text-secondary);
  font-size: 14px;
  margin-top: 8px;
}

.notification-hidden {
  display: none;
}

.btn-hidden {
  display: none;
}
