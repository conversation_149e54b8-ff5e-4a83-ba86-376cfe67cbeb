/* Unified Design System Variables */
:root {
  /* Brand Colors */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;

  /* Semantic Colors */
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --info-color: #06b6d4;

  /* Neutral Colors */
  --white: #ffffff;
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* Application Colors */
  --primary-color: var(--primary-500);
  --secondary-color: var(--primary-600);
  --background-color: var(--gray-50);
  --surface-color: var(--white);
  --text-primary: var(--gray-900);
  --text-secondary: var(--gray-600);
  --text-muted: var(--gray-400);
  --border-color: var(--gray-200);
  --border-light: var(--gray-100);

  /* Spacing Scale */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;

  /* Border Radius Scale */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;

  /* Shadow Scale */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Typography Scale */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;

  /* Font Weights */
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;

  /* Transitions */
  --transition-fast: 150ms ease;
  --transition-base: 200ms ease;
  --transition-slow: 300ms ease;

  /* Layout */
  --sidebar-width: 280px;
  --sidebar-collapsed-width: 72px;
  --header-height: 80px;
  --content-padding: var(--space-6);
}

/* Unified Layout Foundation with Beautiful Purple Gradient */
.layout-wrapper {
  display: flex;
  min-height: 100vh;
  width: 100vw; /* Full viewport width */
  max-width: 100vw; /* Prevent overflow */
  overflow-x: hidden; /* Prevent horizontal scroll */
  background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #8e44ad 50%, #9b59b6 75%, #a569bd 100%);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  position: relative;
  box-sizing: border-box; /* Include padding/border in width */
}

/* Remove the purple border */

.content-wrapper {
  flex: 1 !important;
  margin: 0 !important; /* Remove top margin - header is fixed */
  padding: 0 !important; /* No padding */
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px) !important;
  border-radius: 0 !important;
  min-height: 100vh !important;
  width: 100% !important; /* Full width of remaining space */
  max-width: 100% !important; /* Prevent overflow */
  overflow-x: hidden !important; /* Prevent horizontal scroll */
  box-sizing: border-box !important; /* Include padding/border in width */
}

.main-container {
  margin: 0 !important;
  padding: 24px !important; /* Restore padding for proper spacing */
  background: transparent !important;
  min-height: calc(100vh - 80px) !important;
  width: calc(100% - 48px) !important; /* Account for padding to prevent overflow */
  max-width: calc(100% - 48px) !important; /* Prevent horizontal overflow */
  box-sizing: border-box !important; /* Include padding in width calculation */
  overflow-x: hidden !important; /* Prevent horizontal scroll */
}

/* Visual Connection Elements - Removed top border */

/* Unified Surface Pattern */
.surface {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-base);
}

.surface:hover {
  border-color: var(--primary-200);
  box-shadow: var(--shadow-md);
}

.surface-elevated {
  box-shadow: var(--shadow-lg);
}

.surface-elevated:hover {
  box-shadow: var(--shadow-xl);
}

/* Reset Greeting - Minimal styling */
.greeting-card {
  padding: 100px 20px 20px 20px !important; /* Top padding to clear fixed header */
  margin: 0 !important;
  background: none !important;
  border: none !important;
  border-radius: 0 !important;
  box-shadow: none !important;
}

.greeting-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.greeting-text {
  flex: 1;
}

.greeting-text h1 {
  font-size: 24px; /* Increased from 18px for more prominence */
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.greeting-text p {
  color: #6b7280;
  font-size: 12px; /* Match legend-item font size for consistency */
  margin: 0;
  font-weight: 500; /* Match legend-item font weight */
}

.wave {
  animation: wave 2s ease-in-out infinite;
  display: inline-block;
}

@keyframes wave {
  0%, 100% { transform: rotate(0deg); }
  25% { transform: rotate(20deg); }
  75% { transform: rotate(-10deg); }
}

.time-indicator {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  color: var(--text-muted);
  font-size: var(--text-xs);
  background: var(--gray-50);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-light);
  font-weight: var(--font-medium);
}

/* Hide greeting decoration for simple design */
.greeting-decoration {
  display: none;
}

/* Alternative: Even more minimal greeting option */
.greeting-minimal {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-3) 0;
  margin-bottom: var(--space-4);
  border-bottom: 1px solid var(--border-light);
}

.greeting-minimal h1 {
  font-size: var(--text-lg);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  margin: 0;
}

.greeting-minimal .time-indicator {
  background: none;
  border: none;
  padding: 0;
  color: var(--text-muted);
  font-size: var(--text-xs);
}

/* Page title approach - integrate with summary section */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-6);
}

.page-title {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.page-title h1 {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin: 0;
}

.page-subtitle {
  color: var(--text-muted);
  font-size: var(--text-sm);
  font-weight: var(--font-normal);
}

/* Reset Summary Section */
.summary-section {
  background: white;
  border: 1px solid #e5e7eb;
  padding: 20px;
  margin: 20px;
}

.summary-header {
  margin-bottom: 20px;
}

.summary-header h2 {
  margin: 0;
  font-size: 20px;
  color: #1f2937;
}

.summary-header h2 {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: white;
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.summary-header h2::before {
  content: '';
  width: 4px;
  height: var(--space-8);
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
  border-radius: var(--radius-sm);
}

.date-range {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-4);
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md);
  color: rgba(255, 255, 255, 0.9);
  font-size: var(--text-sm);
  border: 1px solid rgba(255, 255, 255, 0.2);
  font-weight: var(--font-medium);
  backdrop-filter: blur(10px);
}

.date-range i {
  color: rgba(255, 255, 255, 0.8);
  font-size: var(--text-sm);
}

/* Unified Summary Stats */
.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-6);
}

.stat-item {
  background: var(--surface-color);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  border: 1px solid var(--border-color);
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
}

.stat-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-500) 0%, var(--primary-600) 100%);
}

.stat-item:hover {
  border-color: var(--primary-300);
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.stat-icon {
  width: var(--space-12);
  height: var(--space-12);
  border-radius: var(--radius-lg);
  background: var(--primary-100);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--space-4);
  box-shadow: var(--shadow-sm);
}

.stat-icon i {
  font-size: var(--text-xl);
  color: var(--primary-600);
}

.stat-info {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.stat-label {
  color: var(--text-secondary);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.stat-value {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  line-height: 1;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  width: fit-content;
}

.stat-trend.positive {
  color: var(--success-color);
  background: rgba(16, 185, 129, 0.1);
}

.stat-trend.negative {
  color: var(--danger-color);
  background: rgba(239, 68, 68, 0.1);
}

/* Reset Dashboard Cards */
.main-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin: 20px;
}

.dashboard-card {
  background: white;
  border: 1px solid #e5e7eb;
  padding: 20px;
}

.dashboard-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.5) 100%);
}

.dashboard-card:hover {
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 0.2);
}

.card-icon {
  width: var(--space-12);
  height: var(--space-12);
  border-radius: var(--radius-lg);
  background: var(--primary-100);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: var(--shadow-sm);
}

.card-icon i {
  font-size: var(--text-xl);
  color: rgba(255, 255, 255, 0.9);
}

.card-content {
  flex: 1;
}

.card-content h3 {
  color: rgba(255, 255, 255, 0.9);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  margin: 0 0 var(--space-2) 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.card-stats {
  display: flex;
  align-items: baseline;
  gap: var(--space-3);
}

.card-stats .number {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: white;
  line-height: 1;
}

.card-stats .change {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
}

.card-stats .change.positive {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.card-stats .change.negative {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

.period {
  color: var(--text-muted);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Charts and Calendar Layout */
.charts-calendar-section {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
}

/* Three Card Row Layout */
.three-card-row {
  display: flex;
  gap: 1rem; /* Reduced gap for even spacing */
  margin-bottom: 3rem; /* Increased bottom margin for better section separation */
  width: 100%; /* Full width */
  max-width: 100%; /* Prevent overflow */
  box-sizing: border-box; /* Include padding/border in width */
}

/* Chart Card Container (50%) */
.chart-card-container {
  flex: 0 0 50%;
}

/* Notifications Card Container (20%) */
.notifications-card-container {
  flex: 0 0 20%;
}

.notifications-card-container .notification-card {
  height: 100%;
  margin: 0;
  display: flex;
  flex-direction: column;
}

.notifications-card-container .notification-header {
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid #e5e7eb;
}

.notifications-card-container .notification-header h3 {
  font-size: 0.875rem;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.notifications-card-container .view-all-link {
  font-size: 0.75rem;
  color: #6b7280;
  text-decoration: none;
}

.notifications-card-container .view-all-link:hover {
  color: #374151;
}

.notifications-card-container .notification-body {
  flex: 1;
}

.notifications-card-container .notification-item {
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  border-radius: 8px;
}

.notifications-card-container .notification-title {
  font-size: 0.75rem;
  margin-bottom: 0.25rem;
}

.notifications-card-container .notification-content {
  font-size: 0.7rem;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.notifications-card-container .notification-date {
  font-size: 0.65rem;
}

.notifications-card-container .no-notifications {
  text-align: center;
  padding: 1rem;
  color: #9ca3af;
}

.notifications-card-container .no-notifications i {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  display: block;
}

.notifications-card-container .no-notifications p {
  font-size: 0.75rem;
  margin: 0;
}

/* Calendar Card Container (30%) */
.calendar-card-container {
  flex: 0 0 30%;
}

/* Compact Calendar Card */
.calendar-card-compact {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 1rem;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Compact Calendar Header */
.calendar-header-compact {
  margin-bottom: 1rem;
}

.calendar-nav-compact {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.calendar-month-title-compact {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.nav-btn-compact {
  width: 28px;
  height: 28px;
  border: none;
  background: #f9fafb;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6b7280;
}

.nav-btn-compact:hover {
  background: #f3f4f6;
  color: #374151;
}

/* Compact Calendar Grid */
.calendar-grid-compact {
  flex: 1;
  margin-bottom: 1rem;
}

.calendar-weekdays-compact {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 2px;
  margin-bottom: 0.5rem;
}

.weekday-compact {
  text-align: center;
  font-size: 0.75rem;
  font-weight: 500;
  color: #6b7280;
  padding: 0.25rem;
}

.calendar-days-compact {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 2px;
}

.calendar-day-compact {
  aspect-ratio: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #374151;
}

.calendar-day-compact:hover {
  background: #f3f4f6;
}

.calendar-day-compact.other-month {
  color: #d1d5db;
}

.calendar-day-compact.selected {
  background: #8b5cf6;
  color: white;
  font-weight: 600;
}

.calendar-day-compact.today {
  background: #ede9fe;
  color: #8b5cf6;
  font-weight: 600;
}

/* Compact Calendar Actions */
.calendar-actions-compact {
  display: flex;
  gap: 0.5rem;
}

.btn-compact {
  flex: 1;
  padding: 0.5rem;
  border: none;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
}

.btn-primary-compact {
  background: #8b5cf6;
  color: white;
}

.btn-primary-compact:hover {
  background: #7c3aed;
}

.btn-secondary-compact {
  background: #f3f4f6;
  color: #6b7280;
}

.btn-secondary-compact:hover {
  background: #e5e7eb;
  color: #374151;
}

/* Reference Design Calendar Card */
.calendar-card-reference {
  background: white;
  border-radius: 16px;
  padding: 0;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  max-width: 320px; /* Adjust width to fit better */
  margin: 0 auto; /* Center the calendar */
}

/* Calendar Art Section */
.calendar-art-section {
  width: 100%;
  height: 120px;
  overflow: hidden;
  border-radius: 16px 16px 0 0;
  position: relative;
}

.calendar-art-image-reference {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 16px 16px 0 0;
}

/* Calendar Header */
.calendar-header-reference {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px 8px 20px;
  background: white;
  border-radius: 16px 16px 0 0;
  position: relative;
  z-index: 2;
  margin-top: -16px; /* Overlap the image */
}

.nav-btn-reference {
  background: none;
  border: none;
  padding: 8px;
  border-radius: 8px;
  cursor: pointer;
  color: #6b7280;
  transition: all 0.2s ease;
  font-size: 16px;
}

.nav-btn-reference:hover {
  background: #f3f4f6;
  color: #374151;
}

.calendar-month-title-reference {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  position: relative;
}

/* Add grey line above month title */
.calendar-month-title-reference::before {
  content: '';
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 2px;
  background: #d1d5db;
  border-radius: 1px;
}

/* Event indicators for calendar days */
.day-reference.has-events {
  position: relative;
}

.day-reference .event-dot {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 6px;
  height: 6px;
  background: #8b5cf6;
  border-radius: 50%;
  border: 1px solid white;
}

/* Calendar Grid */
.calendar-grid-reference {
  padding: 0 20px;
  margin-bottom: 16px;
  background: white;
  border-radius: 16px 16px 0 0;
  position: relative;
  z-index: 2;
  margin-top: -8px; /* Additional overlap for seamless look */
}

.calendar-weekdays-reference {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 2px;
  margin-bottom: 8px;
}

.weekday-reference {
  text-align: center;
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
  padding: 8px 4px;
}

.calendar-days-reference {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 2px;
}

.day-reference {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  font-size: 14px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #374151;
  font-weight: 500;
}

.day-reference:hover {
  background: #f3f4f6;
}

.day-reference.today {
  background: #ddd6fe;
  color: #7c3aed;
  font-weight: 600;
}

.day-reference.selected {
  background: #7c3aed;
  color: white;
  font-weight: 600;
  border-radius: 8px;
}

.day-reference.other-month {
  color: #d1d5db;
}

.day-reference.has-event {
  position: relative;
}

.day-reference.has-event::after {
  content: '';
  position: absolute;
  bottom: 2px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  background: #f59e0b;
  border-radius: 50%;
}

/* Upcoming Schedule Section */
.upcoming-schedule-reference {
  padding: 0 20px 20px 20px;
  border-top: 1px solid #f3f4f6;
  margin-top: 8px;
  padding-top: 16px;
}

.schedule-title-reference {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.schedule-date-reference {
  font-size: 12px;
  color: #6b7280;
  margin: 0 0 12px 0;
}

.schedule-list-reference {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.schedule-item-reference {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
}

.schedule-icon-reference {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: white;
}

.payroll-icon-reference {
  background: linear-gradient(135deg, #7c3aed, #a855f7);
}

.interview-icon-reference {
  background: linear-gradient(135deg, #f59e0b, #f97316);
}

.meeting-icon-reference {
  background: linear-gradient(135deg, #3b82f6, #6366f1);
}

.schedule-details-reference {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.schedule-title-text-reference {
  font-size: 13px;
  font-weight: 500;
  color: #1f2937;
}

.schedule-time-reference {
  font-size: 11px;
  color: #6b7280;
}

.schedule-action-btn-reference {
  background: none;
  border: none;
  padding: 4px;
  border-radius: 4px;
  cursor: pointer;
  color: #6b7280;
  transition: all 0.2s ease;
}

.schedule-action-btn-reference:hover {
  background: #f3f4f6;
  color: #374151;
}

/* Hidden Actions for Functionality */
.calendar-actions-hidden {
  display: none !important;
}

/* Responsive Design for Three Card Row */
@media (max-width: 1200px) {
  .three-card-row {
    flex-direction: column;
  }

  .chart-card-container,
  .notifications-card-container,
  .calendar-card-container {
    flex: none;
  }
}

@media (max-width: 768px) {
  .three-card-row {
    gap: 1rem;
  }

  .calendar-card-compact {
    padding: 0.75rem;
  }

  .calendar-month-title-compact {
    font-size: 0.875rem;
  }

  .nav-btn-compact {
    width: 24px;
    height: 24px;
  }
}



.charts-card {
  background: white !important;
  border: 1px solid #e5e7eb !important;
  padding: 20px !important;
  border-radius: 12px !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

/* Calendar Card Styles */
.calendar-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  height: fit-content;
}

.calendar-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f3f4f6;
}

.calendar-art {
  flex-shrink: 0;
}

.calendar-art-image {
  width: 60px;
  height: 60px;
  object-fit: contain;
}

.calendar-title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 8px;
}

.calendar-subtitle {
  margin: 4px 0 0 0;
  font-size: 14px;
  color: #6b7280;
}

.calendar-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.nav-btn {
  background: #f3f4f6;
  border: none;
  border-radius: 6px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
}

.nav-btn:hover {
  background: #e5e7eb;
  transform: scale(1.05);
}

.current-month {
  font-weight: 600;
  color: #1f2937;
}

.calendar-grid {
  margin-bottom: 20px;
}

.calendar-weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 2px;
  margin-bottom: 8px;
}

.weekday {
  text-align: center;
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
  padding: 8px 4px;
}

.calendar-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 2px;
}

.calendar-day {
  aspect-ratio: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.calendar-day:hover:not(.today) {
  background: #f3f4f6;
  transform: scale(1.05);
}

.calendar-day.today {
  background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
  color: white;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(139, 92, 246, 0.3);
}

.calendar-day.other-month {
  color: #d1d5db;
}

.upcoming-schedule {
  border-top: 1px solid #f3f4f6;
  padding-top: 20px;
}

.upcoming-schedule h4 {
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.schedule-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 0;
  border-bottom: 1px solid #f9fafb;
  transition: all 0.2s ease;
  border-radius: 8px;
  margin: 0 -8px;
  padding-left: 8px;
  padding-right: 8px;
}

.schedule-item:hover {
  background: #f9fafb;
  transform: translateX(4px);
}

.schedule-item:last-child {
  border-bottom: none;
}

.schedule-icon {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.payroll-icon {
  background: #ddd6fe;
  color: #8b5cf6;
}

.interview-icon {
  background: #fed7d7;
  color: #e53e3e;
}

.meeting-icon {
  background: #bee3f8;
  color: #3182ce;
}

.schedule-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.schedule-title {
  font-weight: 500;
  color: #1f2937;
  font-size: 14px;
}

.schedule-time {
  font-size: 12px;
  color: #6b7280;
}

.chart-header {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
}

.chart-header h3 {
  margin: 0;
  font-size: 18px;
  color: #1f2937;
}

.chart-header h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.chart-header h3 i {
  color: #6366f1;
  font-size: 20px;
}

.chart-legend {
  display: flex;
  gap: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.legend-item i {
  color: #6366f1;
  font-size: 14px;
}

/* Chart Title Section */
.chart-title-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* Integrated Chart Filters */
.chart-filters {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.chart-filter-form {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-group-inline {
  display: flex;
  align-items: center;
  gap: 4px;
}

.filter-group-inline label {
  display: flex;
  align-items: center;
  color: #6b7280;
  font-size: 14px;
}

.filter-group-inline label i {
  font-size: 16px;
  color: #6366f1;
}

.filter-select-compact {
  padding: 6px 8px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 12px;
  color: #374151;
  min-width: 60px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-select-compact:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.filter-button-compact {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 6px 8px;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  min-width: 32px;
  height: 32px;
}

.filter-button-compact:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.filter-button-compact i {
  font-size: 14px;
}

/* Responsive Chart Filters */
@media (max-width: 768px) {
  .chart-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .chart-filters {
    justify-content: center;
  }

  .chart-filter-form {
    gap: 6px;
  }

  .filter-select-compact {
    min-width: 50px;
    font-size: 11px;
  }
}

/* ===== MODERN DASHBOARD SECTIONS ===== */

/* Dashboard Section */
.dashboard-section {
  margin-bottom: 2.5rem; /* Increased for better section separation */
  margin-top: 1rem; /* Add consistent top margin */
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
  gap: 1rem;
}

.section-title h2 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.125rem; /* Slightly smaller for better hierarchy */
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.25rem 0;
  line-height: 1.4; /* Better line height for readability */
}

.section-title h2 i {
  color: #6366f1;
  font-size: 1.25rem; /* Proportional to text size */
}

.section-subtitle {
  color: #6b7280;
  font-size: 0.875rem;
  margin: 0;
  font-weight: 400; /* Ensure consistent weight */
}

.section-action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: white;
  text-decoration: none;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
}

.section-action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
  color: white;
  text-decoration: none;
}

/* Dashboard Cards Grid */
.dashboard-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); /* Reduced minimum to prevent overflow */
  gap: 1.25rem; /* Consistent gap with other grid layouts */
  width: 100%; /* Full width */
  max-width: 100%; /* Prevent overflow */
  box-sizing: border-box; /* Include padding/border in width */
}

/* Modern Dashboard Card */
.dashboard-card.modern {
  background: white;
  border-radius: 0.75rem; /* Consistent with other cards */
  padding: 1.5rem;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease; /* Faster transition for better responsiveness */
  position: relative;
  overflow: hidden;
}

.dashboard-card.modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #6366f1 0%, #8b5cf6 100%);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.dashboard-card.modern:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
  border-color: #d1d5db;
}

.dashboard-card.modern:hover::before {
  opacity: 1;
}

.card-header {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1rem;
}

.card-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.card-icon i {
  font-size: 1.5rem;
  color: white;
}

/* Card Icon Variants */
.card-icon.growth {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.card-icon.retention {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.card-icon.distribution {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.card-icon.headcount {
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
}

.card-icon.joiners {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.card-icon.terminations {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.card-title {
  flex: 1;
}

.card-title h3 {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.25rem 0;
}

.card-subtitle {
  font-size: 0.75rem;
  color: #6b7280;
  margin: 0;
}

.card-content {
  margin-top: 1rem;
}

.card-value {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.75rem; /* Increased spacing for better hierarchy */
  line-height: 1.2; /* Better line height for large numbers */
}

.card-trend {
  display: flex;
  align-items: center;
  gap: 0.375rem; /* Slightly larger gap for better readability */
  font-size: 0.875rem;
  font-weight: 500;
  padding: 0.25rem 0.5rem; /* Add padding for better visual weight */
  border-radius: 0.375rem; /* Add border radius for modern look */
  background: rgba(0, 0, 0, 0.02); /* Subtle background */
  width: fit-content; /* Don't stretch full width */
}

.card-trend.positive {
  color: #059669;
  background: rgba(16, 185, 129, 0.08); /* Subtle green background */
}

.card-trend.negative {
  color: #dc2626;
  background: rgba(239, 68, 68, 0.08); /* Subtle red background */
}

.card-trend.neutral {
  color: #6b7280;
  background: rgba(107, 114, 128, 0.08); /* Subtle gray background */
}

.card-trend i {
  font-size: 1rem;
}

/* ===== MODERN COMPANY MANAGEMENT ===== */

.companies-grid-modern {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 1.5rem;
}

.company-card-modern {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.company-card-modern:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #d1d5db;
}

.company-card-header-modern {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  gap: 1rem;
}

.company-info h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.company-employees {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #6b7280;
}

.company-employees i {
  color: #6366f1;
}

.company-status-modern {
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.company-status-modern.active {
  background: #dcfce7;
  color: #166534;
}

.company-status-modern.inactive {
  background: #f3f4f6;
  color: #6b7280;
}

.company-actions-modern {
  display: flex;
  gap: 0.75rem;
}

.action-btn-modern {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.action-btn-modern.primary {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: white;
}

.action-btn-modern.primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
  color: white;
  text-decoration: none;
}

.action-btn-modern.secondary {
  background: white;
  color: #6b7280;
  border-color: #d1d5db;
}

.action-btn-modern.secondary:hover {
  background: #f9fafb;
  color: #374151;
  border-color: #9ca3af;
  text-decoration: none;
}

/* No Companies State */
.no-companies-modern {
  grid-column: 1 / -1;
}

.empty-state {
  text-align: center;
  padding: 3rem 2rem;
  background: white;
  border-radius: 1rem;
  border: 2px dashed #d1d5db;
}

.empty-state i {
  font-size: 3rem;
  color: #d1d5db;
  margin-bottom: 1rem;
}

.empty-state h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.empty-state p {
  color: #6b7280;
  margin: 0 0 1.5rem 0;
}

.btn-primary-modern {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: white;
  text-decoration: none;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-primary-modern:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
  color: white;
  text-decoration: none;
}

/* ===== RESPONSIVE DESIGN FOR MODERN COMPONENTS ===== */

@media (max-width: 768px) {
  .section-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .section-action-btn {
    align-self: flex-start;
  }

  .dashboard-cards-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .dashboard-section {
    margin-bottom: 2rem; /* Reduced margin on mobile */
  }

  .companies-grid-modern {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .company-card-header-modern {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }

  .company-actions-modern {
    flex-direction: column;
    gap: 0.5rem;
  }

  .card-header {
    gap: 0.75rem;
  }

  .card-icon {
    width: 2.5rem;
    height: 2.5rem;
  }

  .card-icon i {
    font-size: 1.25rem;
  }

  .card-value {
    font-size: 1.75rem;
  }
}

@media (max-width: 480px) {
  .dashboard-section {
    margin-bottom: 1.5rem;
  }

  .section-header {
    margin-bottom: 1rem;
  }

  .section-title h2 {
    font-size: 1rem; /* Smaller on very small screens */
  }

  .section-title h2 i {
    font-size: 1.125rem; /* Proportional icon size */
  }

  .dashboard-card.modern {
    padding: 1rem;
  }

  .card-value {
    font-size: 1.75rem; /* Smaller values on mobile */
  }

  .dashboard-cards-grid {
    gap: 0.75rem; /* Tighter spacing on small screens */
  }

  .company-card-modern {
    padding: 1rem;
  }
}

/* Clean Filter Section */
.filter-section {
  background: var(--card-background);
  border-radius: 0.75rem;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border: 1px solid var(--border-color);
}

.filter-header h3 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 1rem 0;
}

.filter-form {
  display: flex;
  align-items: end;
  gap: 1rem;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-group label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: 0.375rem;
}

.filter-select {
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  background: var(--card-background);
  color: var(--text-primary);
  font-size: 0.875rem;
}

.filter-button {
  padding: 0.5rem 1rem;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.375rem;
  transition: all 0.2s ease;
}

.filter-button:hover {
  background: var(--secondary-color);
}

/* Reset Notification Card */
.notification-card {
  background: white;
  border: 1px solid #e5e7eb;
  padding: 20px;
  margin: 20px;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.notification-header h3 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.view-all-link {
  color: var(--primary-color);
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.view-all-link:hover {
  color: var(--secondary-color);
}

.notification-item {
  padding: 1rem;
  background: var(--border-light);
  border-radius: 0.5rem;
}

.notification-meta {
  margin-bottom: 0.5rem;
}

.notification-date {
  color: var(--text-muted);
  font-size: 0.8125rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.notification-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 0.5rem 0;
}

.notification-content {
  color: var(--text-secondary);
  font-size: 0.875rem;
  line-height: 1.5;
}

.no-notifications {
  text-align: center;
  padding: 2rem;
  color: var(--text-muted);
}

.no-notifications i {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  display: block;
}

/* Reset Company Management Card */
.company-management-card {
  background: white;
  border: 1px solid #e5e7eb;
  padding: 20px;
  margin: 20px;
}

.management-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.management-header h3 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.add-company-btn {
  background: var(--primary-color);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.375rem;
  transition: all 0.2s ease;
}

.add-company-btn:hover {
  background: var(--secondary-color);
  color: white;
}

.companies-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1rem;
}

.company-card {
  background: var(--border-light);
  border-radius: 0.5rem;
  padding: 1rem;
  border: 1px solid var(--border-color);
}

.company-card-header {
  display: flex;
  justify-content: space-between;
  align-items: start;
  margin-bottom: 0.75rem;
}

.company-card h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.company-status {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  background: var(--border-color);
  color: var(--text-secondary);
}

.company-status.active {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.company-stats {
  margin-bottom: 0.75rem;
}

.company-stats .stat {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  color: var(--text-secondary);
  font-size: 0.8125rem;
}

.company-actions {
  display: flex;
  gap: 0.5rem;
}

.action-link {
  color: var(--primary-color);
  text-decoration: none;
  font-size: 0.8125rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.action-link:hover {
  color: var(--secondary-color);
}

.no-companies {
  text-align: center;
  padding: 2rem;
  color: var(--text-muted);
}

.no-companies i {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  display: block;
}

/* Responsive Design */
@media screen and (max-width: 1024px) {
  .main-container {
    padding: 1.5rem;
  }

  .summary-stats {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }

  .main-cards {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }

  .companies-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}

@media screen and (max-width: 768px) {
  .main-container {
    padding: 1rem;
    margin-top: 0;
  }

  /* Stack charts and calendar vertically on mobile */
  .charts-calendar-section {
    flex-direction: column;
    gap: 15px;
  }

  .charts-section-half,
  .calendar-section-half {
    flex: none;
    width: 100%;
  }

  .calendar-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .calendar-art-image {
    width: 50px;
    height: 50px;
  }

  .greeting-card {
    display: block;
    text-align: left;
    margin-bottom: 2rem;
  }

  .greeting-decoration {
    display: none;
  }

  .summary-header {
    flex-direction: column;
    align-items: start;
    gap: 1rem;
  }

  .summary-stats {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .main-cards {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .dashboard-card {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .card-stats {
    justify-content: center;
  }

  .filter-form {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-group {
    width: 100%;
  }

  .filter-select,
  .filter-button {
    width: 100%;
  }

  .management-header {
    flex-direction: column;
    align-items: start;
    gap: 1rem;
  }

  .companies-grid {
    grid-template-columns: 1fr;
  }

  .notification-header {
    flex-direction: column;
    align-items: start;
    gap: 0.5rem;
  }
}

@media screen and (max-width: 480px) {
  .main-container {
    padding: 0.75rem;
  }

  /* Mobile styles removed - using base styles */

  .greeting-text h1 {
    font-size: 1.25rem; /* Increased from 1rem for better mobile prominence */
  }

  .stat-value {
    font-size: 1.5rem;
  }

  .card-stats .number {
    font-size: 1.5rem;
  }

  .company-actions {
    flex-direction: column;
    gap: 0.25rem;
  }
}

/* Unified Component Library */
.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  text-decoration: none;
  transition: all var(--transition-base);
  cursor: pointer;
  border: 1px solid transparent;
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-slow);
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
  color: var(--white);
  border-color: var(--primary-400);
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
  color: var(--white);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn-secondary {
  background: var(--surface-color);
  color: var(--text-secondary);
  border-color: var(--border-color);
}

.btn-secondary:hover {
  background: var(--gray-50);
  color: var(--text-primary);
  border-color: var(--gray-300);
  box-shadow: var(--shadow-md);
}

.btn-sm {
  padding: var(--space-2) var(--space-3);
  font-size: var(--text-xs);
  border-radius: var(--radius-sm);
}

.btn-lg {
  padding: var(--space-4) var(--space-6);
  font-size: var(--text-base);
  border-radius: var(--radius-lg);
}

/* Unified Form Elements */
.form-input,
.form-select,
.form-textarea {
  padding: var(--space-3) var(--space-4);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  background: var(--surface-color);
  color: var(--text-primary);
  transition: all var(--transition-base);
  box-shadow: var(--shadow-sm);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px var(--primary-100);
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: var(--text-muted);
}

/* Unified Card Patterns */
.card {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-500) 0%, var(--primary-600) 100%);
}

.card:hover {
  border-color: var(--primary-200);
  box-shadow: var(--shadow-lg);
  transform: translateY(-1px);
}

.card-header {
  padding: var(--space-6) var(--space-6) var(--space-4) var(--space-6);
  border-bottom: 1px solid var(--border-light);
}

.card-body {
  padding: var(--space-6);
}

.card-footer {
  padding: var(--space-4) var(--space-6) var(--space-6) var(--space-6);
  border-top: 1px solid var(--border-light);
  background: var(--gray-50);
}

/* Unified Badge System */
.badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.badge-primary {
  background: var(--primary-100);
  color: var(--primary-700);
}

.badge-success {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.badge-warning {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.badge-danger {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

/* Unified Animation System */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Apply Unified Animations */
.greeting-text {
  animation: fadeIn 0.6s ease-out;
}

.summary-section {
  animation: slideInUp 0.6s ease-out 0.1s both;
}

.dashboard-card:nth-child(1) {
  animation: slideInUp 0.6s ease-out 0.2s both;
}

.dashboard-card:nth-child(2) {
  animation: slideInUp 0.6s ease-out 0.3s both;
}

.dashboard-card:nth-child(3) {
  animation: slideInUp 0.6s ease-out 0.4s both;
}

.stat-item:nth-child(1) {
  animation: slideInLeft 0.6s ease-out 0.2s both;
}

.stat-item:nth-child(2) {
  animation: slideInLeft 0.6s ease-out 0.3s both;
}

.stat-item:nth-child(3) {
  animation: slideInLeft 0.6s ease-out 0.4s both;
}

.charts-card {
  /* Animation removed for consistency */
}

/* Loading States */
.loading {
  animation: pulse 2s infinite;
}

/* Unified Focus System */
.focus-ring:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* Visual Hierarchy Helpers */
.text-primary {
  color: var(--text-primary);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-muted {
  color: var(--text-muted);
}

.bg-primary {
  background: var(--primary-500);
  color: var(--white);
}

.bg-surface {
  background: var(--surface-color);
}

.border-primary {
  border-color: var(--primary-500);
}

/* Unified Spacing Utilities */
.p-0 { padding: 0; }
.p-1 { padding: var(--space-1); }
.p-2 { padding: var(--space-2); }
.p-3 { padding: var(--space-3); }
.p-4 { padding: var(--space-4); }
.p-6 { padding: var(--space-6); }
.p-8 { padding: var(--space-8); }

.m-0 { margin: 0; }
.m-1 { margin: var(--space-1); }
.m-2 { margin: var(--space-2); }
.m-3 { margin: var(--space-3); }
.m-4 { margin: var(--space-4); }
.m-6 { margin: var(--space-6); }
.m-8 { margin: var(--space-8); }

.mb-2 { margin-bottom: var(--space-2); }
.mb-4 { margin-bottom: var(--space-4); }
.mb-6 { margin-bottom: var(--space-6); }
.mb-8 { margin-bottom: var(--space-8); }

.wave {
  animation: wave 2s ease-in-out infinite;
}

@keyframes wave {
  0%, 100% { transform: rotate(0deg); }
  25% { transform: rotate(20deg); }
  75% { transform: rotate(-10deg); }
}

/* Focus States for Accessibility */
.sidebar-item:focus,
.toggle-btn:focus,
.company-select-btn:focus,
.search-container input:focus,
.user-pic:focus,
.sub-menu-link:focus,
.btn:focus,
.filter-select:focus,
.filter-button:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Print Styles */
@media print {
  .sidebar,
  .main-header,
  .toggle-btn,
  .calendar-actions,
  .filter-section {
    display: none !important;
  }

  .main-container {
    margin-left: 0 !important;
    margin-top: 0 !important;
    padding: 1rem !important;
  }

  .dashboard-card,
  .charts-card,
  .summary-section {
    break-inside: avoid;
    box-shadow: none !important;
    border: 1px solid #ccc !important;
  }
}

/* Payroll Calendar Section - Reference Image Design */
.payroll-calendar-section {
  background: white;
  border-radius: 1.25rem;
  padding: 0;
  margin-top: 2rem;
  border: 1px solid #e5e7eb;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

/* Calendar and Schedule Container */
.calendar-schedule-container {
  display: grid;
  grid-template-columns: 1fr 300px;
  min-height: 400px;
}

/* Calendar Section */
.calendar-section {
  padding: 2rem;
  background: white;
  border-right: 1px solid #e5e7eb;
}

/* Calendar Header Navigation */
.calendar-header-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2rem;
}

.calendar-month-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.nav-btn {
  width: 40px;
  height: 40px;
  border: none;
  background: #f9fafb;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6b7280;
}

.nav-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

/* Calendar Grid Container */
.calendar-grid-container {
  background: white;
}

/* Calendar Weekdays */
.calendar-weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  margin-bottom: 1rem;
}

.weekday {
  padding: 0.75rem;
  text-align: center;
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Calendar Days Grid */
.calendar-days-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  background: #f9fafb;
  border-radius: 8px;
  overflow: hidden;
}

.calendar-day {
  aspect-ratio: 1;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.calendar-day:hover {
  background: #f3f4f6;
}

.calendar-day.other-month {
  color: #d1d5db;
}

.calendar-day.selected {
  background: #8b5cf6;
  color: white;
  font-weight: 600;
}

.calendar-day.today {
  background: #ede9fe;
  color: #8b5cf6;
  font-weight: 600;
}

.calendar-day.has-event::after {
  content: '';
  position: absolute;
  bottom: 4px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  background: #f59e0b;
  border-radius: 50%;
}

/* Upcoming Schedule Section */
.upcoming-schedule-section {
  padding: 2rem;
  background: #fafbfc;
  border-left: 1px solid #e5e7eb;
}

.schedule-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.schedule-date {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0 0 1.5rem 0;
}

/* Schedule List */
.schedule-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.schedule-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
  cursor: pointer;
}

.schedule-item:hover {
  border-color: #d1d5db;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* Schedule Icons */
.schedule-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.schedule-icon.payroll-icon {
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
  color: white;
}

.schedule-icon.interview-icon {
  background: linear-gradient(135deg, #f59e0b, #f97316);
  color: white;
}

.schedule-icon.meeting-icon {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
}

/* Schedule Details */
.schedule-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.schedule-title-text {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1f2937;
}

.schedule-time {
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 500;
}

/* Schedule Action Button */
.schedule-action-btn {
  width: 24px;
  height: 24px;
  border: none;
  background: #f9fafb;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #9ca3af;
  flex-shrink: 0;
}

.schedule-action-btn:hover {
  background: #f3f4f6;
  color: #6b7280;
}

/* Advanced Calendar Features (Hidden by default) */
.advanced-calendar-features {
  padding: 2rem;
  border-top: 1px solid #e5e7eb;
  background: #fafbfc;
}

/* Button Styles */
.btn-outline {
  background: transparent;
  border: 1px solid #d1d5db;
  color: #6b7280;
}

.btn-outline:hover {
  background: #f9fafb;
  border-color: #9ca3af;
  color: #374151;
}

/* Responsive Design for New Calendar Layout */
@media (max-width: 1024px) {
  .calendar-schedule-container {
    grid-template-columns: 1fr;
  }

  .calendar-section {
    border-right: none;
    border-bottom: 1px solid #e5e7eb;
  }

  .upcoming-schedule-section {
    border-left: none;
    border-top: 1px solid #e5e7eb;
  }
}

@media (max-width: 768px) {
  .calendar-section,
  .upcoming-schedule-section {
    padding: 1.5rem;
  }

  .calendar-header-nav {
    margin-bottom: 1.5rem;
  }

  .calendar-month-title {
    font-size: 1.25rem;
  }

  .nav-btn {
    width: 36px;
    height: 36px;
  }

  .calendar-days-grid {
    gap: 0;
  }

  .calendar-day {
    font-size: 0.8rem;
  }

  .schedule-item {
    padding: 0.875rem;
  }

  .schedule-icon {
    width: 36px;
    height: 36px;
    font-size: 1.125rem;
  }
}

.payroll-calendar-section:hover {
  box-shadow: 0 12px 35px -5px rgba(0, 0, 0, 0.15), 0 6px 10px -2px rgba(0, 0, 0, 0.08);
}

.payroll-calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 2px solid var(--border-color);
  position: relative;
}

.payroll-calendar-header::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  border-radius: 1px;
}

.payroll-calendar-header h3 {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  letter-spacing: -0.025em;
}

.payroll-calendar-header h3 i {
  font-size: 1.75rem;
  color: var(--primary-color);
  background: rgba(99, 102, 241, 0.1);
  padding: 0.5rem;
  border-radius: 0.75rem;
}

.calendar-actions {
  display: flex;
  gap: 0.75rem;
  align-items: center;
  flex-wrap: wrap;
}

.btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.875rem 1.25rem;
  border-radius: 0.75rem;
  font-size: 0.875rem;
  font-weight: 600;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  letter-spacing: 0.025em;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  box-shadow: 0 4px 14px 0 rgba(99, 102, 241, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px 0 rgba(99, 102, 241, 0.4);
}

.btn-secondary {
  background: white;
  color: var(--text-primary);
  border: 1.5px solid var(--border-color);
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.05);
}

.btn-secondary:hover {
  background: var(--background-color);
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 14px 0 rgba(0, 0, 0, 0.1);
}

.btn-outline-secondary {
  background: transparent;
  color: var(--text-secondary);
  border: 1.5px solid var(--border-color);
  box-shadow: none;
}

.btn-outline-secondary:hover {
  background: var(--background-color);
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-1px);
}



/* Dropdown styling */
.dropdown-toggle::after {
  margin-left: 0.5rem;
  transition: transform 0.3s ease;
}

.dropdown-toggle[aria-expanded="true"]::after {
  transform: rotate(180deg);
}

.dropdown-menu {
  border: none;
  border-radius: 0.75rem;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  padding: 0.5rem;
  margin-top: 0.5rem;
}

.dropdown-item {
  border-radius: 0.5rem;
  padding: 0.75rem 1rem;
  transition: all 0.2s ease;
  font-weight: 500;
}

.dropdown-item:hover {
  background: var(--background-color);
  color: var(--primary-color);
  transform: translateX(4px);
}



/* Calendar Stats */
.calendar-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 1.25rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: linear-gradient(135deg, white 0%, var(--background-color) 100%);
  border-radius: 1rem;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1.25rem;
  border: 1px solid var(--border-color);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 25px -5px rgba(0, 0, 0, 0.15), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.stat-card:hover::before {
  transform: scaleX(1);
}

.stat-card .stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 0.875rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  position: relative;
  transition: all 0.3s ease;
}

.stat-card:hover .stat-icon {
  transform: scale(1.1);
}

.stat-icon.pending {
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.15), rgba(251, 191, 36, 0.05));
  color: #f59e0b;
  box-shadow: 0 4px 12px rgba(251, 191, 36, 0.2);
}

.stat-icon.overdue {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.15), rgba(239, 68, 68, 0.05));
  color: #ef4444;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.2);
}

.stat-icon.this-month {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.15), rgba(99, 102, 241, 0.05));
  color: var(--primary-color);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.2);
}

.stat-card .stat-info {
  display: flex;
  flex-direction: column;
  gap: 0.375rem;
  flex: 1;
}

.stat-number {
  font-size: 1.875rem;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1;
  letter-spacing: -0.025em;
}

.stat-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Calendar Container */
.payroll-calendar-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.calendar-main-area {
  display: grid;
  grid-template-columns: 1fr 320px;
  gap: 2rem;
}

.calendar-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.25rem;
  background: linear-gradient(135deg, var(--background-color) 0%, white 100%);
  border-radius: 1rem;
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  gap: 2rem;
}

.calendar-controls:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.view-controls {
  display: flex;
  gap: 0.5rem;
}

.view-btn {
  padding: 0.5rem 1rem;
  border: 1px solid var(--border-color);
  background: white;
  color: var(--text-secondary);
  border-radius: 0.375rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.view-btn.active,
.view-btn:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.filter-controls {
  display: flex;
  gap: 1rem;
  align-items: center;
}

/* Integrated Reminder Controls */
.reminder-controls {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  align-items: flex-end;
}

.reminder-controls-label {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.reminder-controls-label i {
  font-size: 0.875rem;
  color: var(--primary-color);
}

.reminder-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(99, 102, 241, 0.05);
  border: 1px solid rgba(99, 102, 241, 0.1);
  border-radius: 0.75rem;
  padding: 0.375rem;
}

.reminder-action-btn {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.5rem 0.875rem;
  border: none;
  border-radius: 0.5rem;
  font-size: 0.8rem;
  font-weight: 500;
  font-family: Inter, sans-serif;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.reminder-action-btn i {
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.reminder-action-btn .btn-label {
  font-size: 0.75rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

/* Status Button Styling */
.reminder-action-btn.status-btn {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(129, 140, 248, 0.1) 100%);
  color: var(--primary-color);
  border: 1px solid rgba(99, 102, 241, 0.2);
}

.reminder-action-btn.status-btn:hover {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

/* Send Button Styling */
.reminder-action-btn.send-btn {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
  color: #059669;
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.reminder-action-btn.send-btn:hover {
  background: linear-gradient(135deg, #22c55e 0%, #10b981 100%);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
}

/* Divider */
.reminder-divider {
  width: 1px;
  height: 1.5rem;
  background: linear-gradient(to bottom, transparent, rgba(99, 102, 241, 0.3), transparent);
  margin: 0 0.25rem;
}

/* Hover Effects */
.reminder-action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.reminder-action-btn:hover::before {
  left: 100%;
}

.filter-select {
  padding: 0.75rem 1rem;
  border: 1.5px solid var(--border-color);
  border-radius: 0.75rem;
  background: white;
  color: var(--text-primary);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 140px;
}

.filter-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.filter-select:hover {
  border-color: var(--primary-color);
}

/* Modern Calendar Widget */
.calendar-widget {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 1.25rem;
  border: 1px solid rgba(99, 102, 241, 0.1);
  min-height: 600px;
  padding: 2rem;
  width: 100%;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.05),
    0 1px 3px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.calendar-widget::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  border-radius: 1.25rem 1.25rem 0 0;
}

.calendar-widget:hover {
  box-shadow:
    0 12px 32px rgba(0, 0, 0, 0.1),
    0 4px 12px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  border-color: rgba(99, 102, 241, 0.2);
}

/* Upcoming Events */
.upcoming-events {
  background: linear-gradient(135deg, var(--background-color) 0%, white 100%);
  border-radius: 1rem;
  padding: 1.5rem;
  border: 1px solid var(--border-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  height: fit-content;
}

.upcoming-events:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.upcoming-events h4 {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  font-size: 1.125rem;
  font-weight: 700;
  letter-spacing: -0.025em;
}

.upcoming-events h4 i {
  font-size: 1.25rem;
  color: var(--primary-color);
  background: rgba(99, 102, 241, 0.1);
  padding: 0.375rem;
  border-radius: 0.5rem;
}

.events-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-height: 500px;
  overflow-y: auto;
  padding-right: 0.5rem;
}

.events-list::-webkit-scrollbar {
  width: 4px;
}

.events-list::-webkit-scrollbar-track {
  background: var(--background-color);
  border-radius: 2px;
}

.events-list::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 2px;
}

.events-list::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}

.event-item {
  background: white;
  border-radius: 0.75rem;
  padding: 1rem;
  border: 1px solid var(--border-color);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.event-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--primary-color);
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.event-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: var(--primary-color);
}

.event-item:hover::before {
  transform: scaleY(1);
}

.event-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.75rem;
}

.event-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.4;
  margin-bottom: 0.25rem;
}

.event-date {
  font-size: 0.75rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.event-type {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.event-priority {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 600;
  margin-top: 0.5rem;
}

.event-priority.critical {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.event-priority.high {
  background: rgba(251, 191, 36, 0.1);
  color: #f59e0b;
}

.event-priority.medium {
  background: rgba(99, 102, 241, 0.1);
  color: var(--primary-color);
}

.event-priority.low {
  background: rgba(107, 114, 128, 0.1);
  color: #6b7280;
}

/* Responsive Design for Payroll Calendar */
@media (max-width: 1200px) {
  .calendar-main-area {
    grid-template-columns: 1fr 280px;
    gap: 1.5rem;
  }

  .upcoming-events {
    padding: 1.25rem;
  }
}

@media (max-width: 992px) {
  .payroll-calendar-section {
    padding: 1.5rem;
  }

  .payroll-calendar-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .calendar-actions {
    width: 100%;
    justify-content: flex-start;
    flex-wrap: wrap;
    gap: 1rem;
  }

  .calendar-controls {
    flex-direction: column;
    gap: 1.5rem;
    align-items: stretch;
  }

  .reminder-controls {
    align-items: center;
    width: 100%;
  }

  .reminder-actions {
    justify-content: center;
  }

  .calendar-main-area {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .calendar-stats {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1rem;
  }

  .calendar-controls {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .filter-controls {
    justify-content: center;
    flex-wrap: wrap;
  }
}

@media (max-width: 768px) {
  .payroll-calendar-section {
    padding: 1rem;
    margin-top: 1rem;
  }

  .payroll-calendar-header h3 {
    font-size: 1.25rem;
  }

  .calendar-actions {
    gap: 0.5rem;
  }

  .btn {
    padding: 0.75rem 1rem;
    font-size: 0.8rem;
  }

  .calendar-stats {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .stat-card {
    padding: 1rem;
    gap: 1rem;
  }

  .stat-card .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 1.25rem;
  }

  .stat-number {
    font-size: 1.5rem;
  }

  .calendar-widget {
    min-height: 400px;
    padding: 1.5rem;
    border-radius: 1rem;
  }

  .fc-header-toolbar {
    padding: 0.75rem 1rem !important;
    margin-bottom: 1rem !important;
  }

  .fc-toolbar-title {
    font-size: 1.25rem !important;
  }

  .fc-button {
    padding: 0.5rem 0.75rem !important;
    font-size: 0.8rem !important;
  }

  .fc-prev-button, .fc-next-button {
    width: 2rem !important;
    height: 2rem !important;
  }

  .fc-daygrid-day {
    min-height: 80px !important;
  }

  .upcoming-events {
    padding: 1rem;
  }

  .events-list {
    max-height: 300px;
  }
}

@media (max-width: 480px) {
  .payroll-calendar-header {
    padding-bottom: 1rem;
  }

  .payroll-calendar-header h3 {
    font-size: 1.125rem;
    gap: 0.5rem;
  }

  .payroll-calendar-header h3 i {
    font-size: 1.25rem;
    padding: 0.375rem;
  }

  .calendar-actions {
    flex-direction: column;
    width: 100%;
    gap: 1rem;
  }

  .btn {
    width: 100%;
    justify-content: center;
  }

  .calendar-controls {
    flex-direction: column;
    gap: 1.5rem;
    align-items: stretch;
  }

  .filter-controls {
    justify-content: center;
    flex-wrap: wrap;
  }

  .reminder-controls {
    align-items: center;
    width: 100%;
  }

  .reminder-controls-label {
    justify-content: center;
  }

  .reminder-actions {
    justify-content: center;
    width: 100%;
    max-width: 300px;
  }

  .reminder-action-btn {
    flex: 1;
    justify-content: center;
  }

  .filter-controls {
    flex-direction: column;
    gap: 0.75rem;
  }

  .filter-select {
    width: 100%;
  }
}
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.event-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1);
}

.event-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.event-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.3;
}

.event-date {
  font-size: 0.75rem;
  color: var(--text-secondary);
  white-space: nowrap;
}

.event-type {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
  margin-top: 0.5rem;
}

.event-type.emp201 {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.event-type.emp501 {
  background: rgba(220, 38, 38, 0.1);
  color: #dc2626;
}

.event-type.irp5,
.event-type.it3a {
  background: rgba(99, 102, 241, 0.1);
  color: var(--primary-color);
}

.event-type.uif {
  background: rgba(139, 92, 246, 0.1);
  color: #8b5cf6;
}

.event-type.sdl {
  background: rgba(6, 182, 212, 0.1);
  color: #06b6d4;
}

.event-type.eti {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.event-type.paye {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.event-type.compliance {
  background: rgba(107, 114, 128, 0.1);
  color: #6b7280;
}

.event-type.cutoff {
  background: rgba(251, 191, 36, 0.1);
  color: #f59e0b;
}

.event-type.custom {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
}

/* Modern FullCalendar Customizations */
.fc {
  font-family: 'Inter', sans-serif !important;
  height: auto !important;
  min-height: 500px;
  background: transparent !important;
}

.fc-view-harness {
  height: auto !important;
  min-height: 500px;
  background: transparent !important;
}

/* Modern Calendar Header */
.fc-header-toolbar {
  margin-bottom: 1.5rem !important;
  padding: 1rem 1.25rem !important;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(129, 140, 248, 0.05) 100%) !important;
  border-radius: 1rem !important;
  border: 1px solid rgba(99, 102, 241, 0.1) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04) !important;
}

.fc-toolbar {
  margin-bottom: 0 !important;
  gap: 1rem !important;
}

.fc-toolbar-chunk {
  display: flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
}

/* Modern Calendar Title */
.fc-toolbar-title {
  font-size: 1.5rem !important;
  font-weight: 700 !important;
  color: var(--text-primary) !important;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
  margin: 0 !important;
  letter-spacing: -0.025em !important;
}

/* Modern Calendar Buttons */
.fc-button-group {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08) !important;
  border-radius: 0.75rem !important;
  overflow: hidden !important;
  border: 1px solid rgba(99, 102, 241, 0.2) !important;
}

.fc-button {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
  border: none !important;
  color: var(--text-primary) !important;
  border-radius: 0 !important;
  font-size: 0.875rem !important;
  font-weight: 600 !important;
  padding: 0.75rem 1rem !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
  overflow: hidden !important;
  border-right: 1px solid rgba(99, 102, 241, 0.1) !important;
}

.fc-button:last-child {
  border-right: none !important;
}

.fc-button:hover {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
  color: white !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3) !important;
  z-index: 2 !important;
}

.fc-button:focus {
  outline: none !important;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2) !important;
}

.fc-button-active {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
  color: white !important;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.fc-button:disabled {
  background: rgba(248, 250, 252, 0.5) !important;
  color: var(--text-secondary) !important;
  cursor: not-allowed !important;
  transform: none !important;
  box-shadow: none !important;
}

/* Navigation Buttons Special Styling */
.fc-prev-button, .fc-next-button {
  width: 2.5rem !important;
  height: 2.5rem !important;
  border-radius: 50% !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin: 0 0.25rem !important;
  border: 1px solid rgba(99, 102, 241, 0.2) !important;
}

.fc-today-button {
  border-radius: 0.5rem !important;
  margin: 0 0.5rem !important;
}

/* Modern Calendar Grid */
.fc-scrollgrid {
  border: none !important;
  border-radius: 1rem !important;
  overflow: hidden !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05) !important;
  background: white !important;
}

.fc-scrollgrid-section-header {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%) !important;
}

.fc-col-header {
  background: transparent !important;
  border: none !important;
}

.fc-col-header-cell {
  background: transparent !important;
  border: none !important;
  border-bottom: 2px solid rgba(99, 102, 241, 0.1) !important;
  padding: 1rem 0.5rem !important;
}

.fc-col-header-cell-cushion {
  color: var(--text-primary) !important;
  font-weight: 700 !important;
  font-size: 0.875rem !important;
  text-transform: uppercase !important;
  letter-spacing: 0.05em !important;
}

/* Modern Calendar Days */
.fc-daygrid-day {
  background: white !important;
  border: 1px solid rgba(226, 232, 240, 0.5) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
  min-height: 120px !important;
}

.fc-daygrid-day:hover {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.02) 0%, rgba(129, 140, 248, 0.02) 100%) !important;
  border-color: rgba(99, 102, 241, 0.2) !important;
  transform: scale(1.01) !important;
  z-index: 1 !important;
}

.fc-daygrid-day-number {
  color: var(--text-primary) !important;
  font-weight: 600 !important;
  font-size: 0.875rem !important;
  padding: 0.5rem !important;
  text-decoration: none !important;
  transition: all 0.3s ease !important;
}

.fc-day-today {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.08) 0%, rgba(129, 140, 248, 0.08) 100%) !important;
  border-color: var(--primary-color) !important;
  position: relative !important;
}

.fc-day-today .fc-daygrid-day-number {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
  color: white !important;
  border-radius: 50% !important;
  width: 2rem !important;
  height: 2rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin: 0.25rem !important;
  font-weight: 700 !important;
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3) !important;
}

.fc-day-other .fc-daygrid-day-number {
  color: var(--text-secondary) !important;
  opacity: 0.5 !important;
}

/* Modern Calendar Events */
.fc-event {
  border: none !important;
  border-radius: 0.5rem !important;
  padding: 0.375rem 0.75rem !important;
  margin: 0.125rem !important;
  font-size: 0.75rem !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  position: relative !important;
  overflow: hidden !important;
  backdrop-filter: blur(10px) !important;
}

.fc-event::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 2px !important;
  background: rgba(255, 255, 255, 0.3) !important;
}

.fc-event:hover {
  transform: translateY(-2px) scale(1.02) !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
  z-index: 10 !important;
}

.fc-event-title {
  font-weight: 600 !important;
  line-height: 1.2 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

.fc-event.overdue-event {
  animation: modernPulse 2s infinite !important;
  border: 2px solid rgba(239, 68, 68, 0.3) !important;
}

@keyframes modernPulse {
  0% {
    opacity: 0.7;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 0 0 0 rgba(239, 68, 68, 0.4);
  }
  50% {
    opacity: 0.9;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 0 0 8px rgba(239, 68, 68, 0.1);
  }
  100% {
    opacity: 0.7;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 0 0 0 rgba(239, 68, 68, 0.4);
  }
}

/* Modern Event Dots for List View */
.fc-list-event-dot {
  border-radius: 50% !important;
  width: 8px !important;
  height: 8px !important;
  margin-right: 0.75rem !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* Modern Week View Styling */
.fc-timegrid-slot {
  border-color: rgba(226, 232, 240, 0.5) !important;
}

.fc-timegrid-axis {
  border-color: rgba(226, 232, 240, 0.5) !important;
}

/* Loading States */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(99, 102, 241, 0.3);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 2rem;
  color: var(--text-secondary);
}

/* Empty States */
.empty-state {
  text-align: center;
  padding: 2rem;
  color: var(--text-secondary);
}

.empty-state i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

/* Notification Styles */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background: white;
  border-radius: 0.5rem;
  padding: 1rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  border-left: 4px solid var(--primary-color);
  z-index: 1000;
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

.notification.show {
  transform: translateX(0);
}

.notification.success {
  border-left-color: var(--success-color);
}

.notification.error {
  border-left-color: var(--danger-color);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .main-container {
    padding: 1.5rem;
  }

  .summary-stats,
  .main-cards {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }

  .calendar-main-area {
    grid-template-columns: 1fr;
  }

  .upcoming-events {
    order: -1;
  }
}

@media (max-width: 768px) {
  .main-container {
    margin-left: 0;
    padding: 1rem;
  }

  .summary-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .summary-stats,
  .main-cards {
    grid-template-columns: 1fr;
  }

  .payroll-calendar-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .calendar-actions {
    justify-content: center;
    flex-direction: column;
    gap: 1rem;
  }

  .calendar-controls {
    padding: 1rem;
  }

  .filter-controls {
    gap: 0.5rem;
  }

  .filter-select {
    font-size: 0.8rem;
  }

  .reminder-controls {
    align-items: center;
  }

  .reminder-controls-label span {
    display: none;
  }

  .reminder-actions {
    max-width: 250px;
  }

  .reminder-action-btn .btn-label {
    font-size: 0.7rem;
  }

  .reminder-action-btn {
    padding: 0.4rem 0.6rem;
  }

  .calendar-widget {
    padding: 1rem;
    border-radius: 0.75rem;
  }

  .fc-header-toolbar {
    padding: 0.5rem !important;
    margin-bottom: 0.75rem !important;
    border-radius: 0.75rem !important;
  }

  .fc-toolbar {
    flex-direction: column !important;
    gap: 0.75rem !important;
  }

  .fc-toolbar-chunk {
    justify-content: center !important;
  }

  .fc-toolbar-title {
    font-size: 1.125rem !important;
    order: -1 !important;
  }

  .fc-button {
    padding: 0.4rem 0.6rem !important;
    font-size: 0.75rem !important;
  }

  .fc-prev-button, .fc-next-button {
    width: 1.75rem !important;
    height: 1.75rem !important;
  }

  .fc-daygrid-day {
    min-height: 60px !important;
  }

  .fc-event {
    font-size: 0.7rem !important;
    padding: 0.25rem 0.5rem !important;
  }

  .fc-scrollgrid {
    border-radius: 0.75rem !important;
  }

  .calendar-controls {
    flex-direction: column;
    gap: 1rem;
  }

  .filter-controls {
    justify-content: center;
  }

  .calendar-stats {
    grid-template-columns: 1fr;
  }
}

/* Add these styles for the restored components */

/* Date Filter Section */
.filter-section {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  margin: 1.5rem 0;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border-color);
}

.filter-header {
  margin-bottom: 1rem;
}

.filter-header h3 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-primary);
  font-size: 1rem;
  font-weight: 600;
}

.filter-form {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-group label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.filter-select {
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  background: white;
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.filter-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.filter-button {
  padding: 0.75rem 1.5rem;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-button:hover {
  background: var(--secondary-color);
  transform: translateY(-1px);
}

/* Duplicate notification card styles removed */

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.notification-header h3 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-primary);
  font-size: 1rem;
  font-weight: 600;
}

.view-all-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--primary-color);
  text-decoration: none;
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.view-all-link:hover {
  color: var(--secondary-color);
}

.notification-item {
  background: var(--background-color);
  border-radius: 0.75rem;
  padding: 1rem;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.notification-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.notification-meta {
  margin-bottom: 0.5rem;
}

.notification-date {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
  font-size: 0.75rem;
}

.notification-title {
  color: var(--text-primary);
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.notification-content {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* Duplicate company management card styles removed */

.management-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.management-header h3 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-primary);
  font-size: 1rem;
  font-weight: 600;
}

.add-company-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: var(--primary-color);
  color: white;
  border-radius: 0.5rem;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.add-company-btn:hover {
  background: var(--secondary-color);
  transform: translateY(-1px);
}

.companies-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1rem;
}

.company-card {
  background: var(--background-color);
  border-radius: 0.75rem;
  padding: 1.25rem;
  transition: all 0.3s ease;
}

.company-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.company-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.company-card-header h4 {
  color: var(--text-primary);
  font-size: 0.875rem;
  font-weight: 600;
}

.company-status {
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.company-status.active {
  background: rgba(34, 197, 94, 0.1);
  color: var(--success-color);
}

.company-stats {
  margin-bottom: 1rem;
}

.stat {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.company-actions {
  display: flex;
  gap: 0.75rem;
}

.action-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  text-decoration: none;
  font-size: 0.75rem;
  transition: all 0.3s ease;
}

.action-link:not(.switch) {
  background: rgba(99, 102, 241, 0.1);
  color: var(--primary-color);
}

.action-link.switch {
  background: var(--background-color);
  color: var(--text-secondary);
}

.action-link:hover {
  transform: translateY(-1px);
}

/* No Data States */
.no-notifications,
.no-companies {
  text-align: center;
  padding: 2rem;
  color: var(--text-secondary);
}

.no-notifications i,
.no-companies i {
  font-size: 2rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

/* ===== COMPACT TWO-COLUMN LAYOUT ===== */

.two-column-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
  margin-top: 2rem; /* Add top margin to prevent overlap with sections above */
  width: 100%; /* Full width */
  max-width: 100%; /* Prevent overflow */
  box-sizing: border-box; /* Include padding/border in width */
}

.overview-column,
.management-column {
  display: flex;
  flex-direction: column;
}

/* Compact Cards Stack */
.compact-cards-stack {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.compact-card {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.2s ease;
}

.compact-card:hover {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.compact-card-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
}

.compact-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: white;
}

.compact-icon.growth {
  background: linear-gradient(135deg, #10b981, #059669);
}

.compact-icon.retention {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.compact-icon.distribution {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.compact-icon.turnover {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.compact-icon.serving-notice {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.compact-icon.tenure {
  background: linear-gradient(135deg, #06b6d4, #0891b2);
}

.compact-icon.company {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.compact-icon.empty {
  background: linear-gradient(135deg, #6b7280, #4b5563);
}

/* Termination Breakdown Styles */
.termination-breakdown-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.termination-reason-card {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.termination-reason-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #f3f4f6, #e5e7eb);
  transition: all 0.3s ease;
}

.termination-reason-card.modern::before {
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
}

.termination-reason-card:hover {
  border-color: #d1d5db;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.termination-reason-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.termination-reason-icon {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: white;
  background: linear-gradient(135deg, #ef4444, #dc2626);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.termination-reason-info h4 {
  font-size: 15px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 4px 0;
  line-height: 1.3;
}

.termination-count {
  font-size: 13px;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 500;
}

.termination-metrics {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.termination-percentage {
  font-size: 20px;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1;
}

.termination-trend {
  font-size: 11px;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: 3px;
  font-weight: 500;
  opacity: 0.8;
}

/* Termination Summary Styles */
.termination-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--border-color);
}

.summary-card {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  transition: all 0.2s ease;
}

.summary-card:hover {
  border-color: #d1d5db;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.summary-icon {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: white;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  flex-shrink: 0;
}

.summary-content h4 {
  font-size: 13px;
  font-weight: 600;
  color: var(--text-secondary);
  margin: 0 0 2px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.summary-value {
  font-size: 18px;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1;
}

/* Growth Trend Styles */
.compact-value {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.growth-rate {
  font-size: 18px;
  font-weight: 700;
  color: var(--text-primary);
}

.growth-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 4px;
}

.growth-trend.positive {
  color: #059669;
  background: rgba(16, 185, 129, 0.1);
}

.growth-trend.negative {
  color: #dc2626;
  background: rgba(239, 68, 68, 0.1);
}

.growth-trend.neutral {
  color: #6b7280;
  background: rgba(107, 114, 128, 0.1);
}

/* Enhanced Retention Rate Card */
.compact-card.retention-highlight {
  border: 1px solid #e0e7ff;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  position: relative;
  overflow: hidden;
}

.compact-card.retention-highlight::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
}

.retention-rate {
  font-size: 18px;
  font-weight: 700;
  color: var(--text-primary);
}

.retention-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  font-weight: 600;
  padding: 3px 8px;
  border-radius: 6px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.retention-indicator.excellent {
  color: #059669;
  background: rgba(16, 185, 129, 0.1);
}

.retention-indicator.good {
  color: #d97706;
  background: rgba(245, 158, 11, 0.1);
}

.retention-indicator.needs-attention {
  color: #dc2626;
  background: rgba(239, 68, 68, 0.1);
}

.compact-info h4 {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  line-height: 1.2;
}

.compact-subtitle {
  font-size: 12px;
  color: #6b7280;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  margin-top: 2px;
}

.compact-value {
  font-size: 18px;
  font-weight: 700;
  color: #1f2937;
  min-width: 60px;
  text-align: right;
}

/* Company Management Specific Styles */
.compact-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.company-status {
  font-size: 11px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.company-status.active {
  background: #dcfce7;
  color: #166534;
}

.company-status.inactive {
  background: #f3f4f6;
  color: #6b7280;
}

.compact-action-btn {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background: #f3f4f6;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  font-size: 12px;
  transition: all 0.2s ease;
}

.compact-action-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.compact-action-btn.primary {
  background: #3b82f6;
  color: white;
}

.compact-action-btn.primary:hover {
  background: #2563eb;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .two-column-section {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .compact-card {
    padding: 0.75rem;
  }

  .compact-icon {
    width: 28px;
    height: 28px;
    font-size: 14px;
  }

  /* Responsive Termination Analysis */
  .termination-breakdown-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .termination-reason-card {
    padding: 1rem;
  }

  .termination-reason-icon {
    width: 36px;
    height: 36px;
    font-size: 16px;
  }

  .termination-summary {
    grid-template-columns: 1fr;
    gap: 0.75rem;
    margin-top: 1.5rem;
  }

  .summary-card {
    padding: 0.75rem;
  }

  .summary-icon {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }

  .compact-info h4 {
    font-size: 13px;
  }

  .compact-subtitle {
    font-size: 11px;
  }

  .compact-value {
    font-size: 16px;
  }
}


