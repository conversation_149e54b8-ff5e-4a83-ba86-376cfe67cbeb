/**
 * Trial Restriction Client-Side Script
 * Makes UI elements read-only when account is restricted
 */
document.addEventListener('DOMContentLoaded', function() {
  // Check if the account is restricted (set by the server-side template)
  const isRestricted = document.body.getAttribute('data-account-restricted') === 'true';
  
  if (!isRestricted) return;
  
  console.log('Account restricted - applying read-only mode');
  
  // Add a warning banner at the top of the page
  const warningBanner = document.createElement('div');
  warningBanner.className = 'account-restriction-banner';
  warningBanner.innerHTML = `
    <div class="account-restriction-content">
      <i class="ph ph-warning-circle"></i>
      <div class="restriction-message">
        <p><strong>Account Restricted:</strong> Your trial period has expired.</p>
        <p>The system is in read-only mode. <a href="/billing/preferences">Update your billing information</a> to restore full access.</p>
      </div>
    </div>
  `;
  document.body.insertBefore(warningBanner, document.body.firstChild);
  
  // Add restriction styles
  const restrictionStyle = document.createElement('style');
  restrictionStyle.textContent = `
    .account-restriction-banner {
      background-color: #FEF2F2;
      border-bottom: 1px solid #FECACA;
      padding: 0.75rem 1rem;
      color: #991B1B;
      position: sticky;
      top: 0;
      z-index: 50;
    }
    
    .account-restriction-content {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      max-width: 1200px;
      margin: 0 auto;
    }
    
    .account-restriction-content i {
      font-size: 1.5rem;
      color: #DC2626;
    }
    
    .restriction-message p {
      margin: 0;
      font-size: 0.875rem;
    }
    
    .restriction-message a {
      color: #DC2626;
      font-weight: 600;
      text-decoration: underline;
    }
    
    /* Make all form elements look disabled */
    input:not([type="submit"]):not([type="button"]), 
    select, 
    textarea {
      background-color: #F3F4F6 !important;
      border-color: #D1D5DB !important;
      color: #6B7280 !important;
      cursor: not-allowed !important;
      opacity: 0.75 !important;
    }
    
    /* Style buttons to look disabled except for billing-related buttons */
    button:not([data-billing-action]), 
    .btn:not([data-billing-action]), 
    [type="submit"]:not([data-billing-action]), 
    [type="button"]:not([data-billing-action]),
    .action-button:not([data-billing-action]) {
      background-color: #E5E7EB !important;
      border-color: #D1D5DB !important;
      color: #9CA3AF !important;
      cursor: not-allowed !important;
      pointer-events: none !important;
    }
    
    /* Add visual cue on hover */
    table tr:hover {
      position: relative;
    }
    
    table tr:hover:after {
      content: "System in read-only mode";
      position: absolute;
      bottom: calc(100% + 5px);
      left: 50%;
      transform: translateX(-50%);
      background-color: #1F2937;
      color: white;
      padding: 0.25rem 0.5rem;
      border-radius: 0.25rem;
      font-size: 0.75rem;
      white-space: nowrap;
      z-index: 10;
    }
  `;
  document.head.appendChild(restrictionStyle);
  
  // Disable all form elements
  const formElements = document.querySelectorAll('input:not([type="submit"]):not([type="button"]), select, textarea');
  formElements.forEach(element => {
    element.setAttribute('disabled', 'disabled');
    element.setAttribute('readonly', 'readonly');
    element.setAttribute('title', 'System is in read-only mode');
  });
  
  // Disable all buttons except those related to billing
  const buttons = document.querySelectorAll('button:not([data-billing-action]), .btn:not([data-billing-action]), [type="submit"]:not([data-billing-action]), [type="button"]:not([data-billing-action]), .action-button:not([data-billing-action])');
  buttons.forEach(button => {
    button.setAttribute('disabled', 'disabled');
    button.setAttribute('title', 'System is in read-only mode');
    
    // Prevent click events
    button.addEventListener('click', function(event) {
      event.preventDefault();
      event.stopPropagation();
      
      // Show tooltip
      const tooltip = document.createElement('div');
      tooltip.className = 'restriction-tooltip';
      tooltip.textContent = 'System is in read-only mode. Please update your billing information.';
      tooltip.style.cssText = `
        position: absolute;
        top: -40px;
        left: 50%;
        transform: translateX(-50%);
        background-color: #1F2937;
        color: white;
        padding: 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        white-space: nowrap;
        z-index: 50;
      `;
      
      this.style.position = 'relative';
      this.appendChild(tooltip);
      
      // Remove tooltip after a short delay
      setTimeout(() => {
        tooltip.remove();
      }, 3000);
      
      return false;
    });
  });
  
  // Mark special billing-related buttons
  const billingButtons = document.querySelectorAll('#pay-with-card, .pay-invoice-btn');
  billingButtons.forEach(button => {
    button.setAttribute('data-billing-action', 'true');
  });
  
  // Override form submissions
  const forms = document.querySelectorAll('form:not([action^="/billing/"])');
  forms.forEach(form => {
    form.addEventListener('submit', function(event) {
      if (!form.action.includes('/billing/')) {
        event.preventDefault();
        event.stopPropagation();
        
        // Show toast notification instead of browser alert for better UX
        showTrialRestrictionToast('Your account is currently in read-only mode. Please update your billing information to enable form submissions.');
        
        return false;
      }
    });
  });
  
  console.log('Read-only mode applied successfully');
});

// Toast notification function for trial restriction messages
function showTrialRestrictionToast(message) {
  // Create toast container if it doesn't exist
  let toastContainer = document.querySelector('.toast-container');
  if (!toastContainer) {
    toastContainer = document.createElement('div');
    toastContainer.className = 'toast-container';
    toastContainer.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 10000;
      display: flex;
      flex-direction: column;
      gap: 10px;
    `;
    document.body.appendChild(toastContainer);
  }

  // Create toast element
  const toast = document.createElement('div');
  toast.className = 'toast toast-warning';
  toast.style.cssText = `
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 16px 20px;
    min-width: 300px;
    max-width: 400px;
    display: flex;
    align-items: center;
    gap: 12px;
    transform: translateX(120%);
    transition: all 0.3s ease-in-out;
    font-family: 'Inter', sans-serif;
    font-size: 14px;
    line-height: 1.4;
    border-left: 4px solid #f59e0b;
    color: #1f2937;
  `;

  toast.innerHTML = `
    <i class="ph ph-warning" style="font-size: 20px; color: #f59e0b; flex-shrink: 0;"></i>
    <span>${message}</span>
  `;

  // Add toast to container
  toastContainer.appendChild(toast);

  // Show with animation
  setTimeout(() => {
    toast.style.transform = 'translateX(0)';
  }, 10);

  // Remove the toast after 5 seconds
  setTimeout(() => {
    toast.style.transform = 'translateX(120%)';
    setTimeout(() => {
      toast.remove();
      if (toastContainer.children.length === 0) {
        toastContainer.remove();
      }
    }, 300);
  }, 5000);
}