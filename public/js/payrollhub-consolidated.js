/**
 * Payroll Hub Consolidated JavaScript
 *
 * This file consolidates all JavaScript functionality for the payrollhub.ejs page:
 * - payrollhub-actions.js: Main modal and payroll actions
 * - payslip-modal-checkboxes.js: Checkbox functionality for modals
 * - payrollhub.js: Core hub functionality and UI management
 * - payrollhub-modal-fix.js: Modal fixes and enhancements
 * - Inline JavaScript: Template-specific logic and data parsing
 * 
 * Organization:
 * 1. Global Variables and Configuration
 * 2. Utility Functions
 * 3. Data Management
 * 4. Modal Management
 * 5. Checkbox and Selection Management
 * 6. Payroll Actions and Operations
 * 7. UI Management and Event Handlers
 * 8. Initialization
 */

console.log("🚀 PAYROLL HUB CONSOLIDATED SCRIPT LOADING...");

// We'll define the global function after the implementation is ready

// ============================================================================
// 1. GLOBAL VARIABLES AND CONFIGURATION
// ============================================================================

// Global state variables
let payslipsData = {};
let currentModalFrequency = null;
let currentModalStartDate = null;
let currentModalEndDate = null;
let selectedPeriods = new Set();

// Configuration
const TOAST_DURATION = 5000;
const LOADING_DELAY = 1000;

// ============================================================================
// 2. UTILITY FUNCTIONS
// ============================================================================

/**
 * Safe modal operation wrapper with error handling
 */
function safeModalOperation(operation, errorMessage) {
  try {
    console.log('Executing safe modal operation...');
    const result = operation();
    console.log('Safe modal operation completed successfully:', result);
    return result;
  } catch (error) {
    console.error('ERROR in safeModalOperation:', errorMessage, error);
    console.error('Error stack:', error.stack);
    showToast({
      type: "error",
      message: errorMessage,
      duration: TOAST_DURATION,
    });
    return false;
  }
}

/**
 * Create fetch options with CSRF token
 */
function createFetchOptions(method, body = null) {
  const options = {
    method: method,
    headers: {
      "Content-Type": "application/json",
      "CSRF-Token": document
        .querySelector('meta[name="csrf-token"]')
        ?.getAttribute("content") || "",
    },
  };

  if (body) {
    options.body = JSON.stringify(body);
  }

  return options;
}

/**
 * Show loading overlay
 */
function showLoadingOverlay(message = "Loading...") {
  const overlay = document.getElementById("loadingOverlay");
  const messageElement = document.getElementById("loadingMessage");
  
  if (overlay) {
    if (messageElement) {
      messageElement.textContent = message;
    }
    overlay.style.display = "flex";
  }
}

/**
 * Hide loading overlay
 */
function hideLoadingOverlay() {
  const overlay = document.getElementById("loadingOverlay");
  if (overlay) {
    overlay.style.display = "none";
  }
}

/**
 * Show toast notification
 */
function showToast(options) {
  // Implementation depends on your toast system
  // This is a placeholder - replace with your actual toast implementation
  console.log("Toast:", options);
}

/**
 * Show success message
 */
function showSuccess(message) {
  showToast({
    type: "success",
    message: message,
    duration: TOAST_DURATION,
  });
}

/**
 * Show error message
 */
function showError(message) {
  showToast({
    type: "error",
    message: message,
    duration: TOAST_DURATION,
  });
}

// ============================================================================
// 3. DATA MANAGEMENT
// ============================================================================

/**
 * Parse payslips data from the JSON script tag
 */
function parsePayslipsData() {
  try {
    const scriptElement = document.getElementById('payslips-data');
    if (scriptElement && scriptElement.textContent) {
      payslipsData = JSON.parse(scriptElement.textContent);
      console.log('Payslips data parsed successfully:', Object.keys(payslipsData));
    }
  } catch (error) {
    console.error('Failed to parse payslips data:', error);
    payslipsData = {};
  }
}

/**
 * Get company code from meta tag
 */
function getCompanyCode() {
  const companyCodeMeta = document.querySelector('meta[name="company-code"]');
  if (!companyCodeMeta) {
    throw new Error("Company code meta tag not found");
  }
  const companyCode = companyCodeMeta.getAttribute("content");
  if (!companyCode) {
    throw new Error("Company code not found");
  }
  return companyCode;
}

/**
 * Group payslips by period
 */
function groupPayslipsByPeriod() {
  const payslipItems = document.querySelectorAll(".payslip-item");
  const periods = new Map();

  payslipItems.forEach((item) => {
    const periodKey = `${item.dataset.frequency}-${item.dataset.startDate}-${item.dataset.endDate}`;
    if (!periods.has(periodKey)) {
      periods.set(periodKey, []);
    }
    periods.get(periodKey).push(item);
  });

  // Add visual grouping if needed
  periods.forEach((items) => {
    if (items.length > 1) {
      items.forEach((item, index) => {
        item.classList.add("grouped-payslip");
        if (index === 0) {
          item.classList.add("group-first");
        }
        if (index === items.length - 1) {
          item.classList.add("group-last");
        }
      });
    }
  });
}

// ============================================================================
// 4. MODAL MANAGEMENT (Part 1)
// ============================================================================

/**
 * Initialize payroll hub modal functionality
 */
function initializePayrollHubModals() {
  return safeModalOperation(() => {
    console.log("Initializing payroll hub modals...");

    // Ensure global variables are properly initialized
    if (typeof currentModalFrequency === 'undefined') {
      window.currentModalFrequency = null;
    }
    if (typeof currentModalStartDate === 'undefined') {
      window.currentModalStartDate = null;
    }
    if (typeof currentModalEndDate === 'undefined') {
      window.currentModalEndDate = null;
    }

    // Bind any existing finalize buttons
    bindFinalizeButton();

    console.log("Payroll hub modals initialized successfully");
    return true;
  }, "Failed to initialize payroll hub modals");
}

/**
 * Bind finalize button functionality
 */
function bindFinalizeButton() {
  console.log("=== bindFinalizeButton: Starting button binding ===");

  // Look for both possible button IDs
  const mainFinalizeButton = document.getElementById("finalize-selected");
  const frequencyButtons = document.querySelectorAll('[id^="finalize-selected-"]');

  console.log("Found buttons:", {
    mainButton: !!mainFinalizeButton,
    frequencyButtons: frequencyButtons.length
  });

  let boundButtons = 0;

  // Bind main finalize button
  if (mainFinalizeButton) {
    // Remove any existing event listeners
    mainFinalizeButton.onclick = null;
    mainFinalizeButton.removeEventListener('click', handleFinalizeClick);

    // Add new event handler
    mainFinalizeButton.addEventListener('click', handleFinalizeClick);
    console.log("Main finalize button bound successfully");
    boundButtons++;
  }

  // Bind frequency-specific buttons
  frequencyButtons.forEach((button, index) => {
    // Remove any existing event listeners
    button.onclick = null;
    button.removeEventListener('click', handleFinalizeClick);

    // Add new event handler
    button.addEventListener('click', handleFinalizeClick);
    console.log(`Frequency button ${index + 1} bound successfully (ID: ${button.id})`);
    boundButtons++;
  });

  if (boundButtons > 0) {
    console.log(`Successfully bound ${boundButtons} finalize buttons for frequency:`, currentModalFrequency);
    return true;
  } else {
    console.warn("No finalize buttons found in DOM");
    return false;
  }
}

/**
 * Centralized click handler for finalize buttons
 */
function handleFinalizeClick(event) {
  event.preventDefault();
  console.log("Finalize button clicked, event target:", event.target);

  // Get frequency from button data attribute or global variable
  let frequency = event.target.getAttribute('data-frequency') || currentModalFrequency;

  if (frequency) {
    console.log("Finalize button clicked with frequency:", frequency);
    finalizeSelectedPayslips(frequency);
  } else {
    console.error("No frequency available for finalize operation");
    showToast({
      type: "error",
      message: "Please select a frequency first",
      duration: 3000
    });
  }
}

/**
 * Open payslip review modal
 */
function openPayslipReviewModal(frequency, startDate, endDate) {
  console.log('openPayslipReviewModal called with:', { frequency, startDate, endDate });
  return safeModalOperation(() => {
    console.log(`Opening payslip review modal for ${frequency}: ${startDate} to ${endDate}`);

    // Store current modal parameters with validation
    currentModalFrequency = frequency;
    currentModalStartDate = startDate;
    currentModalEndDate = endDate;

    // Validate period dates for strict filtering
    if (!startDate || !endDate) {
      console.error('Modal opened without proper start/end dates:', { frequency, startDate, endDate });
      throw new Error('Period dates are required for accurate payslip filtering');
    }

    console.log(`Modal period validation: ${frequency} from ${startDate} to ${endDate}`);

    // Get the modal element
    const modal = document.getElementById('payslipReviewModal');
    console.log('Modal element found:', modal);
    if (!modal) {
      throw new Error('Payslip review modal not found');
    }

    // Show the modal (matching original onclick behavior)
    console.log('Setting modal display properties...');
    modal.style.display = 'flex';
    modal.style.opacity = '1';
    modal.style.visibility = 'visible';
    modal.style.zIndex = '999999';
    modal.classList.remove('hide');
    modal.classList.add('show');

    // Populate the modal with data
    console.log('Populating modal with payslips...');
    populateModalWithPayslips(frequency, startDate, endDate);

    // Initialize modal checkboxes and bind finalize button
    setTimeout(() => {
      console.log('Initializing modal checkboxes...');
      initializeModalCheckboxes();

      // Bind finalize button after modal content is loaded
      setTimeout(() => {
        console.log('Binding finalize button after modal initialization...');
        const bindResult = bindFinalizeButton();
        if (!bindResult) {
          console.warn('Failed to bind finalize button, will retry...');
          setTimeout(() => {
            bindFinalizeButton();
          }, 500);
        }
      }, 100);
    }, 100);

    console.log('Modal should now be visible');
    return true;
  }, "Failed to open payslip review modal");
}

// Expose the function globally after it's defined to avoid recursion
window.openPayslipReviewModal = openPayslipReviewModal;

/**
 * Close payslip review modal
 */
function closePayslipReviewModal() {
  return safeModalOperation(() => {
    console.log("Closing payslip review modal");

    const modal = document.getElementById('payslipReviewModal');
    if (modal) {
      // Reset all modal styling properties
      modal.style.display = 'none';
      modal.style.opacity = '';
      modal.style.visibility = '';
      modal.style.zIndex = '';
      modal.classList.remove('show');
      modal.classList.add('hide');
    }

    // Clear current modal parameters
    currentModalFrequency = null;
    currentModalStartDate = null;
    currentModalEndDate = null;

    return true;
  }, "Failed to close payslip review modal");
}

/**
 * Populate modal with payslip data using the simple method from inline script
 */
function simplePopulateModal(frequency) {
  // Find the table body
  const tableBody = document.getElementById('frequency-payslips-body');
  if (!tableBody) {
    return;
  }

  // Get payslips for this frequency
  let payslips = payslipsData[frequency];
  if (!payslips || !Array.isArray(payslips) || payslips.length === 0) {
    tableBody.innerHTML = '<tr><td colspan="7" style="text-align: center; padding: 20px;">No payslips found</td></tr>';
    return;
  }

  // Debug payslip data structure before filtering
  console.log('=== PAYSLIP DATA STRUCTURE DEBUG ===');
  console.log('Modal period:', { start: currentModalStartDate, end: currentModalEndDate });
  console.log('Total payslips before filtering:', payslips.length);

  if (payslips.length > 0) {
    const firstPayslip = payslips[0];
    console.log('First payslip structure:', {
      id: firstPayslip._id,
      startDate: firstPayslip.startDate,
      endDate: firstPayslip.endDate,
      startDateBusiness: firstPayslip.startDateBusiness,
      endDateBusiness: firstPayslip.endDateBusiness,
      payPeriod: firstPayslip.payPeriod,
      allKeys: Object.keys(firstPayslip)
    });
  }

  // Temporarily disable strict frontend filtering to fix modal display issue
  // The backend API filtering should be sufficient for now
  console.log('Frontend filtering temporarily disabled - using backend API filtering only');

  // TODO: Re-enable frontend filtering once we understand the payslip data structure
  /*
  // Additional frontend validation: Filter payslips to ensure they belong to the exact period
  if (currentModalStartDate && currentModalEndDate) {
    console.log(`Filtering payslips for exact period: ${currentModalStartDate} to ${currentModalEndDate}`);

    const originalCount = payslips.length;
    payslips = payslips.filter(payslip => {
      // Check if payslip period matches the modal period exactly
      const payslipStart = payslip.startDateBusiness || payslip.startDate;
      const payslipEnd = payslip.endDateBusiness || payslip.endDate;

      if (!payslipStart || !payslipEnd) {
        console.warn('Payslip missing date information:', payslip._id);
        return false;
      }

      // Convert to comparable date strings (YYYY-MM-DD format)
      const payslipStartStr = new Date(payslipStart).toISOString().split('T')[0];
      const payslipEndStr = new Date(payslipEnd).toISOString().split('T')[0];
      const modalStartStr = new Date(currentModalStartDate).toISOString().split('T')[0];
      const modalEndStr = new Date(currentModalEndDate).toISOString().split('T')[0];

      const matches = payslipStartStr === modalStartStr && payslipEndStr === modalEndStr;

      if (!matches) {
        console.log(`Filtering out payslip ${payslip._id}: period ${payslipStartStr} to ${payslipEndStr} doesn't match modal period ${modalStartStr} to ${modalEndStr}`);
      }

      return matches;
    });

    console.log(`Period filtering: ${originalCount} payslips → ${payslips.length} payslips (filtered out ${originalCount - payslips.length})`);

    if (payslips.length === 0) {
      tableBody.innerHTML = '<tr><td colspan="7" style="text-align: center; padding: 20px;">No payslips found for this exact period</td></tr>';
      return;
    }
  }
  */

  // Create table rows and calculate totals
  let html = '';
  let totalEmployees = 0;
  let totalGross = 0;
  let totalDeductions = 0;
  let totalNet = 0;

  payslips.forEach(payslip => {
    const employee = payslip.employee || payslip.employeeId;
    const employeeName = employee ? `${employee.firstName} ${employee.lastName}` : 'Unknown Employee';

    // Note: PAYE and UIF amounts are stored but not used in display calculations

    // Extract pro-rata information
    const isFirstPeriodWithDOA = payslip.isFirstPeriodWithDOA || false;
    const workedDays = payslip.workedDays || 0;
    const totalDaysInPeriod = payslip.totalDaysInPeriod || 0;

    // Calculate full precision pro-rata percentage (same as employeeProfile.ejs)
    const proratedPercentage = totalDaysInPeriod > 0 ? (workedDays / totalDaysInPeriod) * 100 : 100;
    const isProrated = isFirstPeriodWithDOA && proratedPercentage < 100;

    // Use EXACT same calculation method as employeeProfile.ejs
    const basicSalary = Number(payslip.basicSalary || 0);

    // Calculate grossPay with FULL PRECISION
    let grossPay;
    if (isProrated) {
      grossPay = (basicSalary * proratedPercentage) / 100;
    } else {
      grossPay = basicSalary;
    }

    // Use stored deduction values for accuracy
    const deductions = Number(payslip.totalDeductions || 0);
    const netPay = grossPay - deductions;

    // Add to totals
    totalEmployees++;
    totalGross += grossPay;
    totalDeductions += deductions;
    totalNet += netPay;

    html += `
      <tr data-payslip-id="${payslip._id}">
        <td><input type="checkbox" class="payslip-checkbox" data-payslip-id="${payslip._id}" /></td>
        <td>
          <div class="employee-info">
            <span class="employee-name">${employeeName}</span>
            ${isProrated ? `<span class="pro-rata-badge" style="background: #fff3cd; color: #856404; padding: 2px 6px; border-radius: 3px; font-size: 11px; margin-left: 5px;">Pro-rata ${proratedPercentage.toFixed(2)}%</span>` : ''}
          </div>
        </td>
        <td>
          ${(() => {
            const startDateStr = payslip.startDateBusiness || payslip.startDate;
            const endDateStr = payslip.endDateBusiness || payslip.endDate;
            const startDateFormatted = new Date(startDateStr).toLocaleDateString('en-GB', { day: '2-digit', month: 'short' });
            const endDateFormatted = new Date(endDateStr).toLocaleDateString('en-GB', { day: '2-digit', month: 'short', year: 'numeric' });
            return `${startDateFormatted} - ${endDateFormatted}`;
          })()}
          ${isProrated ? `<br><small style="color: #666;">${workedDays}/${totalDaysInPeriod} days</small>` : ''}
        </td>
        <td class="amount text-right">R${grossPay.toFixed(2)}</td>
        <td class="amount text-right">R${deductions.toFixed(2)}</td>
        <td class="amount text-right">R${netPay.toFixed(2)}</td>
        <td><span class="status-badge status-pending">Pending</span></td>
      </tr>
    `;
  });

  // Update summary cards
  updateSummaryCards(totalEmployees, totalGross, totalDeductions, totalNet);
  tableBody.innerHTML = html;

  // Ensure finalize button is properly bound after payslips are loaded
  setTimeout(() => {
    console.log("Re-binding finalize button after payslips loaded");
    bindFinalizeButton();
  }, 200);
}

/**
 * Update summary cards in the modal
 */
function updateSummaryCards(totalEmployees, totalGross, totalDeductions, totalNet) {
  const employeeCard = document.getElementById('stat-total-employees');
  const grossCard = document.getElementById('stat-total-gross');
  const deductionsCard = document.getElementById('stat-total-deductions');
  const netCard = document.getElementById('stat-total-net');

  if (employeeCard) {
    employeeCard.textContent = totalEmployees;
  }
  if (grossCard) {
    grossCard.textContent = `R${totalGross.toFixed(2)}`;
  }
  if (deductionsCard) {
    deductionsCard.textContent = `R${totalDeductions.toFixed(2)}`;
  }
  if (netCard) {
    netCard.textContent = `R${totalNet.toFixed(2)}`;
  }
}

// ============================================================================
// 5. CHECKBOX AND SELECTION MANAGEMENT
// ============================================================================

/**
 * Initialize modal checkboxes with frequency-specific targeting
 */
function initializeModalCheckboxes() {
  console.log("Initializing modal checkboxes");

  // Target the currently active frequency container
  const activeFrequencyContainer = document.querySelector('.modal-frequency-payslips.show');
  if (!activeFrequencyContainer) {
    console.error("No active frequency container found");
    return;
  }

  // Initialize select all functionality
  initializeSelectAllCheckboxes();

  // Initialize individual checkbox handling
  initializeIndividualCheckboxes();

  // Add hover effects to rows
  addHoverEffect();
}

/**
 * Initialize select all checkboxes
 */
function initializeSelectAllCheckboxes() {
  document.addEventListener("change", function(e) {
    if (e.target.classList.contains("select-all-checkbox")) {
      const frequency = e.target.dataset.frequency;
      let checkboxSelector = '.payslip-checkbox:not([disabled])';

      // If we have a frequency, target that specific frequency's checkboxes
      if (frequency) {
        const frequencyBody = document.getElementById(`${frequency}-payslips-body`);
        if (frequencyBody) {
          const checkboxes = frequencyBody.querySelectorAll(checkboxSelector);
          checkboxes.forEach(checkbox => {
            checkbox.checked = e.target.checked;
          });
        }
      } else {
        // Fallback to all checkboxes in the modal
        const modal = e.target.closest('.modal');
        if (modal) {
          const checkboxes = modal.querySelectorAll(checkboxSelector);
          checkboxes.forEach(checkbox => {
            checkbox.checked = e.target.checked;
          });
        }
      }

      // Update finalize button visibility
      updateFinalizeButtonVisibility(frequency);
    }
  });
}

/**
 * Initialize individual checkbox handling
 */
function initializeIndividualCheckboxes() {
  document.addEventListener("change", function(e) {
    if (e.target.classList.contains("payslip-checkbox")) {
      // Find the parent frequency
      const row = e.target.closest('tr');
      if (!row) return;

      const frequencyBody = row.closest('tbody');
      if (!frequencyBody) return;

      const frequencyId = frequencyBody.id;
      const frequency = frequencyId.replace('-payslips-body', '');

      // Update "Select All" checkbox
      const selectAll = document.querySelector(`.select-all-checkbox[data-frequency="${frequency}"]`);
      if (selectAll) {
        const allCheckboxes = frequencyBody.querySelectorAll('.payslip-checkbox:not([disabled])');
        const allChecked = Array.from(allCheckboxes).every(cb => cb.checked);
        selectAll.checked = allChecked;
      }

      // Update finalize button visibility
      updateFinalizeButtonVisibility(frequency);
    }
  });
}

/**
 * Update finalize button visibility based on selected checkboxes
 */
function updateFinalizeButtonVisibility(frequency) {
  const finalizeBtn = document.getElementById('finalize-selected');
  if (!finalizeBtn) return;

  let hasSelected = false;

  if (frequency) {
    const frequencyBody = document.getElementById(`${frequency}-payslips-body`);
    if (frequencyBody) {
      const selectedCheckboxes = frequencyBody.querySelectorAll('.payslip-checkbox:checked');
      hasSelected = selectedCheckboxes.length > 0;
    }
  } else {
    // Check all checkboxes in the modal
    const modal = document.getElementById('payslipReviewModal');
    if (modal) {
      const selectedCheckboxes = modal.querySelectorAll('.payslip-checkbox:checked');
      hasSelected = selectedCheckboxes.length > 0;
    }
  }

  finalizeBtn.style.display = hasSelected ? 'inline-flex' : 'none';
}

/**
 * Add hover effect to table rows
 */
function addHoverEffect() {
  const tableRows = document.querySelectorAll('#frequency-payslips-body tr');

  tableRows.forEach(row => {
    // Remove existing event listeners to prevent duplicates
    row.removeEventListener('click', handleRowClick);
    row.removeEventListener('mouseenter', handleRowHover);
    row.removeEventListener('mouseleave', handleRowLeave);

    // Add new event listeners
    row.addEventListener('click', handleRowClick);
    row.addEventListener('mouseenter', handleRowHover);
    row.addEventListener('mouseleave', handleRowLeave);
  });
}

/**
 * Handle row click to toggle checkbox
 */
function handleRowClick(e) {
  // Don't trigger if clicking on the checkbox itself or buttons
  if (e.target.type === 'checkbox' || e.target.closest('button')) {
    return;
  }

  const checkbox = this.querySelector('.payslip-checkbox');
  if (checkbox && !checkbox.disabled) {
    checkbox.checked = !checkbox.checked;
    // Trigger change event to update other elements
    checkbox.dispatchEvent(new Event('change', { bubbles: true }));
  }
}

/**
 * Handle row hover enter
 */
function handleRowHover() {
  this.style.backgroundColor = '#f8f9fa';
  this.style.cursor = 'pointer';
}

/**
 * Handle row hover leave
 */
function handleRowLeave() {
  this.style.backgroundColor = '';
  this.style.cursor = '';
}

// ============================================================================
// 6. PAYROLL ACTIONS AND OPERATIONS
// ============================================================================

/**
 * Finalize selected payslips
 */
async function finalizeSelectedPayslips(frequency) {
  console.log("\n=== finalizeSelectedPayslips Debug ===");
  console.log("Called with frequency:", frequency);
  console.log("Current modal frequency:", currentModalFrequency);

  try {
    // Use currentModalFrequency if passed frequency is undefined
    if (!frequency) {
      frequency = currentModalFrequency;
      console.log("Using currentModalFrequency instead:", frequency);
    }

    // Enhanced frequency validation
    if (!frequency || typeof frequency !== 'string' || frequency.trim() === '') {
      console.error("ERROR: No valid frequency provided");
      showToast({
        type: "error",
        message: "Error: Unable to determine frequency. Please close the modal and try again.",
        duration: 5000,
      });
      return;
    }

    // Get all selected payslips with enhanced validation
    const selectedCheckboxes = document.querySelectorAll('.payslip-checkbox:checked:not(:disabled)');
    console.log("Selected checkboxes found:", selectedCheckboxes.length);

    if (selectedCheckboxes.length === 0) {
      showToast({
        type: "error",
        message: "Please select at least one payslip to finalize.",
        duration: 5000,
      });
      return;
    }

    showLoadingOverlay("Finalizing selected payslips...");

    // Get company code
    const companyCode = getCompanyCode();
    if (!companyCode) {
      throw new Error("Company code not found");
    }

    // Get CSRF token with production-safe fallback
    let csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
    if (!csrfToken) {
      // Fallback: try alternative CSRF token sources
      csrfToken = document.querySelector('input[name="_csrf"]')?.value ||
                  document.querySelector('[name="csrf-token"]')?.content;

      if (!csrfToken) {
        console.error("❌ CSRF token not found in any location");
        throw new Error("CSRF token not found - please refresh the page and try again");
      }
    }
    console.log("✅ CSRF token retrieved successfully");

    // Get payslip IDs from selected checkboxes
    const payslipIds = Array.from(selectedCheckboxes)
      .map((checkbox) => checkbox.getAttribute("data-payslip-id"))
      .filter(id => id && id.trim() !== '');

    console.log("Payslip IDs to finalize:", payslipIds);

    if (payslipIds.length === 0) {
      throw new Error("No valid payslip IDs found");
    }

    // Get modal date information
    const modal = document.getElementById("payslipReviewModal");
    const startDate = modal?.getAttribute("data-start-date") || currentModalStartDate;
    const endDate = modal?.getAttribute("data-end-date") || currentModalEndDate;

    if (!startDate || !endDate) {
      throw new Error("Date range information missing");
    }

    // Use the correct bulk finalize endpoint
    const apiUrl = `/clients/${companyCode}/payroll/bulk-finalize`;
    console.log("Sending request to:", apiUrl);

    const requestBody = {
      payslipIds,
      frequency,
      generateNextPeriod: true,
      startDate,
      endDate,
    };

    // Create AbortController for timeout handling
    const controller = new AbortController();
    const timeoutId = setTimeout(() => {
      console.warn("⏰ Request timeout - aborting finalization request");
      controller.abort();
    }, 30000); // 30 second timeout for production

    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "CSRF-Token": csrfToken,
      },
      credentials: "same-origin",
      body: JSON.stringify(requestBody),
      signal: controller.signal, // Add timeout signal
    });

    // Clear timeout if request completes
    clearTimeout(timeoutId);

    // Production-safe response validation
    if (!response.ok) {
      let errorText = `HTTP ${response.status}`;
      try {
        const errorBody = await response.text();
        if (errorBody) {
          errorText += `: ${errorBody}`;
        }
      } catch (parseError) {
        console.warn("Could not parse error response body:", parseError);
      }

      console.error("❌ Server responded with error:", errorText);
      throw new Error(errorText);
    }

    // Validate response content type
    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      console.error("❌ Invalid response content type:", contentType);
      throw new Error("Server returned invalid response format");
    }

    const data = await response.json();
    console.log("✅ Finalization response data:", data);

    // Validate response structure
    if (!data || typeof data !== 'object') {
      throw new Error("Invalid response data structure");
    }

    hideLoadingOverlay();

    // Show immediate success message
    showToast({
      type: "success",
      message: `${payslipIds.length} payslips finalized successfully! Refreshing page...`,
      duration: 3000,
    });

    // Update UI to reflect finalized payslips (temporary visual feedback)
    payslipIds.forEach((payslipId) => {
      const row = document.querySelector(`tr[data-payslip-id="${payslipId}"]`);
      if (row) {
        row.classList.add("finalized");
        const checkbox = row.querySelector(".payslip-checkbox");
        if (checkbox) {
          checkbox.disabled = true;
          checkbox.checked = true;
        }
        const statusCell = row.querySelector("td:nth-child(7)");
        if (statusCell) {
          statusCell.innerHTML = '<span class="status-badge finalized">Finalized</span>';
        }
      }
    });

    // Close modal and refresh page after successful finalization
    setTimeout(() => {
      console.log("Closing modal and preparing for page refresh...");
      closePayslipReviewModal();

      // Show final success message before refresh
      showToast({
        type: "success",
        message: `Successfully finalized ${payslipIds.length} payslips. Refreshing to update progress tracker...`,
        duration: 2000,
      });

      // Refresh the page after a brief delay to show the updated payslip statuses
      setTimeout(() => {
        console.log("Refreshing payroll hub page to reflect updated statuses...");

        // Get current company code for maintaining context
        const companyCode = getCompanyCode();
        if (companyCode) {
          // Add timestamp to prevent caching and ensure fresh data
          const timestamp = new Date().getTime();
          const refreshUrl = `/clients/${companyCode}/payrollhub?refresh=true&t=${timestamp}`;

          console.log("Redirecting to:", refreshUrl);

          // Use window.location.href for a clean page refresh
          window.location.href = refreshUrl;
        } else {
          // Fallback: simple page reload if company code not found
          console.log("Company code not found, performing simple page reload");
          window.location.reload();
        }
      }, 1500); // 1.5 second delay after closing modal

    }, 800); // 0.8 second delay before closing modal

  } catch (error) {
    hideLoadingOverlay();
    console.error("Error finalizing payslips:", error);

    let userMessage = "Failed to finalize payslips. Please try again.";

    // Enhanced error handling for production issues
    if (error.name === 'AbortError') {
      userMessage = "Request timed out. Please check your connection and try again.";
      console.error("🕐 Finalization request timed out");
    } else if (error.message?.includes('CSRF')) {
      userMessage = "Security token expired. Please refresh the page and try again.";
      console.error("🔒 CSRF token issue detected");
    } else if (error.message?.includes('Network')) {
      userMessage = "Network error. Please check your connection and try again.";
      console.error("🌐 Network connectivity issue");
    } else if (error.message) {
      userMessage = error.message;
    }

    showToast({
      type: "error",
      message: userMessage,
      duration: 7000,
    });

    // Log additional debugging info for production troubleshooting
    const debugCheckboxes = document.querySelectorAll('.payslip-checkbox:checked:not(:disabled)');
    console.error("🔍 Finalization error details:", {
      errorName: error.name,
      errorMessage: error.message,
      errorStack: error.stack,
      timestamp: new Date().toISOString(),
      companyCode: getCompanyCode(),
      payslipCount: debugCheckboxes?.length || 0
    });
  }
}

/**
 * Proceed to pay run creation
 */
function proceedToPayRun() {
  if (!currentModalFrequency) {
    showError("No frequency selected");
    return;
  }

  // Get selected payslip IDs
  const selectedCheckboxes = document.querySelectorAll('.payslip-checkbox:checked');
  const payslipIds = Array.from(selectedCheckboxes).map(checkbox => {
    return checkbox.closest('tr').dataset.payslipId;
  }).filter(id => id);

  if (payslipIds.length === 0) {
    showError("Please select at least one payslip");
    return;
  }

  console.log(`Proceeding to pay run for ${currentModalFrequency} with ${payslipIds.length} payslips`);

  // Close the modal
  closePayslipReviewModal();

  // Show the "Select" button in the tracker
  showSelectButton(currentModalFrequency, payslipIds);

  // Show success toast
  showSuccess("Payslips ready for pay run. Click 'Select' to create the pay run.");
}

/**
 * Create pay run
 */
async function createPayRun(frequency, startDate, endDate) {
  try {
    console.log("🎯 FRONTEND: Creating pay run with params:", { frequency, startDate, endDate });

    showLoadingOverlay("Creating pay run...");

    const companyCode = getCompanyCode();

    console.log("🎯 FRONTEND: Company code:", companyCode);
    console.log("🎯 FRONTEND: API URL:", `/clients/${companyCode}/payruns`);

    // Use the parameters passed from the button click
    const requestBody = {
      frequency: frequency,
      startDate: startDate,
      endDate: endDate
    };

    console.log("🎯 FRONTEND: Request body:", JSON.stringify(requestBody, null, 2));

    const response = await fetch(`/clients/${companyCode}/payruns`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "CSRF-Token": document
          .querySelector('meta[name="csrf-token"]')
          .getAttribute("content"),
      },
      body: JSON.stringify(requestBody),
    });

    console.log("🎯 FRONTEND: Response status:", response.status);
    console.log("🎯 FRONTEND: Response ok:", response.ok);

    const data = await response.json();

    console.log("🎯 FRONTEND: Response data:", data);

    if (data.success) {
      console.log("✅ FRONTEND: Pay run created successfully!");
      showSuccess("Pay run created successfully");

      // Redirect to the new pay run
      setTimeout(() => {
        window.location.href = `/clients/${companyCode}/payruns/${data.payRunId}`;
      }, LOADING_DELAY);
    } else {
      console.log("❌ FRONTEND: Pay run creation failed:", data);
      throw new Error(data.message || data.error || "Failed to create pay run");
    }

  } catch (error) {
    console.error("❌ FRONTEND: Error creating pay run:", error);
    showError(error.message || "Failed to create pay run");
  } finally {
    hideLoadingOverlay();
  }
}

/**
 * Show select button in tracker
 */
function showSelectButton(frequency, payslipIds) {
  // Implementation depends on your UI structure
  console.log(`Showing select button for ${frequency} with ${payslipIds.length} payslips`);

  // Find the tracker element for this frequency
  const tracker = document.querySelector(`[data-frequency="${frequency}"] .tracker`);
  if (tracker) {
    const selectButton = tracker.querySelector('.select-button');
    if (selectButton) {
      selectButton.style.display = 'block';
      selectButton.dataset.payslipIds = payslipIds.join(',');
    }
  }
}

// ============================================================================
// 7. UI MANAGEMENT AND EVENT HANDLERS
// ============================================================================

/**
 * Initialize UI elements
 */
function initializeUIElements() {
  console.log("Initializing UI elements...");

  // Initialize any UI-specific functionality
  initializeTooltips();
  initializeDropdowns();
  initializeModals();
}

/**
 * Initialize tooltips
 */
function initializeTooltips() {
  // Add tooltip functionality if needed
  const tooltipElements = document.querySelectorAll('[title]');
  tooltipElements.forEach(element => {
    // Basic tooltip implementation
    element.addEventListener('mouseenter', function() {
      // Show tooltip
    });
    element.addEventListener('mouseleave', function() {
      // Hide tooltip
    });
  });
}

/**
 * Initialize dropdowns
 */
function initializeDropdowns() {
  // Add dropdown functionality if needed
  const dropdowns = document.querySelectorAll('.dropdown');
  dropdowns.forEach(dropdown => {
    const trigger = dropdown.querySelector('.dropdown-trigger');
    const menu = dropdown.querySelector('.dropdown-menu');

    if (trigger && menu) {
      trigger.addEventListener('click', function(e) {
        e.stopPropagation();
        menu.classList.toggle('show');
      });
    }
  });

  // Close dropdowns when clicking outside
  document.addEventListener('click', function() {
    const openDropdowns = document.querySelectorAll('.dropdown-menu.show');
    openDropdowns.forEach(menu => {
      menu.classList.remove('show');
    });
  });
}

/**
 * Initialize modals
 */
function initializeModals() {
  console.log("Initializing modals...");

  // Close modal when clicking outside
  window.addEventListener('click', function(event) {
    const payslipModal = document.getElementById('payslipReviewModal');
    const bankModal = document.getElementById('bankFileModal');

    if (event.target === payslipModal) {
      closePayslipReviewModal();
    }

    if (event.target === bankModal) {
      closeBankFileModal();
    }
  });

  // Initialize payroll hub modals
  initializePayrollHubModals();
}

/**
 * Initialize event listeners
 */
function initializeEventListeners() {
  console.log("Initializing event listeners...");

  // Initialize checkbox functionality for main page
  initializeMainPageCheckboxes();

  // Initialize button click handlers using event delegation
  initializeButtonHandlers();

  // Initialize form handlers
  initializeFormHandlers();
}

/**
 * Initialize main page checkboxes
 */
function initializeMainPageCheckboxes() {
  // Handle "Select All" checkboxes on the main page
  const selectAllCheckboxes = document.querySelectorAll(".select-all-payslips");
  selectAllCheckboxes.forEach((checkbox) => {
    checkbox.addEventListener("change", function () {
      const frequency = this.getAttribute("data-frequency");
      const payslipCheckboxes = document.querySelectorAll(
        `.payslip-checkbox-input[data-frequency="${frequency}"]`
      );
      const bulkFinalizeBtn =
        this.closest(".payslips-header").querySelector(".bulk-finalize-btn");

      payslipCheckboxes.forEach((cb) => {
        cb.checked = this.checked;
      });

      if (this.checked && payslipCheckboxes.length > 0) {
        bulkFinalizeBtn.style.display = "block";
      } else {
        bulkFinalizeBtn.style.display = "none";
      }
    });
  });
}

/**
 * Initialize button handlers using event delegation
 */
function initializeButtonHandlers() {
  // Use event delegation for dynamically added buttons
  document.addEventListener('click', function(e) {
    const target = e.target.closest('[data-action]');
    if (!target) return;

    const action = target.dataset.action;

    switch (action) {
      case 'open-payslip-review':
        handleOpenPayslipReview(target);
        break;
      case 'close-payslip-review':
        closePayslipReviewModal();
        break;
      case 'finalize-selected':
        const frequency = target.dataset.frequency;
        finalizeSelectedPayslips(frequency);
        break;
      case 'proceed-to-payrun':
        proceedToPayRun();
        break;
      case 'generate-bank-file':
        const payRunId = target.dataset.payrunId;
        generateBankFile(payRunId);
        break;
      case 'close-bank-file':
        closeBankFileModal();
        break;
      case 'view-payslip':
        const payslipId = target.dataset.payslipId;
        viewPayslip(payslipId);
        break;
      case 'close-view-payslip':
        closeViewPayslipModal();
        break;
      case 'open-modal-frequency':
        handleOpenModalFrequency(target);
        break;
    }
  });

  // SURGICAL FIX: Add event listeners for payrun action buttons
  console.log('🔧 SURGICAL FIX: Setting up payrun action button listeners...');

  // View PayRun buttons
  document.querySelectorAll('.view-payrun-btn').forEach((button, index) => {
    button.addEventListener('click', function() {
      const payrunId = this.getAttribute('data-payrun-id');
      console.log('🎯 View PayRun button clicked:', { payrunId, index });
      if (typeof window.viewPayRun === 'function') {
        window.viewPayRun(payrunId);
      } else {
        console.error('❌ viewPayRun function not available');
      }
    });
  });

  // Download Payslips buttons
  document.querySelectorAll('.download-payslips-btn').forEach((button, index) => {
    button.addEventListener('click', function() {
      const payrunId = this.getAttribute('data-payrun-id');
      console.log('🎯 Download Payslips button clicked:', { payrunId, index });
      if (typeof window.downloadPayslips === 'function') {
        window.downloadPayslips(payrunId);
      } else {
        console.error('❌ downloadPayslips function not available');
      }
    });
  });

  // Generate Bank File buttons
  document.querySelectorAll('.open-bank-file-modal-btn').forEach((button, index) => {
    button.addEventListener('click', function() {
      const payrunId = this.getAttribute('data-payrun-id');
      console.log('🎯 Generate Bank File button clicked:', { payrunId, index });
      if (typeof window.generateBankFile === 'function') {
        window.generateBankFile(payrunId);
      } else {
        console.error('❌ generateBankFile function not available');
      }
    });
  });

  // Upload to Bank buttons
  document.querySelectorAll('.upload-to-bank-btn').forEach((button, index) => {
    button.addEventListener('click', function() {
      const payrunId = this.getAttribute('data-payrun-id');
      console.log('🎯 Upload to Bank button clicked:', { payrunId, index });
      if (typeof window.uploadToBank === 'function') {
        window.uploadToBank(payrunId);
      } else {
        console.error('❌ uploadToBank function not available');
      }
    });
  });

  // Release to Self Service buttons
  document.querySelectorAll('.release-to-self-service-btn').forEach((button, index) => {
    button.addEventListener('click', function() {
      const payrunId = this.getAttribute('data-payrun-id');
      console.log('🎯 Release to Self Service button clicked:', { payrunId, index });
      if (typeof window.releaseToSelfService === 'function') {
        window.releaseToSelfService(payrunId);
      } else {
        console.error('❌ releaseToSelfService function not available');
      }
    });
  });

  // Send to Xero buttons
  document.querySelectorAll('.send-to-xero-btn').forEach((button, index) => {
    button.addEventListener('click', function() {
      const payrunId = this.getAttribute('data-payrun-id');
      console.log('🎯 Send to Xero button clicked:', { payrunId, index });
      if (typeof window.sendToXero === 'function') {
        window.sendToXero(payrunId);
      } else {
        console.error('❌ sendToXero function not available');
      }
    });
  });

  // Send to QuickBooks buttons
  document.querySelectorAll('.send-to-quickbooks-btn').forEach((button, index) => {
    button.addEventListener('click', function() {
      const payrunId = this.getAttribute('data-payrun-id');
      console.log('🎯 Send to QuickBooks button clicked:', { payrunId, index });
      if (typeof window.sendToQuickBooks === 'function') {
        window.sendToQuickBooks(payrunId);
      } else {
        console.error('❌ sendToQuickBooks function not available');
      }
    });
  });

  // View Accounting Info buttons
  document.querySelectorAll('.view-accounting-info-btn').forEach((button, index) => {
    button.addEventListener('click', function() {
      const payrunId = this.getAttribute('data-payrun-id');
      console.log('🎯 View Accounting Info button clicked:', { payrunId, index });
      if (typeof window.viewAccountingInfo === 'function') {
        window.viewAccountingInfo(payrunId);
      } else {
        console.error('❌ viewAccountingInfo function not available');
      }
    });
  });

  // Unfinalize Pay Run buttons
  document.querySelectorAll('.unfinalize-payrun-btn').forEach((button, index) => {
    button.addEventListener('click', function() {
      const payrunId = this.getAttribute('data-payrun-id');
      console.log('🎯 Unfinalize Pay Run button clicked:', { payrunId, index });
      if (typeof window.unfinalizePayRun === 'function') {
        window.unfinalizePayRun(payrunId);
      } else {
        console.error('❌ unfinalizePayRun function not available');
      }
    });
  });

  console.log('✅ SURGICAL FIX: All payrun action button listeners added successfully');
}

/**
 * Handle opening payslip review modal
 */
function handleOpenPayslipReview(button) {
  const frequency = button.dataset.frequency;
  const startDate = button.dataset.startDate;
  const endDate = button.dataset.endDate;

  if (frequency && startDate && endDate) {
    openPayslipReviewModal(frequency, startDate, endDate);
  } else {
    console.error('Missing required data attributes for payslip review modal', { frequency, startDate, endDate });
  }
}

/**
 * Close view payslip modal
 */
function closeViewPayslipModal() {
  const modal = document.getElementById('viewPayslipModal');
  if (modal) {
    modal.style.display = 'none';
  }
}

/**
 * Handle opening modal frequency
 */
function handleOpenModalFrequency(button) {
  const frequency = button.dataset.frequency;
  console.log(`Opening modal for frequency: ${frequency}`);

  // Implementation depends on your specific modal frequency functionality
  // This is a placeholder for the openModalFrequency function
  if (typeof window.openModalFrequency === 'function') {
    window.openModalFrequency(button, frequency);
  } else {
    console.warn('openModalFrequency function not found - implementing basic functionality');
    // Basic implementation for opening modal frequency
    openPayslipReviewModal(frequency, '', '');
  }
}

/**
 * Initialize form handlers
 */
function initializeFormHandlers() {
  // Handle form submissions
  const forms = document.querySelectorAll('form[data-ajax]');
  forms.forEach(form => {
    form.addEventListener('submit', function(e) {
      e.preventDefault();
      handleAjaxForm(this);
    });
  });
}

/**
 * Handle AJAX form submission
 */
async function handleAjaxForm(form) {
  try {
    const formData = new FormData(form);
    const action = form.action;
    const method = form.method || 'POST';

    showLoadingOverlay("Processing...");

    const response = await fetch(action, {
      method: method,
      body: formData,
      headers: {
        'CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
      }
    });

    const result = await response.json();

    if (result.success) {
      showSuccess(result.message || "Operation completed successfully");

      // Handle any specific post-submission actions
      if (result.redirect) {
        setTimeout(() => {
          window.location.href = result.redirect;
        }, LOADING_DELAY);
      } else if (result.reload) {
        setTimeout(() => {
          window.location.reload();
        }, LOADING_DELAY);
      }
    } else {
      showError(result.message || "Operation failed");
    }

  } catch (error) {
    console.error("Form submission error:", error);
    showError("An error occurred while processing the form");
  } finally {
    hideLoadingOverlay();
  }
}

// ============================================================================
// 8. ADDITIONAL MODAL AND UTILITY FUNCTIONS
// ============================================================================

/**
 * Generate bank file (EFT Format-Based)
 */
async function generateBankFile(payRunId, actionDate) {
  try {
    showLoadingOverlay("Generating bank file...");

    const companyCode = getCompanyCode();
    if (!companyCode) {
      throw new Error("Company code not found");
    }

    console.log('🏦 Generating bank file for pay run:', payRunId);

    const response = await fetch(`/clients/${companyCode}/payruns/${payRunId}/bank-file/standard`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
      },
      body: JSON.stringify({ actionDate })
    });

    if (!response.ok) {
      let errorData;
      try {
        errorData = await response.json();
      } catch (e) {
        // If response is not JSON, try to get text
        const errorText = await response.text();
        throw new Error(errorText || 'Failed to generate bank file');
      }

      if (response.status === 400 && errorData.error === "Missing bank details") {
        // Show notification for missing bank details
        showToast({
          type: "warning",
          message: errorData.message || 'Some employees have missing bank details',
          duration: 8000,
        });
        return;
      }

      // Handle EFT configuration errors
      if (errorData.error && errorData.error.includes('EFT configuration')) {
        showToast({
          type: "error",
          message: "EFT configuration not found. Please configure your EFT settings first.",
          duration: 8000,
        });
        return;
      }

      throw new Error(errorData.error || errorData.details || 'Failed to generate bank file');
    }

    // Handle file download
    const blob = await response.blob();

    // Get filename from response headers if available
    const contentDisposition = response.headers.get('Content-Disposition');
    let filename = `bank_file_${payRunId}_${new Date().toISOString().split('T')[0]}.txt`;

    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename="(.+)"/);
      if (filenameMatch) {
        filename = filenameMatch[1];
      }
    }

    console.log('📁 Downloading bank file:', filename);

    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.style.display = 'none';
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);

    // Determine bank format from filename for success message
    const bankFormat = filename.split('_')[0] || 'Bank';

    showToast({
      type: "success",
      message: `${bankFormat} format bank file generated successfully`,
      duration: 5000,
    });

  } catch (error) {
    console.error('Error generating bank file:', error);
    showToast({
      type: "error",
      message: error.message || "Failed to generate bank file",
      duration: 5000,
    });
  } finally {
    hideLoadingOverlay();
  }
}

/**
 * Close bank file modal
 */
function closeBankFileModal() {
  const modal = document.getElementById('bankFileModal');
  if (modal) {
    modal.style.display = 'none';
  }
}

/**
 * View payslip
 */
function viewPayslip(payslipId) {
  console.log(`Viewing payslip ${payslipId}`);
  // Implementation depends on your payslip viewing system
  window.location.href = `/payslips/${payslipId}`;
}

/**
 * View pay run
 */
function viewPayRun(payRunId) {
  const companyCode = getCompanyCode();
  window.location.href = `/clients/${companyCode}/payruns/${payRunId}`;
}

/**
 * Download payslips for a pay run
 */
function downloadPayslips(payRunId) {
  if (!payRunId) {
    showToast({
      type: "error",
      message: "Invalid pay run ID",
      duration: 5000,
    });
    return;
  }

  showLoadingOverlay("Preparing payslips for download...");

  // Get the CSRF token
  try {
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
    if (!csrfToken) {
      console.warn("CSRF token not found");
    }
  } catch (e) {
    console.warn("Error getting CSRF token:", e);
  }

  // Set up the URL for the bulk download
  const downloadUrl = `/payslip/bulk-download/${payRunId}`;

  // Create a hidden iframe to handle the download
  const iframe = document.createElement('iframe');
  iframe.style.display = 'none';
  document.body.appendChild(iframe);

  // Set up a timeout to hide the loading overlay after a reasonable time
  const loadingTimeout = setTimeout(() => {
    hideLoadingOverlay();
    showToast({
      type: "success",
      message: "Payslips download started",
      duration: 5000,
    });
  }, 5000); // Increased timeout to give more time for large files

  // Set up error handling for the iframe
  iframe.onerror = function() {
    clearTimeout(loadingTimeout);
    hideLoadingOverlay();
    showToast({
      type: "error",
      message: "Failed to download payslips. Please try again later.",
      duration: 5000,
    });
    if (iframe && iframe.parentNode) {
      document.body.removeChild(iframe);
    }
  };

  // Add a global error handler to catch any issues
  const originalOnError = window.onerror;
  window.onerror = function(message, source, lineno, colno, error) {
    if (source && source.includes(downloadUrl)) {
      clearTimeout(loadingTimeout);
      hideLoadingOverlay();
      showToast({
        type: "error",
        message: "Error downloading payslips: " + message,
        duration: 5000,
      });
      if (iframe && iframe.parentNode) {
        document.body.removeChild(iframe);
      }
      return true; // Prevent default error handling
    }
    return originalOnError ? originalOnError(message, source, lineno, colno, error) : false;
  };

  // Navigate the iframe to the download URL
  iframe.src = downloadUrl;

  // Clean up the iframe after it's loaded
  iframe.onload = function() {
    clearTimeout(loadingTimeout);
    hideLoadingOverlay();

    // Check if there was an error (iframe content would have error message)
    try {
      const iframeContent = iframe.contentDocument?.body?.textContent || '';
      if (iframeContent && (iframeContent.includes("Error") || iframeContent.includes("error"))) {
        showToast({
          type: "error",
          message: "Failed to download payslips: " + iframeContent,
          duration: 5000,
        });
      } else {
        showToast({
          type: "success",
          message: "Payslips downloaded successfully",
          duration: 5000,
        });
      }
    } catch (e) {
      // If we can't access iframe content due to CORS, assume success
      console.log("Could not access iframe content, assuming download started:", e);
      showToast({
        type: "success",
        message: "Payslips download started",
        duration: 5000,
      });
    }

    // Remove the iframe after a delay
    setTimeout(() => {
      if (iframe && iframe.parentNode) {
        document.body.removeChild(iframe);
      }
      // Restore original error handler
      window.onerror = originalOnError;
    }, 1000);
  };
}

/**
 * Release pay run to self-service
 */
function releaseToSelfService(payRunId) {
  if (confirm("Are you sure you want to release this pay run to self-service? Employees will be able to view their payslips.")) {
    showLoadingOverlay("Releasing to self-service...");

    const companyCode = getCompanyCode();
    if (!companyCode) {
      showToast({
        type: "error",
        message: "Company code not found",
        duration: 5000,
      });
      hideLoadingOverlay();
      return;
    }

    fetch(`/clients/${companyCode}/payruns/${payRunId}/release`, {
      method: "POST",
      headers: {
        "CSRF-Token": document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
      },
    })
      .then((response) => response.json())
      .then((data) => {
        if (data.success) {
          showToast({
            type: "success",
            message: "Pay run released to self-service successfully",
            duration: 5000,
          });
          setTimeout(() => {
            const timestamp = new Date().getTime();
            const url = `/clients/${companyCode}/payrollhub?t=${timestamp}&refresh=true`;
            window.location.replace(url);
          }, 1000);
        } else {
          showToast({
            type: "error",
            message: data.error || "Failed to release pay run",
            duration: 5000,
          });
        }
      })
      .catch((error) => {
        console.error("Error releasing pay run:", error);
        showToast({
          type: "error",
          message: "An error occurred while releasing the pay run",
          duration: 5000,
        });
      })
      .finally(() => {
        hideLoadingOverlay();
      });
  }
}

/**
 * Upload to bank
 */
async function uploadToBank(payRunId) {
  const companyCode = getCompanyCode();
  if (!companyCode) {
    showToast({
      type: "error",
      message: "Company code not found",
      duration: 5000,
    });
    return;
  }

  try {
    showLoadingOverlay("Uploading to bank...");

    const response = await fetch(`/clients/${companyCode}/payruns/${payRunId}/upload-to-bank`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "CSRF-Token": document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
      },
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || "Failed to upload to bank");
    }

    showToast({
      type: "success",
      message: "Successfully uploaded to bank",
      duration: 5000,
    });

    setTimeout(() => {
      const timestamp = new Date().getTime();
      const url = `/clients/${companyCode}/payrollhub?t=${timestamp}&refresh=true`;
      window.location.replace(url);
    }, 1000);

  } catch (error) {
    console.error("Error uploading to bank:", error);
    showToast({
      type: "error",
      message: error.message,
      duration: 5000,
    });
  } finally {
    hideLoadingOverlay();
  }
}

/**
 * Send to Xero
 */
async function sendToXero(payRunId) {
  try {
    showLoadingOverlay("Sending to Xero...");

    const companyCode = getCompanyCode();
    if (!companyCode) {
      throw new Error("Company code not found");
    }

    // Use the correct payrollhub route for Xero sync
    const response = await fetch(`/clients/${companyCode}/pay-runs/${payRunId}/xero`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "CSRF-Token": document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to send to Xero");
    }

    const result = await response.json();
    showToast({
      type: "success",
      message: result.message || "Successfully sent to Xero",
      duration: 5000,
    });

    // Force a full page reload by adding a timestamp to the URL
    setTimeout(() => {
      const timestamp = new Date().getTime();
      const url = `/clients/${companyCode}/payrollhub?t=${timestamp}&refresh=true`;
      window.location.replace(url);
    }, 1000);

  } catch (error) {
    console.error("Error sending to Xero:", error);
    showToast({
      type: "error",
      message: error.message || "Failed to send to Xero",
      duration: 5000,
    });
  } finally {
    hideLoadingOverlay();
  }
}

/**
 * Send to QuickBooks
 */
async function sendToQuickBooks(payRunId) {
  try {
    showLoadingOverlay("Sending to QuickBooks...");

    const companyCode = getCompanyCode();
    if (!companyCode) {
      throw new Error("Company code not found");
    }

    // Use the correct payrollhub route for QuickBooks sync
    const response = await fetch(`/clients/${companyCode}/pay-runs/${payRunId}/quickbooks`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-CSRF-Token": getCSRFToken(),
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to send to QuickBooks");
    }

    const result = await response.json();
    showToast({
      type: "success",
      message: result.message || "Successfully sent to QuickBooks",
      duration: 5000,
    });

    // Force a full page reload by adding a timestamp to the URL
    setTimeout(() => {
      const timestamp = Date.now();
      const url = `/clients/${companyCode}/payrollhub?t=${timestamp}&refresh=true`;
      window.location.replace(url);
    }, 1000);

  } catch (error) {
    console.error("Error sending to QuickBooks:", error);
    showToast({
      type: "error",
      message: error.message || "Failed to send to QuickBooks",
      duration: 5000,
    });
  } finally {
    hideLoadingOverlay();
  }
}

/**
 * View accounting information
 */
function viewAccountingInfo(payRunId) {
  const companyCode = getCompanyCode();
  if (!companyCode) {
    showToast({
      type: "error",
      message: "Company code not found",
      duration: 5000,
    });
    return;
  }

  // Navigate to accounting info page
  window.location.href = `/clients/${companyCode}/payruns/${payRunId}/accounting`;
}

/**
 * Unfinalize pay run
 */
function unfinalizePayRun(payRunId) {
  if (confirm("Are you sure you want to unfinalize this pay run? This will revert the pay run to draft status and allow modifications.")) {
    showLoadingOverlay("Unfinalizing pay run...");

    const companyCode = getCompanyCode();
    if (!companyCode) {
      showToast({
        type: "error",
        message: "Company code not found",
        duration: 5000,
      });
      hideLoadingOverlay();
      return;
    }

    fetch(`/clients/${companyCode}/payruns/${payRunId}/unfinalize`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "CSRF-Token": document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
      },
    })
      .then((response) => response.json())
      .then((data) => {
        if (data.success) {
          showToast({
            type: "success",
            message: "Pay run unfinalized successfully",
            duration: 5000,
          });
          setTimeout(() => {
            const timestamp = new Date().getTime();
            const url = `/clients/${companyCode}/payrollhub?t=${timestamp}&refresh=true`;
            window.location.replace(url);
          }, 1000);
        } else {
          showToast({
            type: "error",
            message: data.error || "Failed to unfinalize pay run",
            duration: 5000,
          });
        }
      })
      .catch((error) => {
        console.error("Error unfinalizing pay run:", error);
        showToast({
          type: "error",
          message: "An error occurred while unfinalizing the pay run",
          duration: 5000,
        });
      })
      .finally(() => {
        hideLoadingOverlay();
      });
  }
}

/**
 * Populate modal with payslips (enhanced version with API call for period filtering)
 */
function populateModalWithPayslips(frequency, startDate, endDate) {
  console.log(`📋 Populating modal with payslips for ${frequency}: ${startDate} to ${endDate}`);

  // Check if we have period dates for API filtering
  if (startDate && endDate) {
    console.log('🌐 Using API call for period-specific filtering...');
    fetchAndPopulatePayslips(frequency, startDate, endDate);
  } else {
    console.log('⚠️ No period dates provided, falling back to simple populate...');
    // Fallback to simple populate if no dates provided
    simplePopulateModal(frequency);

    // Additional enhancements can be added here
    setTimeout(() => {
      initializeModalCheckboxes();
    }, 100);
  }
}

/**
 * Fetch payslips from API with period filtering
 */
async function fetchAndPopulatePayslips(frequency, startDate, endDate) {
  console.log('🔄 Fetching payslip data for modal...');
  console.log('  - Frequency:', frequency);
  console.log('  - Start Date:', startDate);
  console.log('  - End Date:', endDate);

  // Get company code
  const companyCode = getCompanyCode();
  if (!companyCode) {
    console.error('❌ No company code available for API call');
    showModalError('Company code not found');
    return;
  }

  // Get CSRF token
  const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
  if (!csrfToken) {
    console.error('❌ No CSRF token available for API call');
    showModalError('CSRF token not found');
    return;
  }

  try {
    console.log('🌐 Making API request...');
    const apiUrl = `/clients/${companyCode}/payroll/payslips/${frequency}?startDate=${startDate}&endDate=${endDate}`;
    console.log('  - API URL:', apiUrl);

    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'CSRF-Token': csrfToken,
        'Content-Type': 'application/json'
      }
    });

    console.log('📡 API Response received:', {
      status: response.status,
      statusText: response.statusText,
      ok: response.ok
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    console.log('📊 API Data received:', data);

    if (data.success && data.payslips) {
      console.log(`✅ Successfully fetched ${data.payslips.length} payslips for period ${startDate} to ${endDate}`);
      populateModalTableWithPayslips(frequency, data.payslips, data.totals);
    } else {
      console.warn('⚠️ API returned success=false or no payslips');
      showModalError('No payslip data available for this period');
    }

  } catch (error) {
    console.error('❌ Error fetching payslip data:', error);
    showModalError(`Failed to load payslip data: ${error.message}`);
  }
}

/**
 * Format BusinessDate string (YYYY-MM-DD) for display
 * @param {string} dateStr - Date string in YYYY-MM-DD format
 * @returns {string} Formatted date for display
 */
function formatBusinessDateForDisplay(dateStr) {
  if (!dateStr) return 'N/A';

  try {
    // Parse YYYY-MM-DD string directly without timezone conversion
    const parts = dateStr.split('-');
    if (parts.length !== 3) return 'N/A';

    const year = parseInt(parts[0]);
    const month = parseInt(parts[1]);
    const day = parseInt(parts[2]);

    // Create date in local timezone to avoid UTC conversion issues
    const date = new Date(year, month - 1, day);
    return date.toLocaleDateString('en-ZA');
  } catch (error) {
    console.error('Error formatting business date:', dateStr, error);
    return 'N/A';
  }
}

/**
 * Format Date object for display with timezone safety
 * @param {string|Date} dateInput - Date object or ISO string
 * @returns {string} Formatted date for display
 */
function formatDateObjectForDisplay(dateInput) {
  if (!dateInput) return 'N/A';

  try {
    let date;
    if (typeof dateInput === 'string') {
      // For ISO strings, extract just the date part to avoid timezone issues
      const datePart = dateInput.split('T')[0]; // Get YYYY-MM-DD part
      return formatBusinessDateForDisplay(datePart);
    } else {
      date = new Date(dateInput);
    }

    if (isNaN(date.getTime())) return 'N/A';

    return date.toLocaleDateString('en-ZA');
  } catch (error) {
    console.error('Error formatting date object:', dateInput, error);
    return 'N/A';
  }
}

/**
 * Populate modal table with API-fetched payslips
 */
function populateModalTableWithPayslips(frequency, payslips, totals) {
  console.log(`📋 Populating modal table with ${payslips.length} payslips for ${frequency}`);

  // Find the table body
  const tableBody = document.getElementById('frequency-payslips-body');
  if (!tableBody) {
    console.error('❌ Table body not found');
    return;
  }

  if (!payslips || payslips.length === 0) {
    tableBody.innerHTML = '<tr><td colspan="7" style="text-align: center; padding: 20px;">No payslips found for this period</td></tr>';
    return;
  }

  // CRITICAL: Filter payslips to ensure only the correct frequency is shown
  const filteredPayslips = payslips.filter(payslip => {
    const payslipFrequency = payslip.frequency || payslip.payFrequency;
    const matches = payslipFrequency === frequency;
    if (!matches) {
      console.warn(`⚠️ Filtering out payslip ${payslip._id} with frequency '${payslipFrequency}' (expected '${frequency}')`);
    }
    return matches;
  });

  console.log(`✅ Filtered ${payslips.length} payslips to ${filteredPayslips.length} matching frequency '${frequency}'`);

  if (filteredPayslips.length === 0) {
    tableBody.innerHTML = `<tr><td colspan="7" style="text-align: center; padding: 20px;">No ${frequency} payslips found for this period</td></tr>`;
    return;
  }

  // Build HTML for payslips (use filtered payslips)
  let html = '';
  let totalEmployees = 0;
  let totalGross = 0;
  let totalDeductions = 0;
  let totalNet = 0;

  filteredPayslips.forEach(payslip => {
    // Handle the enhanced API response structure
    const employeeName = payslip.employeeName || 'Unknown Employee';

    const grossPay = parseFloat(payslip.grossPay) || 0;
    const deductions = parseFloat(payslip.totalDeductions) || 0;
    const netPay = parseFloat(payslip.netPay) || 0;

    // Use the isFinalized field from API response
    const status = payslip.isFinalized ? 'Finalized' : 'Pending';

    // CRITICAL FIX: Use BusinessDate strings to avoid timezone conversion issues
    let periodStartDate, periodEndDate;

    console.log('🔍 Date processing for payslip:', {
      id: payslip._id,
      startDateDisplay: payslip.startDateDisplay,
      endDateDisplay: payslip.endDateDisplay,
      startDateBusiness: payslip.startDateBusiness,
      endDateBusiness: payslip.endDateBusiness,
      startDate: payslip.startDate,
      endDate: payslip.endDate
    });

    // Priority 1: Use pre-formatted display dates (no timezone conversion needed)
    if (payslip.startDateDisplay && payslip.endDateDisplay) {
      periodStartDate = formatBusinessDateForDisplay(payslip.startDateDisplay);
      periodEndDate = formatBusinessDateForDisplay(payslip.endDateDisplay);
      console.log('✅ Using startDateDisplay/endDateDisplay');
    }
    // Priority 2: Use BusinessDate strings (timezone-safe)
    else if (payslip.startDateBusiness && payslip.endDateBusiness) {
      periodStartDate = formatBusinessDateForDisplay(payslip.startDateBusiness);
      periodEndDate = formatBusinessDateForDisplay(payslip.endDateBusiness);
      console.log('✅ Using startDateBusiness/endDateBusiness');
    }
    // Priority 3: Use Date objects with careful timezone handling
    else if (payslip.startDate && payslip.endDate) {
      periodStartDate = formatDateObjectForDisplay(payslip.startDate);
      periodEndDate = formatDateObjectForDisplay(payslip.endDate);
      console.log('⚠️ Using startDate/endDate with timezone handling');
    }
    // Fallback: Use modal period dates
    else {
      periodStartDate = currentModalStartDate ? formatDateObjectForDisplay(currentModalStartDate) : 'N/A';
      periodEndDate = currentModalEndDate ? formatDateObjectForDisplay(currentModalEndDate) : 'N/A';
      console.log('⚠️ Using modal fallback dates');
    }

    const period = `${periodStartDate} - ${periodEndDate}`;

    console.log('📋 Final processed payslip:', {
      id: payslip._id,
      employeeName: employeeName,
      grossPay: grossPay,
      period: period,
      status: status,
      isFinalized: payslip.isFinalized
    });

    html += `
      <tr data-payslip-id="${payslip._id}">
        <td><input type="checkbox" class="payslip-checkbox" data-payslip-id="${payslip._id}" ${payslip.isFinalized ? 'disabled' : ''} /></td>
        <td>${employeeName}</td>
        <td>${period}</td>
        <td>R${grossPay.toFixed(2)}</td>
        <td>R${deductions.toFixed(2)}</td>
        <td>R${netPay.toFixed(2)}</td>
        <td><span class="status-badge ${status.toLowerCase()}">${status}</span></td>
      </tr>
    `;

    // Update totals
    totalEmployees++;
    totalGross += grossPay;
    totalDeductions += deductions;
    totalNet += netPay;
  });

  // Update table content
  tableBody.innerHTML = html;

  // Update summary cards if they exist
  updateSummaryCards(totalEmployees, totalGross, totalDeductions, totalNet);

  // Initialize checkboxes and button binding
  setTimeout(() => {
    console.log('🔄 Initializing modal checkboxes after API data load...');
    initializeModalCheckboxes();

    // Re-bind finalize button
    setTimeout(() => {
      console.log('🔄 Re-binding finalize button after API data load...');
      bindFinalizeButton();
    }, 100);
  }, 100);
}

/**
 * Show error message in modal
 */
function showModalError(message) {
  const tableBody = document.getElementById('frequency-payslips-body');
  if (tableBody) {
    tableBody.innerHTML = `<tr><td colspan="7" style="text-align: center; padding: 20px; color: #dc3545;">${message}</td></tr>`;
  }
}

/**
 * Show modal error state
 */
function showModalError(message) {
  const frequencies = ['weekly', 'biweekly', 'monthly'];
  frequencies.forEach(freq => {
    const tableBody = document.getElementById(`${freq}-payslips-body`);
    if (tableBody) {
      tableBody.innerHTML = `
        <tr>
          <td colspan="7" style="text-align: center; padding: 40px; color: #dc3545;">
            <i class="ph ph-warning-circle" style="font-size: 24px; margin-bottom: 10px;"></i><br>
            ${message}
          </td>
        </tr>
      `;
    }
  });
}

/**
 * Restore previous state if available
 */
function restorePreviousState() {
  // Check for any saved state in localStorage or sessionStorage
  try {
    const savedState = localStorage.getItem('payrollhub-state');
    if (savedState) {
      const state = JSON.parse(savedState);

      // Restore selected periods
      if (state.selectedPeriods) {
        selectedPeriods = new Set(state.selectedPeriods);
      }

      // Restore any other state as needed
      console.log('Previous state restored:', state);
    }
  } catch (error) {
    console.error('Error restoring previous state:', error);
  }
}

/**
 * Save current state
 */
function saveCurrentState() {
  try {
    const state = {
      selectedPeriods: Array.from(selectedPeriods),
      timestamp: Date.now()
    };

    localStorage.setItem('payrollhub-state', JSON.stringify(state));
  } catch (error) {
    console.error('Error saving current state:', error);
  }
}

// ============================================================================
// 9. INITIALIZATION
// ============================================================================

/**
 * Initialize all payroll hub functionality
 */
function initializePayrollHub() {
  console.log("🚀 Initializing Payroll Hub...");

  try {
    // Parse payslips data first
    parsePayslipsData();

    // Initialize UI elements
    initializeUIElements();

    // Initialize event listeners
    initializeEventListeners();

    // Group payslips by period
    groupPayslipsByPeriod();

    // Restore previous state if available
    restorePreviousState();

    // Save state on page unload
    window.addEventListener('beforeunload', saveCurrentState);

    console.log("✅ Payroll Hub initialized successfully");

  } catch (error) {
    console.error("❌ Error initializing Payroll Hub:", error);
    showError("Failed to initialize Payroll Hub. Please refresh the page.");
  }
}

// ============================================================================
// 10. GLOBAL EXPORTS AND EVENT LISTENERS
// ============================================================================

// Note: openPayslipReviewModal is already exposed globally at the top of the file to avoid recursion
// Expose other functions to the global scope for backward compatibility
window.closePayslipReviewModal = closePayslipReviewModal;
window.finalizeSelectedPayslips = finalizeSelectedPayslips;
window.proceedToPayRun = proceedToPayRun;
window.createPayRun = createPayRun;
window.generateBankFile = generateBankFile;
window.closeBankFileModal = closeBankFileModal;
window.viewPayslip = viewPayslip;
window.viewPayRun = viewPayRun;
window.downloadPayslips = downloadPayslips; // SURGICAL FIX: Export downloadPayslips function
window.generateBankFile = generateBankFile; // SURGICAL FIX: Export generateBankFile function
window.uploadToBank = uploadToBank; // SURGICAL FIX: Export uploadToBank function
window.releaseToSelfService = releaseToSelfService; // SURGICAL FIX: Export releaseToSelfService function
window.sendToXero = sendToXero;
window.sendToQuickBooks = sendToQuickBooks;
window.viewAccountingInfo = viewAccountingInfo;
window.unfinalizePayRun = unfinalizePayRun; // SURGICAL FIX: Export unfinalizePayRun function
window.simplePopulateModal = simplePopulateModal;
window.updateSummaryCards = updateSummaryCards;

// Initialize when DOM is loaded
document.addEventListener("DOMContentLoaded", function() {
  console.log("DOM loaded, initializing Payroll Hub...");
  initializePayrollHub();
});

// Debug function for testing finalize button
window.testFinalizeButton = function() {
  console.log("\n=== FINALIZE BUTTON TEST ===");

  // Check if button exists
  const button = document.getElementById("finalize-selected");
  console.log("Button found:", !!button);

  if (button) {
    console.log("Button properties:", {
      id: button.id,
      classes: button.className,
      style: button.style.cssText,
      onclick: !!button.onclick,
      visible: button.style.display !== 'none'
    });

    // Test button binding
    const bindResult = bindFinalizeButton();
    console.log("Bind result:", bindResult);

    // Show button for testing
    button.style.display = 'inline-block';
    console.log("Button made visible for testing");
  }

  console.log("Current modal state:", {
    frequency: currentModalFrequency,
    startDate: currentModalStartDate,
    endDate: currentModalEndDate
  });

  // Check for frequency-specific buttons
  const frequencyButtons = document.querySelectorAll('[id^="finalize-selected-"]');
  console.log("Frequency-specific buttons found:", frequencyButtons.length);
  frequencyButtons.forEach((btn, index) => {
    console.log(`Frequency button ${index + 1}:`, {
      id: btn.id,
      visible: btn.style.display !== 'none'
    });
  });

  // Test company code retrieval for refresh functionality
  try {
    const companyCode = getCompanyCode();
    console.log("Company code for refresh:", companyCode);
    const refreshUrl = `/clients/${companyCode}/payrollhub?refresh=true&t=${Date.now()}`;
    console.log("Refresh URL would be:", refreshUrl);
  } catch (error) {
    console.error("Error getting company code:", error);
  }
};

// Debug function for testing API period filtering
window.testPeriodAPI = function(frequency, startDate, endDate) {
  console.log("\n=== PERIOD API FILTERING TEST ===");

  if (!frequency) frequency = 'monthly';
  if (!startDate) startDate = '2024-06-01';
  if (!endDate) endDate = '2024-06-30';

  console.log('Testing API call with:', { frequency, startDate, endDate });

  const companyCode = getCompanyCode();
  const apiUrl = `/clients/${companyCode}/payroll/payslips/${frequency}?startDate=${startDate}&endDate=${endDate}`;
  console.log('API URL:', apiUrl);

  // Test the API call
  fetchAndPopulatePayslips(frequency, startDate, endDate)
    .then(() => {
      console.log('✅ API test completed - check modal for results');
    })
    .catch(error => {
      console.error('❌ API test failed:', error);
    });
};

// Debug function for testing API response structure
window.testAPIResponse = async function(frequency, startDate, endDate) {
  console.log("\n=== API RESPONSE STRUCTURE TEST ===");

  if (!frequency) frequency = 'monthly';
  if (!startDate) startDate = '2024-06-01';
  if (!endDate) endDate = '2024-06-30';

  const companyCode = getCompanyCode();
  const apiUrl = `/clients/${companyCode}/payroll/payslips/${frequency}?startDate=${startDate}&endDate=${endDate}`;

  try {
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content'),
        'Content-Type': 'application/json'
      }
    });

    const data = await response.json();
    console.log('📊 Full API Response:', data);

    if (data.payslips && data.payslips.length > 0) {
      console.log('📋 First payslip structure:', data.payslips[0]);
      console.log('📋 Available fields:', Object.keys(data.payslips[0]));

      data.payslips.forEach((payslip, index) => {
        const startDateFormatted = payslip.startDate ? new Date(payslip.startDate).toLocaleDateString('en-ZA') : 'N/A';
        const endDateFormatted = payslip.endDate ? new Date(payslip.endDate).toLocaleDateString('en-ZA') : 'N/A';

        console.log(`📋 Payslip ${index + 1}:`, {
          id: payslip._id,
          employeeName: payslip.employeeName,
          employee: payslip.employee,
          startDate: payslip.startDate,
          endDate: payslip.endDate,
          periodFormatted: `${startDateFormatted} - ${endDateFormatted}`,
          isFinalized: payslip.isFinalized,
          grossPay: payslip.grossPay
        });
      });
    }
  } catch (error) {
    console.error('❌ API test failed:', error);
  }
};

// Debug function for testing period date correction
window.testPeriodDates = function() {
  console.log("\n=== PERIOD DATE CORRECTION TEST ===");

  // Test various date scenarios
  const testCases = [
    { input: '2024-06-01', expected: '2024-05-31', description: 'First day of month should become last day of previous month' },
    { input: '2024-07-01', expected: '2024-06-30', description: 'July 1st should become June 30th' },
    { input: '2024-06-30', expected: '2024-06-30', description: 'Last day of month should stay the same' },
    { input: '2024-02-01', expected: '2024-01-31', description: 'February 1st should become January 31st' }
  ];

  testCases.forEach(testCase => {
    const inputDate = new Date(testCase.input);
    const isFirstDay = inputDate.getDate() === 1;

    let correctedDate;
    if (isFirstDay) {
      correctedDate = new Date(inputDate);
      correctedDate.setDate(0); // This sets to last day of previous month
    } else {
      correctedDate = inputDate;
    }

    const result = correctedDate.toLocaleDateString('en-ZA');
    const expected = new Date(testCase.expected).toLocaleDateString('en-ZA');
    const passed = result === expected;

    console.log(`${passed ? '✅' : '❌'} ${testCase.description}`);
    console.log(`   Input: ${testCase.input} → Output: ${result} (Expected: ${expected})`);
  });
};

// Debug function for testing date formatting functions
window.testDateFormatting = function() {
  console.log("\n=== DATE FORMATTING TEST ===");

  const testCases = [
    {
      businessDate: '2025-05-31',
      isoDate: '2025-05-31T23:59:59.999Z',
      expected: '31/05/2025',
      description: 'May 31st should display as 31/05/2025'
    },
    {
      businessDate: '2025-06-30',
      isoDate: '2025-06-30T23:59:59.999Z',
      expected: '30/06/2025',
      description: 'June 30th should display as 30/06/2025'
    },
    {
      businessDate: '2025-02-28',
      isoDate: '2025-02-28T23:59:59.999Z',
      expected: '28/02/2025',
      description: 'February 28th should display as 28/02/2025'
    }
  ];

  testCases.forEach(testCase => {
    console.log(`\n🧪 Testing: ${testCase.description}`);

    // Test BusinessDate formatting
    const businessResult = formatBusinessDateForDisplay(testCase.businessDate);
    const businessPassed = businessResult === testCase.expected;
    console.log(`  📅 BusinessDate: ${testCase.businessDate} → ${businessResult} ${businessPassed ? '✅' : '❌'}`);

    // Test Date object formatting
    const dateResult = formatDateObjectForDisplay(testCase.isoDate);
    const datePassed = dateResult === testCase.expected;
    console.log(`  📅 Date Object: ${testCase.isoDate} → ${dateResult} ${datePassed ? '✅' : '❌'}`);

    // Test timezone impact
    const directDate = new Date(testCase.isoDate).toLocaleDateString('en-ZA');
    console.log(`  ⚠️  Direct Date: ${testCase.isoDate} → ${directDate} (may have timezone issues)`);
  });
};

// Debug function for testing current modal data
window.debugModalDates = function() {
  console.log("\n=== CURRENT MODAL DATE DEBUG ===");

  console.log('🔍 Modal State:', {
    currentModalFrequency,
    currentModalStartDate,
    currentModalEndDate
  });

  // Test current modal dates
  if (currentModalStartDate && currentModalEndDate) {
    console.log('📅 Modal Date Formatting:');
    console.log(`  Start: ${currentModalStartDate} → ${formatDateObjectForDisplay(currentModalStartDate)}`);
    console.log(`  End: ${currentModalEndDate} → ${formatDateObjectForDisplay(currentModalEndDate)}`);
  }

  // Check if modal is open and has data
  const modal = document.getElementById('payslipReviewModal');
  const tableBody = document.getElementById('frequency-payslips-body');

  if (modal && tableBody) {
    const rows = tableBody.querySelectorAll('tr');
    console.log(`📋 Modal has ${rows.length} payslip rows`);

    rows.forEach((row, index) => {
      const cells = row.querySelectorAll('td');
      if (cells.length >= 3) {
        console.log(`  Row ${index + 1}: Employee: ${cells[1].textContent}, Period: ${cells[2].textContent}`);
      }
    });
  }
};

// Debug function to recalculate a specific PayrollPeriod
window.recalculatePeriod = async function(periodId, forceUpdate = false) {
  console.log("\n=== PAYROLL PERIOD RECALCULATION ===");

  if (!periodId) {
    console.error('❌ Please provide a periodId');
    console.log('💡 Usage: recalculatePeriod("6895ff0ef5963b9fad64fdf4", true)');
    return;
  }

  try {
    const companyCode = getCompanyCode();
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

    console.log(`🔄 Recalculating period ${periodId} with forceUpdate: ${forceUpdate}`);

    const response = await fetch(`/clients/${companyCode}/payroll/recalculate-period/${periodId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'CSRF-Token': csrfToken
      },
      body: JSON.stringify({ forceUpdate })
    });

    const result = await response.json();

    if (result.success) {
      console.log('✅ Period recalculated successfully!');
      console.log('📊 Recalculation results:', result.data);

      // Refresh the page to see updated data
      console.log('🔄 Refreshing page to show updated data...');
      setTimeout(() => {
        window.location.reload();
      }, 2000);
    } else {
      console.error('❌ Recalculation failed:', result.message);
    }

  } catch (error) {
    console.error('❌ Error during recalculation:', error);
  }
};

// Debug function to recalculate the problematic May period
window.fixMayPeriod = function() {
  console.log("\n=== FIXING MAY PERIOD PRORATION ===");
  console.log('🎯 Recalculating period 6895ff0ef5963b9fad64fdf4 with force update...');
  recalculatePeriod('6895ff0ef5963b9fad64fdf4', true);
};

// Debug function for testing period filtering
window.testPeriodFiltering = function() {
  console.log("\n=== PERIOD FILTERING TEST ===");

  console.log("Current modal state:", {
    frequency: currentModalFrequency,
    startDate: currentModalStartDate,
    endDate: currentModalEndDate
  });

  // Check available payslip data
  if (typeof payslipsData !== 'undefined') {
    console.log("Available payslip frequencies:", Object.keys(payslipsData));

    Object.keys(payslipsData).forEach(freq => {
      const payslips = payslipsData[freq];
      console.log(`${freq} payslips:`, payslips.length);

      if (payslips.length > 0) {
        payslips.forEach((payslip, index) => {
          const payslipStart = payslip.startDateBusiness || payslip.startDate;
          const payslipEnd = payslip.endDateBusiness || payslip.endDate;
          console.log(`  ${index + 1}. ID: ${payslip._id}, Period: ${payslipStart} to ${payslipEnd}`);
        });
      }
    });
  } else {
    console.log("No payslipsData available");
  }

  // Test filtering logic if modal is open
  if (currentModalFrequency && currentModalStartDate && currentModalEndDate) {
    console.log("\nTesting filtering for current modal...");
    const payslips = payslipsData[currentModalFrequency] || [];

    payslips.forEach((payslip, index) => {
      const payslipStart = payslip.startDateBusiness || payslip.startDate;
      const payslipEnd = payslip.endDateBusiness || payslip.endDate;

      const payslipStartStr = new Date(payslipStart).toISOString().split('T')[0];
      const payslipEndStr = new Date(payslipEnd).toISOString().split('T')[0];
      const modalStartStr = new Date(currentModalStartDate).toISOString().split('T')[0];
      const modalEndStr = new Date(currentModalEndDate).toISOString().split('T')[0];

      const matches = payslipStartStr === modalStartStr && payslipEndStr === modalEndStr;

      console.log(`  ${index + 1}. ${payslip._id}: ${matches ? '✅ MATCH' : '❌ FILTERED'} (${payslipStartStr} to ${payslipEndStr})`);
    });
  }
};

// Debug: Verify global function exposure
console.log("\n=== GLOBAL FUNCTION VERIFICATION ===");
console.log("openPayslipReviewModal available globally:", typeof window.openPayslipReviewModal);
console.log("closePayslipReviewModal available globally:", typeof window.closePayslipReviewModal);
console.log("finalizeSelectedPayslips available globally:", typeof window.finalizeSelectedPayslips);
console.log("proceedToPayRun available globally:", typeof window.proceedToPayRun);
console.log("createPayRun available globally:", typeof window.createPayRun);
