const SOUTH_AFRICAN_BANKS = {
  "ABSA Bank": {
    name: "ABSA Bank",
    universalCode: "632005",
  },
  "Capitec Bank": {
    name: "Capitec Bank",
    universalCode: "470010",
  },
  FNB: {
    name: "First National Bank",
    universalCode: "250655",
  },
  Nedbank: {
    name: "Nedbank",
    universalCode: "198765",
  },
  "Standard Bank": {
    name: "Standard Bank",
    universalCode: "051001",
  },
  "African Bank": {
    name: "African Bank",
    universalCode: "430000",
  },
  "Bidvest Bank": {
    name: "Bidvest Bank",
    universalCode: "462005",
  },
  "Discovery Bank": {
    name: "Discovery Bank",
    universalCode: "679000",
  },
  TymeBank: {
    name: "TymeBank",
    universalCode: "678910",
  },
  Other: {
    name: "Other Bank",
    universalCode: "",
  },
};
class AddEmployeeForm {
  constructor() {
    console.log("Constructor initialized");
    this.form = document.getElementById("addEmployeeForm");
    console.log("Form element found:", this.form);

    this.currentStep = 1;
    this.totalSteps = 3;
    this.employeeData = {};

    this.steps = document.querySelectorAll(".form-step");

    this.initializeSteps();
    this.initializeEventListeners();
    this.initializeDirectorFields();
    this.initializeFlatpickr();
    this.updateStepIndicator(1);
    this.initializeRegularHours();
    this.initializeBankingDetails();
  }

  initializeBankingDetails() {
    console.log("Initializing banking details");
    const bankSelect = document.getElementById("bankName");
    const branchCodeInput = document.getElementById("branchCode");
    const accountHolderInput = document.getElementById("accountHolder");
    const firstNameInput = document.getElementById("firstName");
    const lastNameInput = document.getElementById("lastName");

    if (bankSelect) {
      console.log("Bank select found, populating options");
      // Clear existing options except the first one
      while (bankSelect.options.length > 1) {
        bankSelect.remove(1);
      }

      // Populate bank dropdown
      Object.keys(SOUTH_AFRICAN_BANKS).forEach((bankKey) => {
        const bank = SOUTH_AFRICAN_BANKS[bankKey];
        const option = new Option(bank.name, bank.name);
        bankSelect.add(option);
      });

      // Handle bank selection change
      bankSelect.addEventListener("change", () => {
        console.log("Bank selected:", bankSelect.value);
        const selectedBank = SOUTH_AFRICAN_BANKS[bankSelect.value];
        
        if (selectedBank) {
          console.log("Setting branch code:", selectedBank.universalCode);
          branchCodeInput.value = selectedBank.universalCode;
          branchCodeInput.readOnly = true;
          branchCodeInput.classList.add("auto-populated");
        } else {
          console.log("Clearing branch code");
          branchCodeInput.value = "";
          branchCodeInput.readOnly = false;
          branchCodeInput.classList.remove("auto-populated");
        }
      });

      // Auto-populate account holder name
      const updateAccountHolder = () => {
        if (firstNameInput && lastNameInput) {
          const firstName = firstNameInput.value.trim();
          const lastName = lastNameInput.value.trim();
          if (firstName && lastName) {
            accountHolderInput.value = `${firstName} ${lastName}`;
          }
        }
      };

      if (firstNameInput && lastNameInput) {
        firstNameInput.addEventListener("input", updateAccountHolder);
        lastNameInput.addEventListener("input", updateAccountHolder);
      }
    } else {
      console.error("Bank select element not found");
    }
  }

  // Add form submission handler
  async handleSubmit(e) {
    e.preventDefault();
    console.log("Form submission started");

    try {
      const formData = new FormData(this.form);
      const isDirector = document.getElementById("directorCheckbox").checked;

      // Convert mobile number to international format before saving
      function toInternationalFormat(number) {
        if (!number) return '';
        number = number.replace(/[^\d]/g, '');
        if (number.startsWith('0') && number.length === 10) {
          return '27' + number.slice(1);
        }
        return number;
      }

      // Create the submission data object
      const submissionData = {
        // Basic Information
        firstName: formData.get("firstName"),
        lastName: formData.get("lastName"),
        idType: formData.get("idType") || "none",
        idNumber: formData.get("idNumber"),
        dob: formData.get("dob") || null,
        email: formData.get("email"),
        personalDetails: {
          mobileNumber: toInternationalFormat(formData.get("personalDetails.mobileNumber")),
        },

        // Address Information
        streetAddress: formData.get("streetAddress"),
        suburb: formData.get("suburb"),
        city: formData.get("city"),
        postalCode: formData.get("postalCode"),

        // Banking Details
        bankName: formData.get("bankName"),
        accountType: formData.get("accountType"),
        accountNumber: formData.get("accountNumber"),
        branchCode: formData.get("branchCode"),
        holderRelationship: formData.get("holderRelationship"),

        // Employment Details
        payFrequency: formData.get("payFrequency"),
        workingHours: formData.get("workingHours"),
        doa: formData.get("doa"),
        jobTitle: formData.get("jobTitle"),
        department: formData.get("department"),
        costCentre: formData.get("costCentre"),

        // Director Information
        isDirector: isDirector,
        typeOfDirector: isDirector ? formData.get("typeOfDirector") : "", // Set to empty string when not a director

        // Regular Hours
        regularHours: {
          hourlyPaid: document.getElementById("hourlyPaid").checked,
          hoursPerDay: parseFloat(formData.get("hoursPerDay")) || 8,
          schedule: formData.get("schedule"),
          workingDays: Array.from(document.querySelectorAll('input[name="workingDays[]"]:checked')).map(cb => cb.value),
          fullDaysPerWeek: parseFloat(document.getElementById("fullDaysPerWeek").value) || 5
        },

        // Pay Frequency Details
        payFrequencyDetails: {
          id: formData.get("payFrequency"),
          frequency: document.querySelector(`option[value="${formData.get("payFrequency")}"]`)?.getAttribute("data-frequency"),
          lastDayOfPeriod: document.querySelector(`option[value="${formData.get("payFrequency")}"]`)?.getAttribute("data-lastday")
        }
      };

      console.log("Submitting data:", submissionData);

      // Get CSRF token with error handling
      const csrfInput = document.querySelector('input[name="_csrf"]');
      const headers = {
        "Content-Type": "application/json"
      };

      if (csrfInput && csrfInput.value) {
        headers["CSRF-Token"] = csrfInput.value;
      } else {
        console.warn("CSRF token not found, proceeding without it");
      }

      const response = await fetch(this.form.action, {
        method: "POST",
        headers: headers,
        body: JSON.stringify(submissionData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to create employee");
      }

      const result = await response.json();
      console.log("Employee created successfully:", result);

      // Success message will be handled by toast notification system
      // Removed browser alert for better UX

      // Use redirectUrl from response with enhanced cache busting
      let redirectUrl = result.redirectUrl;
      if (!redirectUrl) {
        const timestamp = Date.now();
        redirectUrl = `/clients/${companyCode}/employeeManagement?refresh=${timestamp}&action=add`;
      }

      // Add delay before redirect to ensure user sees success message and DB is consistent
      setTimeout(() => {
        console.log("Redirecting to:", redirectUrl);
        window.location.href = redirectUrl;
      }, 800);

    } catch (error) {
      console.error("Error submitting form:", error);
      // Error handling will be managed by existing error handling system
      // Removed browser alert for better UX
    }
  }

  validateStep(step) {
    console.log("Validating step:", step);
    const fields = this.form.querySelectorAll(`[data-step="${step}"] [required]`);
    let isValid = true;

    fields.forEach((field) => {
      this.clearFieldError(field);

      if (!field.value.trim()) {
        this.showFieldError(field, "This field is required");
        isValid = false;
      }

      // Additional validation for specific fields
      if (field.id === "payFrequency" && !field.value) {
        this.showFieldError(field, "Please select a pay frequency");
        isValid = false;
      }
    });

    return isValid;
  }

  initializeSteps() {
    console.log("Initializing form steps");
    this.steps = document.querySelectorAll(".form-step");
    console.log("Found form steps:", this.steps.length);

    this.steps.forEach((step, index) => {
      if (index === 0) {
        step.classList.add("active");
        step.style.display = "block";
      } else {
        step.classList.remove("active");
        step.style.display = "none";
      }
      console.log(`Step ${index + 1} display:`, step.style.display);
    });
  }

  initializeEventListeners() {
    console.log("Initializing event listeners");

    // Navigation buttons
    const nextButtons = document.querySelectorAll(".next-step");
    const prevButtons = document.querySelectorAll(".prev-step");
    const submitButton = document.querySelector('button[type="submit"]');

    nextButtons.forEach((button) => {
      button.addEventListener("click", (e) => {
        const step = parseInt(e.target.closest(".form-step").dataset.step);
        this.nextStep(step);
      });
    });

    prevButtons.forEach((button) => {
      button.addEventListener("click", (e) => {
        const step = parseInt(e.target.closest(".form-step").dataset.step);
        this.prevStep(step);
      });
    });

    if (submitButton) {
      submitButton.addEventListener("click", (e) => this.handleSubmit(e));
    }

    // Working days checkboxes
    const workingDaysCheckboxes = document.querySelectorAll(
      'input[name="workingDays[]"]'
    );
    workingDaysCheckboxes.forEach((checkbox) => {
      checkbox.addEventListener("change", () => this.updateFullDaysPerWeek());
    });

    // Handle ID Type change
    const idTypeSelect = document.getElementById("idType");
    if (idTypeSelect) {
      idTypeSelect.addEventListener("change", () => this.handleIdTypeChange());
      // Trigger initial state
      this.handleIdTypeChange();
    }

    // Handle Postal Address checkbox
    const sameAsResidentialCheckbox = document.getElementById("sameAsResidential");
    if (sameAsResidentialCheckbox) {
      sameAsResidentialCheckbox.addEventListener("change", () =>
        this.handlePostalAddressChange()
      );
      // Set initial state
      this.handlePostalAddressChange();
    }

    // Add form submit handler
    this.form.addEventListener("submit", (e) => this.handleSubmit(e));
  }

  nextStep(step) {
    console.log("Moving to next step from step:", step);
    if (this.validateStep(step)) {
      if (step < this.totalSteps) {
        // Remove active class from current step
        this.steps[step - 1].classList.remove("active");
        this.steps[step - 1].style.display = "none";

        // Add active class to next step
        this.steps[step].classList.add("active");
        this.steps[step].style.display = "block";

        this.currentStep = step + 1;
        this.updateStepIndicator(this.currentStep);

        // Scroll to top of the form with smooth behavior
        this.scrollToTop();

        console.log("Current step visibility:", this.steps[step - 1].style.display);
        console.log("Next step visibility:", this.steps[step].style.display);
      }
    }
  }

  prevStep(step) {
    console.log("Moving to previous step from step:", step);
    if (step > 1) {
      // Remove active class from current step
      this.steps[step - 1].classList.remove("active");
      this.steps[step - 1].style.display = "none";

      // Add active class to previous step
      this.steps[step - 2].classList.add("active");
      this.steps[step - 2].style.display = "block";

      this.currentStep = step - 1;
      this.updateStepIndicator(this.currentStep);

      // Scroll to top of the form with smooth behavior
      this.scrollToTop();

      console.log("Current step visibility:", this.steps[step - 1].style.display);
      console.log("Previous step visibility:", this.steps[step - 2].style.display);
    }
  }

  updateStepIndicator(currentStep) {
    const indicators = document.querySelectorAll(".step-number");
    const progressLine = document.querySelector(".progress-line");

    indicators.forEach((indicator, index) => {
      const step = index + 1;
      
      // Remove all classes first
      indicator.classList.remove("active", "completed");
      
      // Add appropriate class based on step number
      if (step < currentStep) {
        indicator.classList.add("completed");
        // Add tick mark for completed steps
        let tickMark = indicator.querySelector('.tick-mark');
        if (!tickMark) {
          tickMark = document.createElement('i');
          tickMark.className = 'ph ph-check tick-mark';
          indicator.appendChild(tickMark);
        }
      } else if (step === currentStep) {
        indicator.classList.add("active");
        // Remove tick mark for current step
        const tickMark = indicator.querySelector('.tick-mark');
        if (tickMark) {
          tickMark.remove();
        }
      } else {
        // Remove tick mark for future steps
        const tickMark = indicator.querySelector('.tick-mark');
        if (tickMark) {
          tickMark.remove();
        }
      }
    });

    if (progressLine) {
      const progress = ((currentStep - 1) / (this.totalSteps - 1)) * 100;
      progressLine.style.width = `${progress}%`;
    }
  }

  scrollToTop() {
    // Find the form container or step indicator to scroll to
    const formContainer = document.querySelector('.employee-section') ||
                         document.querySelector('.step-indicator') ||
                         document.querySelector('.main-container');

    if (formContainer) {
      // Smooth scroll to the top of the form
      formContainer.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
        inline: 'nearest'
      });
    } else {
      // Fallback to window scroll
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    }

    console.log('Scrolled to top of form');
  }

  showFieldError(field, message) {
    field.classList.add("invalid");
    // Remove any existing error message
    this.clearFieldError(field);

    // Create and add new error message
    const errorDiv = document.createElement("div");
    errorDiv.className = "error-message";
    errorDiv.textContent = message;
    errorDiv.style.color = "red";
    errorDiv.style.fontSize = "0.875rem";
    errorDiv.style.marginTop = "0.25rem";

    field.parentNode.appendChild(errorDiv);

    // Add error styling to the field
    field.style.borderColor = "red";
  }

  clearFieldError(field) {
    field.classList.remove("invalid");
    field.style.borderColor = "";
    const existingError = field.parentNode.querySelector(".error-message");
    if (existingError) {
      existingError.remove();
    }
  }

  handleFieldChange(e) {
    const { name, value, type, checked } = e.target;
    this.employeeData[name] = type === "checkbox" ? checked : value;
    this.updatePreview();
    this.saveToLocalStorage();
  }

  updatePreview() {
    const previewDetails = document.querySelector(".preview-details");
    if (!previewDetails) return;

    let content = "";

    // Employment Type Preview
    if (this.employeeData.isDirector) {
      content += this.createPreviewItem("Employment Type", "Director");
      if (this.employeeData.typeOfDirector) {
        content += this.createPreviewItem(
          "Director Type",
          this.employeeData.typeOfDirector
        );
      }
    }

    // Personal Details Preview
    if (this.employeeData.firstName || this.employeeData.lastName) {
      content += this.createPreviewItem(
        "Name",
        `${this.employeeData.firstName || ""} ${
          this.employeeData.lastName || ""
        }`
      );
    }

    // Work Schedule Preview
    if (this.employeeData.workingHours) {
      content += this.createPreviewItem(
        "Working Hours",
        this.employeeData.workingHours
      );
    }

    previewDetails.innerHTML = content;
  }

  createPreviewItem(label, value) {
    return `
      <div class="preview-item">
        <div class="preview-label">${label}</div>
        <div class="preview-value">${value}</div>
      </div>
    `;
  }

  saveToLocalStorage() {
    localStorage.setItem("employeeFormData", JSON.stringify(this.employeeData));
  }

  populateFormFields() {
    Object.entries(this.employeeData).forEach(([name, value]) => {
      const field = this.form.elements[name];
      if (field) {
        if (field.type === "checkbox") {
          field.checked = value;
        } else {
          field.value = value;
        }
      }
    });
  }

  handleIdTypeChange() {
    console.log("Handling ID type change");
    const idType = document.getElementById("idType").value;
    const idNumberContainer = document.getElementById("idNumberContainer");
    const passportNumberContainer = document.getElementById(
      "passportNumberContainer"
    );
    const idNumberInput = document.getElementById("idNumber");
    const passportNumberInput = document.getElementById("passportNumber");

    // First hide both containers and remove required attributes
    idNumberContainer.style.display = "none";
    passportNumberContainer.style.display = "none";
    idNumberInput.removeAttribute("required");
    passportNumberInput.removeAttribute("required");

    // Clear values when hiding fields
    if (idType === "none") {
      idNumberInput.value = "";
      passportNumberInput.value = "";
    }

    // Show and set required for relevant container based on selection
    switch (idType) {
      case "rsa":
        idNumberContainer.style.display = "block";
        idNumberInput.setAttribute("required", "required");
        passportNumberInput.value = ""; // Clear other field
        break;
      case "passport":
        passportNumberContainer.style.display = "block";
        passportNumberInput.setAttribute("required", "required");
        idNumberInput.value = ""; // Clear other field
        break;
      case "none":
        // Both containers remain hidden
        break;
    }
  }

  handlePostalAddressChange() {
    const sameAsResidential =
      document.getElementById("sameAsResidential").checked;
    const postalAddressFields = document.getElementById("postalAddressFields");

    if (sameAsResidential) {
      postalAddressFields.style.display = "none";
      // Clear postal address fields
      document.querySelectorAll(".postal-address-field").forEach((field) => {
        field.value = "";
        field.disabled = true;
      });
    } else {
      postalAddressFields.style.display = "block";
      document.querySelectorAll(".postal-address-field").forEach((field) => {
        field.disabled = false;
      });
    }
  }

  populatePayFrequencies(payFrequencies) {
    const payFrequencySelect = document.getElementById("payFrequency");
    if (payFrequencySelect && payFrequencies) {
      payFrequencySelect.innerHTML =
        '<option value="">Select a pay frequency</option>';
      payFrequencies.forEach((pf) => {
        const option = document.createElement("option");
        option.value = pf._id;
        option.textContent = pf.name;
        option.dataset.frequency = pf.frequency;
        option.dataset.lastDay = pf.lastDayOfPeriod;
        payFrequencySelect.appendChild(option);
      });
    }
  }

  initializeRegularHours() {
    // Hourly paid checkbox handler
    const hourlyPaidCheckbox = document.getElementById("hourlyPaid");
    if (hourlyPaidCheckbox) {
      console.log("Hourly paid checkbox found, adding event listener");
      hourlyPaidCheckbox.addEventListener("change", (e) => {
        console.log("Hourly paid checkbox changed:", e.target.checked);
        this.handleHourlyPaidChange(e.target.checked);
      });
    } else {
      console.warn("Hourly paid checkbox not found");
    }

    // Working days change handler
    const workingDaysCheckboxes = document.querySelectorAll(
      'input[name="workingDays[]"]'
    );
    workingDaysCheckboxes.forEach((checkbox) => {
      checkbox.addEventListener("change", () => this.updateFullDaysPerWeek());
    });

    // Override button handler
    const overrideButton = document.getElementById("overrideButton");
    const overrideInput = document.getElementById("overrideInput");
    const cancelOverrideButton = document.querySelector(".btn-cancel-override");
    const fullDaysPerWeekDisplay = document.getElementById("fullDaysPerWeek");
    const fullDaysPerWeekOverride = document.getElementById(
      "fullDaysPerWeekOverride"
    );

    if (overrideButton) {
      overrideButton.addEventListener("click", () => {
        overrideButton.style.display = "none";
        overrideInput.style.display = "flex";
        fullDaysPerWeekOverride.value = fullDaysPerWeekDisplay.textContent;
        fullDaysPerWeekOverride.focus();
      });
    }

    if (cancelOverrideButton) {
      cancelOverrideButton.addEventListener("click", () => {
        overrideInput.style.display = "none";
        overrideButton.style.display = "flex";
        this.updateFullDaysPerWeek(); // Reset to calculated value
      });
    }

    if (fullDaysPerWeekOverride) {
      fullDaysPerWeekOverride.addEventListener("change", (e) => {
        const value = parseFloat(e.target.value);
        if (value >= 0 && value <= 7) {
          fullDaysPerWeekDisplay.textContent = value.toFixed(1);
        }
      });
    }
  }

  updateFullDaysPerWeek() {
    const workingDays = document.querySelectorAll(
      'input[name="workingDays[]"]:checked'
    );
    const fullDaysCount = workingDays.length;
    const fullDaysPerWeekDisplay = document.getElementById("fullDaysPerWeek");
    const fullDaysPerWeekInput = document.querySelector(
      'input[name="fullDaysPerWeek"]'
    );

    if (fullDaysPerWeekDisplay && fullDaysPerWeekInput) {
      fullDaysPerWeekDisplay.textContent = fullDaysCount.toFixed(1);
      fullDaysPerWeekInput.value = fullDaysCount.toFixed(1);
    }
  }

  handleHourlyPaidChange(isHourlyPaid) {
    console.log("Handling hourly paid change:", isHourlyPaid);

    // Add any conditional logic here for showing/hiding related fields
    // For example, if there are fields that should only be visible for hourly employees

    // You can add logic here to:
    // - Show/hide additional fields
    // - Update form validation
    // - Change labels or hints
    // - Update calculations

    // For now, just log the change
    console.log(`Employee payment type changed to: ${isHourlyPaid ? 'Hourly' : 'Salary'}`);
  }

  initializeFlatpickr() {
    console.log("Initializing Flatpickr...");
    console.log("Flatpickr availability:", typeof flatpickr);
    
    // Check if flatpickr is loaded
    if (typeof flatpickr === "undefined") {
      console.warn("Flatpickr is not loaded yet. Date pickers will not be initialized.");
      return;
    }

    // Modern calendar config
    const dateConfig = {
      dateFormat: "Y-m-d",
      allowInput: true,
      altInput: true,
      altFormat: "F j, Y",
      disableMobile: false,
      animate: true,
      monthSelectorType: "static",
      nextArrow: '<i class="ph ph-caret-right"></i>',
      prevArrow: '<i class="ph ph-caret-left"></i>',
      theme: "modern",
    };

    // Initialize date of birth picker with max date as today
    const dobInput = document.getElementById("dob");
    console.log("DOB input found:", !!dobInput);

    if (dobInput) {
      try {
        flatpickr(dobInput, {
          ...dateConfig,
          maxDate: "today",
          defaultDate: null, // Ensure no default date is set
          allowInput: true,
          placeholder: "Select date of birth"
        });
        console.log("DOB picker initialized successfully");
      } catch (error) {
        console.error("Error initializing DOB picker:", error);
      }
    }

    // Initialize date of appointment picker with no date restrictions
    const doaInput = document.getElementById("doa");
    console.log("DOA input found:", !!doaInput);
    
    if (doaInput) {
      try {
        flatpickr(doaInput, {
          ...dateConfig,
        });
        console.log("DOA picker initialized successfully");
      } catch (error) {
        console.error("Error initializing DOA picker:", error);
      }
    }
  }

  initializeDirectorFields() {
    console.log("Initializing director fields...");
    
    const directorTypeGroup = document.getElementById("directorTypeGroup");
    const directorCheckbox = document.getElementById("directorCheckbox");
    const directorTypeSelect = document.getElementById("typeOfDirector");

    console.log("Director elements found:", {
      directorTypeGroup: !!directorTypeGroup,
      directorCheckbox: !!directorCheckbox,
      directorTypeSelect: !!directorTypeSelect
    });

    if (directorTypeGroup && directorCheckbox && directorTypeSelect) {
      console.log("Setting initial state for director fields");
      
      // Force initial state with inline style
      directorTypeGroup.setAttribute("style", "display: none !important");
      directorTypeSelect.required = false;
      directorTypeSelect.value = "";

      console.log("Initial state set:", {
        groupDisplay: directorTypeGroup.style.display,
        selectRequired: directorTypeSelect.required,
        selectValue: directorTypeSelect.value
      });

      // Add event listener
      directorCheckbox.addEventListener("change", (event) => {
        console.log("Director checkbox changed:", {
          checked: directorCheckbox.checked,
          event: event.type
        });

        // Use !important to override any existing styles
        directorTypeGroup.setAttribute(
          "style", 
          directorCheckbox.checked ? "display: block !important" : "display: none !important"
        );
        directorTypeSelect.required = directorCheckbox.checked;
        
        if (!directorCheckbox.checked) {
          directorTypeSelect.value = "";
        }

        console.log("Updated director fields:", {
          groupDisplay: directorTypeGroup.style.display,
          selectRequired: directorTypeSelect.required,
          selectValue: directorTypeSelect.value
        });
      });

      // Trigger the change event to ensure initial state
      directorCheckbox.dispatchEvent(new Event('change'));
      
      console.log("Director checkbox event listener added");
    } else {
      console.warn("Some director elements not found:", {
        typeGroup: directorTypeGroup,
        checkbox: directorCheckbox,
        typeSelect: directorTypeSelect
      });
    }
  }
}

// Initialize the form when the DOM is loaded
document.addEventListener("DOMContentLoaded", () => {
  console.log("DOM fully loaded");
  console.log("Initializing AddEmployeeForm");
  
  const addEmployeeForm = new AddEmployeeForm();

  // Set default ID type to RSA
  const idTypeSelect = document.getElementById("idType");
  if (idTypeSelect) {
    idTypeSelect.value = "rsa";
    addEmployeeForm.handleIdTypeChange();
  }

  // Check if Same as Residential is checked by default
  const sameAsResidentialCheckbox =
    document.getElementById("sameAsResidential");
  if (sameAsResidentialCheckbox && sameAsResidentialCheckbox.checked) {
    addEmployeeForm.handlePostalAddressChange();
  }

  // Populate pay frequencies if available in the window object
  if (window.payFrequencies) {
    addEmployeeForm.populatePayFrequencies(window.payFrequencies);
  }

  const paymentMethodSelect = document.getElementById('paymentMethod');
  const bankingDetailsContainer = document.getElementById('bankingDetailsFields');

  function toggleBankingFields(paymentMethod) {
    console.log('Payment method changed to:', paymentMethod);

    if (!bankingDetailsContainer) {
      console.warn('Banking details container not found');
      return;
    }

    const requiredFields = bankingDetailsContainer.querySelectorAll('select[required], input[required]');

    if (paymentMethod === 'eft') {
      // Show banking details for EFT payments
      bankingDetailsContainer.style.display = 'block';
      bankingDetailsContainer.classList.add('show');

      // Add required attributes for EFT fields
      const eftRequiredFields = ['bankName', 'accountType', 'accountNumber', 'accountHolder'];
      eftRequiredFields.forEach(fieldName => {
        const field = document.getElementById(fieldName);
        if (field) {
          field.setAttribute('required', '');
        }
      });

      console.log('Banking details shown for EFT payment');
    } else {
      // Hide banking details for cash/cheque payments
      bankingDetailsContainer.style.display = 'none';
      bankingDetailsContainer.classList.remove('show');

      // Remove required attributes when fields are hidden
      requiredFields.forEach(field => {
        field.removeAttribute('required');
      });

      console.log('Banking details hidden for non-EFT payment');
    }
  }

  // Initial state
  if (paymentMethodSelect) {
    toggleBankingFields(paymentMethodSelect.value);

    // Add change event listener
    paymentMethodSelect.addEventListener('change', function() {
      toggleBankingFields(this.value);
    });

    console.log('Payment method toggle initialized');
  } else {
    console.warn('Payment method select not found');
  }
});

// Update the global nextStep function
window.nextStep = function (step) {
  console.log("Global nextStep called with step:", step);
  if (window.employeeForm) {
    window.employeeForm.nextStep(step);
  } else {
    console.error("employeeForm instance not found");
  }
};

// Update the global prevStep function
window.prevStep = function (step) {
  console.log("Global prevStep called with step:", step);
  if (window.employeeForm) {
    window.employeeForm.prevStep(step);
  } else {
    console.error("employeeForm instance not found");
  }
};

function updateStepIndicator(currentStep) {
  const stepIndicator = document.querySelector(".step-indicator");
  const steps = stepIndicator.querySelectorAll(".step-number");

  stepIndicator.dataset.currentStep = currentStep;

  steps.forEach((step, index) => {
    const stepNum = index + 1;
    
    // Remove all classes first
    step.classList.remove("active", "completed");
    
    // Add appropriate class based on step number
    if (stepNum < currentStep) {
      step.classList.add("completed");
      // Add tick mark for completed steps
      let tickMark = step.querySelector('.tick-mark');
      if (!tickMark) {
        tickMark = document.createElement('i');
        tickMark.className = 'ph ph-check tick-mark';
        step.appendChild(tickMark);
      }
    } else if (stepNum === currentStep) {
      step.classList.add("active");
      // Remove tick mark for current step
      const tickMark = step.querySelector('.tick-mark');
      if (tickMark) {
        tickMark.remove();
      }
    } else {
      // Remove tick mark for future steps
      const tickMark = step.querySelector('.tick-mark');
      if (tickMark) {
        tickMark.remove();
      }
    }
  });
}

// Bank selection handling
const bankSelect = document.getElementById("bankName");
const branchCodeInput = document.getElementById("branchCode");
const accountHolderInput = document.getElementById("accountHolder");
const firstNameInput = document.getElementById("firstName");
const lastNameInput = document.getElementById("lastName");

if (bankSelect) {
  // Populate bank dropdown
  Object.keys(SOUTH_AFRICAN_BANKS).forEach((bankKey) => {
    const bank = SOUTH_AFRICAN_BANKS[bankKey];
    const option = new Option(bank.name, bank.name);
    bankSelect.appendChild(option);
  });

  // Handle bank selection change
  bankSelect.addEventListener("change", function () {
    const selectedBank = SOUTH_AFRICAN_BANKS[bankSelect.value];
    if (selectedBank) {
      branchCodeInput.value = selectedBank.universalCode;
      if (selectedBank.universalCode) {
        branchCodeInput.readOnly = true;
        branchCodeInput.classList.add("auto-populated");
      } else {
        branchCodeInput.readOnly = false;
        branchCodeInput.classList.remove("auto-populated");
      }
    }
  });

  // Auto-populate account holder name when first and last names are entered
  function updateAccountHolder() {
    if (firstNameInput && lastNameInput) {
      const firstName = firstNameInput.value.trim();
      const lastName = lastNameInput.value.trim();
      if (firstName && lastName) {
        accountHolderInput.value = `${firstName} ${lastName}`;
      }
    }
  }

  if (firstNameInput && lastNameInput) {
    firstNameInput.addEventListener("input", updateAccountHolder);
    lastNameInput.addEventListener("input", updateAccountHolder);
  }
}
