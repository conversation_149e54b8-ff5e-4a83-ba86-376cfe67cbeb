<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Help Navigation Test</title>
    <style>
        .nav-link {
            display: block;
            padding: 10px;
            margin: 5px 0;
            background: #f0f0f0;
            text-decoration: none;
            color: #333;
            border: 1px solid #ccc;
        }
        .nav-link:hover {
            background: #e0e0e0;
        }
        #helpContent {
            border: 1px solid #ccc;
            padding: 20px;
            margin: 20px 0;
            min-height: 200px;
        }
    </style>
</head>
<body>
    <h1>Help Navigation Test</h1>
    
    <div>
        <h2>Navigation Links</h2>
        <a href="#free-trial" class="nav-link">Free Trial Setup</a>
        <a href="#setup-checklist" class="nav-link">Setup Checklist</a>
        <a href="#general-setup" class="nav-link">General Setup</a>
    </div>
    
    <div id="helpContent">
        <p>Content will appear here...</p>
    </div>
    
    <button onclick="testFunction()">Test JavaScript</button>
    
    <script>
        function testFunction() {
            console.log('Test button clicked - JavaScript is working!');
            document.getElementById('helpContent').innerHTML = '<h2>JavaScript Test</h2><p>JavaScript is working correctly!</p>';
        }
        
        // Simple navigation test
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded');
            
            const navLinks = document.querySelectorAll('.nav-link');
            console.log('Found nav links:', navLinks.length);
            
            navLinks.forEach(function(link, index) {
                console.log('Adding click handler to link', index, ':', link.getAttribute('href'));
                
                link.addEventListener('click', function(e) {
                    console.log('Link clicked:', link.getAttribute('href'));
                    e.preventDefault();
                    
                    const href = link.getAttribute('href');
                    const sectionId = href.replace('#', '');
                    
                    document.getElementById('helpContent').innerHTML = 
                        '<h2>Section: ' + sectionId + '</h2><p>Navigation is working! Clicked on: ' + link.textContent + '</p>';
                });
            });
        });
    </script>
</body>
</html>
