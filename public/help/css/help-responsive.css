/* PandaPayroll Help Center - Responsive Styles */

/* Tablet Styles */
@media (max-width: 1024px) {
  .container {
    padding: 0 1rem;
  }

  .header-container {
    padding: 0 1rem;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.125rem;
  }

  .content-layout {
    grid-template-columns: 250px 1fr;
    gap: 2rem;
  }

  .help-sidebar {
    padding: 1rem;
  }

  .help-content {
    padding: 2rem;
  }

  .content-header h1 {
    font-size: 2rem;
  }

  .quick-links-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
  }

  .quick-link-card {
    padding: 1.5rem;
  }

  .footer-content {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
  }
}

/* Mobile Styles */
@media (max-width: 768px) {
  .header-container {
    flex-direction: column;
    height: auto;
    padding: 1rem;
    gap: 1rem;
  }

  .logo-link {
    order: 1;
  }

  .back-to-app {
    order: 2;
    width: 100%;
    justify-content: center;
  }

  .hero-section {
    padding: 3rem 0;
  }

  .hero-container {
    padding: 0 1rem;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1rem;
    margin-bottom: 2rem;
  }

  .search-container {
    margin: 0 1rem;
  }

  #helpSearch {
    padding: 0.875rem 0.875rem 0.875rem 2.5rem;
    font-size: 0.875rem;
  }

  .search-icon {
    left: 0.875rem;
  }

  .quick-links-section {
    padding: 3rem 0;
  }

  .section-title {
    font-size: 1.75rem;
    margin-bottom: 2rem;
  }

  .quick-links-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 0 1rem;
  }

  .quick-link-card {
    padding: 1.25rem;
  }

  .quick-link-icon {
    width: 3rem;
    height: 3rem;
    font-size: 1.25rem;
    margin-bottom: 1rem;
  }

  .main-content {
    padding: 2rem 0;
  }

  .content-layout {
    grid-template-columns: 1fr;
    gap: 0;
  }

  .help-sidebar {
    position: static;
    max-height: none;
    margin-bottom: 2rem;
    border-radius: var(--radius-md);
  }

  .help-content {
    padding: 1.5rem;
    border-radius: var(--radius-md);
  }

  .content-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
  }

  .content-header h1 {
    font-size: 1.75rem;
  }

  .content-description {
    font-size: 1rem;
  }

  .help-article h2 {
    font-size: 1.5rem;
  }

  .help-article h3 {
    font-size: 1.125rem;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .contact-button {
    display: block;
    text-align: center;
  }
}

/* Small Mobile Styles */
@media (max-width: 480px) {
  .container {
    padding: 0 0.75rem;
  }

  .header-container {
    padding: 0.75rem;
  }

  .logo {
    height: 1.5rem;
  }

  .logo-text {
    font-size: 1.125rem;
  }

  .hero-section {
    padding: 2rem 0;
  }

  .hero-title {
    font-size: 1.75rem;
  }

  .hero-subtitle {
    font-size: 0.875rem;
  }

  .search-container {
    margin: 0 0.75rem;
  }

  .quick-links-section {
    padding: 2rem 0;
  }

  .section-title {
    font-size: 1.5rem;
  }

  .quick-links-grid {
    padding: 0 0.75rem;
  }

  .quick-link-card {
    padding: 1rem;
  }

  .help-sidebar {
    padding: 1rem;
  }

  .help-content {
    padding: 1rem;
  }

  .content-header h1 {
    font-size: 1.5rem;
  }

  .help-article h2 {
    font-size: 1.25rem;
  }

  .help-article h3 {
    font-size: 1rem;
  }

  .help-footer {
    padding: 2rem 0 1rem;
  }
}

/* Mobile Navigation Enhancements */
@media (max-width: 768px) {
  .help-sidebar {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    overflow: hidden;
  }

  .mobile-nav-toggle {
    display: block;
    width: 100%;
    padding: 1rem;
    background: var(--primary-color);
    color: white;
    border: none;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    text-align: left;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .mobile-nav-toggle i {
    transition: transform 0.2s ease;
  }

  .mobile-nav-toggle.active i {
    transform: rotate(180deg);
  }

  .help-nav {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
  }

  .help-nav.active {
    max-height: 1000px;
    padding: 1rem;
  }

  .nav-section {
    margin-bottom: 1rem;
  }

  .nav-section:last-child {
    margin-bottom: 0;
  }

  .nav-section-title {
    font-size: 0.8rem;
    margin-bottom: 0.5rem;
  }

  .nav-link {
    padding: 0.375rem 0.5rem;
    font-size: 0.8rem;
  }
}

/* Search Results Mobile Optimization */
@media (max-width: 768px) {
  .search-results {
    margin-top: 0.25rem;
    border-radius: var(--radius-md);
    max-height: 300px;
  }

  .search-result-item {
    padding: 0.75rem;
  }

  .search-result-title {
    font-size: 0.875rem;
  }

  .search-result-description {
    font-size: 0.8rem;
  }

  .search-result-category {
    font-size: 0.7rem;
  }
}

/* Print Styles */
@media print {
  .help-header,
  .hero-section,
  .quick-links-section,
  .help-sidebar,
  .help-footer {
    display: none;
  }

  .main-content {
    padding: 0;
  }

  .content-layout {
    grid-template-columns: 1fr;
  }

  .help-content {
    border: none;
    padding: 0;
    box-shadow: none;
  }

  .help-article {
    display: block !important;
  }

  .help-article h2,
  .help-article h3 {
    page-break-after: avoid;
  }

  .help-article p,
  .help-article ul,
  .help-article ol {
    page-break-inside: avoid;
  }

  body {
    background: white;
    color: black;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  :root {
    --border-color: #000000;
    --text-secondary: #000000;
    --text-muted: #333333;
  }

  .quick-link-card {
    border: 2px solid var(--border-color);
  }

  .nav-link:hover,
  .nav-link.active {
    background: #000000;
    color: #ffffff;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  html {
    scroll-behavior: auto;
  }
}

/* Dark Mode Support (Future Enhancement) */
@media (prefers-color-scheme: dark) {
  :root {
    --background-color: #0f172a;
    --card-background: #1e293b;
    --text-primary: #f1f5f9;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;
    --border-color: #334155;
    --border-light: #475569;
  }

  .help-header {
    background: var(--card-background);
  }

  .hero-section {
    background: linear-gradient(135deg, #4338ca 0%, #6366f1 100%);
  }

  #helpSearch {
    background: var(--card-background);
    color: var(--text-primary);
  }

  .search-results {
    background: var(--card-background);
  }

  .search-result-item:hover {
    background-color: var(--background-color);
  }
}

/* Focus Styles for Accessibility */
.nav-link:focus,
.quick-link-card:focus,
#helpSearch:focus,
.back-to-app:focus,
.contact-button:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Skip Link for Screen Readers */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--primary-color);
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 1000;
}

.skip-link:focus {
  top: 6px;
}

/* Loading Animation for Mobile */
@media (max-width: 768px) {
  .loading {
    padding: 2rem;
  }
}

/* Sticky Sidebar Adjustments for Tablet */
@media (max-width: 1024px) and (min-width: 769px) {
  .help-sidebar {
    top: 5rem;
    max-height: calc(100vh - 7rem);
  }
}
