// PandaPayroll Help Center - Content Management

class HelpContent {
  constructor() {
    this.content = {};
    this.loadAllContent();
  }

  loadAllContent() {
    // Home/Welcome Content
    this.content['home'] = `
      <div class="help-article active">
        <h2>Welcome to PandaPayroll Help Center</h2>
        <p>PandaPayroll is South Africa's leading cloud-based payroll management system, designed to simplify payroll processing while ensuring full compliance with SARS regulations.</p>
        
        <h3>What You'll Find Here</h3>
        <ul>
          <li><strong>Step-by-step guides</strong> for all PandaPayroll features</li>
          <li><strong>Best practices</strong> for South African payroll management</li>
          <li><strong>Troubleshooting tips</strong> for common issues</li>
          <li><strong>Compliance guidance</strong> for SARS submissions</li>
          <li><strong>Integration tutorials</strong> for accounting software</li>
        </ul>

        <h3>Getting Started</h3>
        <p>New to PandaPayroll? Start with our <a href="#setup-checklist">Setup Checklist</a> to get your payroll system configured correctly from day one.</p>

        <h3>Popular Topics</h3>
        <ul>
          <li><a href="#payslip-creation">Creating Payslips</a></li>
          <li><a href="#statutory-deductions">Understanding PAYE, UIF, and SDL</a></li>
          <li><a href="#monthly-submissions">EMP201 Monthly Submissions</a></li>
          <li><a href="#employee-setup">Adding New Employees</a></li>
          <li><a href="#leave-management">Managing Employee Leave</a></li>
        </ul>

        <h3>Need Help?</h3>
        <p>Can't find what you're looking for? Use the search bar above or <a href="mailto:<EMAIL>">contact our support team</a> for personalized assistance.</p>
      </div>
    `;

    // Getting Started Content
    this.content['free-trial'] = `
      <div class="help-article active">
        <h2>Free Trial Setup</h2>
        <p>Get started with PandaPayroll's 30-day free trial and experience the power of automated South African payroll processing.</p>

        <h3>Starting Your Free Trial</h3>
        <ol>
          <li><strong>Visit the Registration Page</strong>
            <p>Go to <a href="https://payroll.pss-group.co.za/register" target="_blank">payroll.pss-group.co.za/register</a></p>
          </li>
          <li><strong>Enter Your Details</strong>
            <ul>
              <li>Full name and email address</li>
              <li>Company name and registration number</li>
              <li>Contact phone number</li>
              <li>Number of employees (for pricing estimation)</li>
            </ul>
          </li>
          <li><strong>Verify Your Email</strong>
            <p>Check your email for a verification link and click to activate your account.</p>
          </li>
          <li><strong>Complete Company Setup</strong>
            <p>Follow the setup wizard to configure your company details and tax settings.</p>
          </li>
        </ol>

        <h3>What's Included in Your Trial</h3>
        <ul>
          <li>Full access to all PandaPayroll features</li>
          <li>Up to 10 employees (no payment required)</li>
          <li>Complete payroll processing capabilities</li>
          <li>SARS-compliant tax calculations</li>
          <li>Email support during business hours</li>
          <li>Access to all help documentation</li>
        </ul>

        <h3>Trial Limitations</h3>
        <ul>
          <li>30-day time limit</li>
          <li>Maximum of 10 employees</li>
          <li>No EFT file generation (view-only)</li>
          <li>Watermarked payslips and reports</li>
        </ul>

        <h3>After Your Trial</h3>
        <p>When your trial expires, you can:</p>
        <ul>
          <li><strong>Subscribe</strong> to continue using PandaPayroll</li>
          <li><strong>Export your data</strong> before the trial ends</li>
          <li><strong>Contact sales</strong> for custom pricing options</li>
        </ul>

        <h3>Need Help During Your Trial?</h3>
        <p>Our support team is available to help you get the most out of your trial:</p>
        <ul>
          <li>Email: <a href="mailto:<EMAIL>"><EMAIL></a></li>
          <li>Phone: +27 11 234 5678 (Business hours)</li>
          <li>Live chat: Available in the application</li>
        </ul>
      </div>
    `;

    this.content['setup-checklist'] = `
      <div class="help-article active">
        <h2>Setup Checklist</h2>
        <p>Follow this comprehensive checklist to ensure your PandaPayroll system is configured correctly for South African payroll compliance.</p>

        <h3>Pre-Setup Requirements</h3>
        <p>Before you begin, gather the following information:</p>
        <ul>
          <li>Company registration documents</li>
          <li>SARS tax reference numbers</li>
          <li>UIF reference number</li>
          <li>SDL reference number (if applicable)</li>
          <li>Banking details for EFT payments</li>
          <li>Employee information and contracts</li>
        </ul>

        <h3>Step 1: Company Configuration</h3>
        <ul>
          <li>☐ Enter company legal name and trading name</li>
          <li>☐ Add company registration number</li>
          <li>☐ Configure business address</li>
          <li>☐ Set up SARS tax reference number</li>
          <li>☐ Enter UIF reference number</li>
          <li>☐ Add SDL reference number (if applicable)</li>
          <li>☐ Configure payroll calendar and pay dates</li>
        </ul>

        <h3>Step 2: Tax and Compliance Settings</h3>
        <ul>
          <li>☐ Verify PAYE tax tables (automatically updated)</li>
          <li>☐ Set UIF contribution rates</li>
          <li>☐ Configure SDL levy settings</li>
          <li>☐ Set up Employment Tax Incentive (ETI) if applicable</li>
          <li>☐ Configure medical aid tax credits</li>
        </ul>

        <h3>Step 3: Employee Setup</h3>
        <ul>
          <li>☐ Create employee profiles</li>
          <li>☐ Set pay frequencies (monthly, weekly, bi-weekly)</li>
          <li>☐ Configure basic salaries and wages</li>
          <li>☐ Set up banking details for EFT payments</li>
          <li>☐ Add tax directives (if applicable)</li>
          <li>☐ Configure leave entitlements</li>
        </ul>

        <h3>Step 4: System Items Configuration</h3>
        <ul>
          <li>☐ Set up income types (overtime, bonuses, allowances)</li>
          <li>☐ Configure deduction types (medical aid, pension, loans)</li>
          <li>☐ Set up benefit types (company car, accommodation)</li>
          <li>☐ Configure leave types and policies</li>
        </ul>

        <h3>Step 5: Integration Setup</h3>
        <ul>
          <li>☐ Connect accounting software (Xero, QuickBooks) if needed</li>
          <li>☐ Set up time and attendance integration</li>
          <li>☐ Configure email settings for payslip delivery</li>
          <li>☐ Set up user accounts and permissions</li>
        </ul>

        <h3>Step 6: Testing and Validation</h3>
        <ul>
          <li>☐ Process a test payroll for one employee</li>
          <li>☐ Verify tax calculations are correct</li>
          <li>☐ Test payslip generation and email delivery</li>
          <li>☐ Validate EFT file generation</li>
          <li>☐ Check reporting functionality</li>
        </ul>

        <h3>Step 7: Go-Live Preparation</h3>
        <ul>
          <li>☐ Import historical payroll data (if needed)</li>
          <li>☐ Train users on the system</li>
          <li>☐ Set up backup and security procedures</li>
          <li>☐ Schedule first live payroll run</li>
          <li>☐ Prepare communication for employees</li>
        </ul>

        <h3>Post Go-Live</h3>
        <ul>
          <li>☐ Monitor first payroll run closely</li>
          <li>☐ Collect feedback from users and employees</li>
          <li>☐ Schedule regular system maintenance</li>
          <li>☐ Set up ongoing support procedures</li>
        </ul>

        <h3>Need Help?</h3>
        <p>Our implementation team can assist with setup and configuration. Contact us at <a href="mailto:<EMAIL>"><EMAIL></a> for personalized assistance.</p>
      </div>
    `;

    this.content['general-setup'] = `
      <div class="help-article active">
        <h2>General Setup</h2>
        <p>Configure the basic settings and preferences for your PandaPayroll system to ensure optimal performance and compliance.</p>

        <h3>Accessing System Settings</h3>
        <ol>
          <li>Log in to your PandaPayroll account</li>
          <li>Navigate to <strong>Settings</strong> in the main menu</li>
          <li>Select <strong>General Settings</strong> from the sidebar</li>
        </ol>

        <h3>Company Information</h3>
        <p>Set up your company's basic information:</p>
        <ul>
          <li><strong>Company Name</strong>: Legal name as registered with CIPC</li>
          <li><strong>Trading Name</strong>: Name used for business operations</li>
          <li><strong>Registration Number</strong>: CIPC registration number</li>
          <li><strong>VAT Number</strong>: If VAT registered</li>
          <li><strong>Physical Address</strong>: Business location</li>
          <li><strong>Postal Address</strong>: Mailing address</li>
          <li><strong>Contact Details</strong>: Phone, email, website</li>
        </ul>

        <h3>Tax Reference Numbers</h3>
        <p>Configure all required tax reference numbers:</p>
        <ul>
          <li><strong>PAYE Reference</strong>: SARS PAYE reference number</li>
          <li><strong>UIF Reference</strong>: Department of Employment reference</li>
          <li><strong>SDL Reference</strong>: Skills Development Levy reference</li>
          <li><strong>Workmen's Compensation</strong>: Industry-specific reference</li>
        </ul>

        <h3>Payroll Calendar</h3>
        <p>Set up your payroll processing schedule:</p>
        <ul>
          <li><strong>Pay Frequency</strong>: Monthly, weekly, or bi-weekly</li>
          <li><strong>Pay Day</strong>: Day of the month/week for payments</li>
          <li><strong>Cut-off Date</strong>: Last day for payroll inputs</li>
          <li><strong>Processing Date</strong>: When payroll is calculated</li>
          <li><strong>Public Holidays</strong>: South African public holiday calendar</li>
        </ul>

        <h3>Email Configuration</h3>
        <p>Configure email settings for automated communications:</p>
        <ul>
          <li><strong>SMTP Settings</strong>: Email server configuration</li>
          <li><strong>From Address</strong>: Sender email address</li>
          <li><strong>Email Templates</strong>: Customize payslip and notification emails</li>
          <li><strong>Delivery Options</strong>: When to send automated emails</li>
        </ul>

        <h3>Security Settings</h3>
        <p>Configure security and access controls:</p>
        <ul>
          <li><strong>Password Policy</strong>: Minimum requirements for user passwords</li>
          <li><strong>Session Timeout</strong>: Automatic logout after inactivity</li>
          <li><strong>Two-Factor Authentication</strong>: Enable 2FA for enhanced security</li>
          <li><strong>IP Restrictions</strong>: Limit access to specific IP addresses</li>
        </ul>

        <h3>Backup and Data Retention</h3>
        <p>Configure data backup and retention policies:</p>
        <ul>
          <li><strong>Automatic Backups</strong>: Daily system backups</li>
          <li><strong>Data Retention</strong>: How long to keep historical data</li>
          <li><strong>Export Options</strong>: Regular data exports for compliance</li>
        </ul>

        <h3>Regional Settings</h3>
        <p>Set up location-specific preferences:</p>
        <ul>
          <li><strong>Time Zone</strong>: South Africa Standard Time (SAST)</li>
          <li><strong>Currency</strong>: South African Rand (ZAR)</li>
          <li><strong>Date Format</strong>: DD/MM/YYYY or YYYY-MM-DD</li>
          <li><strong>Number Format</strong>: Decimal and thousand separators</li>
        </ul>

        <h3>Saving Your Settings</h3>
        <p>Remember to:</p>
        <ul>
          <li>Click <strong>Save Changes</strong> after each section</li>
          <li>Test settings before going live</li>
          <li>Document any custom configurations</li>
          <li>Regularly review and update settings</li>
        </ul>
      </div>
    `;

    this.content['company-management'] = `
      <div class="help-article active">
        <h2>Company Management</h2>
        <p>Learn how to manage multiple companies, switch between them, and configure company-specific settings in PandaPayroll.</p>

        <h3>Multi-Company Overview</h3>
        <p>PandaPayroll supports multiple companies under a single account, allowing you to:</p>
        <ul>
          <li>Manage payroll for multiple business entities</li>
          <li>Maintain separate employee databases</li>
          <li>Generate company-specific reports</li>
          <li>Handle different tax configurations</li>
          <li>Process payrolls independently</li>
        </ul>

        <h3>Adding a New Company</h3>
        <ol>
          <li>Navigate to the <strong>Companies</strong> section</li>
          <li>Click <strong>Add New Company</strong></li>
          <li>Enter company details:
            <ul>
              <li>Company name and trading name</li>
              <li>Registration number</li>
              <li>Tax reference numbers</li>
              <li>Contact information</li>
            </ul>
          </li>
          <li>Configure payroll settings</li>
          <li>Set up user access permissions</li>
          <li>Save and activate the company</li>
        </ol>

        <h3>Switching Between Companies</h3>
        <p>To switch between companies:</p>
        <ol>
          <li>Click the <strong>Company Selector</strong> in the top navigation</li>
          <li>Select the company you want to work with</li>
          <li>The system will reload with the selected company's data</li>
        </ol>

        <h3>Company-Specific Settings</h3>
        <p>Each company can have unique configurations:</p>
        <ul>
          <li><strong>Tax Settings</strong>: Different PAYE, UIF, SDL configurations</li>
          <li><strong>Pay Frequencies</strong>: Monthly, weekly, or bi-weekly schedules</li>
          <li><strong>Leave Policies</strong>: Company-specific leave types and entitlements</li>
          <li><strong>System Items</strong>: Custom income, deduction, and benefit types</li>
          <li><strong>Reporting</strong>: Company-specific report templates</li>
        </ul>

        <h3>User Access Management</h3>
        <p>Control which users can access each company:</p>
        <ul>
          <li><strong>Company Administrators</strong>: Full access to all features</li>
          <li><strong>Payroll Clerks</strong>: Access to payroll processing only</li>
          <li><strong>Managers</strong>: View-only access to reports</li>
          <li><strong>Employees</strong>: Self-service portal access only</li>
        </ul>

        <h3>Data Separation</h3>
        <p>Each company maintains separate:</p>
        <ul>
          <li>Employee databases</li>
          <li>Payroll histories</li>
          <li>Tax submissions</li>
          <li>Reports and analytics</li>
          <li>Integration settings</li>
        </ul>

        <h3>Consolidation and Reporting</h3>
        <p>Generate consolidated reports across companies:</p>
        <ul>
          <li>Combined employee counts</li>
          <li>Total payroll costs</li>
          <li>Tax liability summaries</li>
          <li>Cross-company analytics</li>
        </ul>

        <h3>Best Practices</h3>
        <ul>
          <li>Use consistent naming conventions</li>
          <li>Maintain separate tax reference numbers</li>
          <li>Regular backup of company data</li>
          <li>Document company-specific procedures</li>
          <li>Train users on multi-company workflows</li>
        </ul>
      </div>
    `;

    this.content['user-management'] = `
      <div class="help-article active">
        <h2>User Management</h2>
        <p>Manage user accounts, permissions, and access controls to ensure secure and efficient use of PandaPayroll.</p>

        <h3>User Roles and Permissions</h3>
        <p>PandaPayroll offers several user roles with different access levels:</p>

        <h4>System Administrator</h4>
        <ul>
          <li>Full system access and configuration</li>
          <li>User management and permissions</li>
          <li>Company setup and settings</li>
          <li>Integration management</li>
          <li>System maintenance and backups</li>
        </ul>

        <h4>Company Administrator</h4>
        <ul>
          <li>Full access to assigned companies</li>
          <li>Employee management</li>
          <li>Payroll processing and approval</li>
          <li>Report generation and export</li>
          <li>Tax submissions and compliance</li>
        </ul>

        <h4>Payroll Clerk</h4>
        <ul>
          <li>Payroll data entry and processing</li>
          <li>Employee information updates</li>
          <li>Basic reporting</li>
          <li>Payslip generation</li>
          <li>Limited system configuration</li>
        </ul>

        <h4>Manager</h4>
        <ul>
          <li>View-only access to reports</li>
          <li>Employee information viewing</li>
          <li>Leave approval (if configured)</li>
          <li>Dashboard analytics</li>
        </ul>

        <h4>Employee</h4>
        <ul>
          <li>Self-service portal access</li>
          <li>Personal payslip viewing</li>
          <li>Leave request submission</li>
          <li>Personal information updates</li>
          <li>Tax certificate downloads</li>
        </ul>

        <h3>Adding New Users</h3>
        <ol>
          <li>Navigate to <strong>Settings > User Management</strong></li>
          <li>Click <strong>Add New User</strong></li>
          <li>Enter user details:
            <ul>
              <li>Full name and email address</li>
              <li>Username (if different from email)</li>
              <li>Contact information</li>
            </ul>
          </li>
          <li>Assign user role and permissions</li>
          <li>Select accessible companies</li>
          <li>Set password requirements</li>
          <li>Send invitation email</li>
        </ol>

        <h3>Managing Existing Users</h3>
        <p>To modify user accounts:</p>
        <ul>
          <li><strong>Edit Profile</strong>: Update personal information</li>
          <li><strong>Change Role</strong>: Modify permissions and access level</li>
          <li><strong>Reset Password</strong>: Force password reset on next login</li>
          <li><strong>Suspend Account</strong>: Temporarily disable access</li>
          <li><strong>Delete User</strong>: Permanently remove account</li>
        </ul>

        <h3>Security Settings</h3>
        <p>Configure security policies for all users:</p>
        <ul>
          <li><strong>Password Requirements</strong>:
            <ul>
              <li>Minimum length (8-16 characters)</li>
              <li>Complexity requirements</li>
              <li>Password expiry (30-90 days)</li>
              <li>Password history (prevent reuse)</li>
            </ul>
          </li>
          <li><strong>Session Management</strong>:
            <ul>
              <li>Automatic logout after inactivity</li>
              <li>Maximum concurrent sessions</li>
              <li>IP address restrictions</li>
            </ul>
          </li>
          <li><strong>Two-Factor Authentication</strong>:
            <ul>
              <li>Mandatory for administrators</li>
              <li>Optional for other users</li>
              <li>SMS or authenticator app options</li>
            </ul>
          </li>
        </ul>

        <h3>User Activity Monitoring</h3>
        <p>Track user activity and system access:</p>
        <ul>
          <li>Login/logout timestamps</li>
          <li>Actions performed</li>
          <li>Data accessed or modified</li>
          <li>Failed login attempts</li>
          <li>Security violations</li>
        </ul>

        <h3>Bulk User Operations</h3>
        <p>Manage multiple users efficiently:</p>
        <ul>
          <li><strong>Bulk Import</strong>: Import users from CSV file</li>
          <li><strong>Bulk Update</strong>: Update multiple user properties</li>
          <li><strong>Bulk Deactivation</strong>: Disable multiple accounts</li>
          <li><strong>Permission Templates</strong>: Apply standard permission sets</li>
        </ul>

        <h3>Best Practices</h3>
        <ul>
          <li>Follow principle of least privilege</li>
          <li>Regularly review user access</li>
          <li>Remove access for departed employees immediately</li>
          <li>Use strong password policies</li>
          <li>Enable two-factor authentication</li>
          <li>Monitor user activity logs</li>
          <li>Conduct regular security audits</li>
        </ul>
      </div>
    `;

    // Payroll Setup Content
    this.content['company-setup'] = `
      <div class="help-article active">
        <h2>Company Setup</h2>
        <p>Configure your company details, tax settings, and payroll preferences to ensure accurate and compliant payroll processing.</p>

        <h3>Basic Company Information</h3>
        <ol>
          <li>Navigate to <strong>Settings > Company Setup</strong></li>
          <li>Enter your company details:
            <ul>
              <li><strong>Legal Name</strong>: As registered with CIPC</li>
              <li><strong>Trading Name</strong>: Business operating name</li>
              <li><strong>Registration Number</strong>: CIPC registration number</li>
              <li><strong>VAT Number</strong>: If VAT registered</li>
              <li><strong>Industry</strong>: Select your business sector</li>
            </ul>
          </li>
          <li>Add contact information and addresses</li>
          <li>Save your changes</li>
        </ol>

        <h3>Tax Configuration</h3>
        <p>Set up all required South African tax references:</p>
        <ul>
          <li><strong>PAYE Reference Number</strong>: From SARS registration</li>
          <li><strong>UIF Reference Number</strong>: Department of Employment reference</li>
          <li><strong>SDL Reference Number</strong>: For companies with payroll > R500,000</li>
          <li><strong>Workmen's Compensation</strong>: Industry-specific reference</li>
          <li><strong>Bargaining Council</strong>: If applicable to your industry</li>
        </ul>

        <h3>Payroll Calendar Setup</h3>
        <p>Configure your payroll processing schedule:</p>
        <ul>
          <li><strong>Default Pay Frequency</strong>: Monthly, weekly, or bi-weekly</li>
          <li><strong>Pay Day</strong>: Day of month/week for salary payments</li>
          <li><strong>Cut-off Date</strong>: Last day for payroll inputs</li>
          <li><strong>Processing Day</strong>: When payroll calculations are done</li>
          <li><strong>Public Holidays</strong>: South African holiday calendar</li>
        </ul>

        <h3>Banking and EFT Settings</h3>
        <p>Set up banking details for electronic payments:</p>
        <ul>
          <li><strong>Company Bank Account</strong>: For salary payments</li>
          <li><strong>EFT Format</strong>: Bank-specific file format</li>
          <li><strong>Payment Reference</strong>: Default payment descriptions</li>
          <li><strong>Approval Workflow</strong>: Who can approve payments</li>
        </ul>

        <h3>Leave Policies</h3>
        <p>Configure company-wide leave policies:</p>
        <ul>
          <li><strong>Annual Leave</strong>: Days per year and accrual method</li>
          <li><strong>Sick Leave</strong>: Entitlement and documentation requirements</li>
          <li><strong>Maternity/Paternity</strong>: Leave duration and pay rates</li>
          <li><strong>Study Leave</strong>: Educational leave policies</li>
          <li><strong>Custom Leave Types</strong>: Company-specific leave categories</li>
        </ul>

        <h3>Compliance Settings</h3>
        <p>Ensure compliance with South African regulations:</p>
        <ul>
          <li><strong>Employment Equity</strong>: Enable EE reporting if required</li>
          <li><strong>Skills Development</strong>: SDL levy configuration</li>
          <li><strong>Employment Tax Incentive</strong>: ETI settings for qualifying employees</li>
          <li><strong>Sectoral Determinations</strong>: Industry-specific wage requirements</li>
        </ul>
      </div>
    `;

    this.content['employee-setup'] = `
      <div class="help-article active">
        <h2>Employee Setup</h2>
        <p>Learn how to add new employees, configure their payroll details, and manage employee information effectively.</p>

        <h3>Adding a New Employee</h3>
        <ol>
          <li>Navigate to <strong>Employees > Add New Employee</strong></li>
          <li>Enter personal information:
            <ul>
              <li>Full name and preferred name</li>
              <li>ID number and ID type (RSA ID, Passport, etc.)</li>
              <li>Date of birth</li>
              <li>Contact details (phone, email, address)</li>
              <li>Emergency contact information</li>
            </ul>
          </li>
          <li>Configure employment details:
            <ul>
              <li>Employee number (auto-generated or custom)</li>
              <li>Start date and probation period</li>
              <li>Job title and department</li>
              <li>Employment type (permanent, contract, casual)</li>
              <li>Cost center allocation</li>
            </ul>
          </li>
          <li>Set up payroll information</li>
          <li>Configure banking details</li>
          <li>Save the employee profile</li>
        </ol>

        <h3>Payroll Configuration</h3>
        <p>Set up the employee's payroll details:</p>
        <ul>
          <li><strong>Pay Frequency</strong>: Monthly, weekly, or bi-weekly</li>
          <li><strong>Basic Salary</strong>: Annual or period amount</li>
          <li><strong>Salary Type</strong>: Fixed salary, hourly rate, or commission</li>
          <li><strong>Tax Directive</strong>: Custom PAYE rate if applicable</li>
          <li><strong>Medical Aid</strong>: Scheme details and contribution amounts</li>
          <li><strong>Pension Fund</strong>: Retirement fund contributions</li>
        </ul>

        <h3>Banking Details</h3>
        <p>Configure payment information:</p>
        <ul>
          <li><strong>Bank Name</strong>: Select from South African banks</li>
          <li><strong>Account Type</strong>: Current, savings, or transmission</li>
          <li><strong>Account Number</strong>: Employee's bank account</li>
          <li><strong>Branch Code</strong>: Bank branch identifier</li>
          <li><strong>Account Holder</strong>: Own account or third party</li>
        </ul>

        <h3>Leave Entitlements</h3>
        <p>Set up employee leave balances:</p>
        <ul>
          <li><strong>Annual Leave</strong>: Days per year based on employment date</li>
          <li><strong>Sick Leave</strong>: 30 days per 3-year cycle</li>
          <li><strong>Maternity Leave</strong>: 4 months for eligible employees</li>
          <li><strong>Study Leave</strong>: Company-specific entitlements</li>
          <li><strong>Carry Over</strong>: Previous year's unused leave</li>
        </ul>

        <h3>Tax and Compliance</h3>
        <p>Configure tax-related information:</p>
        <ul>
          <li><strong>Tax Number</strong>: Employee's SARS tax reference</li>
          <li><strong>Tax Directive</strong>: Custom PAYE rates from SARS</li>
          <li><strong>Medical Aid Credits</strong>: Tax credits for medical scheme</li>
          <li><strong>Employment Tax Incentive</strong>: ETI qualification status</li>
          <li><strong>Disability Status</strong>: For tax rebate purposes</li>
        </ul>

        <h3>Employment Equity Information</h3>
        <p>Capture EE data for reporting (if required):</p>
        <ul>
          <li><strong>Race</strong>: As per Employment Equity Act</li>
          <li><strong>Gender</strong>: Male, female, or other</li>
          <li><strong>Disability</strong>: Disability status</li>
          <li><strong>Nationality</strong>: South African or foreign national</li>
          <li><strong>Occupational Level</strong>: Management level classification</li>
        </ul>

        <h3>Bulk Employee Import</h3>
        <p>Import multiple employees from Excel:</p>
        <ol>
          <li>Download the employee import template</li>
          <li>Fill in employee details in the spreadsheet</li>
          <li>Upload the completed file</li>
          <li>Review and validate the imported data</li>
          <li>Confirm the import to create employee profiles</li>
        </ol>

        <h3>Employee Status Management</h3>
        <p>Manage employee lifecycle:</p>
        <ul>
          <li><strong>Active</strong>: Currently employed and on payroll</li>
          <li><strong>Inactive</strong>: Temporarily not on payroll</li>
          <li><strong>Terminated</strong>: Employment ended</li>
          <li><strong>Suspended</strong>: Temporarily suspended from work</li>
        </ul>
      </div>
    `;

    this.content['dashboard-features'] = `
      <div class="help-article active">
        <h2>Dashboard Features</h2>
        <p>Understand the main dashboard and how to navigate PandaPayroll's key features efficiently.</p>

        <h3>Dashboard Overview</h3>
        <p>The PandaPayroll dashboard provides a comprehensive view of your payroll system:</p>
        <ul>
          <li>Real-time payroll status and progress</li>
          <li>Employee count and statistics</li>
          <li>Upcoming deadlines and tasks</li>
          <li>Recent activity and notifications</li>
          <li>Quick access to common functions</li>
        </ul>

        <h3>Main Navigation</h3>
        <p>The main menu provides access to all system features:</p>
        <ul>
          <li><strong>Dashboard</strong>: Overview and quick actions</li>
          <li><strong>Employees</strong>: Employee management and profiles</li>
          <li><strong>Payroll</strong>: Payroll processing and pay runs</li>
          <li><strong>Leave</strong>: Leave management and approvals</li>
          <li><strong>Reports</strong>: Analytics and compliance reports</li>
          <li><strong>Filing</strong>: SARS submissions and compliance</li>
          <li><strong>Settings</strong>: System configuration and preferences</li>
        </ul>

        <h3>Payroll Status Cards</h3>
        <p>Monitor payroll progress with status cards:</p>
        <ul>
          <li><strong>Pending Payslips</strong>: Payslips awaiting processing</li>
          <li><strong>In Progress</strong>: Currently being processed</li>
          <li><strong>Ready for Review</strong>: Calculated and awaiting approval</li>
          <li><strong>Finalized</strong>: Completed and ready for payment</li>
          <li><strong>Paid</strong>: Payments processed and sent</li>
        </ul>

        <h3>Quick Actions</h3>
        <p>Access common tasks directly from the dashboard:</p>
        <ul>
          <li><strong>Add Employee</strong>: Create new employee profile</li>
          <li><strong>Process Payroll</strong>: Start payroll calculation</li>
          <li><strong>Generate Reports</strong>: Create payroll reports</li>
          <li><strong>Submit to SARS</strong>: File monthly returns</li>
          <li><strong>Download Payslips</strong>: Bulk payslip download</li>
        </ul>

        <h3>Notifications and Alerts</h3>
        <p>Stay informed with system notifications:</p>
        <ul>
          <li><strong>Deadline Reminders</strong>: SARS submission deadlines</li>
          <li><strong>Error Alerts</strong>: Payroll calculation issues</li>
          <li><strong>System Updates</strong>: New features and improvements</li>
          <li><strong>Compliance Warnings</strong>: Regulatory compliance issues</li>
        </ul>

        <h3>Company Selector</h3>
        <p>Switch between multiple companies:</p>
        <ul>
          <li>Click the company name in the top navigation</li>
          <li>Select from the dropdown list</li>
          <li>Dashboard updates with selected company data</li>
          <li>All subsequent actions apply to the selected company</li>
        </ul>

        <h3>User Profile and Settings</h3>
        <p>Access personal settings and preferences:</p>
        <ul>
          <li><strong>Profile Settings</strong>: Update personal information</li>
          <li><strong>Password Change</strong>: Update login credentials</li>
          <li><strong>Notification Preferences</strong>: Email and system alerts</li>
          <li><strong>Two-Factor Authentication</strong>: Enhanced security setup</li>
        </ul>

        <h3>Search and Filters</h3>
        <p>Find information quickly:</p>
        <ul>
          <li><strong>Global Search</strong>: Search across all data</li>
          <li><strong>Employee Search</strong>: Find specific employees</li>
          <li><strong>Date Filters</strong>: Filter by pay periods</li>
          <li><strong>Status Filters</strong>: Filter by payroll status</li>
        </ul>

        <h3>Mobile Responsiveness</h3>
        <p>Access PandaPayroll on mobile devices:</p>
        <ul>
          <li>Responsive design for tablets and phones</li>
          <li>Touch-friendly interface</li>
          <li>Essential features available on mobile</li>
          <li>Offline capability for basic functions</li>
        </ul>
      </div>
    `;

    // Payroll Processing Content
    this.content['payslip-creation'] = `
      <div class="help-article active">
        <h2>Payslip Creation</h2>
        <p>Learn how to create, process, and manage employee payslips in PandaPayroll.</p>

        <h3>Creating Individual Payslips</h3>
        <ol>
          <li>Navigate to <strong>Payroll > Payslips</strong></li>
          <li>Click <strong>Create New Payslip</strong></li>
          <li>Select the employee from the dropdown</li>
          <li>Choose the pay period (start and end dates)</li>
          <li>Enter payroll inputs:
            <ul>
              <li>Basic salary (auto-populated from employee profile)</li>
              <li>Overtime hours and rates</li>
              <li>Bonuses and allowances</li>
              <li>Deductions (loans, garnishee orders)</li>
              <li>Leave taken during the period</li>
            </ul>
          </li>
          <li>Review calculated amounts</li>
          <li>Save as draft or finalize</li>
        </ol>

        <h3>Bulk Payslip Creation</h3>
        <p>Create payslips for multiple employees simultaneously:</p>
        <ol>
          <li>Go to <strong>Payroll > Bulk Operations</strong></li>
          <li>Select <strong>Create Payslips</strong></li>
          <li>Choose employees (all or filtered selection)</li>
          <li>Set the pay period dates</li>
          <li>Configure default settings:
            <ul>
              <li>Standard working hours</li>
              <li>Default overtime rates</li>
              <li>Automatic leave calculations</li>
            </ul>
          </li>
          <li>Click <strong>Generate Payslips</strong></li>
          <li>Review and edit individual payslips as needed</li>
        </ol>

        <h3>Payslip Components</h3>
        <p>Understanding payslip sections:</p>
        <ul>
          <li><strong>Employee Information</strong>: Name, employee number, ID number</li>
          <li><strong>Pay Period</strong>: Start and end dates of the pay period</li>
          <li><strong>Earnings</strong>:
            <ul>
              <li>Basic salary</li>
              <li>Overtime pay</li>
              <li>Bonuses and incentives</li>
              <li>Allowances (travel, housing, etc.)</li>
              <li>Commission payments</li>
            </ul>
          </li>
          <li><strong>Deductions</strong>:
            <ul>
              <li>PAYE (Pay As You Earn tax)</li>
              <li>UIF (Unemployment Insurance Fund)</li>
              <li>Medical aid contributions</li>
              <li>Pension fund contributions</li>
              <li>Loan repayments</li>
              <li>Garnishee orders</li>
            </ul>
          </li>
          <li><strong>Employer Contributions</strong>:
            <ul>
              <li>UIF employer contribution</li>
              <li>SDL (Skills Development Levy)</li>
              <li>Workmen's compensation</li>
            </ul>
          </li>
          <li><strong>Net Pay</strong>: Final amount paid to employee</li>
        </ul>

        <h3>Payslip Status Workflow</h3>
        <p>Payslips progress through several statuses:</p>
        <ul>
          <li><strong>Draft</strong>: Initial creation, can be edited</li>
          <li><strong>Calculated</strong>: Taxes and deductions computed</li>
          <li><strong>Under Review</strong>: Awaiting approval</li>
          <li><strong>Approved</strong>: Ready for payment processing</li>
          <li><strong>Finalized</strong>: Locked and ready for payment</li>
          <li><strong>Paid</strong>: Payment processed and sent</li>
        </ul>

        <h3>Editing Payslips</h3>
        <p>Modify payslips before finalization:</p>
        <ul>
          <li>Click on the payslip to open the editor</li>
          <li>Modify earnings, deductions, or hours</li>
          <li>Add notes or comments for audit trail</li>
          <li>Recalculate taxes automatically</li>
          <li>Save changes and update status</li>
        </ul>

        <h3>Payslip Approval Process</h3>
        <p>Set up approval workflows:</p>
        <ol>
          <li>Configure approval rules in settings</li>
          <li>Assign approvers by department or amount</li>
          <li>Submit payslips for approval</li>
          <li>Approvers receive notifications</li>
          <li>Approved payslips move to finalized status</li>
        </ol>

        <h3>Common Issues and Solutions</h3>
        <ul>
          <li><strong>Incorrect Tax Calculations</strong>:
            <ul>
              <li>Verify employee tax directive</li>
              <li>Check medical aid tax credits</li>
              <li>Ensure correct annual salary</li>
            </ul>
          </li>
          <li><strong>Missing Deductions</strong>:
            <ul>
              <li>Check employee profile for recurring deductions</li>
              <li>Verify deduction start and end dates</li>
              <li>Ensure deduction amounts are current</li>
            </ul>
          </li>
          <li><strong>Overtime Calculation Errors</strong>:
            <ul>
              <li>Verify overtime rates in employee profile</li>
              <li>Check overtime calculation method</li>
              <li>Ensure correct hours are entered</li>
            </ul>
          </li>
        </ul>
      </div>
    `;

    this.content['statutory-deductions'] = `
      <div class="help-article active">
        <h2>Statutory Deductions (South Africa)</h2>
        <p>Understand how PAYE, UIF, SDL, and other statutory deductions are calculated and applied in South African payroll.</p>

        <h3>PAYE (Pay As You Earn)</h3>
        <p>PAYE is the income tax deducted from employee salaries:</p>
        <ul>
          <li><strong>Calculation Method</strong>: Progressive tax rates based on annual income</li>
          <li><strong>Tax Tables</strong>: Updated annually by SARS</li>
          <li><strong>Tax Rebates</strong>:
            <ul>
              <li>Primary rebate: R17,235 (2024/2025 tax year)</li>
              <li>Secondary rebate: R9,444 (age 65+)</li>
              <li>Tertiary rebate: R3,145 (age 75+)</li>
            </ul>
          </li>
          <li><strong>Medical Aid Credits</strong>:
            <ul>
              <li>R347 per month per member</li>
              <li>R234 per month per dependant</li>
            </ul>
          </li>
        </ul>

        <h4>PAYE Calculation Example</h4>
        <p>For an employee earning R25,000 per month:</p>
        <ol>
          <li>Annual salary: R25,000 × 12 = R300,000</li>
          <li>Annual tax (before rebates): R42,678</li>
          <li>Less primary rebate: R42,678 - R17,235 = R25,443</li>
          <li>Monthly PAYE: R25,443 ÷ 12 = R2,120</li>
        </ol>

        <h3>UIF (Unemployment Insurance Fund)</h3>
        <p>UIF provides benefits to workers who become unemployed:</p>
        <ul>
          <li><strong>Employee Contribution</strong>: 1% of gross salary</li>
          <li><strong>Employer Contribution</strong>: 1% of gross salary</li>
          <li><strong>Maximum Monthly Contribution</strong>: R177.12 (based on R17,712 income ceiling)</li>
          <li><strong>Exemptions</strong>:
            <ul>
              <li>Foreign nationals on work permits</li>
              <li>Employees working less than 24 hours per month</li>
              <li>Learners receiving stipends</li>
            </ul>
          </li>
        </ul>

        <h4>UIF Calculation Example</h4>
        <p>For an employee earning R25,000 per month:</p>
        <ul>
          <li>Employee UIF: R25,000 × 1% = R250</li>
          <li>Employer UIF: R25,000 × 1% = R250</li>
          <li>Total UIF: R500 (R250 from employee + R250 from employer)</li>
        </ul>

        <h3>SDL (Skills Development Levy)</h3>
        <p>SDL funds skills development and training:</p>
        <ul>
          <li><strong>Rate</strong>: 1% of total payroll</li>
          <li><strong>Threshold</strong>: Only applies to companies with annual payroll > R500,000</li>
          <li><strong>Paid by</strong>: Employer only (not deducted from employee)</li>
          <li><strong>Calculation</strong>: Based on total company payroll, not individual salaries</li>
        </ul>

        <h4>SDL Calculation Example</h4>
        <p>Company with monthly payroll of R500,000:</p>
        <ul>
          <li>Monthly SDL: R500,000 × 1% = R5,000</li>
          <li>Annual SDL: R5,000 × 12 = R60,000</li>
        </ul>

        <h3>Other Statutory Deductions</h3>

        <h4>Workmen's Compensation</h4>
        <ul>
          <li>Industry-specific rates</li>
          <li>Paid by employer</li>
          <li>Based on risk assessment of industry</li>
          <li>Rates vary from 0.22% to 1.52% of payroll</li>
        </ul>

        <h4>Bargaining Council Levies</h4>
        <ul>
          <li>Industry-specific contributions</li>
          <li>Varies by sector (e.g., Motor Industry, Building Industry)</li>
          <li>Usually split between employer and employee</li>
          <li>Rates determined by each bargaining council</li>
        </ul>

        <h3>Tax Directives</h3>
        <p>SARS-issued instructions for custom tax rates:</p>
        <ul>
          <li><strong>When Issued</strong>:
            <ul>
              <li>Multiple employers</li>
              <li>Significant other income</li>
              <li>Large medical aid contributions</li>
              <li>Retirement annuity contributions</li>
            </ul>
          </li>
          <li><strong>Implementation</strong>: Override standard PAYE calculation</li>
          <li><strong>Validity</strong>: Usually valid for one tax year</li>
          <li><strong>Compliance</strong>: Must be applied as directed by SARS</li>
        </ul>

        <h3>Medical Aid Tax Credits</h3>
        <p>Tax credits for medical scheme contributions:</p>
        <ul>
          <li><strong>Main Member</strong>: R347 per month</li>
          <li><strong>First Dependant</strong>: R234 per month</li>
          <li><strong>Additional Dependants</strong>: R234 per month each</li>
          <li><strong>Application</strong>: Reduces PAYE liability</li>
          <li><strong>Excess Contributions</strong>: May be claimed as deduction</li>
        </ul>

        <h3>Compliance and Reporting</h3>
        <p>Ensure accurate statutory deduction reporting:</p>
        <ul>
          <li><strong>Monthly EMP201</strong>: Report PAYE, UIF, SDL to SARS</li>
          <li><strong>Bi-annual EMP501</strong>: Reconcile annual amounts</li>
          <li><strong>IRP5 Certificates</strong>: Annual employee tax certificates</li>
          <li><strong>UIF Declarations</strong>: Monthly submissions to Department of Employment</li>
        </ul>

        <h3>Common Calculation Errors</h3>
        <ul>
          <li><strong>Incorrect Annual Salary</strong>: Ensure pro-rata calculations for new employees</li>
          <li><strong>Missing Tax Directives</strong>: Apply SARS directives when issued</li>
          <li><strong>Wrong UIF Ceiling</strong>: Cap contributions at R177.12 per month</li>
          <li><strong>SDL Threshold</strong>: Only apply to companies above R500,000 annual payroll</li>
        </ul>
      </div>
    `;

    // Troubleshooting Content
    this.content['login-issues'] = `
      <div class="help-article active">
        <h2>Login Issues</h2>
        <p>Resolve common login and access problems with PandaPayroll.</p>

        <h3>Forgot Password</h3>
        <ol>
          <li>Go to the login page</li>
          <li>Click <strong>"Forgot Password?"</strong></li>
          <li>Enter your email address</li>
          <li>Check your email for reset instructions</li>
          <li>Click the reset link in the email</li>
          <li>Create a new password</li>
          <li>Log in with your new password</li>
        </ol>

        <h3>Account Locked</h3>
        <p>If your account is locked due to multiple failed login attempts:</p>
        <ul>
          <li>Wait 15 minutes for automatic unlock</li>
          <li>Or contact support for immediate unlock</li>
          <li>Ensure you're using the correct password</li>
          <li>Check if Caps Lock is enabled</li>
        </ul>

        <h3>Two-Factor Authentication Issues</h3>
        <p>Problems with 2FA codes:</p>
        <ul>
          <li><strong>Code Not Working</strong>:
            <ul>
              <li>Ensure your device time is synchronized</li>
              <li>Try the next code generated</li>
              <li>Check if you're using the correct authenticator app</li>
            </ul>
          </li>
          <li><strong>Lost Authenticator Device</strong>:
            <ul>
              <li>Use backup codes provided during setup</li>
              <li>Contact support to reset 2FA</li>
              <li>Provide identity verification</li>
            </ul>
          </li>
        </ul>

        <h3>Browser Issues</h3>
        <p>Common browser-related problems:</p>
        <ul>
          <li><strong>Clear Browser Cache</strong>:
            <ul>
              <li>Press Ctrl+Shift+Delete (Windows) or Cmd+Shift+Delete (Mac)</li>
              <li>Select "All time" for time range</li>
              <li>Clear cookies and cached files</li>
            </ul>
          </li>
          <li><strong>Disable Browser Extensions</strong>:
            <ul>
              <li>Try logging in using incognito/private mode</li>
              <li>Disable ad blockers and security extensions</li>
              <li>Test with a different browser</li>
            </ul>
          </li>
          <li><strong>Enable JavaScript</strong>:
            <ul>
              <li>Ensure JavaScript is enabled in browser settings</li>
              <li>Allow pop-ups for the PandaPayroll domain</li>
            </ul>
          </li>
        </ul>

        <h3>Network and Connectivity</h3>
        <p>Check network-related issues:</p>
        <ul>
          <li><strong>Internet Connection</strong>: Verify stable internet connectivity</li>
          <li><strong>Firewall Settings</strong>: Ensure PandaPayroll isn't blocked</li>
          <li><strong>VPN Issues</strong>: Try connecting without VPN</li>
          <li><strong>Corporate Network</strong>: Check with IT department for restrictions</li>
        </ul>

        <h3>Email Not Received</h3>
        <p>If you're not receiving password reset emails:</p>
        <ul>
          <li>Check spam/junk folder</li>
          <li>Add <EMAIL> to safe senders</li>
          <li>Try a different email address</li>
          <li>Contact support if emails still don't arrive</li>
        </ul>

        <h3>Still Can't Log In?</h3>
        <p>Contact our support team:</p>
        <ul>
          <li><strong>Email</strong>: <EMAIL></li>
          <li><strong>Phone</strong>: +27 11 234 5678</li>
          <li><strong>Live Chat</strong>: Available during business hours</li>
          <li><strong>Include</strong>: Your email address, error messages, browser type</li>
        </ul>
      </div>
    `;

    this.content['monthly-submissions'] = `
      <div class="help-article active">
        <h2>Monthly Submissions (EMP201)</h2>
        <p>Learn how to prepare and submit monthly EMP201 returns to SARS for PAYE, UIF, and SDL compliance.</p>

        <h3>EMP201 Overview</h3>
        <p>The EMP201 is a monthly return that reports:</p>
        <ul>
          <li>PAYE (Pay As You Earn) deducted from employees</li>
          <li>UIF (Unemployment Insurance Fund) contributions</li>
          <li>SDL (Skills Development Levy) if applicable</li>
          <li>Total amounts due to SARS</li>
        </ul>

        <h3>Submission Deadlines</h3>
        <ul>
          <li><strong>Monthly Deadline</strong>: 7th of the following month</li>
          <li><strong>Payment Deadline</strong>: 7th of the following month</li>
          <li><strong>Late Submission Penalty</strong>: R250 per month or 10% of tax due</li>
          <li><strong>Interest on Late Payment</strong>: Prescribed rate plus 4%</li>
        </ul>

        <h3>Preparing EMP201 in PandaPayroll</h3>
        <ol>
          <li>Navigate to <strong>Filing > Monthly Submissions</strong></li>
          <li>Select the month and year for submission</li>
          <li>Click <strong>Generate EMP201</strong></li>
          <li>Review the calculated amounts:
            <ul>
              <li>Total PAYE deducted</li>
              <li>Total UIF (employee + employer)</li>
              <li>SDL amount (if applicable)</li>
              <li>Interest and penalties (if any)</li>
            </ul>
          </li>
          <li>Verify employee count and total remuneration</li>
          <li>Check for any errors or warnings</li>
          <li>Generate the submission file</li>
        </ol>

        <h3>Submission Methods</h3>

        <h4>eFiling (Recommended)</h4>
        <ol>
          <li>Log in to SARS eFiling</li>
          <li>Select <strong>Returns > EMP201</strong></li>
          <li>Choose <strong>File Return</strong></li>
          <li>Upload the CSV file from PandaPayroll</li>
          <li>Review and submit</li>
          <li>Print acknowledgment of receipt</li>
        </ol>

        <h4>Manual Entry</h4>
        <ol>
          <li>Log in to SARS eFiling</li>
          <li>Select <strong>Returns > EMP201</strong></li>
          <li>Choose <strong>Prepare Online</strong></li>
          <li>Enter amounts from PandaPayroll report</li>
          <li>Submit and print acknowledgment</li>
        </ol>

        <h3>EMP201 Components</h3>
        <p>Understanding the EMP201 sections:</p>
        <ul>
          <li><strong>3601 - PAYE</strong>: Total PAYE deducted from employees</li>
          <li><strong>3603 - SDL</strong>: Skills Development Levy (1% of payroll)</li>
          <li><strong>3604 - UIF</strong>: Total UIF contributions (employee + employer)</li>
          <li><strong>3605 - ETI</strong>: Employment Tax Incentive claimed</li>
          <li><strong>4001 - Total Due</strong>: Net amount payable to SARS</li>
        </ul>

        <h3>Common Errors and Solutions</h3>
        <ul>
          <li><strong>Mismatched Amounts</strong>:
            <ul>
              <li>Ensure all payslips are finalized</li>
              <li>Check for manual adjustments</li>
              <li>Verify tax directive applications</li>
            </ul>
          </li>
          <li><strong>Missing Employees</strong>:
            <ul>
              <li>Include all employees paid during the month</li>
              <li>Check for employees with zero pay</li>
              <li>Verify employee status settings</li>
            </ul>
          </li>
          <li><strong>SDL Calculation Errors</strong>:
            <ul>
              <li>Ensure company qualifies for SDL (payroll > R500,000)</li>
              <li>Check SDL rate configuration</li>
              <li>Verify total payroll calculation</li>
            </ul>
          </li>
        </ul>

        <h3>Payment Process</h3>
        <ol>
          <li>Submit EMP201 return</li>
          <li>Note the payment reference number</li>
          <li>Make payment via:
            <ul>
              <li>Internet banking</li>
              <li>Bank branch</li>
              <li>SARS eFiling payment portal</li>
            </ul>
          </li>
          <li>Use correct SARS banking details</li>
          <li>Include payment reference number</li>
          <li>Keep proof of payment</li>
        </ol>

        <h3>Record Keeping</h3>
        <p>Maintain proper records:</p>
        <ul>
          <li>EMP201 submission confirmations</li>
          <li>Payment receipts and bank statements</li>
          <li>Supporting payroll reports</li>
          <li>Employee tax certificates (IRP5)</li>
          <li>Correspondence with SARS</li>
        </ul>

        <h3>Penalties and Interest</h3>
        <p>Avoid penalties by:</p>
        <ul>
          <li>Submitting returns by the 7th of each month</li>
          <li>Making payments by the due date</li>
          <li>Ensuring accurate calculations</li>
          <li>Keeping proper records</li>
          <li>Responding promptly to SARS queries</li>
        </ul>
      </div>
    `;

    this.content['quickbooks-integration'] = `
      <div class="help-article active">
        <h2>QuickBooks Online Integration</h2>
        <p>Connect PandaPayroll with QuickBooks Online to automatically sync payroll data and streamline your accounting processes.</p>

        <h3>Integration Benefits</h3>
        <ul>
          <li>Automatic posting of payroll journals</li>
          <li>Synchronized employee data</li>
          <li>Reduced manual data entry</li>
          <li>Improved accuracy and consistency</li>
          <li>Real-time financial reporting</li>
        </ul>

        <h3>Prerequisites</h3>
        <p>Before setting up the integration:</p>
        <ul>
          <li>Active QuickBooks Online subscription</li>
          <li>Administrator access to both systems</li>
          <li>Chart of accounts set up in QuickBooks</li>
          <li>Employee records in both systems</li>
        </ul>

        <h3>Setting Up the Integration</h3>
        <ol>
          <li>Navigate to <strong>Settings > Integrations</strong></li>
          <li>Click <strong>QuickBooks Online</strong></li>
          <li>Click <strong>Connect to QuickBooks</strong></li>
          <li>Log in to your QuickBooks Online account</li>
          <li>Authorize PandaPayroll to access your data</li>
          <li>Select your QuickBooks company</li>
          <li>Configure mapping settings</li>
          <li>Test the connection</li>
        </ol>

        <h3>Account Mapping</h3>
        <p>Map PandaPayroll accounts to QuickBooks accounts:</p>
        <ul>
          <li><strong>Salary Expense</strong>: Map to salary/wage expense accounts</li>
          <li><strong>PAYE Liability</strong>: Map to PAYE payable account</li>
          <li><strong>UIF Liability</strong>: Map to UIF payable account</li>
          <li><strong>SDL Expense</strong>: Map to SDL expense account</li>
          <li><strong>Bank Account</strong>: Map to payroll bank account</li>
        </ul>

        <h3>Employee Synchronization</h3>
        <p>Sync employee data between systems:</p>
        <ul>
          <li><strong>Initial Sync</strong>: Match existing employees by name or ID</li>
          <li><strong>New Employees</strong>: Automatically create in QuickBooks</li>
          <li><strong>Updates</strong>: Sync changes to employee information</li>
          <li><strong>Deactivation</strong>: Mark terminated employees as inactive</li>
        </ul>

        <h3>Payroll Journal Entries</h3>
        <p>Automatic posting of payroll transactions:</p>
        <ul>
          <li><strong>Gross Salaries</strong>: Posted to salary expense accounts</li>
          <li><strong>Tax Deductions</strong>: Posted to liability accounts</li>
          <li><strong>Net Pay</strong>: Posted as credit to bank account</li>
          <li><strong>Employer Contributions</strong>: Posted to expense accounts</li>
        </ul>

        <h4>Sample Journal Entry</h4>
        <pre><code>
Debit: Salary Expense          R100,000
Debit: UIF Expense (Employer)    R1,000
Debit: SDL Expense               R1,000
Credit: PAYE Payable                      R18,000
Credit: UIF Payable                        R2,000
Credit: Bank Account                      R82,000
        </code></pre>

        <h3>Sync Frequency</h3>
        <p>Configure how often data syncs:</p>
        <ul>
          <li><strong>Real-time</strong>: Immediate sync after payroll finalization</li>
          <li><strong>Daily</strong>: Once per day at specified time</li>
          <li><strong>Weekly</strong>: Weekly sync on specified day</li>
          <li><strong>Manual</strong>: Sync only when manually triggered</li>
        </ul>

        <h3>Troubleshooting Common Issues</h3>
        <ul>
          <li><strong>Connection Errors</strong>:
            <ul>
              <li>Check internet connectivity</li>
              <li>Verify QuickBooks login credentials</li>
              <li>Re-authorize the connection</li>
            </ul>
          </li>
          <li><strong>Mapping Errors</strong>:
            <ul>
              <li>Ensure all required accounts exist in QuickBooks</li>
              <li>Check account types match expected categories</li>
              <li>Verify account names and numbers</li>
            </ul>
          </li>
          <li><strong>Duplicate Entries</strong>:
            <ul>
              <li>Check sync settings for duplicates</li>
              <li>Review date ranges for sync</li>
              <li>Manually delete duplicate transactions</li>
            </ul>
          </li>
        </ul>

        <h3>Best Practices</h3>
        <ul>
          <li>Set up chart of accounts before integration</li>
          <li>Test with a small payroll first</li>
          <li>Review synced data regularly</li>
          <li>Keep both systems updated</li>
          <li>Maintain backup of data</li>
          <li>Train users on both systems</li>
        </ul>

        <h3>Disconnecting the Integration</h3>
        <p>To disconnect QuickBooks integration:</p>
        <ol>
          <li>Go to <strong>Settings > Integrations</strong></li>
          <li>Find QuickBooks Online integration</li>
          <li>Click <strong>Disconnect</strong></li>
          <li>Confirm disconnection</li>
          <li>Revoke access in QuickBooks if needed</li>
        </ol>

        <h3>Support and Resources</h3>
        <ul>
          <li>Integration setup assistance</li>
          <li>Account mapping guidance</li>
          <li>Troubleshooting support</li>
          <li>Training on integrated workflows</li>
          <li>Regular sync monitoring</li>
        </ul>
      </div>
    `;
  }

  getContent(sectionId) {
    return this.content[sectionId] || this.getDefaultContent(sectionId);
  }

  getDefaultContent(sectionId) {
    return `
      <div class="help-article active">
        <h2>Content Coming Soon</h2>
        <p>We're working on creating comprehensive documentation for this section.</p>
        <p>In the meantime, please <a href="mailto:<EMAIL>">contact our support team</a> for assistance with <strong>${sectionId.replace('-', ' ')}</strong>.</p>
        
        <h3>What You Can Do</h3>
        <ul>
          <li>Use the search function to find related topics</li>
          <li>Browse other help sections for similar information</li>
          <li>Contact support for immediate assistance</li>
          <li>Check back soon for updated content</li>
        </ul>
      </div>
    `;
  }
}

// Initialize content when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.helpContent = new HelpContent();
});
