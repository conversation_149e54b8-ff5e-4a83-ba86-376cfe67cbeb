// PandaPayroll Help Center - Content Management

class HelpContent {
  constructor() {
    console.log('HelpContent constructor called');
    this.content = {};
    this.loadAllContent();
    console.log('Content loaded, total sections:', Object.keys(this.content).length);
  }

  loadAllContent() {
    // Home/Welcome Content
    this.content['home'] = `
      <div class="help-article active">
        <h2>Welcome to PandaPayroll Help Center</h2>
        <p>PandaPayroll is South Africa's leading cloud-based payroll management system, designed to simplify payroll processing while ensuring full compliance with SARS regulations.</p>
        
        <h3>What You'll Find Here</h3>
        <ul>
          <li><strong>Step-by-step guides</strong> for all PandaPayroll features</li>
          <li><strong>Best practices</strong> for South African payroll management</li>
          <li><strong>Troubleshooting tips</strong> for common issues</li>
          <li><strong>Compliance guidance</strong> for SARS submissions</li>
          <li><strong>Integration tutorials</strong> for accounting software</li>
        </ul>

        <h3>Getting Started</h3>
        <p>New to PandaPayroll? Start with our <a href="#setup-checklist">Setup Checklist</a> to get your payroll system configured correctly from day one.</p>

        <h3>Popular Topics</h3>
        <ul>
          <li><a href="#payslip-creation">Creating Payslips</a></li>
          <li><a href="#statutory-deductions">Understanding PAYE, UIF, and SDL</a></li>
          <li><a href="#monthly-submissions">EMP201 Monthly Submissions</a></li>
          <li><a href="#employee-setup">Adding New Employees</a></li>
          <li><a href="#leave-management">Managing Employee Leave</a></li>
        </ul>

        <h3>Need Help?</h3>
        <p>Can't find what you're looking for? Use the search bar above or <a href="mailto:<EMAIL>">contact our support team</a> for personalized assistance.</p>
      </div>
    `;

    // Getting Started Content
    this.content['free-trial'] = `
      <div class="help-article active">
        <h2>Free Trial Setup</h2>
        <p>Get started with PandaPayroll's 30-day free trial and experience the power of automated South African payroll processing.</p>

        <h3>Starting Your Free Trial</h3>
        <ol>
          <li><strong>Visit the Registration Page</strong>
            <p>Go to <a href="https://payroll.pss-group.co.za/register" target="_blank">payroll.pss-group.co.za/register</a></p>
          </li>
          <li><strong>Enter Your Details</strong>
            <ul>
              <li>Full name and email address</li>
              <li>Company name and registration number</li>
              <li>Contact phone number</li>
              <li>Number of employees (for pricing estimation)</li>
            </ul>
          </li>
          <li><strong>Verify Your Email</strong>
            <p>Check your email for a verification link and click to activate your account.</p>
          </li>
          <li><strong>Complete Company Setup</strong>
            <p>Follow the setup wizard to configure your company details and tax settings.</p>
          </li>
        </ol>

        <h3>What's Included in Your Trial</h3>
        <ul>
          <li>Full access to all PandaPayroll features</li>
          <li>Up to 10 employees (no payment required)</li>
          <li>Complete payroll processing capabilities</li>
          <li>SARS-compliant tax calculations</li>
          <li>Email support during business hours</li>
          <li>Access to all help documentation</li>
        </ul>

        <h3>Trial Limitations</h3>
        <ul>
          <li>30-day time limit</li>
          <li>Maximum of 10 employees</li>
          <li>No EFT file generation (view-only)</li>
          <li>Watermarked payslips and reports</li>
        </ul>

        <h3>After Your Trial</h3>
        <p>When your trial expires, you can:</p>
        <ul>
          <li><strong>Subscribe</strong> to continue using PandaPayroll</li>
          <li><strong>Export your data</strong> before the trial ends</li>
          <li><strong>Contact sales</strong> for custom pricing options</li>
        </ul>

        <h3>Need Help During Your Trial?</h3>
        <p>Our support team is available to help you get the most out of your trial:</p>
        <ul>
          <li>Email: <a href="mailto:<EMAIL>"><EMAIL></a></li>
          <li>Phone: +27 11 234 5678 (Business hours)</li>
          <li>Live chat: Available in the application</li>
        </ul>
      </div>
    `;

    this.content['setup-checklist'] = `
      <div class="help-article active">
        <h2>Setup Checklist</h2>
        <p>Follow this comprehensive checklist to ensure your PandaPayroll system is configured correctly for South African payroll compliance.</p>

        <h3>Pre-Setup Requirements</h3>
        <p>Before you begin, gather the following information:</p>
        <ul>
          <li>Company registration documents</li>
          <li>SARS tax reference numbers</li>
          <li>UIF reference number</li>
          <li>SDL reference number (if applicable)</li>
          <li>Banking details for EFT payments</li>
          <li>Employee information and contracts</li>
        </ul>

        <h3>Step 1: Company Configuration</h3>
        <ul>
          <li>☐ Enter company legal name and trading name</li>
          <li>☐ Add company registration number</li>
          <li>☐ Configure business address</li>
          <li>☐ Set up SARS tax reference number</li>
          <li>☐ Enter UIF reference number</li>
          <li>☐ Add SDL reference number (if applicable)</li>
          <li>☐ Configure payroll calendar and pay dates</li>
        </ul>

        <h3>Step 2: Tax and Compliance Settings</h3>
        <ul>
          <li>☐ Verify PAYE tax tables (automatically updated)</li>
          <li>☐ Set UIF contribution rates</li>
          <li>☐ Configure SDL levy settings</li>
          <li>☐ Set up Employment Tax Incentive (ETI) if applicable</li>
          <li>☐ Configure medical aid tax credits</li>
        </ul>

        <h3>Step 3: Employee Setup</h3>
        <ul>
          <li>☐ Create employee profiles</li>
          <li>☐ Set pay frequencies (monthly, weekly, bi-weekly)</li>
          <li>☐ Configure basic salaries and wages</li>
          <li>☐ Set up banking details for EFT payments</li>
          <li>☐ Add tax directives (if applicable)</li>
          <li>☐ Configure leave entitlements</li>
        </ul>

        <h3>Step 4: System Items Configuration</h3>
        <ul>
          <li>☐ Set up income types (overtime, bonuses, allowances)</li>
          <li>☐ Configure deduction types (medical aid, pension, loans)</li>
          <li>☐ Set up benefit types (company car, accommodation)</li>
          <li>☐ Configure leave types and policies</li>
        </ul>

        <h3>Step 5: Integration Setup</h3>
        <ul>
          <li>☐ Connect accounting software (Xero, QuickBooks) if needed</li>
          <li>☐ Set up time and attendance integration</li>
          <li>☐ Configure email settings for payslip delivery</li>
          <li>☐ Set up user accounts and permissions</li>
        </ul>

        <h3>Step 6: Testing and Validation</h3>
        <ul>
          <li>☐ Process a test payroll for one employee</li>
          <li>☐ Verify tax calculations are correct</li>
          <li>☐ Test payslip generation and email delivery</li>
          <li>☐ Validate EFT file generation</li>
          <li>☐ Check reporting functionality</li>
        </ul>

        <h3>Step 7: Go-Live Preparation</h3>
        <ul>
          <li>☐ Import historical payroll data (if needed)</li>
          <li>☐ Train users on the system</li>
          <li>☐ Set up backup and security procedures</li>
          <li>☐ Schedule first live payroll run</li>
          <li>☐ Prepare communication for employees</li>
        </ul>

        <h3>Post Go-Live</h3>
        <ul>
          <li>☐ Monitor first payroll run closely</li>
          <li>☐ Collect feedback from users and employees</li>
          <li>☐ Schedule regular system maintenance</li>
          <li>☐ Set up ongoing support procedures</li>
        </ul>

        <h3>Need Help?</h3>
        <p>Our implementation team can assist with setup and configuration. Contact us at <a href="mailto:<EMAIL>"><EMAIL></a> for personalized assistance.</p>
      </div>
    `;

    this.content['general-setup'] = `
      <div class="help-article active">
        <h2>General Setup</h2>
        <p>Configure the basic settings and preferences for your PandaPayroll system to ensure optimal performance and compliance.</p>

        <h3>Accessing System Settings</h3>
        <ol>
          <li>Log in to your PandaPayroll account</li>
          <li>Navigate to <strong>Settings</strong> in the main menu</li>
          <li>Select <strong>General Settings</strong> from the sidebar</li>
        </ol>

        <h3>Company Information</h3>
        <p>Set up your company's basic information:</p>
        <ul>
          <li><strong>Company Name</strong>: Legal name as registered with CIPC</li>
          <li><strong>Trading Name</strong>: Name used for business operations</li>
          <li><strong>Registration Number</strong>: CIPC registration number</li>
          <li><strong>VAT Number</strong>: If VAT registered</li>
          <li><strong>Physical Address</strong>: Business location</li>
          <li><strong>Postal Address</strong>: Mailing address</li>
          <li><strong>Contact Details</strong>: Phone, email, website</li>
        </ul>

        <h3>Tax Reference Numbers</h3>
        <p>Configure all required tax reference numbers:</p>
        <ul>
          <li><strong>PAYE Reference</strong>: SARS PAYE reference number</li>
          <li><strong>UIF Reference</strong>: Department of Employment reference</li>
          <li><strong>SDL Reference</strong>: Skills Development Levy reference</li>
          <li><strong>Workmen's Compensation</strong>: Industry-specific reference</li>
        </ul>

        <h3>Payroll Calendar</h3>
        <p>Set up your payroll processing schedule:</p>
        <ul>
          <li><strong>Pay Frequency</strong>: Monthly, weekly, or bi-weekly</li>
          <li><strong>Pay Day</strong>: Day of the month/week for payments</li>
          <li><strong>Cut-off Date</strong>: Last day for payroll inputs</li>
          <li><strong>Processing Date</strong>: When payroll is calculated</li>
          <li><strong>Public Holidays</strong>: South African public holiday calendar</li>
        </ul>

        <h3>Email Configuration</h3>
        <p>Configure email settings for automated communications:</p>
        <ul>
          <li><strong>SMTP Settings</strong>: Email server configuration</li>
          <li><strong>From Address</strong>: Sender email address</li>
          <li><strong>Email Templates</strong>: Customize payslip and notification emails</li>
          <li><strong>Delivery Options</strong>: When to send automated emails</li>
        </ul>

        <h3>Security Settings</h3>
        <p>Configure security and access controls:</p>
        <ul>
          <li><strong>Password Policy</strong>: Minimum requirements for user passwords</li>
          <li><strong>Session Timeout</strong>: Automatic logout after inactivity</li>
          <li><strong>Two-Factor Authentication</strong>: Enable 2FA for enhanced security</li>
          <li><strong>IP Restrictions</strong>: Limit access to specific IP addresses</li>
        </ul>

        <h3>Backup and Data Retention</h3>
        <p>Configure data backup and retention policies:</p>
        <ul>
          <li><strong>Automatic Backups</strong>: Daily system backups</li>
          <li><strong>Data Retention</strong>: How long to keep historical data</li>
          <li><strong>Export Options</strong>: Regular data exports for compliance</li>
        </ul>

        <h3>Regional Settings</h3>
        <p>Set up location-specific preferences:</p>
        <ul>
          <li><strong>Time Zone</strong>: South Africa Standard Time (SAST)</li>
          <li><strong>Currency</strong>: South African Rand (ZAR)</li>
          <li><strong>Date Format</strong>: DD/MM/YYYY or YYYY-MM-DD</li>
          <li><strong>Number Format</strong>: Decimal and thousand separators</li>
        </ul>

        <h3>Saving Your Settings</h3>
        <p>Remember to:</p>
        <ul>
          <li>Click <strong>Save Changes</strong> after each section</li>
          <li>Test settings before going live</li>
          <li>Document any custom configurations</li>
          <li>Regularly review and update settings</li>
        </ul>
      </div>
    `;

    this.content['company-management'] = `
      <div class="help-article active">
        <h2>Company Management</h2>
        <p>Learn how to manage multiple companies, switch between them, and configure company-specific settings in PandaPayroll.</p>

        <h3>Multi-Company Overview</h3>
        <p>PandaPayroll supports multiple companies under a single account, allowing you to:</p>
        <ul>
          <li>Manage payroll for multiple business entities</li>
          <li>Maintain separate employee databases</li>
          <li>Generate company-specific reports</li>
          <li>Handle different tax configurations</li>
          <li>Process payrolls independently</li>
        </ul>

        <h3>Adding a New Company</h3>
        <ol>
          <li>Navigate to the <strong>Companies</strong> section</li>
          <li>Click <strong>Add New Company</strong></li>
          <li>Enter company details:
            <ul>
              <li>Company name and trading name</li>
              <li>Registration number</li>
              <li>Tax reference numbers</li>
              <li>Contact information</li>
            </ul>
          </li>
          <li>Configure payroll settings</li>
          <li>Set up user access permissions</li>
          <li>Save and activate the company</li>
        </ol>

        <h3>Switching Between Companies</h3>
        <p>To switch between companies:</p>
        <ol>
          <li>Click the <strong>Company Selector</strong> in the top navigation</li>
          <li>Select the company you want to work with</li>
          <li>The system will reload with the selected company's data</li>
        </ol>

        <h3>Company-Specific Settings</h3>
        <p>Each company can have unique configurations:</p>
        <ul>
          <li><strong>Tax Settings</strong>: Different PAYE, UIF, SDL configurations</li>
          <li><strong>Pay Frequencies</strong>: Monthly, weekly, or bi-weekly schedules</li>
          <li><strong>Leave Policies</strong>: Company-specific leave types and entitlements</li>
          <li><strong>System Items</strong>: Custom income, deduction, and benefit types</li>
          <li><strong>Reporting</strong>: Company-specific report templates</li>
        </ul>

        <h3>User Access Management</h3>
        <p>Control which users can access each company:</p>
        <ul>
          <li><strong>Company Administrators</strong>: Full access to all features</li>
          <li><strong>Payroll Clerks</strong>: Access to payroll processing only</li>
          <li><strong>Managers</strong>: View-only access to reports</li>
          <li><strong>Employees</strong>: Self-service portal access only</li>
        </ul>

        <h3>Data Separation</h3>
        <p>Each company maintains separate:</p>
        <ul>
          <li>Employee databases</li>
          <li>Payroll histories</li>
          <li>Tax submissions</li>
          <li>Reports and analytics</li>
          <li>Integration settings</li>
        </ul>

        <h3>Consolidation and Reporting</h3>
        <p>Generate consolidated reports across companies:</p>
        <ul>
          <li>Combined employee counts</li>
          <li>Total payroll costs</li>
          <li>Tax liability summaries</li>
          <li>Cross-company analytics</li>
        </ul>

        <h3>Best Practices</h3>
        <ul>
          <li>Use consistent naming conventions</li>
          <li>Maintain separate tax reference numbers</li>
          <li>Regular backup of company data</li>
          <li>Document company-specific procedures</li>
          <li>Train users on multi-company workflows</li>
        </ul>
      </div>
    `;

    this.content['user-management'] = `
      <div class="help-article active">
        <h2>User Management</h2>
        <p>Manage user accounts, permissions, and access controls to ensure secure and efficient use of PandaPayroll.</p>

        <h3>User Roles and Permissions</h3>
        <p>PandaPayroll offers several user roles with different access levels:</p>

        <h4>System Administrator</h4>
        <ul>
          <li>Full system access and configuration</li>
          <li>User management and permissions</li>
          <li>Company setup and settings</li>
          <li>Integration management</li>
          <li>System maintenance and backups</li>
        </ul>

        <h4>Company Administrator</h4>
        <ul>
          <li>Full access to assigned companies</li>
          <li>Employee management</li>
          <li>Payroll processing and approval</li>
          <li>Report generation and export</li>
          <li>Tax submissions and compliance</li>
        </ul>

        <h4>Payroll Clerk</h4>
        <ul>
          <li>Payroll data entry and processing</li>
          <li>Employee information updates</li>
          <li>Basic reporting</li>
          <li>Payslip generation</li>
          <li>Limited system configuration</li>
        </ul>

        <h4>Manager</h4>
        <ul>
          <li>View-only access to reports</li>
          <li>Employee information viewing</li>
          <li>Leave approval (if configured)</li>
          <li>Dashboard analytics</li>
        </ul>

        <h4>Employee</h4>
        <ul>
          <li>Self-service portal access</li>
          <li>Personal payslip viewing</li>
          <li>Leave request submission</li>
          <li>Personal information updates</li>
          <li>Tax certificate downloads</li>
        </ul>

        <h3>Adding New Users</h3>
        <ol>
          <li>Navigate to <strong>Settings > User Management</strong></li>
          <li>Click <strong>Add New User</strong></li>
          <li>Enter user details:
            <ul>
              <li>Full name and email address</li>
              <li>Username (if different from email)</li>
              <li>Contact information</li>
            </ul>
          </li>
          <li>Assign user role and permissions</li>
          <li>Select accessible companies</li>
          <li>Set password requirements</li>
          <li>Send invitation email</li>
        </ol>

        <h3>Managing Existing Users</h3>
        <p>To modify user accounts:</p>
        <ul>
          <li><strong>Edit Profile</strong>: Update personal information</li>
          <li><strong>Change Role</strong>: Modify permissions and access level</li>
          <li><strong>Reset Password</strong>: Force password reset on next login</li>
          <li><strong>Suspend Account</strong>: Temporarily disable access</li>
          <li><strong>Delete User</strong>: Permanently remove account</li>
        </ul>

        <h3>Security Settings</h3>
        <p>Configure security policies for all users:</p>
        <ul>
          <li><strong>Password Requirements</strong>:
            <ul>
              <li>Minimum length (8-16 characters)</li>
              <li>Complexity requirements</li>
              <li>Password expiry (30-90 days)</li>
              <li>Password history (prevent reuse)</li>
            </ul>
          </li>
          <li><strong>Session Management</strong>:
            <ul>
              <li>Automatic logout after inactivity</li>
              <li>Maximum concurrent sessions</li>
              <li>IP address restrictions</li>
            </ul>
          </li>
          <li><strong>Two-Factor Authentication</strong>:
            <ul>
              <li>Mandatory for administrators</li>
              <li>Optional for other users</li>
              <li>SMS or authenticator app options</li>
            </ul>
          </li>
        </ul>

        <h3>User Activity Monitoring</h3>
        <p>Track user activity and system access:</p>
        <ul>
          <li>Login/logout timestamps</li>
          <li>Actions performed</li>
          <li>Data accessed or modified</li>
          <li>Failed login attempts</li>
          <li>Security violations</li>
        </ul>

        <h3>Bulk User Operations</h3>
        <p>Manage multiple users efficiently:</p>
        <ul>
          <li><strong>Bulk Import</strong>: Import users from CSV file</li>
          <li><strong>Bulk Update</strong>: Update multiple user properties</li>
          <li><strong>Bulk Deactivation</strong>: Disable multiple accounts</li>
          <li><strong>Permission Templates</strong>: Apply standard permission sets</li>
        </ul>

        <h3>Best Practices</h3>
        <ul>
          <li>Follow principle of least privilege</li>
          <li>Regularly review user access</li>
          <li>Remove access for departed employees immediately</li>
          <li>Use strong password policies</li>
          <li>Enable two-factor authentication</li>
          <li>Monitor user activity logs</li>
          <li>Conduct regular security audits</li>
        </ul>
      </div>
    `;

    // Payroll Setup Content
    this.content['company-setup'] = `
      <div class="help-article active">
        <h2>Company Setup</h2>
        <p>Configure your company details, tax settings, and payroll preferences to ensure accurate and compliant payroll processing.</p>

        <h3>Basic Company Information</h3>
        <ol>
          <li>Navigate to <strong>Settings > Company Setup</strong></li>
          <li>Enter your company details:
            <ul>
              <li><strong>Legal Name</strong>: As registered with CIPC</li>
              <li><strong>Trading Name</strong>: Business operating name</li>
              <li><strong>Registration Number</strong>: CIPC registration number</li>
              <li><strong>VAT Number</strong>: If VAT registered</li>
              <li><strong>Industry</strong>: Select your business sector</li>
            </ul>
          </li>
          <li>Add contact information and addresses</li>
          <li>Save your changes</li>
        </ol>

        <h3>Tax Configuration</h3>
        <p>Set up all required South African tax references:</p>
        <ul>
          <li><strong>PAYE Reference Number</strong>: From SARS registration</li>
          <li><strong>UIF Reference Number</strong>: Department of Employment reference</li>
          <li><strong>SDL Reference Number</strong>: For companies with payroll > R500,000</li>
          <li><strong>Workmen's Compensation</strong>: Industry-specific reference</li>
          <li><strong>Bargaining Council</strong>: If applicable to your industry</li>
        </ul>

        <h3>Payroll Calendar Setup</h3>
        <p>Configure your payroll processing schedule:</p>
        <ul>
          <li><strong>Default Pay Frequency</strong>: Monthly, weekly, or bi-weekly</li>
          <li><strong>Pay Day</strong>: Day of month/week for salary payments</li>
          <li><strong>Cut-off Date</strong>: Last day for payroll inputs</li>
          <li><strong>Processing Day</strong>: When payroll calculations are done</li>
          <li><strong>Public Holidays</strong>: South African holiday calendar</li>
        </ul>

        <h3>Banking and EFT Settings</h3>
        <p>Set up banking details for electronic payments:</p>
        <ul>
          <li><strong>Company Bank Account</strong>: For salary payments</li>
          <li><strong>EFT Format</strong>: Bank-specific file format</li>
          <li><strong>Payment Reference</strong>: Default payment descriptions</li>
          <li><strong>Approval Workflow</strong>: Who can approve payments</li>
        </ul>

        <h3>Leave Policies</h3>
        <p>Configure company-wide leave policies:</p>
        <ul>
          <li><strong>Annual Leave</strong>: Days per year and accrual method</li>
          <li><strong>Sick Leave</strong>: Entitlement and documentation requirements</li>
          <li><strong>Maternity/Paternity</strong>: Leave duration and pay rates</li>
          <li><strong>Study Leave</strong>: Educational leave policies</li>
          <li><strong>Custom Leave Types</strong>: Company-specific leave categories</li>
        </ul>

        <h3>Compliance Settings</h3>
        <p>Ensure compliance with South African regulations:</p>
        <ul>
          <li><strong>Employment Equity</strong>: Enable EE reporting if required</li>
          <li><strong>Skills Development</strong>: SDL levy configuration</li>
          <li><strong>Employment Tax Incentive</strong>: ETI settings for qualifying employees</li>
          <li><strong>Sectoral Determinations</strong>: Industry-specific wage requirements</li>
        </ul>
      </div>
    `;

    this.content['employee-setup'] = `
      <div class="help-article active">
        <h2>Employee Setup</h2>
        <p>Learn how to add new employees, configure their payroll details, and manage employee information effectively.</p>

        <h3>Adding a New Employee</h3>
        <ol>
          <li>Navigate to <strong>Employees > Add New Employee</strong></li>
          <li>Enter personal information:
            <ul>
              <li>Full name and preferred name</li>
              <li>ID number and ID type (RSA ID, Passport, etc.)</li>
              <li>Date of birth</li>
              <li>Contact details (phone, email, address)</li>
              <li>Emergency contact information</li>
            </ul>
          </li>
          <li>Configure employment details:
            <ul>
              <li>Employee number (auto-generated or custom)</li>
              <li>Start date and probation period</li>
              <li>Job title and department</li>
              <li>Employment type (permanent, contract, casual)</li>
              <li>Cost center allocation</li>
            </ul>
          </li>
          <li>Set up payroll information</li>
          <li>Configure banking details</li>
          <li>Save the employee profile</li>
        </ol>

        <h3>Payroll Configuration</h3>
        <p>Set up the employee's payroll details:</p>
        <ul>
          <li><strong>Pay Frequency</strong>: Monthly, weekly, or bi-weekly</li>
          <li><strong>Basic Salary</strong>: Annual or period amount</li>
          <li><strong>Salary Type</strong>: Fixed salary, hourly rate, or commission</li>
          <li><strong>Tax Directive</strong>: Custom PAYE rate if applicable</li>
          <li><strong>Medical Aid</strong>: Scheme details and contribution amounts</li>
          <li><strong>Pension Fund</strong>: Retirement fund contributions</li>
        </ul>

        <h3>Banking Details</h3>
        <p>Configure payment information:</p>
        <ul>
          <li><strong>Bank Name</strong>: Select from South African banks</li>
          <li><strong>Account Type</strong>: Current, savings, or transmission</li>
          <li><strong>Account Number</strong>: Employee's bank account</li>
          <li><strong>Branch Code</strong>: Bank branch identifier</li>
          <li><strong>Account Holder</strong>: Own account or third party</li>
        </ul>

        <h3>Leave Entitlements</h3>
        <p>Set up employee leave balances:</p>
        <ul>
          <li><strong>Annual Leave</strong>: Days per year based on employment date</li>
          <li><strong>Sick Leave</strong>: 30 days per 3-year cycle</li>
          <li><strong>Maternity Leave</strong>: 4 months for eligible employees</li>
          <li><strong>Study Leave</strong>: Company-specific entitlements</li>
          <li><strong>Carry Over</strong>: Previous year's unused leave</li>
        </ul>

        <h3>Tax and Compliance</h3>
        <p>Configure tax-related information:</p>
        <ul>
          <li><strong>Tax Number</strong>: Employee's SARS tax reference</li>
          <li><strong>Tax Directive</strong>: Custom PAYE rates from SARS</li>
          <li><strong>Medical Aid Credits</strong>: Tax credits for medical scheme</li>
          <li><strong>Employment Tax Incentive</strong>: ETI qualification status</li>
          <li><strong>Disability Status</strong>: For tax rebate purposes</li>
        </ul>

        <h3>Employment Equity Information</h3>
        <p>Capture EE data for reporting (if required):</p>
        <ul>
          <li><strong>Race</strong>: As per Employment Equity Act</li>
          <li><strong>Gender</strong>: Male, female, or other</li>
          <li><strong>Disability</strong>: Disability status</li>
          <li><strong>Nationality</strong>: South African or foreign national</li>
          <li><strong>Occupational Level</strong>: Management level classification</li>
        </ul>

        <h3>Bulk Employee Import</h3>
        <p>Import multiple employees from Excel:</p>
        <ol>
          <li>Download the employee import template</li>
          <li>Fill in employee details in the spreadsheet</li>
          <li>Upload the completed file</li>
          <li>Review and validate the imported data</li>
          <li>Confirm the import to create employee profiles</li>
        </ol>

        <h3>Employee Status Management</h3>
        <p>Manage employee lifecycle:</p>
        <ul>
          <li><strong>Active</strong>: Currently employed and on payroll</li>
          <li><strong>Inactive</strong>: Temporarily not on payroll</li>
          <li><strong>Terminated</strong>: Employment ended</li>
          <li><strong>Suspended</strong>: Temporarily suspended from work</li>
        </ul>
      </div>
    `;

    this.content['dashboard-features'] = `
      <div class="help-article active">
        <h2>Dashboard Features</h2>
        <p>Understand the main dashboard and how to navigate PandaPayroll's key features efficiently.</p>

        <h3>Dashboard Overview</h3>
        <p>The PandaPayroll dashboard provides a comprehensive view of your payroll system:</p>
        <ul>
          <li>Real-time payroll status and progress</li>
          <li>Employee count and statistics</li>
          <li>Upcoming deadlines and tasks</li>
          <li>Recent activity and notifications</li>
          <li>Quick access to common functions</li>
        </ul>

        <h3>Main Navigation</h3>
        <p>The main menu provides access to all system features:</p>
        <ul>
          <li><strong>Dashboard</strong>: Overview and quick actions</li>
          <li><strong>Employees</strong>: Employee management and profiles</li>
          <li><strong>Payroll</strong>: Payroll processing and pay runs</li>
          <li><strong>Leave</strong>: Leave management and approvals</li>
          <li><strong>Reports</strong>: Analytics and compliance reports</li>
          <li><strong>Filing</strong>: SARS submissions and compliance</li>
          <li><strong>Settings</strong>: System configuration and preferences</li>
        </ul>

        <h3>Payroll Status Cards</h3>
        <p>Monitor payroll progress with status cards:</p>
        <ul>
          <li><strong>Pending Payslips</strong>: Payslips awaiting processing</li>
          <li><strong>In Progress</strong>: Currently being processed</li>
          <li><strong>Ready for Review</strong>: Calculated and awaiting approval</li>
          <li><strong>Finalized</strong>: Completed and ready for payment</li>
          <li><strong>Paid</strong>: Payments processed and sent</li>
        </ul>

        <h3>Quick Actions</h3>
        <p>Access common tasks directly from the dashboard:</p>
        <ul>
          <li><strong>Add Employee</strong>: Create new employee profile</li>
          <li><strong>Process Payroll</strong>: Start payroll calculation</li>
          <li><strong>Generate Reports</strong>: Create payroll reports</li>
          <li><strong>Submit to SARS</strong>: File monthly returns</li>
          <li><strong>Download Payslips</strong>: Bulk payslip download</li>
        </ul>

        <h3>Notifications and Alerts</h3>
        <p>Stay informed with system notifications:</p>
        <ul>
          <li><strong>Deadline Reminders</strong>: SARS submission deadlines</li>
          <li><strong>Error Alerts</strong>: Payroll calculation issues</li>
          <li><strong>System Updates</strong>: New features and improvements</li>
          <li><strong>Compliance Warnings</strong>: Regulatory compliance issues</li>
        </ul>

        <h3>Company Selector</h3>
        <p>Switch between multiple companies:</p>
        <ul>
          <li>Click the company name in the top navigation</li>
          <li>Select from the dropdown list</li>
          <li>Dashboard updates with selected company data</li>
          <li>All subsequent actions apply to the selected company</li>
        </ul>

        <h3>User Profile and Settings</h3>
        <p>Access personal settings and preferences:</p>
        <ul>
          <li><strong>Profile Settings</strong>: Update personal information</li>
          <li><strong>Password Change</strong>: Update login credentials</li>
          <li><strong>Notification Preferences</strong>: Email and system alerts</li>
          <li><strong>Two-Factor Authentication</strong>: Enhanced security setup</li>
        </ul>

        <h3>Search and Filters</h3>
        <p>Find information quickly:</p>
        <ul>
          <li><strong>Global Search</strong>: Search across all data</li>
          <li><strong>Employee Search</strong>: Find specific employees</li>
          <li><strong>Date Filters</strong>: Filter by pay periods</li>
          <li><strong>Status Filters</strong>: Filter by payroll status</li>
        </ul>

        <h3>Mobile Responsiveness</h3>
        <p>Access PandaPayroll on mobile devices:</p>
        <ul>
          <li>Responsive design for tablets and phones</li>
          <li>Touch-friendly interface</li>
          <li>Essential features available on mobile</li>
          <li>Offline capability for basic functions</li>
        </ul>
      </div>
    `;

    // Payroll Processing Content
    this.content['payslip-creation'] = `
      <div class="help-article active">
        <h2>Payslip Creation</h2>
        <p>Learn how to create, process, and manage employee payslips in PandaPayroll.</p>

        <h3>Creating Individual Payslips</h3>
        <ol>
          <li>Navigate to <strong>Payroll > Payslips</strong></li>
          <li>Click <strong>Create New Payslip</strong></li>
          <li>Select the employee from the dropdown</li>
          <li>Choose the pay period (start and end dates)</li>
          <li>Enter payroll inputs:
            <ul>
              <li>Basic salary (auto-populated from employee profile)</li>
              <li>Overtime hours and rates</li>
              <li>Bonuses and allowances</li>
              <li>Deductions (loans, garnishee orders)</li>
              <li>Leave taken during the period</li>
            </ul>
          </li>
          <li>Review calculated amounts</li>
          <li>Save as draft or finalize</li>
        </ol>

        <h3>Bulk Payslip Creation</h3>
        <p>Create payslips for multiple employees simultaneously:</p>
        <ol>
          <li>Go to <strong>Payroll > Bulk Operations</strong></li>
          <li>Select <strong>Create Payslips</strong></li>
          <li>Choose employees (all or filtered selection)</li>
          <li>Set the pay period dates</li>
          <li>Configure default settings:
            <ul>
              <li>Standard working hours</li>
              <li>Default overtime rates</li>
              <li>Automatic leave calculations</li>
            </ul>
          </li>
          <li>Click <strong>Generate Payslips</strong></li>
          <li>Review and edit individual payslips as needed</li>
        </ol>

        <h3>Payslip Components</h3>
        <p>Understanding payslip sections:</p>
        <ul>
          <li><strong>Employee Information</strong>: Name, employee number, ID number</li>
          <li><strong>Pay Period</strong>: Start and end dates of the pay period</li>
          <li><strong>Earnings</strong>:
            <ul>
              <li>Basic salary</li>
              <li>Overtime pay</li>
              <li>Bonuses and incentives</li>
              <li>Allowances (travel, housing, etc.)</li>
              <li>Commission payments</li>
            </ul>
          </li>
          <li><strong>Deductions</strong>:
            <ul>
              <li>PAYE (Pay As You Earn tax)</li>
              <li>UIF (Unemployment Insurance Fund)</li>
              <li>Medical aid contributions</li>
              <li>Pension fund contributions</li>
              <li>Loan repayments</li>
              <li>Garnishee orders</li>
            </ul>
          </li>
          <li><strong>Employer Contributions</strong>:
            <ul>
              <li>UIF employer contribution</li>
              <li>SDL (Skills Development Levy)</li>
              <li>Workmen's compensation</li>
            </ul>
          </li>
          <li><strong>Net Pay</strong>: Final amount paid to employee</li>
        </ul>

        <h3>Payslip Status Workflow</h3>
        <p>Payslips progress through several statuses:</p>
        <ul>
          <li><strong>Draft</strong>: Initial creation, can be edited</li>
          <li><strong>Calculated</strong>: Taxes and deductions computed</li>
          <li><strong>Under Review</strong>: Awaiting approval</li>
          <li><strong>Approved</strong>: Ready for payment processing</li>
          <li><strong>Finalized</strong>: Locked and ready for payment</li>
          <li><strong>Paid</strong>: Payment processed and sent</li>
        </ul>

        <h3>Editing Payslips</h3>
        <p>Modify payslips before finalization:</p>
        <ul>
          <li>Click on the payslip to open the editor</li>
          <li>Modify earnings, deductions, or hours</li>
          <li>Add notes or comments for audit trail</li>
          <li>Recalculate taxes automatically</li>
          <li>Save changes and update status</li>
        </ul>

        <h3>Payslip Approval Process</h3>
        <p>Set up approval workflows:</p>
        <ol>
          <li>Configure approval rules in settings</li>
          <li>Assign approvers by department or amount</li>
          <li>Submit payslips for approval</li>
          <li>Approvers receive notifications</li>
          <li>Approved payslips move to finalized status</li>
        </ol>

        <h3>Common Issues and Solutions</h3>
        <ul>
          <li><strong>Incorrect Tax Calculations</strong>:
            <ul>
              <li>Verify employee tax directive</li>
              <li>Check medical aid tax credits</li>
              <li>Ensure correct annual salary</li>
            </ul>
          </li>
          <li><strong>Missing Deductions</strong>:
            <ul>
              <li>Check employee profile for recurring deductions</li>
              <li>Verify deduction start and end dates</li>
              <li>Ensure deduction amounts are current</li>
            </ul>
          </li>
          <li><strong>Overtime Calculation Errors</strong>:
            <ul>
              <li>Verify overtime rates in employee profile</li>
              <li>Check overtime calculation method</li>
              <li>Ensure correct hours are entered</li>
            </ul>
          </li>
        </ul>
      </div>
    `;

    this.content['statutory-deductions'] = `
      <div class="help-article active">
        <h2>Statutory Deductions (South Africa)</h2>
        <p>Understand how PAYE, UIF, SDL, and other statutory deductions are calculated and applied in South African payroll.</p>

        <h3>PAYE (Pay As You Earn)</h3>
        <p>PAYE is the income tax deducted from employee salaries:</p>
        <ul>
          <li><strong>Calculation Method</strong>: Progressive tax rates based on annual income</li>
          <li><strong>Tax Tables</strong>: Updated annually by SARS</li>
          <li><strong>Tax Rebates</strong>:
            <ul>
              <li>Primary rebate: R17,235 (2024/2025 tax year)</li>
              <li>Secondary rebate: R9,444 (age 65+)</li>
              <li>Tertiary rebate: R3,145 (age 75+)</li>
            </ul>
          </li>
          <li><strong>Medical Aid Credits</strong>:
            <ul>
              <li>R347 per month per member</li>
              <li>R234 per month per dependant</li>
            </ul>
          </li>
        </ul>

        <h4>PAYE Calculation Example</h4>
        <p>For an employee earning R25,000 per month:</p>
        <ol>
          <li>Annual salary: R25,000 × 12 = R300,000</li>
          <li>Annual tax (before rebates): R42,678</li>
          <li>Less primary rebate: R42,678 - R17,235 = R25,443</li>
          <li>Monthly PAYE: R25,443 ÷ 12 = R2,120</li>
        </ol>

        <h3>UIF (Unemployment Insurance Fund)</h3>
        <p>UIF provides benefits to workers who become unemployed:</p>
        <ul>
          <li><strong>Employee Contribution</strong>: 1% of gross salary</li>
          <li><strong>Employer Contribution</strong>: 1% of gross salary</li>
          <li><strong>Maximum Monthly Contribution</strong>: R177.12 (based on R17,712 income ceiling)</li>
          <li><strong>Exemptions</strong>:
            <ul>
              <li>Foreign nationals on work permits</li>
              <li>Employees working less than 24 hours per month</li>
              <li>Learners receiving stipends</li>
            </ul>
          </li>
        </ul>

        <h4>UIF Calculation Example</h4>
        <p>For an employee earning R25,000 per month:</p>
        <ul>
          <li>Employee UIF: R25,000 × 1% = R250</li>
          <li>Employer UIF: R25,000 × 1% = R250</li>
          <li>Total UIF: R500 (R250 from employee + R250 from employer)</li>
        </ul>

        <h3>SDL (Skills Development Levy)</h3>
        <p>SDL funds skills development and training:</p>
        <ul>
          <li><strong>Rate</strong>: 1% of total payroll</li>
          <li><strong>Threshold</strong>: Only applies to companies with annual payroll > R500,000</li>
          <li><strong>Paid by</strong>: Employer only (not deducted from employee)</li>
          <li><strong>Calculation</strong>: Based on total company payroll, not individual salaries</li>
        </ul>

        <h4>SDL Calculation Example</h4>
        <p>Company with monthly payroll of R500,000:</p>
        <ul>
          <li>Monthly SDL: R500,000 × 1% = R5,000</li>
          <li>Annual SDL: R5,000 × 12 = R60,000</li>
        </ul>

        <h3>Other Statutory Deductions</h3>

        <h4>Workmen's Compensation</h4>
        <ul>
          <li>Industry-specific rates</li>
          <li>Paid by employer</li>
          <li>Based on risk assessment of industry</li>
          <li>Rates vary from 0.22% to 1.52% of payroll</li>
        </ul>

        <h4>Bargaining Council Levies</h4>
        <ul>
          <li>Industry-specific contributions</li>
          <li>Varies by sector (e.g., Motor Industry, Building Industry)</li>
          <li>Usually split between employer and employee</li>
          <li>Rates determined by each bargaining council</li>
        </ul>

        <h3>Tax Directives</h3>
        <p>SARS-issued instructions for custom tax rates:</p>
        <ul>
          <li><strong>When Issued</strong>:
            <ul>
              <li>Multiple employers</li>
              <li>Significant other income</li>
              <li>Large medical aid contributions</li>
              <li>Retirement annuity contributions</li>
            </ul>
          </li>
          <li><strong>Implementation</strong>: Override standard PAYE calculation</li>
          <li><strong>Validity</strong>: Usually valid for one tax year</li>
          <li><strong>Compliance</strong>: Must be applied as directed by SARS</li>
        </ul>

        <h3>Medical Aid Tax Credits</h3>
        <p>Tax credits for medical scheme contributions:</p>
        <ul>
          <li><strong>Main Member</strong>: R347 per month</li>
          <li><strong>First Dependant</strong>: R234 per month</li>
          <li><strong>Additional Dependants</strong>: R234 per month each</li>
          <li><strong>Application</strong>: Reduces PAYE liability</li>
          <li><strong>Excess Contributions</strong>: May be claimed as deduction</li>
        </ul>

        <h3>Compliance and Reporting</h3>
        <p>Ensure accurate statutory deduction reporting:</p>
        <ul>
          <li><strong>Monthly EMP201</strong>: Report PAYE, UIF, SDL to SARS</li>
          <li><strong>Bi-annual EMP501</strong>: Reconcile annual amounts</li>
          <li><strong>IRP5 Certificates</strong>: Annual employee tax certificates</li>
          <li><strong>UIF Declarations</strong>: Monthly submissions to Department of Employment</li>
        </ul>

        <h3>Common Calculation Errors</h3>
        <ul>
          <li><strong>Incorrect Annual Salary</strong>: Ensure pro-rata calculations for new employees</li>
          <li><strong>Missing Tax Directives</strong>: Apply SARS directives when issued</li>
          <li><strong>Wrong UIF Ceiling</strong>: Cap contributions at R177.12 per month</li>
          <li><strong>SDL Threshold</strong>: Only apply to companies above R500,000 annual payroll</li>
        </ul>
      </div>
    `;

    // Troubleshooting Content
    this.content['login-issues'] = `
      <div class="help-article active">
        <h2>Login Issues</h2>
        <p>Resolve common login and access problems with PandaPayroll.</p>

        <h3>Forgot Password</h3>
        <ol>
          <li>Go to the login page</li>
          <li>Click <strong>"Forgot Password?"</strong></li>
          <li>Enter your email address</li>
          <li>Check your email for reset instructions</li>
          <li>Click the reset link in the email</li>
          <li>Create a new password</li>
          <li>Log in with your new password</li>
        </ol>

        <h3>Account Locked</h3>
        <p>If your account is locked due to multiple failed login attempts:</p>
        <ul>
          <li>Wait 15 minutes for automatic unlock</li>
          <li>Or contact support for immediate unlock</li>
          <li>Ensure you're using the correct password</li>
          <li>Check if Caps Lock is enabled</li>
        </ul>

        <h3>Two-Factor Authentication Issues</h3>
        <p>Problems with 2FA codes:</p>
        <ul>
          <li><strong>Code Not Working</strong>:
            <ul>
              <li>Ensure your device time is synchronized</li>
              <li>Try the next code generated</li>
              <li>Check if you're using the correct authenticator app</li>
            </ul>
          </li>
          <li><strong>Lost Authenticator Device</strong>:
            <ul>
              <li>Use backup codes provided during setup</li>
              <li>Contact support to reset 2FA</li>
              <li>Provide identity verification</li>
            </ul>
          </li>
        </ul>

        <h3>Browser Issues</h3>
        <p>Common browser-related problems:</p>
        <ul>
          <li><strong>Clear Browser Cache</strong>:
            <ul>
              <li>Press Ctrl+Shift+Delete (Windows) or Cmd+Shift+Delete (Mac)</li>
              <li>Select "All time" for time range</li>
              <li>Clear cookies and cached files</li>
            </ul>
          </li>
          <li><strong>Disable Browser Extensions</strong>:
            <ul>
              <li>Try logging in using incognito/private mode</li>
              <li>Disable ad blockers and security extensions</li>
              <li>Test with a different browser</li>
            </ul>
          </li>
          <li><strong>Enable JavaScript</strong>:
            <ul>
              <li>Ensure JavaScript is enabled in browser settings</li>
              <li>Allow pop-ups for the PandaPayroll domain</li>
            </ul>
          </li>
        </ul>

        <h3>Network and Connectivity</h3>
        <p>Check network-related issues:</p>
        <ul>
          <li><strong>Internet Connection</strong>: Verify stable internet connectivity</li>
          <li><strong>Firewall Settings</strong>: Ensure PandaPayroll isn't blocked</li>
          <li><strong>VPN Issues</strong>: Try connecting without VPN</li>
          <li><strong>Corporate Network</strong>: Check with IT department for restrictions</li>
        </ul>

        <h3>Email Not Received</h3>
        <p>If you're not receiving password reset emails:</p>
        <ul>
          <li>Check spam/junk folder</li>
          <li>Add <EMAIL> to safe senders</li>
          <li>Try a different email address</li>
          <li>Contact support if emails still don't arrive</li>
        </ul>

        <h3>Still Can't Log In?</h3>
        <p>Contact our support team:</p>
        <ul>
          <li><strong>Email</strong>: <EMAIL></li>
          <li><strong>Phone</strong>: +27 11 234 5678</li>
          <li><strong>Live Chat</strong>: Available during business hours</li>
          <li><strong>Include</strong>: Your email address, error messages, browser type</li>
        </ul>
      </div>
    `;

    this.content['monthly-submissions'] = `
      <div class="help-article active">
        <h2>Monthly Submissions (EMP201)</h2>
        <p>Learn how to prepare and submit monthly EMP201 returns to SARS for PAYE, UIF, and SDL compliance.</p>

        <h3>EMP201 Overview</h3>
        <p>The EMP201 is a monthly return that reports:</p>
        <ul>
          <li>PAYE (Pay As You Earn) deducted from employees</li>
          <li>UIF (Unemployment Insurance Fund) contributions</li>
          <li>SDL (Skills Development Levy) if applicable</li>
          <li>Total amounts due to SARS</li>
        </ul>

        <h3>Submission Deadlines</h3>
        <ul>
          <li><strong>Monthly Deadline</strong>: 7th of the following month</li>
          <li><strong>Payment Deadline</strong>: 7th of the following month</li>
          <li><strong>Late Submission Penalty</strong>: R250 per month or 10% of tax due</li>
          <li><strong>Interest on Late Payment</strong>: Prescribed rate plus 4%</li>
        </ul>

        <h3>Preparing EMP201 in PandaPayroll</h3>
        <ol>
          <li>Navigate to <strong>Filing > Monthly Submissions</strong></li>
          <li>Select the month and year for submission</li>
          <li>Click <strong>Generate EMP201</strong></li>
          <li>Review the calculated amounts:
            <ul>
              <li>Total PAYE deducted</li>
              <li>Total UIF (employee + employer)</li>
              <li>SDL amount (if applicable)</li>
              <li>Interest and penalties (if any)</li>
            </ul>
          </li>
          <li>Verify employee count and total remuneration</li>
          <li>Check for any errors or warnings</li>
          <li>Generate the submission file</li>
        </ol>

        <h3>Submission Methods</h3>

        <h4>eFiling (Recommended)</h4>
        <ol>
          <li>Log in to SARS eFiling</li>
          <li>Select <strong>Returns > EMP201</strong></li>
          <li>Choose <strong>File Return</strong></li>
          <li>Upload the CSV file from PandaPayroll</li>
          <li>Review and submit</li>
          <li>Print acknowledgment of receipt</li>
        </ol>

        <h4>Manual Entry</h4>
        <ol>
          <li>Log in to SARS eFiling</li>
          <li>Select <strong>Returns > EMP201</strong></li>
          <li>Choose <strong>Prepare Online</strong></li>
          <li>Enter amounts from PandaPayroll report</li>
          <li>Submit and print acknowledgment</li>
        </ol>

        <h3>EMP201 Components</h3>
        <p>Understanding the EMP201 sections:</p>
        <ul>
          <li><strong>3601 - PAYE</strong>: Total PAYE deducted from employees</li>
          <li><strong>3603 - SDL</strong>: Skills Development Levy (1% of payroll)</li>
          <li><strong>3604 - UIF</strong>: Total UIF contributions (employee + employer)</li>
          <li><strong>3605 - ETI</strong>: Employment Tax Incentive claimed</li>
          <li><strong>4001 - Total Due</strong>: Net amount payable to SARS</li>
        </ul>

        <h3>Common Errors and Solutions</h3>
        <ul>
          <li><strong>Mismatched Amounts</strong>:
            <ul>
              <li>Ensure all payslips are finalized</li>
              <li>Check for manual adjustments</li>
              <li>Verify tax directive applications</li>
            </ul>
          </li>
          <li><strong>Missing Employees</strong>:
            <ul>
              <li>Include all employees paid during the month</li>
              <li>Check for employees with zero pay</li>
              <li>Verify employee status settings</li>
            </ul>
          </li>
          <li><strong>SDL Calculation Errors</strong>:
            <ul>
              <li>Ensure company qualifies for SDL (payroll > R500,000)</li>
              <li>Check SDL rate configuration</li>
              <li>Verify total payroll calculation</li>
            </ul>
          </li>
        </ul>

        <h3>Payment Process</h3>
        <ol>
          <li>Submit EMP201 return</li>
          <li>Note the payment reference number</li>
          <li>Make payment via:
            <ul>
              <li>Internet banking</li>
              <li>Bank branch</li>
              <li>SARS eFiling payment portal</li>
            </ul>
          </li>
          <li>Use correct SARS banking details</li>
          <li>Include payment reference number</li>
          <li>Keep proof of payment</li>
        </ol>

        <h3>Record Keeping</h3>
        <p>Maintain proper records:</p>
        <ul>
          <li>EMP201 submission confirmations</li>
          <li>Payment receipts and bank statements</li>
          <li>Supporting payroll reports</li>
          <li>Employee tax certificates (IRP5)</li>
          <li>Correspondence with SARS</li>
        </ul>

        <h3>Penalties and Interest</h3>
        <p>Avoid penalties by:</p>
        <ul>
          <li>Submitting returns by the 7th of each month</li>
          <li>Making payments by the due date</li>
          <li>Ensuring accurate calculations</li>
          <li>Keeping proper records</li>
          <li>Responding promptly to SARS queries</li>
        </ul>
      </div>
    `;

    this.content['quickbooks-integration'] = `
      <div class="help-article active">
        <h2>QuickBooks Online Integration</h2>
        <p>Connect PandaPayroll with QuickBooks Online to automatically sync payroll data and streamline your accounting processes.</p>

        <h3>Integration Benefits</h3>
        <ul>
          <li>Automatic posting of payroll journals</li>
          <li>Synchronized employee data</li>
          <li>Reduced manual data entry</li>
          <li>Improved accuracy and consistency</li>
          <li>Real-time financial reporting</li>
        </ul>

        <h3>Prerequisites</h3>
        <p>Before setting up the integration:</p>
        <ul>
          <li>Active QuickBooks Online subscription</li>
          <li>Administrator access to both systems</li>
          <li>Chart of accounts set up in QuickBooks</li>
          <li>Employee records in both systems</li>
        </ul>

        <h3>Setting Up the Integration</h3>
        <ol>
          <li>Navigate to <strong>Settings > Integrations</strong></li>
          <li>Click <strong>QuickBooks Online</strong></li>
          <li>Click <strong>Connect to QuickBooks</strong></li>
          <li>Log in to your QuickBooks Online account</li>
          <li>Authorize PandaPayroll to access your data</li>
          <li>Select your QuickBooks company</li>
          <li>Configure mapping settings</li>
          <li>Test the connection</li>
        </ol>

        <h3>Account Mapping</h3>
        <p>Map PandaPayroll accounts to QuickBooks accounts:</p>
        <ul>
          <li><strong>Salary Expense</strong>: Map to salary/wage expense accounts</li>
          <li><strong>PAYE Liability</strong>: Map to PAYE payable account</li>
          <li><strong>UIF Liability</strong>: Map to UIF payable account</li>
          <li><strong>SDL Expense</strong>: Map to SDL expense account</li>
          <li><strong>Bank Account</strong>: Map to payroll bank account</li>
        </ul>

        <h3>Employee Synchronization</h3>
        <p>Sync employee data between systems:</p>
        <ul>
          <li><strong>Initial Sync</strong>: Match existing employees by name or ID</li>
          <li><strong>New Employees</strong>: Automatically create in QuickBooks</li>
          <li><strong>Updates</strong>: Sync changes to employee information</li>
          <li><strong>Deactivation</strong>: Mark terminated employees as inactive</li>
        </ul>

        <h3>Payroll Journal Entries</h3>
        <p>Automatic posting of payroll transactions:</p>
        <ul>
          <li><strong>Gross Salaries</strong>: Posted to salary expense accounts</li>
          <li><strong>Tax Deductions</strong>: Posted to liability accounts</li>
          <li><strong>Net Pay</strong>: Posted as credit to bank account</li>
          <li><strong>Employer Contributions</strong>: Posted to expense accounts</li>
        </ul>

        <h4>Sample Journal Entry</h4>
        <pre><code>
Debit: Salary Expense          R100,000
Debit: UIF Expense (Employer)    R1,000
Debit: SDL Expense               R1,000
Credit: PAYE Payable                      R18,000
Credit: UIF Payable                        R2,000
Credit: Bank Account                      R82,000
        </code></pre>

        <h3>Sync Frequency</h3>
        <p>Configure how often data syncs:</p>
        <ul>
          <li><strong>Real-time</strong>: Immediate sync after payroll finalization</li>
          <li><strong>Daily</strong>: Once per day at specified time</li>
          <li><strong>Weekly</strong>: Weekly sync on specified day</li>
          <li><strong>Manual</strong>: Sync only when manually triggered</li>
        </ul>

        <h3>Troubleshooting Common Issues</h3>
        <ul>
          <li><strong>Connection Errors</strong>:
            <ul>
              <li>Check internet connectivity</li>
              <li>Verify QuickBooks login credentials</li>
              <li>Re-authorize the connection</li>
            </ul>
          </li>
          <li><strong>Mapping Errors</strong>:
            <ul>
              <li>Ensure all required accounts exist in QuickBooks</li>
              <li>Check account types match expected categories</li>
              <li>Verify account names and numbers</li>
            </ul>
          </li>
          <li><strong>Duplicate Entries</strong>:
            <ul>
              <li>Check sync settings for duplicates</li>
              <li>Review date ranges for sync</li>
              <li>Manually delete duplicate transactions</li>
            </ul>
          </li>
        </ul>

        <h3>Best Practices</h3>
        <ul>
          <li>Set up chart of accounts before integration</li>
          <li>Test with a small payroll first</li>
          <li>Review synced data regularly</li>
          <li>Keep both systems updated</li>
          <li>Maintain backup of data</li>
          <li>Train users on both systems</li>
        </ul>

        <h3>Disconnecting the Integration</h3>
        <p>To disconnect QuickBooks integration:</p>
        <ol>
          <li>Go to <strong>Settings > Integrations</strong></li>
          <li>Find QuickBooks Online integration</li>
          <li>Click <strong>Disconnect</strong></li>
          <li>Confirm disconnection</li>
          <li>Revoke access in QuickBooks if needed</li>
        </ol>

        <h3>Support and Resources</h3>
        <ul>
          <li>Integration setup assistance</li>
          <li>Account mapping guidance</li>
          <li>Troubleshooting support</li>
          <li>Training on integrated workflows</li>
          <li>Regular sync monitoring</li>
        </ul>
      </div>
    `;

    // Bulk Operations Content
    this.content['employee-actions'] = `
      <div class="help-article active">
        <h2>Employee Actions (Bulk Operations)</h2>
        <p>Perform bulk actions on multiple employees simultaneously to streamline HR processes and save time.</p>

        <h3>Accessing Bulk Employee Actions</h3>
        <ol>
          <li>Navigate to <strong>Employees > Employee Management</strong></li>
          <li>Click the <strong>Bulk Actions</strong> tab</li>
          <li>Select the <strong>Employee Actions</strong> section</li>
        </ol>

        <h3>Available Bulk Actions</h3>

        <h4>End Service (Termination)</h4>
        <p>Terminate multiple employees simultaneously:</p>
        <ol>
          <li>Select employees from the list</li>
          <li>Click <strong>End Service</strong></li>
          <li>Enter termination details:
            <ul>
              <li>Last working date</li>
              <li>Reason for termination</li>
              <li>Notice period served</li>
              <li>Final pay calculation method</li>
            </ul>
          </li>
          <li>Review final pay calculations</li>
          <li>Confirm termination</li>
        </ol>

        <h4>Reinstate Employees</h4>
        <p>Reinstate previously terminated employees:</p>
        <ol>
          <li>Select terminated employees</li>
          <li>Click <strong>Reinstate</strong></li>
          <li>Enter reinstatement details:
            <ul>
              <li>Return to work date</li>
              <li>Salary adjustments (if any)</li>
              <li>Position changes</li>
              <li>Leave balance restoration</li>
            </ul>
          </li>
          <li>Confirm reinstatement</li>
        </ol>

        <h4>Undo End of Service</h4>
        <p>Reverse recent terminations:</p>
        <ul>
          <li>Available within 30 days of termination</li>
          <li>Restores employee status to active</li>
          <li>Maintains payroll history</li>
          <li>Requires manager approval</li>
        </ul>

        <h4>Salary Adjustments</h4>
        <p>Apply salary changes to multiple employees:</p>
        <ol>
          <li>Select employees for adjustment</li>
          <li>Choose adjustment type:
            <ul>
              <li>Percentage increase/decrease</li>
              <li>Fixed amount adjustment</li>
              <li>New salary amount</li>
            </ul>
          </li>
          <li>Set effective date</li>
          <li>Add adjustment reason</li>
          <li>Preview changes before applying</li>
        </ol>

        <h4>Department/Cost Center Changes</h4>
        <p>Move employees between departments:</p>
        <ol>
          <li>Select employees to move</li>
          <li>Choose new department/cost center</li>
          <li>Set effective date</li>
          <li>Update reporting manager (if needed)</li>
          <li>Apply changes</li>
        </ol>

        <h3>Bulk Status Changes</h3>
        <p>Change employee status for multiple employees:</p>
        <ul>
          <li><strong>Active to Inactive</strong>: Temporary suspension</li>
          <li><strong>Inactive to Active</strong>: Return from suspension</li>
          <li><strong>Probation to Permanent</strong>: Confirm employment</li>
          <li><strong>Contract to Permanent</strong>: Convert employment type</li>
        </ul>

        <h3>Approval Workflow</h3>
        <p>Bulk actions require approval:</p>
        <ol>
          <li>Submit bulk action request</li>
          <li>Manager receives notification</li>
          <li>Manager reviews and approves/rejects</li>
          <li>Approved actions are processed</li>
          <li>All parties receive confirmation</li>
        </ol>

        <h3>Audit Trail</h3>
        <p>All bulk actions are logged:</p>
        <ul>
          <li>User who initiated the action</li>
          <li>Date and time of action</li>
          <li>Employees affected</li>
          <li>Changes made</li>
          <li>Approval status and approver</li>
        </ul>

        <h3>Best Practices</h3>
        <ul>
          <li>Always preview changes before applying</li>
          <li>Use bulk actions during off-peak hours</li>
          <li>Communicate changes to affected employees</li>
          <li>Keep detailed records of bulk changes</li>
          <li>Test with small groups first</li>
          <li>Ensure proper approvals are in place</li>
        </ul>

        <h3>Common Use Cases</h3>
        <ul>
          <li><strong>Annual Salary Reviews</strong>: Apply percentage increases</li>
          <li><strong>Restructuring</strong>: Move employees between departments</li>
          <li><strong>Contract Conversions</strong>: Convert temporary to permanent</li>
          <li><strong>Probation Confirmations</strong>: Confirm multiple probationers</li>
          <li><strong>Mass Terminations</strong>: Handle retrenchments or closures</li>
        </ul>
      </div>
    `;

    this.content['payslip-management'] = `
      <div class="help-article active">
        <h2>Payslip Management (Bulk Operations)</h2>
        <p>Efficiently manage multiple payslips simultaneously with bulk operations for processing, approval, and distribution.</p>

        <h3>Accessing Bulk Payslip Management</h3>
        <ol>
          <li>Navigate to <strong>Payroll > Payroll Hub</strong></li>
          <li>Select the pay period</li>
          <li>Use the <strong>Select All</strong> checkbox for bulk operations</li>
          <li>Or select individual payslips using checkboxes</li>
        </ol>

        <h3>Bulk Payslip Creation</h3>
        <p>Create payslips for multiple employees at once:</p>
        <ol>
          <li>Go to <strong>Payroll > Bulk Operations</strong></li>
          <li>Click <strong>Create Payslips</strong></li>
          <li>Select employees:
            <ul>
              <li>All active employees</li>
              <li>By department</li>
              <li>By pay frequency</li>
              <li>Custom selection</li>
            </ul>
          </li>
          <li>Set pay period dates</li>
          <li>Configure default settings:
            <ul>
              <li>Standard working hours</li>
              <li>Default overtime rates</li>
              <li>Automatic leave deductions</li>
            </ul>
          </li>
          <li>Generate payslips</li>
          <li>Review and edit individual payslips as needed</li>
        </ol>

        <h3>Bulk Payslip Processing</h3>
        <p>Process multiple payslips through the workflow:</p>

        <h4>Bulk Calculate</h4>
        <ol>
          <li>Select payslips in draft status</li>
          <li>Click <strong>Bulk Calculate</strong></li>
          <li>System calculates:
            <ul>
              <li>PAYE tax</li>
              <li>UIF contributions</li>
              <li>Other deductions</li>
              <li>Net pay amounts</li>
            </ul>
          </li>
          <li>Review calculation results</li>
          <li>Fix any errors before proceeding</li>
        </ol>

        <h4>Bulk Approval</h4>
        <ol>
          <li>Select calculated payslips</li>
          <li>Click <strong>Bulk Approve</strong></li>
          <li>Review summary information</li>
          <li>Add approval comments (optional)</li>
          <li>Confirm approval</li>
        </ol>

        <h4>Bulk Finalization</h4>
        <ol>
          <li>Select approved payslips</li>
          <li>Click <strong>Bulk Finalize</strong></li>
          <li>System locks payslips for editing</li>
          <li>Generates pay run record</li>
          <li>Prepares for payment processing</li>
        </ol>

        <h3>Bulk Payslip Distribution</h3>
        <p>Distribute payslips to employees:</p>

        <h4>Email Distribution</h4>
        <ol>
          <li>Select finalized payslips</li>
          <li>Click <strong>Email Payslips</strong></li>
          <li>Choose email template</li>
          <li>Preview email content</li>
          <li>Send to all selected employees</li>
          <li>Monitor delivery status</li>
        </ol>

        <h4>Print Management</h4>
        <ol>
          <li>Select payslips for printing</li>
          <li>Click <strong>Print Payslips</strong></li>
          <li>Choose print format:
            <ul>
              <li>Individual payslips</li>
              <li>Multiple per page</li>
              <li>Summary format</li>
            </ul>
          </li>
          <li>Generate PDF for printing</li>
        </ol>

        <h3>Bulk Corrections and Adjustments</h3>
        <p>Make corrections to multiple payslips:</p>

        <h4>Bulk Salary Adjustments</h4>
        <ol>
          <li>Select payslips requiring adjustment</li>
          <li>Click <strong>Bulk Adjust</strong></li>
          <li>Choose adjustment type:
            <ul>
              <li>Overtime corrections</li>
              <li>Bonus additions</li>
              <li>Deduction adjustments</li>
              <li>Leave corrections</li>
            </ul>
          </li>
          <li>Enter adjustment amounts</li>
          <li>Recalculate affected payslips</li>
        </ol>

        <h4>Bulk Status Changes</h4>
        <p>Change payslip status for multiple records:</p>
        <ul>
          <li><strong>Revert to Draft</strong>: Unlock for editing</li>
          <li><strong>Mark as Paid</strong>: Update payment status</li>
          <li><strong>Cancel Payslips</strong>: Cancel incorrect payslips</li>
          <li><strong>Regenerate</strong>: Recreate payslips with new data</li>
        </ul>

        <h3>Bulk Payment Processing</h3>
        <p>Process payments for multiple employees:</p>

        <h4>EFT File Generation</h4>
        <ol>
          <li>Select finalized payslips</li>
          <li>Click <strong>Generate EFT File</strong></li>
          <li>Choose bank format</li>
          <li>Set payment date</li>
          <li>Generate banking file</li>
          <li>Download for bank submission</li>
        </ol>

        <h4>Payment Confirmation</h4>
        <ol>
          <li>Upload bank confirmation file</li>
          <li>System matches payments</li>
          <li>Updates payslip status to "Paid"</li>
          <li>Sends payment confirmations</li>
        </ol>

        <h3>Bulk Reporting</h3>
        <p>Generate reports for multiple payslips:</p>
        <ul>
          <li><strong>Payroll Summary</strong>: Total costs and deductions</li>
          <li><strong>Tax Summary</strong>: PAYE and UIF totals</li>
          <li><strong>Department Summary</strong>: Costs by department</li>
          <li><strong>Payment Summary</strong>: Net pay totals</li>
          <li><strong>Exception Report</strong>: Payslips with errors</li>
        </ul>

        <h3>Error Handling</h3>
        <p>Manage errors in bulk operations:</p>
        <ul>
          <li><strong>Validation Errors</strong>: Fix data issues before processing</li>
          <li><strong>Calculation Errors</strong>: Review and correct tax calculations</li>
          <li><strong>Email Failures</strong>: Retry failed email deliveries</li>
          <li><strong>Payment Errors</strong>: Handle failed payment processing</li>
        </ul>

        <h3>Best Practices</h3>
        <ul>
          <li>Process payslips in small batches first</li>
          <li>Always review calculations before finalizing</li>
          <li>Keep backup copies of payroll data</li>
          <li>Test email delivery with small groups</li>
          <li>Monitor system performance during bulk operations</li>
          <li>Schedule bulk operations during off-peak hours</li>
          <li>Maintain audit trails for all bulk changes</li>
        </ul>
      </div>
    `;

    this.content['excel-imports'] = `
      <div class="help-article active">
        <h2>Excel Imports</h2>
        <p>Import employee data, payroll information, and other bulk data using Excel spreadsheets for efficient data management.</p>

        <h3>Supported Import Types</h3>
        <p>PandaPayroll supports importing various types of data:</p>
        <ul>
          <li><strong>Employee Master Data</strong>: Personal and employment information</li>
          <li><strong>Payroll Inputs</strong>: Hours, bonuses, deductions, allowances</li>
          <li><strong>Leave Data</strong>: Leave balances and adjustments</li>
          <li><strong>Banking Details</strong>: Employee banking information</li>
          <li><strong>Salary Adjustments</strong>: Bulk salary changes</li>
          <li><strong>Tax Directives</strong>: SARS tax directive information</li>
        </ul>

        <h3>Employee Data Import</h3>

        <h4>Downloading the Template</h4>
        <ol>
          <li>Navigate to <strong>Employees > Bulk Operations</strong></li>
          <li>Click <strong>Excel Imports</strong></li>
          <li>Select <strong>Employee Data Import</strong></li>
          <li>Click <strong>Download Template</strong></li>
          <li>Save the Excel template to your computer</li>
        </ol>

        <h4>Preparing Employee Data</h4>
        <p>Fill in the Excel template with employee information:</p>
        <ul>
          <li><strong>Required Fields</strong>:
            <ul>
              <li>First Name</li>
              <li>Last Name</li>
              <li>ID Number</li>
              <li>Email Address</li>
              <li>Start Date</li>
              <li>Basic Salary</li>
            </ul>
          </li>
          <li><strong>Optional Fields</strong>:
            <ul>
              <li>Employee Number</li>
              <li>Job Title</li>
              <li>Department</li>
              <li>Phone Number</li>
              <li>Address Information</li>
              <li>Banking Details</li>
            </ul>
          </li>
        </ul>

        <h4>Data Validation Rules</h4>
        <p>Ensure data meets validation requirements:</p>
        <ul>
          <li><strong>ID Numbers</strong>: Valid South African ID format (13 digits)</li>
          <li><strong>Email Addresses</strong>: Valid email format and unique</li>
          <li><strong>Dates</strong>: DD/MM/YYYY format</li>
          <li><strong>Salaries</strong>: Numeric values only</li>
          <li><strong>Phone Numbers</strong>: Valid South African format</li>
          <li><strong>Bank Account Numbers</strong>: Numeric, appropriate length</li>
        </ul>

        <h3>Payroll Input Import</h3>

        <h4>Supported Payroll Inputs</h4>
        <ul>
          <li><strong>Working Hours</strong>: Regular and overtime hours</li>
          <li><strong>Bonuses</strong>: Performance, commission, and other bonuses</li>
          <li><strong>Allowances</strong>: Travel, housing, and other allowances</li>
          <li><strong>Deductions</strong>: Loans, garnishee orders, other deductions</li>
          <li><strong>Leave Days</strong>: Annual, sick, and other leave taken</li>
        </ul>

        <h4>Import Process</h4>
        <ol>
          <li>Download the payroll input template</li>
          <li>Fill in employee data and payroll inputs</li>
          <li>Ensure all employee numbers match existing records</li>
          <li>Validate amounts and dates</li>
          <li>Upload the completed file</li>
          <li>Review import preview</li>
          <li>Confirm import</li>
        </ol>

        <h3>Import Process Steps</h3>

        <h4>File Upload</h4>
        <ol>
          <li>Click <strong>Choose File</strong> or drag and drop</li>
          <li>Select your completed Excel file</li>
          <li>Supported formats: .xlsx, .xls</li>
          <li>Maximum file size: 10MB</li>
          <li>Click <strong>Upload</strong></li>
        </ol>

        <h4>Data Validation</h4>
        <p>System validates uploaded data:</p>
        <ul>
          <li><strong>Format Validation</strong>: Checks data types and formats</li>
          <li><strong>Business Rules</strong>: Validates against business logic</li>
          <li><strong>Duplicate Detection</strong>: Identifies duplicate records</li>
          <li><strong>Reference Validation</strong>: Checks employee numbers exist</li>
        </ul>

        <h4>Error Resolution</h4>
        <p>If validation errors occur:</p>
        <ol>
          <li>Review error report</li>
          <li>Download error file with highlighted issues</li>
          <li>Correct errors in Excel</li>
          <li>Re-upload corrected file</li>
          <li>Repeat until all errors are resolved</li>
        </ol>

        <h4>Import Preview</h4>
        <p>Before final import:</p>
        <ul>
          <li>Review summary of records to be imported</li>
          <li>Check for any warnings or notices</li>
          <li>Verify employee matches are correct</li>
          <li>Confirm import settings</li>
        </ul>

        <h4>Final Import</h4>
        <ol>
          <li>Click <strong>Confirm Import</strong></li>
          <li>System processes all records</li>
          <li>Import progress is displayed</li>
          <li>Success/failure summary provided</li>
          <li>Import log generated for audit</li>
        </ol>

        <h3>Banking Details Import</h3>
        <p>Import employee banking information:</p>
        <ol>
          <li>Download banking details template</li>
          <li>Fill in required fields:
            <ul>
              <li>Employee Number</li>
              <li>Bank Name</li>
              <li>Branch Code</li>
              <li>Account Number</li>
              <li>Account Type</li>
              <li>Account Holder Name</li>
            </ul>
          </li>
          <li>Validate bank details format</li>
          <li>Upload and process</li>
        </ol>

        <h3>Leave Balance Import</h3>
        <p>Import leave balances and adjustments:</p>
        <ul>
          <li><strong>Opening Balances</strong>: Set initial leave balances</li>
          <li><strong>Carry-over Balances</strong>: Import previous year balances</li>
          <li><strong>Adjustments</strong>: Bulk leave balance corrections</li>
          <li><strong>Entitlements</strong>: Set annual leave entitlements</li>
        </ul>

        <h3>Common Import Errors</h3>
        <ul>
          <li><strong>Invalid ID Numbers</strong>: Check format and validity</li>
          <li><strong>Duplicate Employees</strong>: Ensure unique employee numbers</li>
          <li><strong>Invalid Dates</strong>: Use correct date format</li>
          <li><strong>Missing Required Fields</strong>: Complete all mandatory fields</li>
          <li><strong>Invalid Bank Details</strong>: Verify account numbers and codes</li>
          <li><strong>Salary Format Errors</strong>: Use numeric values only</li>
        </ul>

        <h3>Best Practices</h3>
        <ul>
          <li>Always use the latest template version</li>
          <li>Test imports with small data sets first</li>
          <li>Keep backup copies of original data</li>
          <li>Validate data before uploading</li>
          <li>Import during off-peak hours</li>
          <li>Review import logs for any issues</li>
          <li>Train users on proper data formatting</li>
          <li>Maintain data quality standards</li>
        </ul>

        <h3>Troubleshooting</h3>
        <ul>
          <li><strong>File Won't Upload</strong>: Check file size and format</li>
          <li><strong>Validation Failures</strong>: Review error messages carefully</li>
          <li><strong>Slow Processing</strong>: Large files may take time</li>
          <li><strong>Partial Imports</strong>: Check for data conflicts</li>
          <li><strong>Missing Data</strong>: Verify all required fields completed</li>
        </ul>
      </div>
    `;

    this.content['bulk-inputs'] = `
      <div class="help-article active">
        <h2>Bulk Inputs</h2>
        <p>Efficiently enter payroll data for multiple employees using bulk input forms and spreadsheet-style interfaces.</p>

        <h3>Accessing Bulk Input Forms</h3>
        <ol>
          <li>Navigate to <strong>Payroll > Bulk Operations</strong></li>
          <li>Select <strong>Bulk Inputs</strong></li>
          <li>Choose the type of data to input</li>
          <li>Select the pay period</li>
        </ol>

        <h3>Types of Bulk Inputs</h3>

        <h4>Working Hours Input</h4>
        <p>Enter hours for multiple employees:</p>
        <ul>
          <li><strong>Regular Hours</strong>: Standard working hours</li>
          <li><strong>Overtime Hours</strong>: Time-and-a-half and double-time</li>
          <li><strong>Public Holiday Hours</strong>: Holiday work hours</li>
          <li><strong>Night Shift Hours</strong>: Shift differential hours</li>
          <li><strong>Weekend Hours</strong>: Weekend premium hours</li>
        </ul>

        <h4>Bonus and Incentive Input</h4>
        <p>Add bonuses for multiple employees:</p>
        <ul>
          <li><strong>Performance Bonuses</strong>: Individual performance rewards</li>
          <li><strong>Commission Payments</strong>: Sales commission</li>
          <li><strong>Production Bonuses</strong>: Output-based incentives</li>
          <li><strong>Retention Bonuses</strong>: Employee retention payments</li>
          <li><strong>13th Cheque</strong>: Annual bonus payments</li>
        </ul>

        <h4>Allowance Input</h4>
        <p>Enter allowances for employees:</p>
        <ul>
          <li><strong>Travel Allowances</strong>: Reimbursive and fixed allowances</li>
          <li><strong>Housing Allowances</strong>: Accommodation benefits</li>
          <li><strong>Meal Allowances</strong>: Food and subsistence</li>
          <li><strong>Cell Phone Allowances</strong>: Communication allowances</li>
          <li><strong>Tool Allowances</strong>: Equipment and tool allowances</li>
        </ul>

        <h4>Deduction Input</h4>
        <p>Apply deductions to multiple employees:</p>
        <ul>
          <li><strong>Loan Repayments</strong>: Company loan deductions</li>
          <li><strong>Garnishee Orders</strong>: Court-ordered deductions</li>
          <li><strong>Union Dues</strong>: Trade union contributions</li>
          <li><strong>Insurance Premiums</strong>: Group insurance deductions</li>
          <li><strong>Uniform Costs</strong>: Uniform and equipment costs</li>
        </ul>

        <h3>Bulk Input Interface</h3>

        <h4>Spreadsheet-Style Grid</h4>
        <p>Use the grid interface for efficient data entry:</p>
        <ul>
          <li><strong>Employee List</strong>: All employees displayed in rows</li>
          <li><strong>Input Columns</strong>: Different input types in columns</li>
          <li><strong>Quick Navigation</strong>: Tab between cells</li>
          <li><strong>Copy/Paste</strong>: Excel-style copy and paste</li>
          <li><strong>Auto-calculation</strong>: Real-time calculations</li>
        </ul>

        <h4>Filtering and Sorting</h4>
        <p>Manage large employee lists:</p>
        <ul>
          <li><strong>Department Filter</strong>: Show specific departments</li>
          <li><strong>Employee Status</strong>: Active, inactive, probation</li>
          <li><strong>Pay Frequency</strong>: Monthly, weekly, bi-weekly</li>
          <li><strong>Search Function</strong>: Find specific employees</li>
          <li><strong>Sort Options</strong>: By name, number, department</li>
        </ul>

        <h3>Data Entry Methods</h3>

        <h4>Manual Entry</h4>
        <ol>
          <li>Click on the cell to edit</li>
          <li>Enter the value</li>
          <li>Press Tab or Enter to move to next cell</li>
          <li>Use keyboard shortcuts for efficiency</li>
          <li>Save frequently to prevent data loss</li>
        </ol>

        <h4>Copy and Paste</h4>
        <ol>
          <li>Select cells to copy (Ctrl+C)</li>
          <li>Select destination cells</li>
          <li>Paste values (Ctrl+V)</li>
          <li>Confirm paste operation</li>
          <li>Review pasted data</li>
        </ol>

        <h4>Fill Down/Across</h4>
        <ol>
          <li>Enter value in first cell</li>
          <li>Select range to fill</li>
          <li>Use fill down/across function</li>
          <li>Confirm fill operation</li>
          <li>Adjust individual values as needed</li>
        </ol>

        <h4>Formula Application</h4>
        <p>Apply calculations across multiple employees:</p>
        <ul>
          <li><strong>Percentage Calculations</strong>: Apply percentage of salary</li>
          <li><strong>Fixed Amounts</strong>: Same amount for all employees</li>
          <li><strong>Conditional Logic</strong>: Different amounts based on criteria</li>
          <li><strong>Pro-rata Calculations</strong>: Proportional amounts</li>
        </ul>

        <h3>Validation and Error Checking</h3>

        <h4>Real-time Validation</h4>
        <p>System validates data as you enter:</p>
        <ul>
          <li><strong>Data Type Validation</strong>: Numeric fields only accept numbers</li>
          <li><strong>Range Validation</strong>: Values within acceptable limits</li>
          <li><strong>Business Rule Validation</strong>: Compliance with company policies</li>
          <li><strong>Duplicate Detection</strong>: Prevents duplicate entries</li>
        </ul>

        <h4>Error Highlighting</h4>
        <p>Errors are clearly marked:</p>
        <ul>
          <li><strong>Red Borders</strong>: Invalid data entries</li>
          <li><strong>Warning Icons</strong>: Potential issues</li>
          <li><strong>Tooltip Messages</strong>: Detailed error descriptions</li>
          <li><strong>Error Summary</strong>: List of all errors</li>
        </ul>

        <h3>Approval Workflow</h3>
        <p>Bulk inputs may require approval:</p>
        <ol>
          <li>Complete data entry</li>
          <li>Submit for approval</li>
          <li>Manager receives notification</li>
          <li>Manager reviews and approves/rejects</li>
          <li>Approved data is applied to payroll</li>
        </ol>

        <h3>Templates and Presets</h3>

        <h4>Saving Templates</h4>
        <ol>
          <li>Configure bulk input settings</li>
          <li>Enter sample data</li>
          <li>Click <strong>Save as Template</strong></li>
          <li>Name the template</li>
          <li>Template available for future use</li>
        </ol>

        <h4>Using Presets</h4>
        <p>Apply common input patterns:</p>
        <ul>
          <li><strong>Standard Overtime</strong>: Common overtime patterns</li>
          <li><strong>Monthly Allowances</strong>: Regular monthly allowances</li>
          <li><strong>Seasonal Bonuses</strong>: Holiday and year-end bonuses</li>
          <li><strong>Department Specific</strong>: Department-based inputs</li>
        </ul>

        <h3>Integration with Payroll</h3>
        <p>Bulk inputs integrate seamlessly:</p>
        <ul>
          <li><strong>Automatic Application</strong>: Data applied to current payroll</li>
          <li><strong>Payslip Generation</strong>: Inputs appear on payslips</li>
          <li><strong>Tax Calculations</strong>: Automatic tax calculations</li>
          <li><strong>Reporting</strong>: Included in payroll reports</li>
        </ul>

        <h3>Audit Trail</h3>
        <p>All bulk inputs are tracked:</p>
        <ul>
          <li><strong>User Information</strong>: Who entered the data</li>
          <li><strong>Timestamp</strong>: When data was entered</li>
          <li><strong>Changes Made</strong>: What data was modified</li>
          <li><strong>Approval Status</strong>: Approval workflow status</li>
          <li><strong>Source Information</strong>: How data was entered</li>
        </ul>

        <h3>Best Practices</h3>
        <ul>
          <li>Save work frequently to prevent data loss</li>
          <li>Use templates for recurring input patterns</li>
          <li>Validate data before submitting</li>
          <li>Double-check calculations and amounts</li>
          <li>Use filtering to focus on specific groups</li>
          <li>Keep backup copies of input data</li>
          <li>Train users on keyboard shortcuts</li>
          <li>Review approval workflows regularly</li>
        </ul>

        <h3>Keyboard Shortcuts</h3>
        <ul>
          <li><strong>Tab</strong>: Move to next cell</li>
          <li><strong>Shift+Tab</strong>: Move to previous cell</li>
          <li><strong>Enter</strong>: Move down one row</li>
          <li><strong>Ctrl+C</strong>: Copy selected cells</li>
          <li><strong>Ctrl+V</strong>: Paste copied data</li>
          <li><strong>Ctrl+S</strong>: Save current work</li>
          <li><strong>Ctrl+Z</strong>: Undo last action</li>
          <li><strong>F2</strong>: Edit current cell</li>
        </ul>
      </div>
    `;

    // Leave Management Content
    this.content['leave-types'] = `
      <div class="help-article active">
        <h2>Leave Types</h2>
        <p>Configure and manage different types of leave available to employees in accordance with South African labour law.</p>

        <h3>Standard South African Leave Types</h3>
        <p>PandaPayroll includes pre-configured leave types based on South African legislation:</p>

        <h4>Annual Leave</h4>
        <ul>
          <li><strong>Entitlement</strong>: 21 consecutive days per year (Basic Conditions of Employment Act)</li>
          <li><strong>Accrual</strong>: 1.75 days per month worked</li>
          <li><strong>Carry Over</strong>: Up to 6 days to following year</li>
          <li><strong>Cash Out</strong>: Can be paid out on termination</li>
          <li><strong>Pro-rata</strong>: Calculated proportionally for partial years</li>
        </ul>

        <h4>Sick Leave</h4>
        <ul>
          <li><strong>Entitlement</strong>: 30 days per 3-year cycle</li>
          <li><strong>Accrual</strong>: 1 day per month for first 6 months, then 1.25 days per month</li>
          <li><strong>Medical Certificate</strong>: Required for absences > 2 consecutive days</li>
          <li><strong>Carry Over</strong>: Unused days carry over within 3-year cycle</li>
          <li><strong>No Cash Out</strong>: Cannot be paid out on termination</li>
        </ul>

        <h4>Maternity Leave</h4>
        <ul>
          <li><strong>Entitlement</strong>: 4 consecutive months</li>
          <li><strong>Timing</strong>: Can commence 4 weeks before expected birth</li>
          <li><strong>Medical Certificate</strong>: Required from medical practitioner</li>
          <li><strong>UIF Benefits</strong>: Eligible for UIF maternity benefits</li>
          <li><strong>Job Protection</strong>: Position protected during leave</li>
        </ul>

        <h4>Family Responsibility Leave</h4>
        <ul>
          <li><strong>Entitlement</strong>: 3 days per year</li>
          <li><strong>Eligibility</strong>: After 4 months of employment</li>
          <li><strong>Purpose</strong>: Birth of child, illness/death of family member</li>
          <li><strong>Proof Required</strong>: Documentation of family responsibility</li>
          <li><strong>No Carry Over</strong>: Use it or lose it annually</li>
        </ul>

        <h3>Creating Custom Leave Types</h3>
        <ol>
          <li>Navigate to <strong>Settings > Leave Management</strong></li>
          <li>Click <strong>Add Leave Type</strong></li>
          <li>Configure leave type details:
            <ul>
              <li>Name and description</li>
              <li>Category (annual, sick, family, unpaid, custom)</li>
              <li>Days per year entitlement</li>
              <li>Accrual method</li>
              <li>Carry over limits</li>
              <li>Paid or unpaid leave</li>
            </ul>
          </li>
          <li>Set approval requirements</li>
          <li>Configure documentation requirements</li>
          <li>Save the leave type</li>
        </ol>

        <h3>Leave Type Configuration Options</h3>

        <h4>Accrual Methods</h4>
        <ul>
          <li><strong>Monthly Accrual</strong>: Days earned each month</li>
          <li><strong>Annual Allocation</strong>: Full entitlement on anniversary date</li>
          <li><strong>No Accrual</strong>: Fixed allocation (e.g., study leave)</li>
        </ul>

        <h4>Approval Settings</h4>
        <ul>
          <li><strong>Requires Approval</strong>: Manager approval needed</li>
          <li><strong>Auto-Approve</strong>: Automatic approval for certain types</li>
          <li><strong>Approval Hierarchy</strong>: Multi-level approval process</li>
          <li><strong>Notice Period</strong>: Minimum days notice required</li>
        </ul>

        <h4>Documentation Requirements</h4>
        <ul>
          <li><strong>Medical Certificate</strong>: Required for sick leave > 2 days</li>
          <li><strong>Supporting Documents</strong>: Death certificates, birth certificates</li>
          <li><strong>Document Upload</strong>: Attach files to leave requests</li>
          <li><strong>Verification Process</strong>: HR verification of documents</li>
        </ul>

        <h3>Gender-Specific Leave Types</h3>
        <p>Configure leave types available to specific genders:</p>
        <ul>
          <li><strong>Maternity Leave</strong>: Female employees only</li>
          <li><strong>Paternity Leave</strong>: Male employees (if company policy)</li>
          <li><strong>Adoption Leave</strong>: Available to adoptive parents</li>
          <li><strong>Parental Leave</strong>: Gender-neutral parental leave</li>
        </ul>

        <h3>Industry-Specific Leave Types</h3>
        <p>Create leave types for specific industries:</p>
        <ul>
          <li><strong>Study Leave</strong>: Educational institutions</li>
          <li><strong>Sabbatical Leave</strong>: Academic and research positions</li>
          <li><strong>Compassionate Leave</strong>: Extended family emergencies</li>
          <li><strong>Religious Leave</strong>: Religious observances</li>
          <li><strong>Military Leave</strong>: Reserve force duties</li>
        </ul>

        <h3>Leave Type Management</h3>

        <h4>Editing Leave Types</h4>
        <ol>
          <li>Go to <strong>Settings > Leave Management</strong></li>
          <li>Find the leave type to edit</li>
          <li>Click <strong>Edit</strong></li>
          <li>Modify settings as needed</li>
          <li>Save changes</li>
          <li>Changes apply to future leave requests</li>
        </ol>

        <h4>Deactivating Leave Types</h4>
        <ol>
          <li>Select the leave type</li>
          <li>Click <strong>Deactivate</strong></li>
          <li>Confirm deactivation</li>
          <li>Leave type no longer available for new requests</li>
          <li>Existing leave requests remain unaffected</li>
        </ol>

        <h3>Employee-Specific Allocations</h3>
        <p>Override standard allocations for individual employees:</p>
        <ul>
          <li><strong>Senior Management</strong>: Additional annual leave days</li>
          <li><strong>Long Service</strong>: Extra leave for tenure</li>
          <li><strong>Contract Terms</strong>: Negotiated leave entitlements</li>
          <li><strong>Medical Conditions</strong>: Additional sick leave</li>
          <li><strong>Part-time Employees</strong>: Pro-rata allocations</li>
        </ul>

        <h3>Leave Type Reporting</h3>
        <p>Generate reports on leave type usage:</p>
        <ul>
          <li><strong>Leave Type Summary</strong>: Usage by leave type</li>
          <li><strong>Department Analysis</strong>: Leave patterns by department</li>
          <li><strong>Trend Analysis</strong>: Leave usage trends over time</li>
          <li><strong>Compliance Report</strong>: Adherence to labour law requirements</li>
        </ul>

        <h3>Best Practices</h3>
        <ul>
          <li>Align leave types with labour law requirements</li>
          <li>Clearly define leave type purposes and eligibility</li>
          <li>Set appropriate approval workflows</li>
          <li>Regular review of leave type usage</li>
          <li>Train managers on leave type policies</li>
          <li>Maintain documentation requirements</li>
          <li>Monitor compliance with legal requirements</li>
          <li>Update leave types when legislation changes</li>
        </ul>

        <h3>Compliance Considerations</h3>
        <ul>
          <li><strong>Basic Conditions of Employment Act</strong>: Minimum leave entitlements</li>
          <li><strong>Labour Relations Act</strong>: Family responsibility leave</li>
          <li><strong>UIF Act</strong>: Maternity and illness benefits</li>
          <li><strong>Sectoral Determinations</strong>: Industry-specific requirements</li>
          <li><strong>Bargaining Council Agreements</strong>: Collective agreement terms</li>
        </ul>
      </div>
    `;

    this.content['leave-entitlements'] = `
      <div class="help-article active">
        <h2>Leave Entitlements</h2>
        <p>Configure and manage employee leave entitlements, balances, and allocations in compliance with South African labour legislation.</p>

        <h3>Understanding Leave Entitlements</h3>
        <p>Leave entitlements define how much leave each employee is entitled to based on:</p>
        <ul>
          <li><strong>Employment Duration</strong>: Length of service with company</li>
          <li><strong>Leave Type</strong>: Different types have different entitlements</li>
          <li><strong>Employment Status</strong>: Full-time, part-time, contract</li>
          <li><strong>Company Policy</strong>: Enhanced benefits beyond legal minimums</li>
          <li><strong>Individual Agreements</strong>: Negotiated entitlements</li>
        </ul>

        <h3>South African Legal Minimums</h3>

        <h4>Annual Leave Entitlements</h4>
        <ul>
          <li><strong>Standard Entitlement</strong>: 21 consecutive days per year</li>
          <li><strong>Accrual Rate</strong>: 1.75 days per month worked</li>
          <li><strong>Minimum Service</strong>: After 1 month of employment</li>
          <li><strong>Pro-rata Calculation</strong>: For partial years of service</li>
          <li><strong>Maximum Carry Over</strong>: 6 days to following year</li>
        </ul>

        <h4>Sick Leave Entitlements</h4>
        <ul>
          <li><strong>Standard Entitlement</strong>: 30 days per 3-year cycle</li>
          <li><strong>First 6 Months</strong>: 1 day per month worked</li>
          <li><strong>After 6 Months</strong>: 1.25 days per month</li>
          <li><strong>Cycle Reset</strong>: Every 3 years from start date</li>
          <li><strong>Unused Days</strong>: Carry over within 3-year cycle</li>
        </ul>

        <h4>Family Responsibility Leave</h4>
        <ul>
          <li><strong>Standard Entitlement</strong>: 3 days per year</li>
          <li><strong>Eligibility</strong>: After 4 months of employment</li>
          <li><strong>Annual Reset</strong>: Resets each calendar year</li>
          <li><strong>No Carry Over</strong>: Unused days don't carry forward</li>
        </ul>

        <h3>Setting Up Employee Entitlements</h3>

        <h4>Individual Employee Setup</h4>
        <ol>
          <li>Navigate to <strong>Employees > [Employee Name]</strong></li>
          <li>Click <strong>Leave Entitlements</strong> tab</li>
          <li>Configure entitlements for each leave type:
            <ul>
              <li>Annual allocation</li>
              <li>Accrual method</li>
              <li>Start date</li>
              <li>Special conditions</li>
            </ul>
          </li>
          <li>Set any employee-specific overrides</li>
          <li>Save entitlement settings</li>
        </ol>

        <h4>Bulk Entitlement Setup</h4>
        <ol>
          <li>Go to <strong>Leave > Bulk Operations</strong></li>
          <li>Select <strong>Entitlement Setup</strong></li>
          <li>Choose employees to update</li>
          <li>Set entitlement parameters</li>
          <li>Apply to selected employees</li>
          <li>Review and confirm changes</li>
        </ol>

        <h3>Entitlement Calculation Methods</h3>

        <h4>Anniversary-Based Calculation</h4>
        <p>Leave year runs from employee start date:</p>
        <ul>
          <li><strong>Leave Year</strong>: Start date to start date + 1 year</li>
          <li><strong>Full Allocation</strong>: Complete entitlement on anniversary</li>
          <li><strong>Pro-rata First Year</strong>: Proportional allocation for partial year</li>
          <li><strong>Individual Tracking</strong>: Each employee has unique leave year</li>
        </ul>

        <h4>Calendar Year Calculation</h4>
        <p>Leave year runs from January to December:</p>
        <ul>
          <li><strong>Leave Year</strong>: 1 January to 31 December</li>
          <li><strong>Uniform Allocation</strong>: All employees get allocation on 1 January</li>
          <li><strong>Pro-rata New Joiners</strong>: Proportional for mid-year starters</li>
          <li><strong>Simplified Administration</strong>: Easier to manage and report</li>
        </ul>

        <h4>Monthly Accrual</h4>
        <p>Leave accrues monthly throughout the year:</p>
        <ul>
          <li><strong>Monthly Allocation</strong>: Portion of annual entitlement each month</li>
          <li><strong>Immediate Availability</strong>: Can use leave as it accrues</li>
          <li><strong>Automatic Calculation</strong>: System calculates monthly accrual</li>
          <li><strong>Pro-rata Adjustments</strong>: For partial months worked</li>
        </ul>

        <h3>Enhanced Entitlements</h3>

        <h4>Long Service Leave</h4>
        <p>Additional leave for long-serving employees:</p>
        <ul>
          <li><strong>5 Years Service</strong>: Additional 1-2 days annual leave</li>
          <li><strong>10 Years Service</strong>: Additional 3-5 days annual leave</li>
          <li><strong>15+ Years Service</strong>: Additional 5-10 days annual leave</li>
          <li><strong>Sabbatical Leave</strong>: Extended leave after 10+ years</li>
        </ul>

        <h4>Position-Based Entitlements</h4>
        <p>Enhanced leave for specific positions:</p>
        <ul>
          <li><strong>Senior Management</strong>: Additional annual leave days</li>
          <li><strong>Shift Workers</strong>: Compensatory leave for shift work</li>
          <li><strong>Sales Staff</strong>: Additional leave for high performers</li>
          <li><strong>Technical Specialists</strong>: Study leave entitlements</li>
        </ul>

        <h3>Managing Leave Balances</h3>

        <h4>Opening Balances</h4>
        <ol>
          <li>Navigate to <strong>Leave > Balance Management</strong></li>
          <li>Select employee and leave type</li>
          <li>Enter opening balance details:
            <ul>
              <li>Total allocation for year</li>
              <li>Used days (if any)</li>
              <li>Carry over from previous year</li>
              <li>Adjustments</li>
            </ul>
          </li>
          <li>Calculate remaining balance</li>
          <li>Save balance information</li>
        </ol>

        <h4>Balance Adjustments</h4>
        <p>Make corrections to leave balances:</p>
        <ul>
          <li><strong>Manual Adjustments</strong>: Correct calculation errors</li>
          <li><strong>Bonus Allocations</strong>: Award additional leave days</li>
          <li><strong>Penalty Deductions</strong>: Deduct for policy violations</li>
          <li><strong>Transfer Adjustments</strong>: Adjust for department transfers</li>
        </ul>

        <h3>Carry Over Rules</h3>

        <h4>Annual Leave Carry Over</h4>
        <p>Configure carry over policies:</p>
        <ul>
          <li><strong>Maximum Days</strong>: Limit on days that can carry over</li>
          <li><strong>Expiry Period</strong>: When carried over days expire</li>
          <li><strong>Use First Rule</strong>: Carried over days used before new allocation</li>
          <li><strong>Cash Out Option</strong>: Pay out excess days instead of carry over</li>
        </ul>

        <h4>Sick Leave Carry Over</h4>
        <p>Sick leave accumulation rules:</p>
        <ul>
          <li><strong>3-Year Cycle</strong>: Accumulate over 36-month period</li>
          <li><strong>Maximum Accumulation</strong>: Up to 90 days total</li>
          <li><strong>Cycle Reset</strong>: Reset every 3 years from start date</li>
          <li><strong>No Cash Out</strong>: Cannot be paid out on termination</li>
        </ul>

        <h3>Part-Time Employee Entitlements</h3>
        <p>Calculate pro-rata entitlements for part-time staff:</p>
        <ul>
          <li><strong>Hours-Based Calculation</strong>: Based on hours worked vs full-time</li>
          <li><strong>Days-Based Calculation</strong>: Based on days worked per week</li>
          <li><strong>Percentage Method</strong>: Apply percentage of full-time entitlement</li>
          <li><strong>Minimum Entitlements</strong>: Ensure legal minimums are met</li>
        </ul>

        <h3>Entitlement Reporting</h3>

        <h4>Individual Reports</h4>
        <ul>
          <li><strong>Employee Leave Statement</strong>: Individual entitlements and usage</li>
          <li><strong>Balance Summary</strong>: Current balances by leave type</li>
          <li><strong>Accrual History</strong>: How leave was earned over time</li>
          <li><strong>Usage Patterns</strong>: When and how leave was taken</li>
        </ul>

        <h4>Company Reports</h4>
        <ul>
          <li><strong>Entitlement Summary</strong>: Company-wide entitlement overview</li>
          <li><strong>Department Analysis</strong>: Entitlements by department</li>
          <li><strong>Liability Report</strong>: Outstanding leave liability</li>
          <li><strong>Compliance Report</strong>: Adherence to legal requirements</li>
        </ul>

        <h3>Best Practices</h3>
        <ul>
          <li>Regularly review and update entitlement policies</li>
          <li>Ensure compliance with labour legislation</li>
          <li>Communicate entitlements clearly to employees</li>
          <li>Monitor leave liability for financial planning</li>
          <li>Use consistent calculation methods</li>
          <li>Document all entitlement policies</li>
          <li>Train HR staff on entitlement management</li>
          <li>Regular audit of leave balances</li>
        </ul>

        <h3>Common Issues and Solutions</h3>
        <ul>
          <li><strong>Incorrect Balances</strong>: Regular reconciliation and adjustment</li>
          <li><strong>Carry Over Confusion</strong>: Clear communication of carry over rules</li>
          <li><strong>Pro-rata Calculations</strong>: Use system calculations for accuracy</li>
          <li><strong>Policy Changes</strong>: Grandfather existing entitlements when changing policies</li>
          <li><strong>System Errors</strong>: Regular backup and validation of leave data</li>
        </ul>
      </div>
    `;

    this.content['leave-recording'] = `
      <div class="help-article active">
        <h2>Recording Leave</h2>
        <p>Learn how to record, approve, and manage employee leave requests efficiently while maintaining accurate leave records.</p>

        <h3>Leave Request Process</h3>

        <h4>Employee Self-Service Requests</h4>
        <ol>
          <li>Employee logs into self-service portal</li>
          <li>Navigates to <strong>Leave > Request Leave</strong></li>
          <li>Selects leave type from dropdown</li>
          <li>Enters leave dates and duration</li>
          <li>Provides reason for leave</li>
          <li>Uploads supporting documents (if required)</li>
          <li>Submits request for approval</li>
        </ol>

        <h4>Manager/HR Recording</h4>
        <ol>
          <li>Navigate to <strong>Leave > Record Leave</strong></li>
          <li>Select employee from list</li>
          <li>Choose leave type</li>
          <li>Enter leave details:
            <ul>
              <li>Start and end dates</li>
              <li>Number of days</li>
              <li>Reason for leave</li>
              <li>Supporting documentation</li>
            </ul>
          </li>
          <li>Set approval status</li>
          <li>Save leave record</li>
        </ol>

        <h3>Leave Request Information</h3>

        <h4>Required Information</h4>
        <ul>
          <li><strong>Employee Details</strong>: Name and employee number</li>
          <li><strong>Leave Type</strong>: Annual, sick, family responsibility, etc.</li>
          <li><strong>Dates</strong>: Start date, end date, return date</li>
          <li><strong>Duration</strong>: Number of days (including half days)</li>
          <li><strong>Reason</strong>: Purpose or reason for leave</li>
          <li><strong>Contact Details</strong>: How to reach employee during leave</li>
        </ul>

        <h4>Supporting Documentation</h4>
        <p>Required documents vary by leave type:</p>
        <ul>
          <li><strong>Sick Leave (>2 days)</strong>: Medical certificate</li>
          <li><strong>Family Responsibility</strong>: Death certificate, birth certificate</li>
          <li><strong>Maternity Leave</strong>: Medical certificate from doctor</li>
          <li><strong>Study Leave</strong>: Course enrollment confirmation</li>
          <li><strong>Compassionate Leave</strong>: Relevant supporting documents</li>
        </ul>

        <h3>Leave Approval Workflow</h3>

        <h4>Single-Level Approval</h4>
        <ol>
          <li>Employee submits leave request</li>
          <li>Direct manager receives notification</li>
          <li>Manager reviews request and documentation</li>
          <li>Manager approves or rejects with comments</li>
          <li>Employee receives notification of decision</li>
          <li>Approved leave is added to calendar</li>
        </ol>

        <h4>Multi-Level Approval</h4>
        <ol>
          <li>Employee submits leave request</li>
          <li>Direct manager provides initial approval</li>
          <li>Request escalates to senior manager/HR</li>
          <li>Final approval or rejection given</li>
          <li>All parties notified of final decision</li>
        </ol>

        <h4>Automatic Approval</h4>
        <p>Some leave types may be auto-approved:</p>
        <ul>
          <li><strong>Annual Leave</strong>: If sufficient balance and notice given</li>
          <li><strong>Sick Leave (1-2 days)</strong>: Auto-approve short sick leave</li>
          <li><strong>Emergency Leave</strong>: Retrospective approval process</li>
        </ul>

        <h3>Leave Calendar Integration</h3>

        <h4>Team Calendar</h4>
        <p>View team leave on shared calendar:</p>
        <ul>
          <li><strong>Department View</strong>: See all department leave</li>
          <li><strong>Team Conflicts</strong>: Identify overlapping leave</li>
          <li><strong>Coverage Planning</strong>: Plan work coverage</li>
          <li><strong>Public Holidays</strong>: Integrated with SA public holidays</li>
        </ul>

        <h4>Individual Calendar</h4>
        <p>Employee's personal leave calendar:</p>
        <ul>
          <li><strong>Approved Leave</strong>: Confirmed leave dates</li>
          <li><strong>Pending Requests</strong>: Awaiting approval</li>
          <li><strong>Leave Balance</strong>: Remaining days available</li>
          <li><strong>Accrual Schedule</strong>: When new leave is earned</li>
        </ul>

        <h3>Recording Different Leave Types</h3>

        <h4>Annual Leave</h4>
        <ol>
          <li>Check employee has sufficient balance</li>
          <li>Verify minimum notice period met</li>
          <li>Record consecutive days if required</li>
          <li>Update leave balance automatically</li>
          <li>Generate leave certificate if needed</li>
        </ol>

        <h4>Sick Leave</h4>
        <ol>
          <li>Record leave dates and duration</li>
          <li>Check if medical certificate required</li>
          <li>Upload medical certificate if provided</li>
          <li>Verify against sick leave balance</li>
          <li>Flag if balance exceeded</li>
        </ol>

        <h4>Maternity Leave</h4>
        <ol>
          <li>Record expected start date</li>
          <li>Upload medical certificate</li>
          <li>Calculate 4-month entitlement</li>
          <li>Set up UIF benefit application</li>
          <li>Plan return to work date</li>
        </ol>

        <h4>Emergency/Retrospective Leave</h4>
        <ol>
          <li>Record leave that has already been taken</li>
          <li>Provide reason for emergency</li>
          <li>Upload supporting documentation</li>
          <li>Seek retrospective approval</li>
          <li>Update attendance records</li>
        </ol>

        <h3>Half Day and Partial Leave</h3>

        <h4>Half Day Leave</h4>
        <ul>
          <li><strong>Morning Half Day</strong>: Leave in morning, work afternoon</li>
          <li><strong>Afternoon Half Day</strong>: Work morning, leave afternoon</li>
          <li><strong>Balance Deduction</strong>: 0.5 days deducted from balance</li>
          <li><strong>Payroll Impact</strong>: No pay deduction for paid leave types</li>
        </ul>

        <h4>Hourly Leave</h4>
        <ul>
          <li><strong>Medical Appointments</strong>: Few hours for doctor visits</li>
          <li><strong>Personal Errands</strong>: Short personal leave</li>
          <li><strong>Flexible Arrangements</strong>: Make up time later</li>
          <li><strong>Calculation</strong>: Convert hours to day fractions</li>
        </ul>

        <h3>Leave Balance Management</h3>

        <h4>Automatic Balance Updates</h4>
        <p>System automatically updates balances when leave is recorded:</p>
        <ul>
          <li><strong>Deduction</strong>: Days deducted from available balance</li>
          <li><strong>Validation</strong>: Prevents negative balances (with warnings)</li>
          <li><strong>Accrual</strong>: Continues to accrue during paid leave</li>
          <li><strong>Reporting</strong>: Real-time balance reporting</li>
        </ul>

        <h4>Manual Balance Adjustments</h4>
        <p>Make manual corrections when needed:</p>
        <ul>
          <li><strong>Correction Entries</strong>: Fix recording errors</li>
          <li><strong>Bonus Days</strong>: Award additional leave</li>
          <li><strong>Penalty Deductions</strong>: Deduct for policy violations</li>
          <li><strong>Carry Over Adjustments</strong>: Year-end carry over processing</li>
        </ul>

        <h3>Payroll Integration</h3>

        <h4>Paid Leave</h4>
        <ul>
          <li><strong>No Pay Deduction</strong>: Annual, sick, family responsibility leave</li>
          <li><strong>Normal Payslip</strong>: Full pay continues during leave</li>
          <li><strong>Benefits Continue</strong>: Medical aid, pension contributions continue</li>
          <li><strong>Tax Implications</strong>: No change to tax calculations</li>
        </ul>

        <h4>Unpaid Leave</h4>
        <ul>
          <li><strong>Pay Deduction</strong>: Pro-rata salary reduction</li>
          <li><strong>Benefit Adjustments</strong>: May affect benefit contributions</li>
          <li><strong>Tax Adjustments</strong>: Reduced taxable income</li>
          <li><strong>UIF Impact</strong>: May affect UIF contributions</li>
        </ul>

        <h3>Compliance and Audit</h3>

        <h4>Record Keeping</h4>
        <ul>
          <li><strong>Leave Register</strong>: Comprehensive leave records</li>
          <li><strong>Supporting Documents</strong>: Medical certificates, etc.</li>
          <li><strong>Approval Trail</strong>: Who approved what and when</li>
          <li><strong>Balance History</strong>: How balances changed over time</li>
        </ul>

        <h4>Labour Law Compliance</h4>
        <ul>
          <li><strong>Minimum Entitlements</strong>: Ensure legal minimums met</li>
          <li><strong>Documentation</strong>: Proper documentation for sick leave</li>
          <li><strong>Notice Periods</strong>: Compliance with notice requirements</li>
          <li><strong>Dispute Resolution</strong>: Proper records for disputes</li>
        </ul>

        <h3>Best Practices</h3>
        <ul>
          <li>Record leave as soon as possible after approval</li>
          <li>Ensure all required documentation is collected</li>
          <li>Communicate leave policies clearly to employees</li>
          <li>Regular reconciliation of leave balances</li>
          <li>Train managers on approval processes</li>
          <li>Maintain confidentiality of medical information</li>
          <li>Use consistent recording procedures</li>
          <li>Regular backup of leave data</li>
        </ul>
      </div>
    `;

    // Security Content
    this.content['two-factor-auth'] = `
      <div class="help-article active">
        <h2>Two-Factor Authentication (2FA)</h2>
        <p>Enhance your account security with two-factor authentication, adding an extra layer of protection to your PandaPayroll account.</p>

        <h3>What is Two-Factor Authentication?</h3>
        <p>Two-factor authentication (2FA) requires two forms of verification to access your account:</p>
        <ul>
          <li><strong>Something you know</strong>: Your password</li>
          <li><strong>Something you have</strong>: Your mobile device with authenticator app</li>
        </ul>

        <h3>Benefits of 2FA</h3>
        <ul>
          <li><strong>Enhanced Security</strong>: Protects against password theft</li>
          <li><strong>Compliance</strong>: Meets security requirements for payroll systems</li>
          <li><strong>Peace of Mind</strong>: Know your sensitive data is protected</li>
          <li><strong>Audit Trail</strong>: Better tracking of account access</li>
          <li><strong>Industry Standard</strong>: Follows best security practices</li>
        </ul>

        <h3>Setting Up 2FA</h3>

        <h4>Step 1: Access Security Settings</h4>
        <ol>
          <li>Log in to your PandaPayroll account</li>
          <li>Navigate to <strong>Settings > Security</strong></li>
          <li>Find the <strong>Two-Factor Authentication</strong> section</li>
          <li>Click <strong>Enable 2FA</strong></li>
        </ol>

        <h4>Step 2: Install Authenticator App</h4>
        <p>Download a compatible authenticator app:</p>
        <ul>
          <li><strong>Google Authenticator</strong> (iOS/Android)</li>
          <li><strong>Microsoft Authenticator</strong> (iOS/Android)</li>
          <li><strong>Authy</strong> (iOS/Android/Desktop)</li>
          <li><strong>1Password</strong> (Premium feature)</li>
        </ul>

        <h4>Step 3: Scan QR Code</h4>
        <ol>
          <li>Open your authenticator app</li>
          <li>Tap <strong>Add Account</strong> or <strong>+</strong></li>
          <li>Select <strong>Scan QR Code</strong></li>
          <li>Point camera at QR code displayed in PandaPayroll</li>
          <li>Account will be added to your authenticator app</li>
        </ol>

        <h4>Step 4: Enter Verification Code</h4>
        <ol>
          <li>Check your authenticator app for 6-digit code</li>
          <li>Enter the code in PandaPayroll</li>
          <li>Click <strong>Verify and Enable</strong></li>
          <li>Save your backup codes (important!)</li>
          <li>2FA is now active on your account</li>
        </ol>

        <h3>Backup Codes</h3>
        <p>Backup codes allow access if you lose your device:</p>
        <ul>
          <li><strong>Generate Codes</strong>: System provides 10 single-use codes</li>
          <li><strong>Store Safely</strong>: Keep codes in secure location</li>
          <li><strong>One-Time Use</strong>: Each code can only be used once</li>
          <li><strong>Regenerate</strong>: Create new codes if needed</li>
          <li><strong>Emergency Access</strong>: Use when authenticator unavailable</li>
        </ul>

        <h3>Logging In with 2FA</h3>

        <h4>Normal Login Process</h4>
        <ol>
          <li>Enter your email and password</li>
          <li>Click <strong>Login</strong></li>
          <li>System prompts for 2FA code</li>
          <li>Open authenticator app</li>
          <li>Enter the 6-digit code</li>
          <li>Click <strong>Verify</strong></li>
          <li>Access granted to your account</li>
        </ol>

        <h4>Using Backup Codes</h4>
        <ol>
          <li>Enter email and password</li>
          <li>On 2FA prompt, click <strong>Use Backup Code</strong></li>
          <li>Enter one of your saved backup codes</li>
          <li>Click <strong>Verify</strong></li>
          <li>Access granted (code is now used)</li>
        </ol>

        <h3>Managing 2FA Settings</h3>

        <h4>Viewing 2FA Status</h4>
        <p>Check your 2FA configuration:</p>
        <ul>
          <li><strong>Status</strong>: Enabled or disabled</li>
          <li><strong>Setup Date</strong>: When 2FA was configured</li>
          <li><strong>Last Used</strong>: Most recent 2FA verification</li>
          <li><strong>Backup Codes</strong>: Number of unused codes remaining</li>
        </ul>

        <h4>Regenerating Backup Codes</h4>
        <ol>
          <li>Go to <strong>Settings > Security</strong></li>
          <li>Find <strong>Two-Factor Authentication</strong> section</li>
          <li>Click <strong>Regenerate Backup Codes</strong></li>
          <li>Confirm regeneration</li>
          <li>Save new codes securely</li>
          <li>Old codes are invalidated</li>
        </ol>

        <h4>Disabling 2FA</h4>
        <ol>
          <li>Navigate to <strong>Settings > Security</strong></li>
          <li>Click <strong>Disable 2FA</strong></li>
          <li>Enter current password</li>
          <li>Provide 2FA code or backup code</li>
          <li>Confirm disabling</li>
          <li>2FA is removed from account</li>
        </ol>

        <h3>Troubleshooting 2FA</h3>

        <h4>Code Not Working</h4>
        <p>If your 2FA code isn't accepted:</p>
        <ul>
          <li><strong>Check Time Sync</strong>: Ensure device time is correct</li>
          <li><strong>Wait for New Code</strong>: Codes refresh every 30 seconds</li>
          <li><strong>Try Next Code</strong>: Use the next generated code</li>
          <li><strong>Use Backup Code</strong>: Try a backup code instead</li>
        </ul>

        <h4>Lost Authenticator Device</h4>
        <ol>
          <li>Use a backup code to log in</li>
          <li>Go to <strong>Settings > Security</strong></li>
          <li>Disable current 2FA setup</li>
          <li>Set up 2FA again with new device</li>
          <li>Generate new backup codes</li>
        </ol>

        <h4>No Backup Codes Available</h4>
        <p>If you've lost both device and backup codes:</p>
        <ol>
          <li>Contact <NAME_EMAIL></li>
          <li>Provide identity verification</li>
          <li>Support will temporarily disable 2FA</li>
          <li>Set up 2FA again immediately after access</li>
          <li>Generate and save new backup codes</li>
        </ol>

        <h3>2FA for Different User Roles</h3>

        <h4>Administrators</h4>
        <ul>
          <li><strong>Mandatory 2FA</strong>: Required for all admin accounts</li>
          <li><strong>Cannot Disable</strong>: 2FA cannot be turned off</li>
          <li><strong>Regular Review</strong>: Periodic security reviews</li>
          <li><strong>Backup Procedures</strong>: Must maintain backup codes</li>
        </ul>

        <h4>Regular Users</h4>
        <ul>
          <li><strong>Optional 2FA</strong>: Recommended but not mandatory</li>
          <li><strong>Self-Service</strong>: Can enable/disable independently</li>
          <li><strong>Support Available</strong>: Help with setup if needed</li>
        </ul>

        <h4>Employee Portal Users</h4>
        <ul>
          <li><strong>Optional Setup</strong>: Can enable for extra security</li>
          <li><strong>Simplified Process</strong>: Streamlined setup process</li>
          <li><strong>Mobile Friendly</strong>: Optimized for mobile devices</li>
        </ul>

        <h3>Security Best Practices</h3>
        <ul>
          <li><strong>Enable 2FA</strong>: Always enable 2FA for payroll accounts</li>
          <li><strong>Secure Backup Codes</strong>: Store codes in password manager</li>
          <li><strong>Regular Updates</strong>: Keep authenticator app updated</li>
          <li><strong>Multiple Devices</strong>: Consider backup authenticator device</li>
          <li><strong>Monitor Access</strong>: Review login activity regularly</li>
          <li><strong>Report Issues</strong>: Report suspicious activity immediately</li>
        </ul>

        <h3>Company 2FA Policies</h3>

        <h4>Mandatory 2FA Roles</h4>
        <p>These roles require 2FA:</p>
        <ul>
          <li>System Administrators</li>
          <li>Company Administrators</li>
          <li>Payroll Managers</li>
          <li>HR Managers</li>
          <li>Financial Controllers</li>
        </ul>

        <h4>Enforcement Settings</h4>
        <ul>
          <li><strong>Grace Period</strong>: 30 days to set up 2FA</li>
          <li><strong>Account Lockout</strong>: Access restricted without 2FA</li>
          <li><strong>Compliance Reporting</strong>: Track 2FA adoption</li>
          <li><strong>Support Process</strong>: Help users with setup</li>
        </ul>

        <h3>Mobile Device Considerations</h3>
        <ul>
          <li><strong>Device Security</strong>: Use device lock screen</li>
          <li><strong>App Security</strong>: Protect authenticator app</li>
          <li><strong>Backup Strategy</strong>: Plan for device loss/replacement</li>
          <li><strong>Multiple Apps</strong>: Consider using multiple authenticator apps</li>
          <li><strong>Cloud Backup</strong>: Some apps offer cloud backup features</li>
        </ul>
      </div>
    `;

    this.content['employee-portal'] = `
      <div class="help-article active">
        <h2>Employee Portal</h2>
        <p>The employee self-service portal allows employees to access their payroll information, request leave, and manage personal details independently.</p>

        <h3>Accessing the Employee Portal</h3>

        <h4>Initial Setup</h4>
        <ol>
          <li>HR sends employee portal invitation email</li>
          <li>Employee clicks setup link in email</li>
          <li>Creates username and password</li>
          <li>Verifies email address</li>
          <li>Completes security questions</li>
          <li>Portal access is activated</li>
        </ol>

        <h4>Login Process</h4>
        <ol>
          <li>Go to employee portal URL</li>
          <li>Enter username and password</li>
          <li>Complete 2FA if enabled</li>
          <li>Access granted to portal dashboard</li>
        </ol>

        <h3>Portal Dashboard</h3>
        <p>The main dashboard provides quick access to key information:</p>
        <ul>
          <li><strong>Latest Payslip</strong>: Most recent payslip summary</li>
          <li><strong>Leave Balance</strong>: Current leave balances by type</li>
          <li><strong>Pending Requests</strong>: Leave requests awaiting approval</li>
          <li><strong>Announcements</strong>: Company news and updates</li>
          <li><strong>Quick Actions</strong>: Common tasks and shortcuts</li>
          <li><strong>Calendar</strong>: Upcoming leave and company events</li>
        </ul>

        <h3>Payslip Access</h3>

        <h4>Viewing Payslips</h4>
        <ol>
          <li>Navigate to <strong>Payslips</strong> section</li>
          <li>Select pay period from dropdown</li>
          <li>Click <strong>View Payslip</strong></li>
          <li>Payslip displays in browser</li>
          <li>All payslip details are shown</li>
        </ol>

        <h4>Downloading Payslips</h4>
        <ol>
          <li>Open desired payslip</li>
          <li>Click <strong>Download PDF</strong></li>
          <li>PDF file downloads to device</li>
          <li>Can be saved or printed</li>
        </ol>

        <h4>Payslip History</h4>
        <p>Access historical payslips:</p>
        <ul>
          <li><strong>Current Year</strong>: All payslips for current tax year</li>
          <li><strong>Previous Years</strong>: Historical payslip archive</li>
          <li><strong>Search Function</strong>: Find specific pay periods</li>
          <li><strong>Bulk Download</strong>: Download multiple payslips</li>
        </ul>

        <h3>Leave Management</h3>

        <h4>Viewing Leave Balances</h4>
        <p>Check current leave entitlements:</p>
        <ul>
          <li><strong>Annual Leave</strong>: Days available and used</li>
          <li><strong>Sick Leave</strong>: Current sick leave balance</li>
          <li><strong>Family Responsibility</strong>: Available family leave days</li>
          <li><strong>Other Leave Types</strong>: Company-specific leave types</li>
          <li><strong>Accrual Schedule</strong>: When new leave is earned</li>
        </ul>

        <h4>Requesting Leave</h4>
        <ol>
          <li>Click <strong>Request Leave</strong></li>
          <li>Select leave type from dropdown</li>
          <li>Choose start and end dates</li>
          <li>Enter reason for leave</li>
          <li>Upload supporting documents (if required)</li>
          <li>Submit request for approval</li>
          <li>Receive confirmation email</li>
        </ol>

        <h4>Leave Request Status</h4>
        <p>Track leave request progress:</p>
        <ul>
          <li><strong>Pending</strong>: Awaiting manager approval</li>
          <li><strong>Approved</strong>: Leave request approved</li>
          <li><strong>Rejected</strong>: Request denied with reason</li>
          <li><strong>Cancelled</strong>: Employee cancelled request</li>
        </ul>

        <h4>Leave Calendar</h4>
        <p>Visual calendar showing:</p>
        <ul>
          <li><strong>Approved Leave</strong>: Confirmed leave dates</li>
          <li><strong>Pending Requests</strong>: Requests awaiting approval</li>
          <li><strong>Public Holidays</strong>: South African public holidays</li>
          <li><strong>Company Events</strong>: Company-wide events and closures</li>
        </ul>

        <h3>Personal Information</h3>

        <h4>Viewing Personal Details</h4>
        <p>Access personal information:</p>
        <ul>
          <li><strong>Contact Information</strong>: Phone, email, address</li>
          <li><strong>Emergency Contacts</strong>: Emergency contact details</li>
          <li><strong>Banking Details</strong>: Bank account information</li>
          <li><strong>Tax Information</strong>: Tax number and directives</li>
          <li><strong>Employment Details</strong>: Job title, department, start date</li>
        </ul>

        <h4>Updating Information</h4>
        <ol>
          <li>Navigate to <strong>Personal Information</strong></li>
          <li>Click <strong>Edit</strong> next to section to update</li>
          <li>Make necessary changes</li>
          <li>Upload supporting documents if required</li>
          <li>Submit changes for approval</li>
          <li>HR reviews and approves changes</li>
        </ol>

        <h3>Tax Certificates</h3>

        <h4>IRP5 Certificates</h4>
        <ol>
          <li>Go to <strong>Tax Documents</strong> section</li>
          <li>Select tax year</li>
          <li>Click <strong>Download IRP5</strong></li>
          <li>PDF certificate downloads</li>
          <li>Use for tax return submission</li>
        </ol>

        <h4>Tax Directive Management</h4>
        <p>Manage SARS tax directives:</p>
        <ul>
          <li><strong>View Current Directives</strong>: Active tax directives</li>
          <li><strong>Upload New Directives</strong>: Submit SARS directives</li>
          <li><strong>Directive History</strong>: Previous tax directives</li>
          <li><strong>Status Tracking</strong>: Implementation status</li>
        </ul>

        <h3>Benefits Information</h3>

        <h4>Medical Aid</h4>
        <p>View medical aid information:</p>
        <ul>
          <li><strong>Scheme Details</strong>: Medical aid scheme name and option</li>
          <li><strong>Member Number</strong>: Medical aid member number</li>
          <li><strong>Dependants</strong>: Listed dependants on scheme</li>
          <li><strong>Contributions</strong>: Monthly contribution amounts</li>
          <li><strong>Tax Credits</strong>: Medical aid tax credits applied</li>
        </ul>

        <h4>Pension Fund</h4>
        <p>Access pension fund information:</p>
        <ul>
          <li><strong>Fund Details</strong>: Pension fund name and type</li>
          <li><strong>Member Number</strong>: Pension fund member number</li>
          <li><strong>Contributions</strong>: Employee and employer contributions</li>
          <li><strong>Benefit Statements</strong>: Annual benefit statements</li>
        </ul>

        <h3>Communication Features</h3>

        <h4>Company Announcements</h4>
        <ul>
          <li><strong>News Updates</strong>: Company news and updates</li>
          <li><strong>Policy Changes</strong>: HR policy updates</li>
          <li><strong>Event Notifications</strong>: Company events and activities</li>
          <li><strong>System Maintenance</strong>: Planned system downtime</li>
        </ul>

        <h4>Direct Messaging</h4>
        <ul>
          <li><strong>HR Contact</strong>: Direct message to HR department</li>
          <li><strong>Payroll Queries</strong>: Questions about payslips</li>
          <li><strong>Leave Queries</strong>: Questions about leave requests</li>
          <li><strong>General Support</strong>: General help and support</li>
        </ul>

        <h3>Mobile Access</h3>

        <h4>Mobile-Responsive Design</h4>
        <p>Portal works on mobile devices:</p>
        <ul>
          <li><strong>Responsive Layout</strong>: Adapts to screen size</li>
          <li><strong>Touch-Friendly</strong>: Easy navigation on mobile</li>
          <li><strong>Fast Loading</strong>: Optimized for mobile networks</li>
          <li><strong>Offline Viewing</strong>: Cache payslips for offline access</li>
        </ul>

        <h4>Mobile Features</h4>
        <ul>
          <li><strong>Quick Actions</strong>: Common tasks easily accessible</li>
          <li><strong>Push Notifications</strong>: Leave approval notifications</li>
          <li><strong>Biometric Login</strong>: Fingerprint/face recognition</li>
          <li><strong>Document Camera</strong>: Upload documents using camera</li>
        </ul>

        <h3>Security and Privacy</h3>

        <h4>Data Protection</h4>
        <ul>
          <li><strong>Secure Connection</strong>: HTTPS encryption</li>
          <li><strong>Session Management</strong>: Automatic logout after inactivity</li>
          <li><strong>Access Logging</strong>: All access is logged</li>
          <li><strong>Data Privacy</strong>: POPIA compliance</li>
        </ul>

        <h4>Password Management</h4>
        <ol>
          <li>Navigate to <strong>Account Settings</strong></li>
          <li>Click <strong>Change Password</strong></li>
          <li>Enter current password</li>
          <li>Enter new password (twice)</li>
          <li>Save password changes</li>
        </ol>

        <h3>Troubleshooting</h3>

        <h4>Login Issues</h4>
        <ul>
          <li><strong>Forgot Password</strong>: Use password reset link</li>
          <li><strong>Account Locked</strong>: Contact HR for unlock</li>
          <li><strong>Browser Issues</strong>: Clear cache and cookies</li>
          <li><strong>Mobile Issues</strong>: Try desktop browser</li>
        </ul>

        <h4>Common Problems</h4>
        <ul>
          <li><strong>Payslip Not Available</strong>: Check with payroll department</li>
          <li><strong>Leave Request Errors</strong>: Verify leave balance</li>
          <li><strong>Document Upload Fails</strong>: Check file size and format</li>
          <li><strong>Information Not Updated</strong>: Allow time for HR approval</li>
        </ul>

        <h3>Getting Help</h3>
        <ul>
          <li><strong>Help Section</strong>: Built-in help and FAQs</li>
          <li><strong>Contact HR</strong>: Direct contact through portal</li>
          <li><strong>Email Support</strong>: <EMAIL></li>
          <li><strong>Phone Support</strong>: During business hours</li>
          <li><strong>Training Videos</strong>: Video tutorials available</li>
        </ul>
      </div>
    `;

    // Reports Content
    this.content['employee-reports'] = `
      <div class="help-article active">
        <h2>Employee Reports</h2>
        <p>Generate comprehensive employee reports for HR management, compliance, and business analysis.</p>

        <h3>Accessing Employee Reports</h3>
        <ol>
          <li>Navigate to <strong>Reports > Employee Reports</strong></li>
          <li>Select the report type from the menu</li>
          <li>Configure report parameters</li>
          <li>Set date ranges and filters</li>
          <li>Generate and download report</li>
        </ol>

        <h3>Available Employee Reports</h3>

        <h4>Employee Master List</h4>
        <p>Comprehensive list of all employees:</p>
        <ul>
          <li><strong>Personal Information</strong>: Names, ID numbers, contact details</li>
          <li><strong>Employment Details</strong>: Start dates, job titles, departments</li>
          <li><strong>Salary Information</strong>: Basic salaries, pay frequencies</li>
          <li><strong>Status Information</strong>: Active, inactive, terminated</li>
          <li><strong>Banking Details</strong>: Bank accounts for EFT payments</li>
        </ul>

        <h4>Employee Demographics</h4>
        <p>Demographic analysis for Employment Equity:</p>
        <ul>
          <li><strong>Race Distribution</strong>: Breakdown by racial categories</li>
          <li><strong>Gender Analysis</strong>: Male/female distribution</li>
          <li><strong>Age Groups</strong>: Age distribution across workforce</li>
          <li><strong>Disability Status</strong>: Employees with disabilities</li>
          <li><strong>Nationality</strong>: South African vs foreign nationals</li>
        </ul>

        <h4>Department Analysis</h4>
        <p>Employee distribution by organizational structure:</p>
        <ul>
          <li><strong>Headcount by Department</strong>: Employee count per department</li>
          <li><strong>Cost Center Analysis</strong>: Employees by cost center</li>
          <li><strong>Management Levels</strong>: Distribution across hierarchy</li>
          <li><strong>Reporting Structure</strong>: Manager-employee relationships</li>
        </ul>

        <h4>New Joiners Report</h4>
        <p>Track new employee onboarding:</p>
        <ul>
          <li><strong>Recent Hires</strong>: Employees joined in selected period</li>
          <li><strong>Onboarding Status</strong>: Completion of setup processes</li>
          <li><strong>Probation Tracking</strong>: Employees on probation</li>
          <li><strong>Documentation Status</strong>: Required documents received</li>
        </ul>

        <h4>Terminations Report</h4>
        <p>Analysis of employee departures:</p>
        <ul>
          <li><strong>Termination Details</strong>: Dates, reasons, notice periods</li>
          <li><strong>Exit Interview Data</strong>: Feedback from departing employees</li>
          <li><strong>Final Pay Status</strong>: Final payment calculations</li>
          <li><strong>Turnover Analysis</strong>: Turnover rates by department</li>
        </ul>

        <h3>Payroll-Related Employee Reports</h3>

        <h4>Salary Analysis Report</h4>
        <p>Comprehensive salary information:</p>
        <ul>
          <li><strong>Salary Ranges</strong>: Min, max, average by position</li>
          <li><strong>Pay Equity Analysis</strong>: Gender and race pay gaps</li>
          <li><strong>Salary Progression</strong>: Historical salary changes</li>
          <li><strong>Market Comparison</strong>: Benchmarking against market rates</li>
        </ul>

        <h4>Benefits Participation</h4>
        <p>Employee benefit enrollment and costs:</p>
        <ul>
          <li><strong>Medical Aid Participation</strong>: Enrollment by scheme</li>
          <li><strong>Pension Fund Members</strong>: Retirement fund participation</li>
          <li><strong>Group Insurance</strong>: Life and disability coverage</li>
          <li><strong>Benefit Costs</strong>: Total benefit costs per employee</li>
        </ul>

        <h4>Tax Information Report</h4>
        <p>Employee tax-related information:</p>
        <ul>
          <li><strong>Tax Numbers</strong>: SARS tax reference numbers</li>
          <li><strong>Tax Directives</strong>: Active SARS directives</li>
          <li><strong>Medical Aid Credits</strong>: Tax credits claimed</li>
          <li><strong>PAYE Summary</strong>: Annual PAYE deductions</li>
        </ul>

        <h3>Attendance and Leave Reports</h3>

        <h4>Attendance Summary</h4>
        <p>Employee attendance patterns:</p>
        <ul>
          <li><strong>Attendance Rates</strong>: Percentage attendance by employee</li>
          <li><strong>Absenteeism Analysis</strong>: Patterns of absence</li>
          <li><strong>Punctuality Report</strong>: Late arrivals and early departures</li>
          <li><strong>Overtime Analysis</strong>: Overtime hours worked</li>
        </ul>

        <h4>Leave Utilization</h4>
        <p>Leave usage patterns and balances:</p>
        <ul>
          <li><strong>Leave Balances</strong>: Current balances by leave type</li>
          <li><strong>Leave Usage</strong>: Days taken by leave type</li>
          <li><strong>Leave Patterns</strong>: Seasonal leave trends</li>
          <li><strong>Leave Liability</strong>: Outstanding leave financial liability</li>
        </ul>

        <h3>Compliance Reports</h3>

        <h4>Employment Equity Report</h4>
        <p>EE compliance reporting:</p>
        <ul>
          <li><strong>EE Categories</strong>: Breakdown by designated groups</li>
          <li><strong>Occupational Levels</strong>: Distribution across job levels</li>
          <li><strong>Skills Development</strong>: Training and development data</li>
          <li><strong>Progress Tracking</strong>: EE plan implementation progress</li>
        </ul>

        <h4>Skills Development Report</h4>
        <p>Training and development tracking:</p>
        <ul>
          <li><strong>Training Participation</strong>: Employees in training programs</li>
          <li><strong>Skills Assessment</strong>: Current skill levels</li>
          <li><strong>Development Plans</strong>: Individual development plans</li>
          <li><strong>Training Costs</strong>: Investment in employee development</li>
        </ul>

        <h3>Custom Report Builder</h3>

        <h4>Creating Custom Reports</h4>
        <ol>
          <li>Go to <strong>Reports > Custom Reports</strong></li>
          <li>Click <strong>Create New Report</strong></li>
          <li>Select data sources:
            <ul>
              <li>Employee master data</li>
              <li>Payroll information</li>
              <li>Leave data</li>
              <li>Attendance records</li>
            </ul>
          </li>
          <li>Choose fields to include</li>
          <li>Set filters and criteria</li>
          <li>Configure grouping and sorting</li>
          <li>Save report template</li>
        </ol>

        <h4>Report Filters</h4>
        <p>Filter reports by various criteria:</p>
        <ul>
          <li><strong>Date Ranges</strong>: Specific periods or date ranges</li>
          <li><strong>Employee Status</strong>: Active, inactive, terminated</li>
          <li><strong>Departments</strong>: Specific departments or cost centers</li>
          <li><strong>Job Titles</strong>: Specific positions or job grades</li>
          <li><strong>Employment Type</strong>: Permanent, contract, temporary</li>
          <li><strong>Demographics</strong>: Age, gender, race filters</li>
        </ul>

        <h3>Report Formats and Export</h3>

        <h4>Available Formats</h4>
        <ul>
          <li><strong>PDF</strong>: Professional formatted reports</li>
          <li><strong>Excel</strong>: Spreadsheet format for analysis</li>
          <li><strong>CSV</strong>: Data format for import to other systems</li>
          <li><strong>Word</strong>: Document format for editing</li>
        </ul>

        <h4>Export Options</h4>
        <ul>
          <li><strong>Download</strong>: Immediate download to device</li>
          <li><strong>Email</strong>: Send report via email</li>
          <li><strong>Scheduled Delivery</strong>: Automatic report generation</li>
          <li><strong>Cloud Storage</strong>: Save to cloud storage services</li>
        </ul>

        <h3>Scheduled Reports</h3>

        <h4>Setting Up Scheduled Reports</h4>
        <ol>
          <li>Create or select existing report</li>
          <li>Click <strong>Schedule Report</strong></li>
          <li>Set schedule frequency:
            <ul>
              <li>Daily, weekly, monthly</li>
              <li>Specific dates</li>
              <li>Custom intervals</li>
            </ul>
          </li>
          <li>Configure delivery options</li>
          <li>Set recipients</li>
          <li>Activate schedule</li>
        </ol>

        <h4>Managing Scheduled Reports</h4>
        <ul>
          <li><strong>View Schedule</strong>: See all scheduled reports</li>
          <li><strong>Modify Schedule</strong>: Change frequency or recipients</li>
          <li><strong>Pause Schedule</strong>: Temporarily stop generation</li>
          <li><strong>Delete Schedule</strong>: Remove scheduled report</li>
        </ul>

        <h3>Report Security and Access</h3>

        <h4>Access Controls</h4>
        <ul>
          <li><strong>Role-Based Access</strong>: Reports based on user role</li>
          <li><strong>Department Restrictions</strong>: Access to own department only</li>
          <li><strong>Sensitive Data</strong>: Restricted access to salary information</li>
          <li><strong>Audit Trail</strong>: Track who accessed which reports</li>
        </ul>

        <h4>Data Privacy</h4>
        <ul>
          <li><strong>POPIA Compliance</strong>: Adherence to privacy legislation</li>
          <li><strong>Data Masking</strong>: Hide sensitive information</li>
          <li><strong>Consent Management</strong>: Employee consent for data use</li>
          <li><strong>Retention Policies</strong>: Automatic deletion of old reports</li>
        </ul>

        <h3>Best Practices</h3>
        <ul>
          <li>Regular review of employee data accuracy</li>
          <li>Use filters to focus on relevant information</li>
          <li>Schedule regular compliance reports</li>
          <li>Maintain data privacy and security</li>
          <li>Document report purposes and usage</li>
          <li>Train users on report interpretation</li>
          <li>Regular backup of report templates</li>
          <li>Monitor report performance and usage</li>
        </ul>
      </div>
    `;

    this.content['eti-qualification'] = `
      <div class="help-article active">
        <h2>ETI Qualification Criteria</h2>
        <p>Understand the Employment Tax Incentive (ETI) qualification criteria to maximize tax benefits for eligible employees and employers.</p>

        <h3>What is Employment Tax Incentive (ETI)?</h3>
        <p>ETI is a South African tax incentive designed to:</p>
        <ul>
          <li><strong>Encourage Employment</strong>: Incentivize hiring of young workers</li>
          <li><strong>Reduce Youth Unemployment</strong>: Target 18-29 age group</li>
          <li><strong>Lower Employment Costs</strong>: Reduce employer tax burden</li>
          <li><strong>Stimulate Economic Growth</strong>: Increase formal employment</li>
        </ul>

        <h3>Employee Qualification Criteria</h3>

        <h4>Age Requirements</h4>
        <ul>
          <li><strong>Minimum Age</strong>: 18 years old</li>
          <li><strong>Maximum Age</strong>: 29 years old (under 30)</li>
          <li><strong>Age Calculation</strong>: Age on first day of employment</li>
          <li><strong>Age Verification</strong>: Based on ID number or passport</li>
        </ul>

        <h4>Employment History</h4>
        <p>Employee must not have been employed by:</p>
        <ul>
          <li><strong>Current Employer</strong>: Not previously employed by same employer</li>
          <li><strong>Associated Persons</strong>: Not employed by related entities</li>
          <li><strong>Time Limit</strong>: Not employed in previous 6 months</li>
          <li><strong>Exceptions</strong>: Learnership and apprenticeship programs excluded</li>
        </ul>

        <h4>Salary Thresholds</h4>
        <p>Monthly remuneration limits for ETI eligibility:</p>
        <ul>
          <li><strong>Minimum Threshold</strong>: R2,000 per month</li>
          <li><strong>Maximum Threshold</strong>: R6,500 per month</li>
          <li><strong>Calculation</strong>: Based on gross monthly remuneration</li>
          <li><strong>Annual Adjustment</strong>: Thresholds adjusted annually</li>
        </ul>

        <h4>Work Requirements</h4>
        <ul>
          <li><strong>Minimum Hours</strong>: At least 10 hours per week</li>
          <li><strong>Regular Employment</strong>: Ongoing employment relationship</li>
          <li><strong>Formal Employment</strong>: Registered with SARS and UIF</li>
          <li><strong>Employment Contract</strong>: Written or verbal contract</li>
        </ul>

        <h3>Employer Qualification Criteria</h3>

        <h4>Employer Registration</h4>
        <ul>
          <li><strong>SARS Registration</strong>: Registered for PAYE with SARS</li>
          <li><strong>UIF Registration</strong>: Registered with UIF</li>
          <li><strong>Compliance</strong>: Up to date with tax obligations</li>
          <li><strong>Good Standing</strong>: No outstanding tax debts</li>
        </ul>

        <h4>Excluded Employers</h4>
        <p>ETI not available to:</p>
        <ul>
          <li><strong>Government Entities</strong>: National, provincial, local government</li>
          <li><strong>State-Owned Enterprises</strong>: Entities controlled by government</li>
          <li><strong>Municipal Entities</strong>: Municipal corporations</li>
          <li><strong>Public Benefit Organizations</strong>: Certain PBOs and NPOs</li>
        </ul>

        <h3>Special Categories</h3>

        <h4>Disabled Employees</h4>
        <p>Enhanced ETI for employees with disabilities:</p>
        <ul>
          <li><strong>Age Extension</strong>: Up to 65 years old</li>
          <li><strong>Higher Incentive</strong>: Increased ETI amounts</li>
          <li><strong>Longer Duration</strong>: Extended benefit period</li>
          <li><strong>Documentation</strong>: Medical certificate required</li>
        </ul>

        <h4>Special Economic Zones (SEZ)</h4>
        <p>Additional benefits for SEZ employers:</p>
        <ul>
          <li><strong>Enhanced Rates</strong>: Higher ETI percentages</li>
          <li><strong>Extended Period</strong>: Longer benefit duration</li>
          <li><strong>Broader Eligibility</strong>: Relaxed qualification criteria</li>
          <li><strong>SEZ Registration</strong>: Must be registered SEZ operator</li>
        </ul>

        <h3>Qualification Assessment Process</h3>

        <h4>Initial Assessment</h4>
        <ol>
          <li>Verify employee age from ID document</li>
          <li>Check employment history with employee</li>
          <li>Confirm salary falls within thresholds</li>
          <li>Ensure minimum working hours met</li>
          <li>Complete ETI qualification form</li>
        </ol>

        <h4>Documentation Required</h4>
        <ul>
          <li><strong>Employee Declaration</strong>: ETI employee declaration form</li>
          <li><strong>ID Document</strong>: Copy of ID book or card</li>
          <li><strong>Employment Contract</strong>: Contract or appointment letter</li>
          <li><strong>Salary Details</strong>: Remuneration structure</li>
          <li><strong>Disability Certificate</strong>: If claiming disabled employee ETI</li>
        </ul>

        <h4>Ongoing Monitoring</h4>
        <p>Regular checks to maintain qualification:</p>
        <ul>
          <li><strong>Salary Changes</strong>: Monitor salary increases</li>
          <li><strong>Age Progression</strong>: Track when employee turns 30</li>
          <li><strong>Employment Status</strong>: Ensure continuous employment</li>
          <li><strong>Compliance</strong>: Maintain employer compliance</li>
        </ul>

        <h3>Disqualification Events</h3>

        <h4>Employee Disqualification</h4>
        <p>Employee loses ETI eligibility when:</p>
        <ul>
          <li><strong>Age Limit</strong>: Turns 30 years old</li>
          <li><strong>Salary Increase</strong>: Exceeds R6,500 monthly threshold</li>
          <li><strong>Employment Break</strong>: Employment terminated</li>
          <li><strong>Reduced Hours</strong>: Works less than 10 hours per week</li>
        </ul>

        <h4>Employer Disqualification</h4>
        <p>Employer loses ETI eligibility for:</p>
        <ul>
          <li><strong>Non-Compliance</strong>: Outstanding tax obligations</li>
          <li><strong>Deregistration</strong>: SARS or UIF deregistration</li>
          <li><strong>Fraudulent Claims</strong>: False ETI claims</li>
          <li><strong>Related Party Employment</strong>: Employing related persons</li>
        </ul>

        <h3>ETI Calculation Periods</h3>

        <h4>First 12 Months</h4>
        <ul>
          <li><strong>Maximum ETI</strong>: R1,000 per month</li>
          <li><strong>Calculation</strong>: Based on salary and age</li>
          <li><strong>Full Benefit</strong>: For salaries R2,000-R4,500</li>
          <li><strong>Reduced Benefit</strong>: For salaries R4,500-R6,500</li>
        </ul>

        <h4>Second 12 Months</h4>
        <ul>
          <li><strong>Maximum ETI</strong>: R500 per month</li>
          <li><strong>Reduced Rate</strong>: 50% of first year rate</li>
          <li><strong>Same Thresholds</strong>: Same salary thresholds apply</li>
          <li><strong>Continued Eligibility</strong>: Must still meet all criteria</li>
        </ul>

        <h3>Record Keeping Requirements</h3>

        <h4>Employee Records</h4>
        <ul>
          <li><strong>ETI Certificates</strong>: Employee ETI qualification certificates</li>
          <li><strong>Employment Records</strong>: Contracts and appointment letters</li>
          <li><strong>Payroll Records</strong>: Salary and wage records</li>
          <li><strong>Time Records</strong>: Hours worked documentation</li>
        </ul>

        <h4>Compliance Documentation</h4>
        <ul>
          <li><strong>Monthly Calculations</strong>: ETI calculations per employee</li>
          <li><strong>EMP201 Submissions</strong>: Monthly ETI claims to SARS</li>
          <li><strong>Annual Reconciliation</strong>: EMP501 ETI reconciliation</li>
          <li><strong>Supporting Documents</strong>: All qualification documentation</li>
        </ul>

        <h3>Common Qualification Issues</h3>

        <h4>Age Verification Problems</h4>
        <ul>
          <li><strong>Invalid ID Numbers</strong>: Incorrect or invalid ID numbers</li>
          <li><strong>Age Calculation Errors</strong>: Incorrect age determination</li>
          <li><strong>Foreign Nationals</strong>: Passport vs ID number issues</li>
          <li><strong>Document Verification</strong>: Fraudulent documents</li>
        </ul>

        <h4>Employment History Issues</h4>
        <ul>
          <li><strong>Previous Employment</strong>: Undisclosed previous employment</li>
          <li><strong>Related Entities</strong>: Employment by associated companies</li>
          <li><strong>Casual Work</strong>: Informal employment history</li>
          <li><strong>Learnership Programs</strong>: Confusion over learnership exclusions</li>
        </ul>

        <h3>Best Practices</h3>
        <ul>
          <li>Verify all qualification criteria before claiming ETI</li>
          <li>Maintain comprehensive documentation</li>
          <li>Regular review of employee eligibility</li>
          <li>Stay updated on ETI legislation changes</li>
          <li>Train HR staff on ETI requirements</li>
          <li>Implement robust record-keeping systems</li>
          <li>Regular compliance audits</li>
          <li>Seek professional advice when uncertain</li>
        </ul>
      </div>
    `;

    // Add remaining missing content sections
    this.content['employee-hours'] = `
      <div class="help-article active">
        <h2>Employee Hours</h2>
        <p>Record and manage employee working hours, overtime, and time-based calculations for accurate payroll processing.</p>

        <h3>Types of Working Hours</h3>

        <h4>Regular Hours</h4>
        <ul>
          <li><strong>Standard Hours</strong>: Normal working hours per day/week</li>
          <li><strong>Contractual Hours</strong>: Hours specified in employment contract</li>
          <li><strong>Full-time</strong>: Typically 40-45 hours per week</li>
          <li><strong>Part-time</strong>: Less than full-time hours</li>
        </ul>

        <h4>Overtime Hours</h4>
        <ul>
          <li><strong>Time-and-a-half</strong>: 1.5x normal rate for first 3 hours</li>
          <li><strong>Double-time</strong>: 2x normal rate after 3 hours overtime</li>
          <li><strong>Sunday Work</strong>: Double-time for Sunday work</li>
          <li><strong>Public Holiday</strong>: Special rates for public holidays</li>
        </ul>

        <h4>Special Hours</h4>
        <ul>
          <li><strong>Night Shift</strong>: Premium rates for night work</li>
          <li><strong>Weekend Work</strong>: Enhanced rates for weekend work</li>
          <li><strong>Standby Time</strong>: On-call or standby hours</li>
          <li><strong>Travel Time</strong>: Work-related travel hours</li>
        </ul>

        <h3>Recording Employee Hours</h3>

        <h4>Manual Time Entry</h4>
        <ol>
          <li>Navigate to <strong>Payroll > Employee Hours</strong></li>
          <li>Select employee and pay period</li>
          <li>Enter hours by category:
            <ul>
              <li>Regular hours worked</li>
              <li>Overtime hours</li>
              <li>Special rate hours</li>
              <li>Leave hours taken</li>
            </ul>
          </li>
          <li>Add notes or comments</li>
          <li>Save time entries</li>
        </ol>

        <h4>Timesheet Import</h4>
        <ol>
          <li>Download timesheet template</li>
          <li>Fill in employee hours data</li>
          <li>Upload completed timesheet</li>
          <li>Review and validate imported data</li>
          <li>Confirm import</li>
        </ol>

        <h4>Time Clock Integration</h4>
        <p>Integrate with time and attendance systems:</p>
        <ul>
          <li><strong>Clock In/Out</strong>: Automatic time recording</li>
          <li><strong>Break Tracking</strong>: Meal and rest break deductions</li>
          <li><strong>Overtime Calculation</strong>: Automatic overtime detection</li>
          <li><strong>Exception Handling</strong>: Manage missed punches</li>
        </ul>

        <h3>Overtime Calculations</h3>

        <h4>South African Overtime Rules</h4>
        <p>Basic Conditions of Employment Act requirements:</p>
        <ul>
          <li><strong>Daily Overtime</strong>: After 9 hours per day</li>
          <li><strong>Weekly Overtime</strong>: After 45 hours per week</li>
          <li><strong>First 3 Hours</strong>: 1.5x normal rate</li>
          <li><strong>Additional Hours</strong>: 2x normal rate</li>
          <li><strong>Sunday Work</strong>: 2x normal rate</li>
        </ul>

        <h4>Overtime Rate Calculation</h4>
        <p>Calculate overtime rates from basic salary:</p>
        <ol>
          <li>Determine hourly rate: Monthly salary ÷ 4.33 ÷ normal hours per week</li>
          <li>Calculate overtime rates:
            <ul>
              <li>Time-and-a-half: Hourly rate × 1.5</li>
              <li>Double-time: Hourly rate × 2.0</li>
            </ul>
          </li>
          <li>Apply rates to overtime hours worked</li>
        </ol>

        <h3>Shift Work Management</h3>

        <h4>Shift Patterns</h4>
        <ul>
          <li><strong>Day Shift</strong>: Standard daytime hours</li>
          <li><strong>Night Shift</strong>: Evening/night hours with premium</li>
          <li><strong>Rotating Shifts</strong>: Alternating shift patterns</li>
          <li><strong>Split Shifts</strong>: Broken work periods</li>
        </ul>

        <h4>Shift Premiums</h4>
        <ul>
          <li><strong>Night Differential</strong>: Additional pay for night work</li>
          <li><strong>Weekend Premium</strong>: Extra pay for weekend shifts</li>
          <li><strong>Holiday Premium</strong>: Enhanced rates for holiday work</li>
          <li><strong>Shift Allowances</strong>: Fixed allowances for shift work</li>
        </ul>

        <h3>Leave Hours Integration</h3>

        <h4>Paid Leave Hours</h4>
        <ul>
          <li><strong>Annual Leave</strong>: Count as regular hours worked</li>
          <li><strong>Sick Leave</strong>: Paid at normal rate</li>
          <li><strong>Family Responsibility</strong>: Paid leave hours</li>
          <li><strong>Public Holidays</strong>: Automatic holiday pay</li>
        </ul>

        <h4>Unpaid Leave Hours</h4>
        <ul>
          <li><strong>Unpaid Leave</strong>: Deduct from total hours</li>
          <li><strong>Suspension</strong>: No pay for suspended hours</li>
          <li><strong>Strike Action</strong>: No work, no pay principle</li>
          <li><strong>Unauthorized Absence</strong>: Deduct unauthorized hours</li>
        </ul>

        <h3>Bulk Hours Entry</h3>

        <h4>Department-Wide Entry</h4>
        <ol>
          <li>Select department or group</li>
          <li>Choose pay period</li>
          <li>Enter standard hours for all employees</li>
          <li>Add individual overtime hours</li>
          <li>Apply bulk changes</li>
        </ol>

        <h4>Template-Based Entry</h4>
        <ol>
          <li>Create hours template for recurring patterns</li>
          <li>Apply template to selected employees</li>
          <li>Modify individual entries as needed</li>
          <li>Save template for future use</li>
        </ol>

        <h3>Hours Validation and Approval</h3>

        <h4>Validation Rules</h4>
        <ul>
          <li><strong>Maximum Hours</strong>: Daily and weekly hour limits</li>
          <li><strong>Minimum Hours</strong>: Minimum hours for benefits</li>
          <li><strong>Overtime Limits</strong>: Maximum overtime allowed</li>
          <li><strong>Break Requirements</strong>: Mandatory break periods</li>
        </ul>

        <h4>Approval Workflow</h4>
        <ol>
          <li>Employee submits timesheet</li>
          <li>Supervisor reviews hours</li>
          <li>Supervisor approves or rejects</li>
          <li>Approved hours flow to payroll</li>
          <li>Rejected hours returned for correction</li>
        </ol>

        <h3>Reporting and Analytics</h3>

        <h4>Hours Reports</h4>
        <ul>
          <li><strong>Timesheet Summary</strong>: Hours by employee and period</li>
          <li><strong>Overtime Report</strong>: Overtime hours and costs</li>
          <li><strong>Department Hours</strong>: Hours by department</li>
          <li><strong>Attendance Report</strong>: Attendance patterns</li>
        </ul>

        <h4>Cost Analysis</h4>
        <ul>
          <li><strong>Labor Costs</strong>: Total labor costs by period</li>
          <li><strong>Overtime Costs</strong>: Overtime expense analysis</li>
          <li><strong>Productivity Metrics</strong>: Hours vs output analysis</li>
          <li><strong>Budget Variance</strong>: Actual vs budgeted hours</li>
        </ul>

        <h3>Compliance Considerations</h3>

        <h4>Labour Law Compliance</h4>
        <ul>
          <li><strong>Maximum Hours</strong>: 45 hours per week limit</li>
          <li><strong>Overtime Limits</strong>: 10 hours overtime per week</li>
          <li><strong>Rest Periods</strong>: Daily and weekly rest requirements</li>
          <li><strong>Record Keeping</strong>: Maintain accurate time records</li>
        </ul>

        <h4>Sectoral Determinations</h4>
        <ul>
          <li><strong>Industry-Specific Rules</strong>: Sector-specific hour limits</li>
          <li><strong>Minimum Wages</strong>: Sector minimum wage rates</li>
          <li><strong>Special Conditions</strong>: Industry-specific conditions</li>
          <li><strong>Bargaining Council</strong>: Collective agreement terms</li>
        </ul>

        <h3>Best Practices</h3>
        <ul>
          <li>Implement robust time tracking systems</li>
          <li>Regular training on time recording procedures</li>
          <li>Clear policies on overtime authorization</li>
          <li>Regular audit of time records</li>
          <li>Automate calculations where possible</li>
          <li>Maintain compliance with labour laws</li>
          <li>Document all time-related policies</li>
          <li>Regular review of overtime patterns</li>
        </ul>
      </div>
    `;

    this.content['pay-runs'] = `
      <div class="help-article active">
        <h2>Pay Runs</h2>
        <p>Create and manage pay runs to process payroll for multiple employees simultaneously, ensuring accurate and timely salary payments.</p>

        <h3>Understanding Pay Runs</h3>
        <p>A pay run is a batch process that:</p>
        <ul>
          <li><strong>Groups Payslips</strong>: Processes multiple employee payslips together</li>
          <li><strong>Ensures Consistency</strong>: Same pay period and processing date</li>
          <li><strong>Streamlines Workflow</strong>: Efficient payroll processing</li>
          <li><strong>Facilitates Payments</strong>: Batch payment processing</li>
          <li><strong>Maintains Audit Trail</strong>: Complete processing history</li>
        </ul>

        <h3>Creating a Pay Run</h3>

        <h4>Pay Run Setup</h4>
        <ol>
          <li>Navigate to <strong>Payroll > Pay Runs</strong></li>
          <li>Click <strong>Create New Pay Run</strong></li>
          <li>Configure pay run details:
            <ul>
              <li>Pay run name/reference</li>
              <li>Pay period start and end dates</li>
              <li>Payment date</li>
              <li>Pay frequency (monthly, weekly, bi-weekly)</li>
              <li>Employee selection criteria</li>
            </ul>
          </li>
          <li>Select employees to include</li>
          <li>Save pay run configuration</li>
        </ol>

        <h4>Employee Selection</h4>
        <p>Choose employees for the pay run:</p>
        <ul>
          <li><strong>All Active Employees</strong>: Include all active staff</li>
          <li><strong>By Department</strong>: Specific departments only</li>
          <li><strong>By Pay Frequency</strong>: Monthly, weekly, or bi-weekly employees</li>
          <li><strong>Custom Selection</strong>: Manually select individual employees</li>
          <li><strong>Exclude Criteria</strong>: Exclude terminated or inactive employees</li>
        </ul>

        <h3>Pay Run Processing Workflow</h3>

        <h4>Stage 1: Draft</h4>
        <ul>
          <li><strong>Initial Creation</strong>: Pay run created with selected employees</li>
          <li><strong>Payslip Generation</strong>: Individual payslips created</li>
          <li><strong>Data Entry</strong>: Hours, bonuses, deductions entered</li>
          <li><strong>Editable</strong>: Can modify payslips and settings</li>
        </ul>

        <h4>Stage 2: Calculated</h4>
        <ul>
          <li><strong>Tax Calculations</strong>: PAYE, UIF, SDL calculated</li>
          <li><strong>Deduction Processing</strong>: All deductions applied</li>
          <li><strong>Net Pay Calculation</strong>: Final net pay amounts</li>
          <li><strong>Validation</strong>: Error checking and validation</li>
        </ul>

        <h4>Stage 3: Under Review</h4>
        <ul>
          <li><strong>Management Review</strong>: Payroll manager reviews calculations</li>
          <li><strong>Exception Handling</strong>: Resolve any calculation errors</li>
          <li><strong>Approval Process</strong>: Obtain necessary approvals</li>
          <li><strong>Final Adjustments</strong>: Make last-minute corrections</li>
        </ul>

        <h4>Stage 4: Finalized</h4>
        <ul>
          <li><strong>Locked for Editing</strong>: Payslips cannot be modified</li>
          <li><strong>Payment Ready</strong>: Ready for payment processing</li>
          <li><strong>EFT File Generation</strong>: Banking files can be created</li>
          <li><strong>Payslip Distribution</strong>: Payslips can be sent to employees</li>
        </ul>

        <h4>Stage 5: Paid</h4>
        <ul>
          <li><strong>Payments Processed</strong>: Salaries paid to employees</li>
          <li><strong>Bank Confirmation</strong>: Payment confirmations received</li>
          <li><strong>Complete Audit Trail</strong>: Full processing history</li>
          <li><strong>Reporting Available</strong>: Final reports generated</li>
        </ul>

        <h3>Pay Run Management</h3>

        <h4>Monitoring Progress</h4>
        <p>Track pay run status and progress:</p>
        <ul>
          <li><strong>Status Dashboard</strong>: Visual progress indicators</li>
          <li><strong>Employee Count</strong>: Total employees in pay run</li>
          <li><strong>Processing Statistics</strong>: Completed vs pending payslips</li>
          <li><strong>Error Summary</strong>: Outstanding issues to resolve</li>
          <li><strong>Timeline</strong>: Key dates and deadlines</li>
        </ul>

        <h4>Exception Management</h4>
        <p>Handle payroll exceptions and errors:</p>
        <ul>
          <li><strong>Calculation Errors</strong>: Tax or deduction calculation issues</li>
          <li><strong>Missing Data</strong>: Incomplete employee information</li>
          <li><strong>Validation Failures</strong>: Data validation errors</li>
          <li><strong>Approval Issues</strong>: Payslips requiring special approval</li>
        </ul>

        <h3>Pay Run Calculations</h3>

        <h4>Automatic Calculations</h4>
        <p>System automatically calculates:</p>
        <ul>
          <li><strong>Basic Salary</strong>: Monthly or hourly rate calculations</li>
          <li><strong>Overtime Pay</strong>: Time-and-a-half and double-time</li>
          <li><strong>PAYE Tax</strong>: Income tax based on SARS tables</li>
          <li><strong>UIF Contributions</strong>: Employee and employer UIF</li>
          <li><strong>SDL Levy</strong>: Skills Development Levy</li>
          <li><strong>Other Deductions</strong>: Medical aid, pension, loans</li>
        </ul>

        <h4>Pro-rata Calculations</h4>
        <p>Handle partial period calculations:</p>
        <ul>
          <li><strong>New Employees</strong>: Pro-rata for partial months</li>
          <li><strong>Terminated Employees</strong>: Final pay calculations</li>
          <li><strong>Leave Without Pay</strong>: Deduct unpaid leave days</li>
          <li><strong>Salary Changes</strong>: Mid-period salary adjustments</li>
        </ul>

        <h3>Payment Processing</h3>

        <h4>EFT File Generation</h4>
        <ol>
          <li>Finalize pay run</li>
          <li>Click <strong>Generate EFT File</strong></li>
          <li>Select bank format</li>
          <li>Set payment date</li>
          <li>Generate banking file</li>
          <li>Download file for bank submission</li>
        </ol>

        <h4>Payment Confirmation</h4>
        <ol>
          <li>Submit EFT file to bank</li>
          <li>Receive bank confirmation file</li>
          <li>Upload confirmation to system</li>
          <li>System matches payments</li>
          <li>Update pay run status to "Paid"</li>
        </ol>

        <h3>Pay Run Reporting</h3>

        <h4>Summary Reports</h4>
        <ul>
          <li><strong>Pay Run Summary</strong>: Total costs and employee count</li>
          <li><strong>Department Summary</strong>: Costs by department</li>
          <li><strong>Tax Summary</strong>: PAYE, UIF, SDL totals</li>
          <li><strong>Payment Summary</strong>: Net pay and EFT totals</li>
        </ul>

        <h4>Detailed Reports</h4>
        <ul>
          <li><strong>Payslip Register</strong>: All payslips in pay run</li>
          <li><strong>Exception Report</strong>: Errors and exceptions</li>
          <li><strong>Variance Report</strong>: Changes from previous period</li>
          <li><strong>Audit Report</strong>: Complete processing audit trail</li>
        </ul>

        <h3>Multi-Frequency Pay Runs</h3>

        <h4>Monthly Pay Runs</h4>
        <ul>
          <li><strong>Salaried Employees</strong>: Monthly salary payments</li>
          <li><strong>Month-End Processing</strong>: Typically processed end of month</li>
          <li><strong>Full Benefits</strong>: Complete benefit deductions</li>
          <li><strong>Annual Calculations</strong>: Tax calculations based on annual salary</li>
        </ul>

        <h4>Weekly Pay Runs</h4>
        <ul>
          <li><strong>Hourly Employees</strong>: Weekly wage payments</li>
          <li><strong>Variable Hours</strong>: Based on actual hours worked</li>
          <li><strong>Overtime Calculations</strong>: Weekly overtime calculations</li>
          <li><strong>Pro-rata Benefits</strong>: Weekly portion of benefits</li>
        </ul>

        <h4>Bi-weekly Pay Runs</h4>
        <ul>
          <li><strong>Fortnightly Payments</strong>: Every two weeks</li>
          <li><strong>26 Pay Periods</strong>: 26 pay periods per year</li>
          <li><strong>Consistent Dates</strong>: Same day every two weeks</li>
          <li><strong>Adjusted Calculations</strong>: Benefits calculated bi-weekly</li>
        </ul>

        <h3>Year-End Processing</h3>

        <h4>13th Cheque Pay Run</h4>
        <ol>
          <li>Create special pay run for 13th cheque</li>
          <li>Calculate 13th cheque amounts</li>
          <li>Apply appropriate tax calculations</li>
          <li>Process as separate payment</li>
          <li>Include in annual tax certificates</li>
        </ol>

        <h4>Final Pay Runs</h4>
        <ul>
          <li><strong>December Processing</strong>: Final pay run of tax year</li>
          <li><strong>Annual Reconciliation</strong>: Reconcile annual amounts</li>
          <li><strong>Tax Certificate Preparation</strong>: Prepare IRP5 certificates</li>
          <li><strong>Leave Payouts</strong>: Process leave encashments</li>
        </ul>

        <h3>Best Practices</h3>
        <ul>
          <li>Establish consistent pay run schedules</li>
          <li>Implement approval workflows</li>
          <li>Regular backup of pay run data</li>
          <li>Monitor processing deadlines</li>
          <li>Maintain detailed audit trails</li>
          <li>Test calculations before finalizing</li>
          <li>Communicate pay dates to employees</li>
          <li>Plan for public holidays and month-end</li>
        </ul>

        <h3>Troubleshooting</h3>
        <ul>
          <li><strong>Calculation Errors</strong>: Review employee setup and rates</li>
          <li><strong>Missing Employees</strong>: Check employee status and selection criteria</li>
          <li><strong>Payment Failures</strong>: Verify banking details and EFT format</li>
          <li><strong>Performance Issues</strong>: Process smaller batches for large pay runs</li>
          <li><strong>Approval Delays</strong>: Set up automated approval workflows</li>
        </ul>
      </div>
    `;
  }

  getContent(sectionId) {
    console.log('HelpContent.getContent called for:', sectionId);
    console.log('Content exists:', !!this.content[sectionId]);
    return this.content[sectionId] || this.getDefaultContent(sectionId);
  }

  getDefaultContent(sectionId) {
    return `
      <div class="help-article active">
        <h2>Content Coming Soon</h2>
        <p>We're working on creating comprehensive documentation for this section.</p>
        <p>In the meantime, please <a href="mailto:<EMAIL>">contact our support team</a> for assistance with <strong>${sectionId.replace('-', ' ')}</strong>.</p>
        
        <h3>What You Can Do</h3>
        <ul>
          <li>Use the search function to find related topics</li>
          <li>Browse other help sections for similar information</li>
          <li>Contact support for immediate assistance</li>
          <li>Check back soon for updated content</li>
        </ul>
      </div>
    `;
  }
}

// Initialize content when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  console.log('Initializing HelpContent...');
  window.helpContent = new HelpContent();
  console.log('HelpContent initialized:', !!window.helpContent);
});
