// PandaPayroll Help Center - Search Functionality

class HelpSearch {
  constructor() {
    this.searchInput = document.getElementById('helpSearch');
    this.searchResults = document.getElementById('searchResults');
    this.searchData = [];
    this.debounceTimer = null;
    
    this.init();
    this.loadSearchData();
  }

  init() {
    if (!this.searchInput || !this.searchResults) return;

    // Add event listeners
    this.searchInput.addEventListener('input', (e) => this.handleSearch(e));
    this.searchInput.addEventListener('focus', () => this.showResults());
    this.searchInput.addEventListener('blur', (e) => this.hideResults(e));
    
    // Handle keyboard navigation
    this.searchInput.addEventListener('keydown', (e) => this.handleKeyNavigation(e));
    
    // Close search results when clicking outside
    document.addEventListener('click', (e) => {
      if (!e.target.closest('.search-container')) {
        this.hideResults();
      }
    });
  }

  loadSearchData() {
    // Comprehensive search data for all help topics
    this.searchData = [
      // Getting Started
      { title: 'Free Trial Setup', category: 'Getting Started', description: 'How to start your free trial and set up your account', url: '#free-trial' },
      { title: 'Setup Checklist', category: 'Getting Started', description: 'Complete checklist for setting up PandaPayroll', url: '#setup-checklist' },
      { title: 'General Setup', category: 'Getting Started', description: 'Basic configuration and initial setup steps', url: '#general-setup' },
      { title: 'Company Management', category: 'Getting Started', description: 'Managing multiple companies and switching between them', url: '#company-management' },
      { title: 'User Management', category: 'Getting Started', description: 'Adding and managing user accounts and permissions', url: '#user-management' },

      // Payroll Setup
      { title: 'Company Setup', category: 'Payroll Setup', description: 'Configure company details, tax settings, and preferences', url: '#company-setup' },
      { title: 'Employee Setup', category: 'Payroll Setup', description: 'Add employees, set up pay frequencies, and configure details', url: '#employee-setup' },
      { title: 'Dashboard Features', category: 'Payroll Setup', description: 'Understanding the main dashboard and navigation', url: '#dashboard-features' },

      // Payroll Processing
      { title: 'Payslip Creation', category: 'Payroll Processing', description: 'Creating and managing employee payslips', url: '#payslip-creation' },
      { title: 'Employee Hours', category: 'Payroll Processing', description: 'Recording and managing employee working hours', url: '#employee-hours' },
      { title: 'Pay Runs', category: 'Payroll Processing', description: 'Creating and finalizing pay runs for multiple employees', url: '#pay-runs' },
      { title: 'System Items', category: 'Payroll Processing', description: 'Managing benefits, income, deductions, and allowances', url: '#system-items' },

      // Payroll Concepts
      { title: 'Pay Calculations', category: 'Payroll Concepts', description: 'Understanding how pay is calculated in South Africa', url: '#pay-calculations' },
      { title: 'Statutory Deductions', category: 'Payroll Concepts', description: 'PAYE, UIF, SDL and other South African deductions', url: '#statutory-deductions' },
      { title: 'Tax Directives', category: 'Payroll Concepts', description: 'Managing SARS tax directives and custom tax rates', url: '#tax-directives' },
      { title: 'Cost-to-Company', category: 'Payroll Concepts', description: 'Understanding and calculating cost-to-company packages', url: '#cost-to-company' },

      // Bulk Operations
      { title: 'Employee Actions', category: 'Bulk Operations', description: 'Bulk actions for multiple employees', url: '#employee-actions' },
      { title: 'Payslip Management', category: 'Bulk Operations', description: 'Bulk payslip operations and management', url: '#payslip-management' },
      { title: 'Excel Imports', category: 'Bulk Operations', description: 'Importing employee data and payroll information from Excel', url: '#excel-imports' },
      { title: 'Bulk Inputs', category: 'Bulk Operations', description: 'Bulk input of hours, bonuses, and deductions', url: '#bulk-inputs' },

      // Leave Management
      { title: 'Leave Types', category: 'Leave Management', description: 'Setting up and managing different types of leave', url: '#leave-types' },
      { title: 'Leave Entitlements', category: 'Leave Management', description: 'Configuring employee leave entitlements and balances', url: '#leave-entitlements' },
      { title: 'Recording Leave', category: 'Leave Management', description: 'How to record and approve employee leave', url: '#leave-recording' },
      { title: 'Leave Adjustments', category: 'Leave Management', description: 'Making adjustments to leave balances and records', url: '#leave-adjustments' },
      { title: 'Leave Self-Service', category: 'Leave Management', description: 'Employee self-service leave requests and approvals', url: '#leave-self-service' },

      // Filing & Compliance
      { title: 'Monthly Submissions', category: 'Filing & Compliance', description: 'EMP201 monthly submissions to SARS', url: '#monthly-submissions' },
      { title: 'Bi-annual Filing', category: 'Filing & Compliance', description: 'EMP501 bi-annual reconciliation submissions', url: '#biannual-filing' },
      { title: 'OID Returns', category: 'Filing & Compliance', description: 'Other Income Deduction returns and submissions', url: '#oid-returns' },
      { title: 'SARS Processes', category: 'Filing & Compliance', description: 'Understanding SARS compliance and processes', url: '#sars-processes' },

      // Reports
      { title: 'Employee Reports', category: 'Reports', description: 'Generating employee information and payroll reports', url: '#employee-reports' },
      { title: 'ETI Reports', category: 'Reports', description: 'Employment Tax Incentive reporting and management', url: '#eti-reports' },
      { title: 'Leave Reports', category: 'Reports', description: 'Leave balance and usage reports', url: '#leave-reports' },
      { title: 'Transaction History', category: 'Reports', description: 'Viewing and exporting transaction history', url: '#transaction-history' },
      { title: 'Custom Reports', category: 'Reports', description: 'Creating custom reports and exports', url: '#custom-reports' },

      // Integrations
      { title: 'QuickBooks Integration', category: 'Integrations', description: 'Connecting PandaPayroll with QuickBooks Online', url: '#quickbooks-integration' },
      { title: 'Xero Integration', category: 'Integrations', description: 'Connecting PandaPayroll with Xero Accounting', url: '#xero-integration' },
      { title: 'Time & Attendance', category: 'Integrations', description: 'Integrating with time and attendance systems', url: '#time-attendance' },

      // Security
      { title: 'Two-Factor Authentication', category: 'Security', description: 'Setting up and managing 2FA for your account', url: '#two-factor-auth' },
      { title: 'Logout Settings', category: 'Security', description: 'Configuring automatic logout and session management', url: '#logout-settings' },
      { title: 'Support Access', category: 'Security', description: 'Managing support team access to your account', url: '#support-access' },

      // Employment Equity
      { title: 'EE Terminology', category: 'Employment Equity', description: 'Understanding Employment Equity terminology and requirements', url: '#ee-terminology' },
      { title: 'EE Reporting', category: 'Employment Equity', description: 'Employment Equity reporting and compliance', url: '#ee-reporting' },
      { title: 'EE Submissions', category: 'Employment Equity', description: 'Submitting Employment Equity reports', url: '#ee-submissions' },

      // Employment Tax Incentive
      { title: 'ETI Qualification', category: 'Employment Tax Incentive', description: 'Understanding ETI qualification criteria', url: '#eti-qualification' },
      { title: 'ETI Calculations', category: 'Employment Tax Incentive', description: 'How ETI is calculated and applied', url: '#eti-calculations' },
      { title: 'ETI Management', category: 'Employment Tax Incentive', description: 'Managing ETI for employees and reporting', url: '#eti-management' },

      // Self-Service
      { title: 'Admin Portal', category: 'Self-Service', description: 'Using the administrator self-service portal', url: '#admin-portal' },
      { title: 'Employee Portal', category: 'Self-Service', description: 'Employee self-service portal features and access', url: '#employee-portal' },
      { title: 'Payslip Access', category: 'Self-Service', description: 'How employees can access their payslips online', url: '#payslip-access' },
      { title: 'Tax Certificates', category: 'Self-Service', description: 'Accessing and downloading tax certificates', url: '#tax-certificates' },
      { title: 'Employee Requests', category: 'Self-Service', description: 'Managing employee requests and approvals', url: '#employee-requests' },

      // Billing
      { title: 'Payment Methods', category: 'Billing', description: 'Setting up and managing payment methods', url: '#payment-methods' },
      { title: 'Billing Statements', category: 'Billing', description: 'Viewing and downloading billing statements', url: '#billing-statements' },
      { title: 'Account Management', category: 'Billing', description: 'Managing your account and subscription', url: '#account-management' },

      // Troubleshooting
      { title: 'Login Issues', category: 'Troubleshooting', description: 'Resolving login and access problems', url: '#login-issues' },
      { title: 'Payslip Problems', category: 'Troubleshooting', description: 'Fixing common payslip calculation issues', url: '#payslip-problems' },
      { title: 'Email Delivery', category: 'Troubleshooting', description: 'Troubleshooting email delivery issues', url: '#email-delivery' },
      { title: 'Bulk Upload Errors', category: 'Troubleshooting', description: 'Resolving bulk upload and import errors', url: '#bulk-upload-errors' },

      // Common search terms
      { title: 'PAYE Calculation', category: 'Payroll Concepts', description: 'How PAYE tax is calculated in South Africa', url: '#statutory-deductions' },
      { title: 'UIF Calculation', category: 'Payroll Concepts', description: 'Understanding UIF calculations and limits', url: '#statutory-deductions' },
      { title: 'SDL Calculation', category: 'Payroll Concepts', description: 'Skills Development Levy calculation and compliance', url: '#statutory-deductions' },
      { title: 'EMP201 Submission', category: 'Filing & Compliance', description: 'Monthly EMP201 submissions to SARS', url: '#monthly-submissions' },
      { title: 'EMP501 Submission', category: 'Filing & Compliance', description: 'Bi-annual EMP501 reconciliation', url: '#biannual-filing' },
      { title: 'IRP5 Certificates', category: 'Self-Service', description: 'Generating and accessing IRP5 tax certificates', url: '#tax-certificates' },
      { title: 'Payroll Calendar', category: 'Payroll Processing', description: 'Managing payroll calendar and deadlines', url: '#pay-runs' },
      { title: 'Employee Onboarding', category: 'Payroll Setup', description: 'Adding new employees to the system', url: '#employee-setup' },
      { title: 'Salary Adjustments', category: 'Payroll Processing', description: 'Making salary and wage adjustments', url: '#payslip-creation' },
      { title: 'Overtime Calculation', category: 'Payroll Concepts', description: 'Calculating overtime pay and rates', url: '#pay-calculations' }
    ];
  }

  handleSearch(e) {
    const query = e.target.value.trim();
    
    // Clear previous timer
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }
    
    // Debounce search to avoid too many requests
    this.debounceTimer = setTimeout(() => {
      if (query.length >= 2) {
        this.performSearch(query);
      } else {
        this.hideResults();
      }
    }, 300);
  }

  performSearch(query) {
    const results = this.searchData.filter(item => {
      const searchText = `${item.title} ${item.description} ${item.category}`.toLowerCase();
      return searchText.includes(query.toLowerCase());
    });

    this.displayResults(results, query);
  }

  displayResults(results, query) {
    if (results.length === 0) {
      this.searchResults.innerHTML = `
        <div class="search-result-item">
          <div class="search-result-title">No results found</div>
          <div class="search-result-description">Try searching with different keywords</div>
        </div>
      `;
    } else {
      this.searchResults.innerHTML = results.slice(0, 8).map(result => `
        <div class="search-result-item" data-url="${result.url}">
          <div class="search-result-category">${result.category}</div>
          <div class="search-result-title">${this.highlightText(result.title, query)}</div>
          <div class="search-result-description">${this.highlightText(result.description, query)}</div>
        </div>
      `).join('');

      // Add click handlers to results
      this.searchResults.querySelectorAll('.search-result-item').forEach(item => {
        item.addEventListener('click', (e) => {
          const url = e.currentTarget.dataset.url;
          this.navigateToResult(url);
          this.hideResults();
        });
      });
    }

    this.showResults();
  }

  highlightText(text, query) {
    if (!query) return text;
    
    const regex = new RegExp(`(${query})`, 'gi');
    return text.replace(regex, '<mark>$1</mark>');
  }

  navigateToResult(url) {
    // Remove the hash and navigate to the section
    const sectionId = url.replace('#', '');
    
    // Trigger navigation event
    if (window.helpNavigation) {
      window.helpNavigation.navigateToSection(sectionId);
    }
    
    // Clear search
    this.searchInput.value = '';
  }

  showResults() {
    if (this.searchResults.innerHTML.trim()) {
      this.searchResults.style.display = 'block';
    }
  }

  hideResults(e) {
    // Don't hide if clicking within search results
    if (e && e.relatedTarget && e.relatedTarget.closest('.search-results')) {
      return;
    }
    
    setTimeout(() => {
      this.searchResults.style.display = 'none';
    }, 150);
  }

  handleKeyNavigation(e) {
    const items = this.searchResults.querySelectorAll('.search-result-item');
    const currentActive = this.searchResults.querySelector('.search-result-item.active');
    let activeIndex = -1;

    if (currentActive) {
      activeIndex = Array.from(items).indexOf(currentActive);
    }

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        activeIndex = Math.min(activeIndex + 1, items.length - 1);
        this.setActiveResult(items, activeIndex);
        break;
      
      case 'ArrowUp':
        e.preventDefault();
        activeIndex = Math.max(activeIndex - 1, 0);
        this.setActiveResult(items, activeIndex);
        break;
      
      case 'Enter':
        e.preventDefault();
        if (currentActive) {
          currentActive.click();
        }
        break;
      
      case 'Escape':
        this.hideResults();
        this.searchInput.blur();
        break;
    }
  }

  setActiveResult(items, index) {
    // Remove active class from all items
    items.forEach(item => item.classList.remove('active'));
    
    // Add active class to selected item
    if (items[index]) {
      items[index].classList.add('active');
      items[index].scrollIntoView({ block: 'nearest' });
    }
  }
}

// Initialize search when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.helpSearch = new HelpSearch();
});

// Add CSS for search highlighting
const style = document.createElement('style');
style.textContent = `
  .search-result-item.active {
    background-color: var(--background-color);
  }
  
  .search-result-item mark {
    background-color: #fef08a;
    color: #854d0e;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
  }
`;
document.head.appendChild(style);
