<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Helios.io ↔ PandaPayroll Integration Overview</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 20px;
            text-align: center;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .integration-table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .table-header {
            background: #2c3e50;
            color: white;
            padding: 20px;
            font-size: 1.3rem;
            font-weight: 600;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
            font-size: 0.95rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        td {
            font-size: 0.9rem;
        }

        .method-push {
            background-color: #d4edda;
            color: #155724;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .method-pull {
            background-color: #cce5ff;
            color: #004085;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .method-push-pull {
            background-color: #fff3cd;
            color: #856404;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .api-endpoint {
            font-family: 'Courier New', monospace;
            background-color: #f8f9fa;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.85rem;
            color: #495057;
        }

        .description {
            color: #6c757d;
            font-style: italic;
        }

        .section-divider {
            background: linear-gradient(90deg, #667eea, #764ba2);
            height: 3px;
            margin: 30px 0;
            border-radius: 2px;
        }

        .footer {
            text-align: center;
            padding: 30px;
            color: #6c757d;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .footer a {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
        }

        .footer a:hover {
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            table {
                font-size: 0.8rem;
            }
            
            th, td {
                padding: 10px 8px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Helios.io ↔ PandaPayroll Integration</h1>
            <p>Complete API Integration Overview for South African Payroll Processing</p>
            <div style="margin-top: 20px; padding: 20px; background: rgba(255,255,255,0.1); border-radius: 8px;">
                <h3 style="margin-bottom: 10px;">🚀 Quick Integration Summary</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; text-align: left;">
                    <div>
                        <strong>📤 Outbound (5 endpoints)</strong><br>
                        Employee data, payroll inputs, cost centers
                    </div>
                    <div>
                        <strong>⚙️ Processing Engine</strong><br>
                        SARS-compliant PAYE, UIF, SDL calculations
                    </div>
                    <div>
                        <strong>📥 Inbound (5 endpoints)</strong><br>
                        Payslips, journals, EFT files, webhooks
                    </div>
                    <div>
                        <strong>🔐 Security</strong><br>
                        JWT auth, webhook signatures, TLS 1.3
                    </div>
                </div>
            </div>
        </div>

        <!-- Outbound Integration Section -->
        <div class="integration-table">
            <div class="table-header">
                📤 Outbound from Helios → PandaPayroll
            </div>
            <table>
                <thead>
                    <tr>
                        <th>Integration Flow</th>
                        <th>Method</th>
                        <th>Push/Pull Ability</th>
                        <th>API Endpoint</th>
                        <th>Description</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>Employee Master Data</strong></td>
                        <td><span class="api-endpoint">POST/PUT/PATCH</span></td>
                        <td><span class="method-push">Push</span></td>
                        <td><span class="api-endpoint">/api/helios/employees/batch</span></td>
                        <td class="description">Sync employee information from Helios to PandaPayroll</td>
                    </tr>
                    <tr>
                        <td><strong>Employee Delta (ongoing changes)</strong></td>
                        <td><span class="api-endpoint">PATCH/PUT</span></td>
                        <td><span class="method-push">Push</span></td>
                        <td><span class="api-endpoint">/api/employees/{employee_id}</span></td>
                        <td class="description">Update existing employee information</td>
                    </tr>
                    <tr>
                        <td><strong>Compensation & one-time payments</strong></td>
                        <td><span class="api-endpoint">POST/PUT/PATCH</span></td>
                        <td><span class="method-push">Push</span></td>
                        <td><span class="api-endpoint">/api/helios/payroll/inputs</span></td>
                        <td class="description">Submit compensation adjustments and bonuses</td>
                    </tr>
                    <tr>
                        <td><strong>Payroll inputs (hours, bonuses, benefits)</strong></td>
                        <td><span class="api-endpoint">POST (batch)</span></td>
                        <td><span class="method-push">Push</span></td>
                        <td><span class="api-endpoint">/api/helios/payroll/inputs</span></td>
                        <td class="description">Submit payroll inputs for processing</td>
                    </tr>
                    <tr>
                        <td><strong>Cost center / GL mappings</strong></td>
                        <td><span class="api-endpoint">PUT/PATCH, GET</span></td>
                        <td><span class="method-push-pull">Push (updates), Pull (lookup)</span></td>
                        <td><span class="api-endpoint">/api/companies/{code}/cost-centers</span></td>
                        <td class="description">Sync cost center and general ledger mappings</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="section-divider"></div>

        <!-- Payroll Engine Section -->
        <div class="integration-table">
            <div class="table-header">
                ⚙️ Payroll Engine: Gross-to-Net Calculation
            </div>
            <table>
                <thead>
                    <tr>
                        <th>Calculation Component</th>
                        <th>Method</th>
                        <th>Push/Pull Ability</th>
                        <th>API Endpoint</th>
                        <th>Description</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>Country-specific rules (tax, social security)</strong></td>
                        <td><span class="api-endpoint">POST</span></td>
                        <td><span class="method-push-pull">Push/Pull (Async)</span></td>
                        <td><span class="api-endpoint">/api/helios/payroll/calculate</span></td>
                        <td class="description">SARS-compliant PAYE, UIF calculations with webhook notifications</td>
                    </tr>
                    <tr>
                        <td><strong>Employer contributions</strong></td>
                        <td><span class="api-endpoint">Automatic</span></td>
                        <td><span class="method-push-pull">Calculated</span></td>
                        <td><span class="api-endpoint">Included in calculation</span></td>
                        <td class="description">UIF (1% employer), SDL (1% payroll), Workmen's Comp</td>
                    </tr>
                    <tr>
                        <td><strong>Deductions (pre/post tax)</strong></td>
                        <td><span class="api-endpoint">Automatic</span></td>
                        <td><span class="method-push-pull">Calculated</span></td>
                        <td><span class="api-endpoint">Included in calculation</span></td>
                        <td class="description">Pension funds, medical aid, garnishee orders, maintenance</td>
                    </tr>
                    <tr>
                        <td><strong>Net pay calculation</strong></td>
                        <td><span class="api-endpoint">Automatic</span></td>
                        <td><span class="method-push-pull">Calculated</span></td>
                        <td><span class="api-endpoint">Final result</span></td>
                        <td class="description">Gross Pay - PAYE - UIF - Other Deductions = Net Pay</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="section-divider"></div>

        <!-- Inbound Integration Section -->
        <div class="integration-table">
            <div class="table-header">
                📥 Inbound to Helios ← PandaPayroll
            </div>
            <table>
                <thead>
                    <tr>
                        <th>Integration Flow</th>
                        <th>Method</th>
                        <th>Push/Pull Ability</th>
                        <th>API Endpoint</th>
                        <th>Description</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>Payslip breakdown</strong></td>
                        <td><span class="api-endpoint">Webhook + GET</span></td>
                        <td><span class="method-pull">Pull (details)</span></td>
                        <td><span class="api-endpoint">/api/helios/payroll/results/{periodId}</span></td>
                        <td class="description">Detailed payslip information with earnings and deductions</td>
                    </tr>
                    <tr>
                        <td><strong>Net pay summary</strong></td>
                        <td><span class="api-endpoint">Webhook + GET</span></td>
                        <td><span class="method-pull">Pull (details)</span></td>
                        <td><span class="api-endpoint">/api/pay-runs/{pay_run_id}</span></td>
                        <td class="description">Summarized net pay information for multiple employees</td>
                    </tr>
                    <tr>
                        <td><strong>Employer cost allocation</strong></td>
                        <td><span class="api-endpoint">Webhook + GET</span></td>
                        <td><span class="method-pull">Pull (details)</span></td>
                        <td><span class="api-endpoint">/api/helios/payroll/results/{periodId}</span></td>
                        <td class="description">Employer cost breakdown for accounting (UIF, SDL, total costs)</td>
                    </tr>
                    <tr>
                        <td><strong>Accounting journals (GL)</strong></td>
                        <td><span class="api-endpoint">Webhook + GET</span></td>
                        <td><span class="method-pull">Pull (details)</span></td>
                        <td><span class="api-endpoint">/api/accounting/journals/{pay_run_id}</span></td>
                        <td class="description">General ledger entries for accounting system integration</td>
                    </tr>
                    <tr>
                        <td><strong>Bank file details (optional)</strong></td>
                        <td><span class="api-endpoint">Webhook/SFTP</span></td>
                        <td><span class="method-pull">Pull (file)</span></td>
                        <td><span class="api-endpoint">/api/pay-runs/{pay_run_id}/eft-file</span></td>
                        <td class="description">EFT files for payment processing in SA banking format</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Webhook Events Section -->
        <div class="integration-table">
            <div class="table-header">
                🔔 Webhook Events & Notifications
            </div>
            <table>
                <thead>
                    <tr>
                        <th>Event Type</th>
                        <th>Trigger</th>
                        <th>Payload Includes</th>
                        <th>Use Case</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>payslip.finalized</strong></td>
                        <td>When individual payslip is finalized</td>
                        <td>Employee ID, Period ID, Net Pay, Status</td>
                        <td class="description">Notify Helios of completed payslip calculations</td>
                    </tr>
                    <tr>
                        <td><strong>payrun.created</strong></td>
                        <td>When pay run is created</td>
                        <td>Pay Run ID, Company Code, Period</td>
                        <td class="description">Initiate pay run workflow in Helios</td>
                    </tr>
                    <tr>
                        <td><strong>payrun.finalized</strong></td>
                        <td>When entire pay run is finalized</td>
                        <td>Pay Run ID, Total Employees, Total Net Pay</td>
                        <td class="description">Trigger payment processing and reporting</td>
                    </tr>
                    <tr>
                        <td><strong>payroll.calculated</strong></td>
                        <td>When payroll calculations complete</td>
                        <td>Calculation Results, Employee Count</td>
                        <td class="description">Async notification of calculation completion</td>
                    </tr>
                    <tr>
                        <td><strong>eft.file_generated</strong></td>
                        <td>When EFT file is ready</td>
                        <td>File URL, Pay Run ID, Bank Format</td>
                        <td class="description">Download link for payment file processing</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="section-divider"></div>

        <!-- South African Compliance Section -->
        <div class="integration-table">
            <div class="table-header">
                🇿🇦 South African Payroll Compliance Features
            </div>
            <table>
                <thead>
                    <tr>
                        <th>Compliance Area</th>
                        <th>Feature</th>
                        <th>Rate/Calculation</th>
                        <th>Compliance Standard</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>PAYE Tax</strong></td>
                        <td>Progressive tax calculation</td>
                        <td>SARS tax tables (2024/2025)</td>
                        <td class="description">Automatic rebates: Primary (R17,235), Secondary (R9,444)</td>
                    </tr>
                    <tr>
                        <td><strong>UIF Contributions</strong></td>
                        <td>Employee + Employer UIF</td>
                        <td>1% each (capped at R177.12/month)</td>
                        <td class="description">Department of Employment and Labour compliance</td>
                    </tr>
                    <tr>
                        <td><strong>SDL Levy</strong></td>
                        <td>Skills Development Levy</td>
                        <td>1% of payroll (>R500k annually)</td>
                        <td class="description">SETA compliance for skills development</td>
                    </tr>
                    <tr>
                        <td><strong>Medical Aid Credits</strong></td>
                        <td>Tax credits for medical aid</td>
                        <td>R347/member, R234/dependant</td>
                        <td class="description">SARS medical scheme tax credit rates</td>
                    </tr>
                    <tr>
                        <td><strong>Fringe Benefits</strong></td>
                        <td>Company car, accommodation</td>
                        <td>SARS fringe benefit rates</td>
                        <td class="description">Seventh Schedule compliance</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="section-divider"></div>

        <!-- Authentication & Security Section -->
        <div class="integration-table">
            <div class="table-header">
                🔐 Authentication & Security
            </div>
            <table>
                <thead>
                    <tr>
                        <th>Security Feature</th>
                        <th>Implementation</th>
                        <th>Endpoint</th>
                        <th>Details</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>JWT Authentication</strong></td>
                        <td>Bearer token authentication</td>
                        <td><span class="api-endpoint">/api/auth/login</span></td>
                        <td class="description">Secure API access with JWT tokens</td>
                    </tr>
                    <tr>
                        <td><strong>Webhook Security</strong></td>
                        <td>HMAC-SHA256 signatures</td>
                        <td><span class="api-endpoint">X-PandaPayroll-Signature</span></td>
                        <td class="description">Verify webhook authenticity with secret key</td>
                    </tr>
                    <tr>
                        <td><strong>Rate Limiting</strong></td>
                        <td>API request throttling</td>
                        <td><span class="api-endpoint">All API endpoints</span></td>
                        <td class="description">Prevent abuse with exponential backoff</td>
                    </tr>
                    <tr>
                        <td><strong>Data Encryption</strong></td>
                        <td>TLS 1.3 encryption</td>
                        <td><span class="api-endpoint">HTTPS only</span></td>
                        <td class="description">End-to-end encryption for all communications</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="footer">
            <p><strong>PandaPayroll Integration Hub</strong></p>
            <p>For technical support: <a href="mailto:<EMAIL>"><EMAIL></a></p>
            <p>Complete documentation: <a href="/helios-integration-guide">View Full Integration Guide</a></p>
            <p>Sandbox Environment: <a href="/sandbox">Test Integration</a> | <a href="https://payroll.pss-group.co.za/sandbox">Production Sandbox</a></p>
        </div>
    </div>
</body>
</html>
