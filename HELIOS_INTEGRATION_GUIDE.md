# Helios.io ↔ PandaPayroll Integration Guide

## Overview

This document outlines the complete integration between **Helios.io** (HR Management System) and **PandaPayroll** (https://payroll.pss-group.co.za/) as the payroll engine for South African businesses.

## Integration Architecture

```
┌─────────────────┐    API Calls     ┌─────────────────┐
│                 │ ──────────────► │                 │
│   Helios.io     │                 │  PandaPayroll   │
│ (HR Management) │ ◄────────────── │ (Payroll Engine)│
│                 │   Webhooks      │                 │
└─────────────────┘                 └─────────────────┘
```

## Table of Contents

- [Authentication](#authentication)
- [Outbound Integration (Helios → PandaPayroll)](#outbound-integration-helios--pandapayroll)
- [Payroll Engine Processing](#payroll-engine-processing)
- [Inbound Integration (PandaPayroll → Helios)](#inbound-integration-pandapayroll--helios)
- [Webhook Configuration](#webhook-configuration)
- [Error Handling](#error-handling)
- [Testing Environment](#testing-environment)
- [Production Deployment](#production-deployment)

## Authentication

### Initial Setup

1. **Create API User Account** in PandaPayroll
2. **Generate JWT Token** for secure API access
3. **Configure Webhook Endpoints** for real-time notifications

### Authentication Flow

```bash
# Step 1: Obtain JWT Token
POST https://payroll.pss-group.co.za/api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "secure_api_password"
}

# Response
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "user_id",
    "email": "<EMAIL>"
  }
}
```

### Using the Token

All subsequent API calls must include the JWT token in the Authorization header:

```bash
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## Outbound Integration (Helios → PandaPayroll)

### 1. Employee Master Data Sync

**Purpose**: Sync employee information from Helios to PandaPayroll

**Endpoint**: `POST /api/helios/employees/batch`

**Method**: Push (Helios → PandaPayroll)

```bash
POST https://payroll.pss-group.co.za/api/helios/employees/batch
Authorization: Bearer {jwt_token}
Content-Type: application/json

{
  "companyCode": "HELIOS001",
  "employees": [
    {
      "companyEmployeeNumber": "EMP001",
      "firstName": "John",
      "lastName": "Doe",
      "email": "<EMAIL>",
      "dob": "1990-01-15",
      "doa": "2024-01-01",
      "basicSalary": 25000,
      "idType": "rsa",
      "idNumber": "*************",
      "paymentMethod": "EFT",
      "bankDetails": {
        "bank": "First National Bank",
        "accountType": "Current",
        "accountNumber": "**********",
        "branchCode": "250655",
        "accountHolder": "Own"
      },
      "personalDetails": {
        "nationality": "South African",
        "taxResidence": "South Africa"
      }
    }
  ]
}
```

**Response**:
```json
{
  "success": true,
  "message": "Batch employee operation completed",
  "results": {
    "created": [
      {
        "id": "employee_id_1",
        "employeeNumber": "EMP001"
      }
    ],
    "updated": [],
    "errors": []
  }
}
```

### 2. Employee Delta (Ongoing Changes)

**Purpose**: Update existing employee information

**Endpoint**: `PATCH /api/employees/{employee_id}`

**Method**: Push (Helios → PandaPayroll)

```bash
PATCH https://payroll.pss-group.co.za/api/employees/employee_id_1
Authorization: Bearer {jwt_token}
Content-Type: application/json

{
  "employee": {
    "basicSalary": 28000,
    "email": "<EMAIL>",
    "bankDetails": {
      "accountNumber": "**********"
    }
  }
}
```

### 3. Payroll Inputs (Hours, Bonuses, Benefits)

**Purpose**: Submit payroll inputs for processing

**Endpoint**: `POST /api/helios/payroll/inputs`

**Method**: Push (Helios → PandaPayroll)

```bash
POST https://payroll.pss-group.co.za/api/helios/payroll/inputs
Authorization: Bearer {jwt_token}
Content-Type: application/json

{
  "companyCode": "HELIOS001",
  "payrollInputs": [
    {
      "employeeNumber": "EMP001",
      "periodStart": "2025-01-01",
      "periodEnd": "2025-01-31",
      "inputs": {
        "hours": 160,
        "overtime": 10,
        "bonuses": 2000,
        "allowances": 1500,
        "deductions": 500
      }
    },
    {
      "employeeNumber": "EMP002",
      "periodStart": "2025-01-01",
      "periodEnd": "2025-01-31",
      "inputs": {
        "hours": 168,
        "overtime": 0,
        "bonuses": 0,
        "allowances": 800,
        "deductions": 200
      }
    }
  ]
}
```

**Response**:
```json
{
  "success": true,
  "message": "Payroll inputs processed",
  "results": {
    "processed": [
      {
        "employeeNumber": "EMP001",
        "periodId": "period_id_1"
      },
      {
        "employeeNumber": "EMP002",
        "periodId": "period_id_2"
      }
    ],
    "errors": []
  }
}
```

### 4. Cost Center / GL Mappings

**Purpose**: Sync cost center and general ledger mappings

**Endpoint**: `PUT /api/companies/{company_code}/cost-centers`

**Method**: Push (for updates), Pull (for lookup data)

```bash
# Update cost center mappings
PUT https://payroll.pss-group.co.za/api/companies/HELIOS001/cost-centers
Authorization: Bearer {jwt_token}
Content-Type: application/json

{
  "costCenters": [
    {
      "code": "CC001",
      "name": "Sales Department",
      "glAccount": "6000-001",
      "employees": ["EMP001", "EMP002"]
    },
    {
      "code": "CC002", 
      "name": "IT Department",
      "glAccount": "6000-002",
      "employees": ["EMP003", "EMP004"]
    }
  ]
}

# Get cost center lookup data
GET https://payroll.pss-group.co.za/api/companies/HELIOS001/cost-centers
Authorization: Bearer {jwt_token}
```

## Payroll Engine Processing

### Gross-to-Net Calculation

**Purpose**: Calculate South African tax-compliant payroll with full SARS compliance

**Endpoint**: `POST /api/helios/payroll/calculate`

**Method**: Push/Pull (Asynchronous processing with webhook notification)

```bash
POST https://payroll.pss-group.co.za/api/helios/payroll/calculate
Authorization: Bearer {jwt_token}
Content-Type: application/json

{
  "periodIds": ["period_id_1", "period_id_2", "period_id_3"]
}
```

**Response**:
```json
{
  "success": true,
  "message": "Payroll calculations completed",
  "calculations": [
    {
      "periodId": "period_id_1",
      "employee": {
        "id": "employee_id_1",
        "name": "John Doe",
        "employeeNumber": "EMP001"
      },
      "grossPay": 27000,
      "deductions": {
        "paye": 4860,
        "uif": 270,
        "additional": 500
      },
      "employerContributions": {
        "uif": 270,
        "sdl": 270
      },
      "netPay": 21370
    }
  ]
}
```

### Country-Specific Rules (South Africa)

The PandaPayroll engine provides comprehensive South African tax compliance:

#### Tax Calculations
- **PAYE (Pay As You Earn)**: Progressive tax calculation according to current SARS tax tables
- **Age-based rebates**: Primary (R17,235), Secondary (R9,444), Tertiary (R3,145) rebates applied automatically
- **Medical aid tax credits**: R347 per member, R234 per dependant (2024/2025 tax year)
- **Frequency support**: Monthly, weekly, bi-weekly payroll frequencies

#### Social Security Contributions
- **UIF (Unemployment Insurance Fund)**: 1% employee + 1% employer contribution (capped at R177.12 monthly)
- **SDL (Skills Development Levy)**: 1% of payroll for employer contribution (companies with payroll > R500,000)

#### Deductions (Pre/Post Tax)
- **Pre-tax deductions**: Pension fund contributions, retirement annuities, medical aid contributions
- **Post-tax deductions**: Garnishee orders, maintenance orders, union dues, loan repayments

#### Employer Contributions
- **UIF employer contribution**: 1% of gross salary (capped)
- **SDL contribution**: 1% of total payroll
- **Workmen's Compensation**: Industry-specific rates
- **Bargaining Council levies**: Where applicable

#### Net Pay Calculation
Final net pay calculated as:
```
Net Pay = Gross Pay - PAYE - UIF Employee - Other Deductions
```

### Advanced Calculation Features

- **Pro-rata calculations**: Automatic pro-rating for partial periods
- **Overtime calculations**: Standard and double-time rates
- **Allowances and benefits**: Taxable and non-taxable benefit calculations
- **Fringe benefits**: Company car, accommodation, and other fringe benefit taxation
- **Travel allowances**: Fixed and reimbursive travel allowance calculations

## Inbound Integration (PandaPayroll → Helios)

### 1. Payslip Breakdown

**Purpose**: Get detailed payslip information

**Method**: Webhook notification + RESTful API (GET for details)

**Webhook Event**: `payslip.finalized`

**API Endpoint**: `GET /api/helios/payroll/results/{periodId}`

```bash
GET https://payroll.pss-group.co.za/api/helios/payroll/results/period_id_1
Authorization: Bearer {jwt_token}
```

**Response**:
```json
{
  "success": true,
  "payrollResult": {
    "periodId": "period_id_1",
    "employee": {
      "id": "employee_id_1",
      "name": "John Doe",
      "employeeNumber": "EMP001",
      "bankDetails": {
        "bank": "First National Bank",
        "accountNumber": "**********",
        "branchCode": "250655"
      }
    },
    "company": {
      "id": "company_id_1",
      "name": "Demo Company Ltd",
      "code": "HELIOS001"
    },
    "period": {
      "startDate": "2025-01-01",
      "endDate": "2025-01-31"
    },
    "earnings": {
      "basicSalary": 25000,
      "bonuses": 2000,
      "allowances": 1500,
      "grossPay": 28500
    },
    "deductions": {
      "paye": 5130,
      "uif": 285,
      "additional": 500,
      "total": 5915
    },
    "employerContributions": {
      "uif": 285,
      "sdl": 285,
      "total": 570
    },
    "netPay": 22585,
    "status": "finalized",
    "finalizedAt": "2025-01-31T23:59:59.000Z"
  }
}
```

### 2. Net Pay Summary

**Purpose**: Get summarized net pay information for multiple employees

**Method**: Webhook notification + RESTful API

**Webhook Event**: `payrun.finalized`

**API Endpoint**: `GET /api/pay-runs/{pay_run_id}`

```bash
GET https://payroll.pss-group.co.za/api/pay-runs/payrun_id_1
Authorization: Bearer {jwt_token}
```

**Response**:
```json
{
  "success": true,
  "payRun": {
    "id": "payrun_id_1",
    "reference": "PR-2025-01-001",
    "period": {
      "startDate": "2025-01-01",
      "endDate": "2025-01-31"
    },
    "company": {
      "id": "company_id_1",
      "name": "Demo Company Ltd",
      "code": "HELIOS001"
    },
    "status": "finalized",
    "summary": {
      "totalEmployees": 25,
      "totalGrossPay": 625000,
      "totalDeductions": 125000,
      "totalNetPay": 500000,
      "totalEmployerContributions": 12500
    },
    "breakdown": {
      "totalPAYE": 112500,
      "totalUIF": 6250,
      "totalSDL": 6250,
      "totalAdditionalDeductions": 6250
    },
    "finalizedAt": "2025-01-31T23:59:59.000Z"
  }
}
```

### 3. Employer Cost Allocation

**Purpose**: Get employer cost breakdown for accounting

**Method**: Webhook notification + RESTful API

**API Endpoint**: `GET /api/helios/payroll/results/{periodId}`

```bash
GET https://payroll.pss-group.co.za/api/helios/payroll/results/period_id_1
Authorization: Bearer {jwt_token}
```

**Response** (focusing on employer costs):
```json
{
  "success": true,
  "payrollResult": {
    "periodId": "period_id_1",
    "employee": {
      "id": "employee_id_1",
      "name": "John Doe",
      "employeeNumber": "EMP001",
      "costCenter": "CC001"
    },
    "employerContributions": {
      "uif": 285,
      "sdl": 285,
      "workmenCompensation": 142.50,
      "bargainingCouncilLevy": 50,
      "total": 762.50
    },
    "costAllocation": {
      "costCenter": "CC001",
      "glAccount": "6000-001",
      "department": "Sales Department",
      "totalEmployerCost": 29262.50,
      "breakdown": {
        "grossSalary": 28500,
        "employerUIF": 285,
        "employerSDL": 285,
        "workmenComp": 142.50,
        "other": 50
      }
    }
  }
}
```

The response includes detailed `employerContributions` section with:
- **UIF employer contribution**: 1% of gross salary (capped at R177.12)
- **SDL (Skills Development Levy)**: 1% of payroll for companies with annual payroll > R500,000
- **Workmen's Compensation**: Industry-specific rates
- **Bargaining Council levies**: Where applicable
- **Total employer costs**: Sum of all employer contributions

### 4. Accounting Journals (GL)

**Purpose**: Get general ledger entries for accounting system integration

**Method**: Webhook notification + RESTful API

**API Endpoint**: `GET /api/accounting/journals/{pay_run_id}`

```bash
GET https://payroll.pss-group.co.za/api/accounting/journals/payrun_id_1
Authorization: Bearer {jwt_token}
```

**Response**:
```json
{
  "success": true,
  "journals": [
    {
      "account": "6000-001",
      "description": "Salaries - Sales Department",
      "debit": 55000,
      "credit": 0
    },
    {
      "account": "2000-001", 
      "description": "PAYE Payable",
      "debit": 0,
      "credit": 9900
    },
    {
      "account": "2000-002",
      "description": "UIF Payable", 
      "debit": 0,
      "credit": 1100
    }
  ]
}
```

### 5. Bank File Details

**Purpose**: Download EFT files for payment processing

**Method**: Webhook notification with direct download link or SFTP

**API Endpoint**: `GET /api/pay-runs/{pay_run_id}/eft-file`

```bash
GET https://payroll.pss-group.co.za/api/pay-runs/payrun_id_1/eft-file
Authorization: Bearer {jwt_token}
```

**Response**: Binary EFT file download in South African banking format (FNB, Standard Bank, ABSA, etc.)

## Webhook Configuration

### Register Webhook Endpoints

```bash
POST https://payroll.pss-group.co.za/api/webhooks/register
Authorization: Bearer {jwt_token}
Content-Type: application/json

{
  "name": "Helios Payroll Integration",
  "url": "https://helios.io/webhooks/pandapayroll",
  "events": [
    "payslip.finalized",
    "payrun.created", 
    "payrun.finalized",
    "payroll.calculated",
    "employee.created",
    "employee.updated",
    "eft.file_generated"
  ],
  "secret": "webhook_secret_key_for_signature_verification",
  "companyCode": "HELIOS001",
  "active": true
}
```

### Webhook Payload Example

When a payslip is finalized, PandaPayroll sends:

```json
{
  "event": "payslip.finalized",
  "timestamp": "2025-01-31T23:59:59.000Z",
  "data": {
    "payslipId": "payslip_id_1",
    "periodId": "period_id_1", 
    "employeeId": "employee_id_1",
    "employeeNumber": "EMP001",
    "companyCode": "HELIOS001",
    "netPay": 22585,
    "status": "finalized"
  },
  "webhook_id": "webhook_id_1"
}
```

### Webhook Security

All webhooks include HMAC-SHA256 signature verification:

```
X-PandaPayroll-Signature: sha256=calculated_signature
```

Verify the signature using your webhook secret to ensure authenticity.

## Error Handling

### HTTP Status Codes

| Code | Meaning | Action |
|------|---------|--------|
| 200 | Success | Continue processing |
| 201 | Created | Resource created successfully |
| 400 | Bad Request | Check request format and required fields |
| 401 | Unauthorized | Refresh JWT token |
| 403 | Forbidden | Check user permissions |
| 404 | Not Found | Verify resource IDs |
| 429 | Rate Limited | Implement exponential backoff |
| 500 | Server Error | Retry with exponential backoff |

### Retry Logic

Implement exponential backoff for failed requests:

```javascript
const retryRequest = async (requestFn, maxRetries = 3) => {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await requestFn();
    } catch (error) {
      if (attempt === maxRetries) throw error;
      
      const delay = Math.pow(2, attempt) * 1000; // 2s, 4s, 8s
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
};
```

## Testing Environment

### Sandbox Access

**URL**: https://payroll.pss-group.co.za/sandbox

**Test Credentials**:
- Email: `<EMAIL>`
- Password: `demo123`

### Test Company

- **Company Code**: `DEMO001`
- **Company Name**: `Demo Company Ltd`

### Sample Test Flow

1. **Authenticate** with sandbox credentials
2. **Create test employees** using batch API
3. **Submit payroll inputs** for test period
4. **Calculate payroll** and verify results
5. **Test webhook delivery** to your endpoints
6. **Download EFT files** and verify format

## Production Deployment

### Pre-Production Checklist

- [ ] Production API credentials configured
- [ ] Webhook endpoints registered and tested
- [ ] Error handling and retry logic implemented
- [ ] Rate limiting compliance verified
- [ ] Security review completed
- [ ] Data mapping validated
- [ ] Backup and recovery procedures tested

### Go-Live Process

1. **Configure production webhooks**
2. **Migrate employee master data**
3. **Validate first payroll calculation**
4. **Monitor integration health**
5. **Implement ongoing monitoring**

### Support Contacts

- **Technical Support**: <EMAIL>
- **Integration Issues**: <EMAIL>
- **Emergency Contact**: Available 24/7 for production issues

---

*This integration guide provides complete coverage of the Helios.io ↔ PandaPayroll integration requirements. For additional technical details or custom requirements, please contact the PandaPayroll integration team.*
