# PandaPayroll API Documentation

## Table of Contents

- [Introduction](#introduction)
- [Authentication](#authentication)
  - [Example Request with Correct Authentication](#example-request-with-correct-authentication)
- [Available Resources](#available-resources)
- [Companies](#companies)
  - [Get a List of Companies](#get-a-list-of-companies)
- [Employees](#employees)
  - [Get a List of Employees](#get-a-list-of-employees)
  - [Get a Specific Employee](#get-a-specific-employee)
  - [Create an Employee](#create-an-employee)
  - [Employee Attributes](#employee-attributes)
  - [Update an Employee](#update-an-employee)
  - [Delete an Employee](#delete-an-employee)
- [Payroll Periods](#payroll-periods)
  - [Get a List of Payroll Periods](#get-a-list-of-payroll-periods)
  - [Get a Specific Payroll Period](#get-a-specific-payroll-period)
  - [Finalize Payroll Periods](#finalize-payroll-periods)
- [Payslips](#payslips)
  - [Get a List of Payslips for an Employee](#get-a-list-of-payslips-for-an-employee)
  - [Get a Specific Payslip](#get-a-specific-payslip)
  - [Finalize a Payslip](#finalize-a-payslip)
  - [Download Payslip PDF](#download-payslip-pdf)
- [Pay Runs](#pay-runs)
  - [Get a List of Pay Runs](#get-a-list-of-pay-runs)
  - [Create a Pay Run](#create-a-pay-run)
  - [Get Pay Run Details](#get-pay-run-details)
- [Leave Management](#leave-management)
  - [Get Employee Leave Balances](#get-employee-leave-balances)
  - [Get a List of Leave Types](#get-a-list-of-leave-types)
  - [Create a Leave Request](#create-a-leave-request)
  - [Update a Leave Request](#update-a-leave-request)
- [EFT & Banking](#eft--banking)
  - [Get EFT Settings](#get-eft-settings)
  - [Update EFT Settings](#update-eft-settings)
  - [Generate EFT File](#generate-eft-file)
- [Compliance & Reporting](#compliance--reporting)
  - [Get Payroll Calendar Events](#get-payroll-calendar-events)
  - [Generate Tax Reports](#generate-tax-reports)
  - [Get Compliance Status](#get-compliance-status)
- [Rate Limits](#rate-limits)
- [Errors](#errors)

## Introduction

This site explains how to use the PandaPayroll API to send and receive payroll data. The PandaPayroll API is an HTTP RESTful service designed specifically for South African payroll compliance and management.

Please contact <NAME_EMAIL> if you have any queries or issues related to API functionality.

**PandaPayroll Terminology**

Before using the API, please take note of the following terminology:

| Resource | Details |
|----------|---------|
| company | A business entity using PandaPayroll |
| employee | An individual employed by a company |
| payroll_period | A specific pay period for an employee |
| payslip | Generated payslip for a specific period |
| pay_run | A collection of payslips processed together |
| leave_type | Categories of leave (annual, sick, etc.) |

**South African Compliance Features**
- PAYE (Pay As You Earn) calculations
- UIF (Unemployment Insurance Fund) contributions
- SDL (Skills Development Levy) calculations
- EMP201 and EMP501 submissions
- IRP5 certificate generation

## Authentication

Authentication involves generating a JWT token through the login endpoint and using this token with all subsequent API requests. The process is as follows:

- Make a POST request to `/api/auth/login` with your email and password
- Receive a JWT token in the response
- Include this token in all API requests as a header in the following format:

`Authorization: Bearer your_jwt_token_here`

Depending on which tool or programming environment you use, headers are added to HTTP requests in different ways.

### Example Request with Correct Authentication

> Request

```bash
# With the command line you can pass the correct header with each request
curl -i -X GET https://payroll.pss-group.co.za/api/companies \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer your_jwt_token_here'
```

You can test that you are able to access the API using this token with the following request for a list of companies, using the command line tool called cURL.

Make sure to replace `your_jwt_token_here` with your actual JWT token.

> Response

```json
[
  {
    "company": {
      "id": "company_id",
      "name": "Demo Company Ltd",
      "companyCode": "DEMO001",
      "registrationNumber": "2023/123456/07"
    }
  }
]
```

When you run this command from your command line, you should see a response body that looks something like this snippet.

If you can successfully retrieve a list of companies, you have authenticated your API call successfully.

## Available Resources

The PandaPayroll API currently allows you to interact with companies, employees, payroll, and compliance features.

**Company Information**
- Get a List of All Companies

**Employee Profiles**
- Get a List of All Employees for a Company
- Get a Specific Employee
- Create an Employee
- Employee Attributes
- Update an Employee
- Delete an Employee

**Payroll Management**
- Get a List of Payroll Periods for an Employee
- Get a Specific Payroll Period
- Finalize Payroll Periods
- Calculate PAYE, UIF, and SDL

**Payslips**
- Get a List of Payslips for an Employee
- Get a Specific Payslip
- Finalize a Payslip
- Download Payslip in PDF Format

**Pay Runs**
- Get a List of Pay Runs for a Company
- Create a Pay Run
- Get Pay Run Details and Status

**Leave Management**
- Get Employee Leave Balances
- Get a List of Available Leave Types
- Create a Leave Request
- Update Leave Request Status

**EFT & Banking**
- Get EFT Settings for a Company
- Update EFT Settings
- Generate EFT Files for Payment Processing

**Compliance & Reporting**
- Get Payroll Calendar Events
- Generate South African Tax Reports
- Get Compliance Status and Submissions

***Additional functionality is being assessed and developed incrementally based on user requirements***

## Companies

### Get a List of Companies

#### Request

> Request

```
https://payroll.pss-group.co.za/api/companies
```

This is an HTTP GET request to URL ->

#### Response

> Response

```json
[
  {
    "company": {
      "id": "company_id_1",
      "name": "Demo Company Ltd",
      "companyCode": "DEMO001",
      "registrationNumber": "2023/123456/07",
      "taxNumber": "TAX123456789",
      "uifNumber": "UIF123456",
      "sdlNumber": "SDL123456",
      "address": {
        "street": "123 Business Street",
        "city": "Cape Town",
        "province": "Western Cape",
        "postalCode": "8001",
        "country": "South Africa"
      },
      "contactDetails": {
        "phone": "+27 21 123 4567",
        "email": "<EMAIL>",
        "website": "https://democompany.com"
      }
    }
  }
]
```

If executed correctly the response body is a JSON array with a list of companies, which should be similar to this snippet ->

## Employees

### Get a List of Employees

#### Request

> Request

```
https://payroll.pss-group.co.za/api/companies/:company_code/employees
```

This is an HTTP GET request to URL ->

Where the parameter `:company_code` is the company code of a specific company obtained from the call to get a list of companies.

You can use the `include` query parameter to specify a comma-separated list of entities that you want included.

For example, appending `?include=payroll_periods,leave_balances` to the URL will include the payroll periods and leave balances of the employee in the response.

Allowed entities: **payroll_periods** (will return recent payroll periods), **leave_balances** (will return current leave balances), and **bank_details** (will return banking information).

#### Response

> Response

```json
[
  {
    "employee": {
      "id": "employee_id",
      "globalEmployeeId": "DEMO001-001",
      "companyEmployeeNumber": "EMP001",
      "firstName": "John",
      "lastName": "Doe",
      "email": "<EMAIL>",
      "dob": "1990-01-15",
      "doa": "2024-01-01",
      "basicSalary": 25000,
      "status": "Active",
      "idType": "rsa",
      "idNumber": "*************",
      "paymentMethod": "EFT",
      "personalDetails": {
        "nationality": "South African",
        "taxResidence": "South Africa"
      },
      "bankDetails": {
        "bank": "First National Bank",
        "accountType": "Current",
        "accountNumber": "**********",
        "branchCode": "250655",
        "accountHolder": "Own"
      }
    }
  }
]
```

If executed correctly the response body is a JSON array with a list of employees, which should be similar to this snippet ->

### Get a Specific Employee

#### Request

> Request

```
https://payroll.pss-group.co.za/api/employees/:employee_id
```

This is an HTTP GET request to URL ->

Where the parameter `:employee_id` is the ID of a specific employee obtained from the call to get a list of employees.

#### Response

> Response

```json
{
  "employee": {
    "id": "employee_id",
    "globalEmployeeId": "DEMO001-001",
    "companyEmployeeNumber": "EMP001",
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "dob": "1990-01-15",
    "doa": "2024-01-01",
    "basicSalary": 25000,
    "status": "Active",
    "idType": "rsa",
    "idNumber": "*************",
    "paymentMethod": "EFT",
    "personalDetails": {
      "nationality": "South African",
      "countryOfBirth": "South Africa",
      "taxResidence": "South Africa"
    },
    "bankDetails": {
      "bank": "First National Bank",
      "accountType": "Current",
      "accountNumber": "**********",
      "branchCode": "250655",
      "accountHolder": "Own"
    }
  }
}
```

If executed correctly the response body is a JSON object with the employee details, which should be similar to this snippet ->

### Create an Employee

#### Request

> Request

```
https://payroll.pss-group.co.za/api/companies/:company_code/employees
```

This is an HTTP POST request to URL ->

Where the parameter `:company_code` is the company code of a specific company obtained from the call to get a list of companies.

#### Request Body

> Request Body

```json
{
  "employee": {
    "companyEmployeeNumber": "EMP003",
    "firstName": "Alice",
    "lastName": "Williams",
    "email": "<EMAIL>",
    "dob": "1985-03-10",
    "doa": "2025-01-01",
    "basicSalary": 28000,
    "idType": "rsa",
    "idNumber": "*************",
    "paymentMethod": "EFT",
    "bankDetails": {
      "bank": "Standard Bank",
      "accountType": "Current",
      "accountNumber": "0*********",
      "branchCode": "051001",
      "accountHolder": "Own"
    }
  }
}
```

The request body should be a JSON object containing the details of the employee to be saved. An abbreviated request body sample is shown to the right ->

There are several required attributes for creating an employee, as well as certain optional ones. Please see the examples and **Employee Attributes** tables below.

#### Response

> Response

```json
{
  "success": true,
  "message": "Employee has been created successfully",
  "employee": {
    "id": "new_employee_id",
    "globalEmployeeId": "DEMO001-003",
    "companyEmployeeNumber": "EMP003"
  }
}
```

If executed correctly the response body is a JSON object with details of the transaction and the ID of the created employee, and should be similar to this snippet ->

### Employee Attributes

> Request body - RSA Citizen paid by EFT to own bank account

```json
{
  "employee": {
    "companyEmployeeNumber": "EMP001",
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "dob": "1990-01-15",
    "doa": "2024-01-01",
    "basicSalary": 25000,
    "idType": "rsa",
    "idNumber": "*************",
    "paymentMethod": "EFT",
    "bankDetails": {
      "bank": "First National Bank",
      "accountType": "Current",
      "accountNumber": "**********",
      "branchCode": "250655",
      "accountHolder": "Own"
    },
    "personalDetails": {
      "nationality": "South African",
      "countryOfBirth": "South Africa",
      "taxResidence": "South Africa"
    }
  }
}
```

> Request body - Foreign Employee with passport

```json
{
  "employee": {
    "companyEmployeeNumber": "EMP002",
    "firstName": "Jane",
    "lastName": "Smith",
    "email": "<EMAIL>",
    "dob": "1985-05-20",
    "doa": "2024-02-01",
    "basicSalary": 30000,
    "idType": "passport",
    "idNumber": "P123456789",
    "paymentMethod": "EFT",
    "personalDetails": {
      "nationality": "British",
      "countryOfBirth": "United Kingdom",
      "taxResidence": "South Africa"
    },
    "bankDetails": {
      "bank": "ABSA Bank",
      "accountType": "Current",
      "accountNumber": "**********",
      "branchCode": "632005",
      "accountHolder": "Own"
    }
  }
}
```

The following attributes may be required for the creation of an employee. Certain attributes are always required while others are only conditionally required based on the value of another attribute.

#### Parent Attributes

| Attribute | Details | Type | Applicability |
|-----------|---------|------|---------------|
| companyEmployeeNumber | Employee number for payroll purposes | String | Required |
| firstName | Employee first name | String | Required |
| lastName | Employee last name | String | Required |
| email | Employee email address | String | Required |
| dob | Employee date of birth in format YYYY-MM-DD | Date String | Required |
| doa | Date of appointment in format YYYY-MM-DD | Date String | Required |
| basicSalary | Employee basic monthly salary | Number | Required |
| idType | Type of identification: "rsa", "passport", "other" | String | Required |
| idNumber | Employee ID or passport number | String | Required |
| paymentMethod | Payment method: "EFT", "Cash", "Cheque" | String | Required |
| status | Employee status: "Active", "Inactive", "Terminated" | String | Optional (defaults to "Active") |
| bankDetails | Employee bank details object | Hash | Required if paymentMethod is "EFT" |
| personalDetails | Employee personal details object | Hash | Optional |

#### Child Attributes

| Parent | Attribute | Details | Type | Applicability |
|--------|-----------|---------|------|---------------|
| bankDetails | bank | Name of employee's bank | String | Required for parent |
| bankDetails | accountType | Account type: "Current", "Savings", "Transmission" | String | Required for parent |
| bankDetails | accountNumber | Employee bank account number | String | Required for parent |
| bankDetails | branchCode | Employee bank branch code (6 digits) | String | Required for parent |
| bankDetails | accountHolder | Account ownership: "Own", "Joint", "Third Party" | String | Required for parent |
| personalDetails | nationality | Employee nationality | String | Optional |
| personalDetails | countryOfBirth | Country where employee was born | String | Optional |
| personalDetails | taxResidence | Tax residence country | String | Optional |

### Update an Employee

#### Request

> Request

```
https://payroll.pss-group.co.za/api/employees/:employee_id
```

This is an HTTP PATCH request to URL ->

Where the parameter `:employee_id` is the PandaPayroll system ID of an employee.

#### Request Body

> Request body

```json
{
  "employee": {
    "firstName": "Alice",
    "lastName": "Williams",
    "basicSalary": 30000,
    "email": "<EMAIL>"
  }
}
```

The request body should be a JSON object containing the details of the employee to be changed. An example is shown ->

You only need to send those attributes which need to be changed for an employee.

#### Response

> Response

```json
{
  "success": true,
  "message": "Employee has been updated successfully",
  "employee": {
    "id": "employee_id",
    "companyEmployeeNumber": "EMP001"
  }
}
```

If executed correctly the response body is a JSON object with details of the transaction, and should be similar to this snippet ->

### Delete an Employee

#### Request

> Request

```
https://payroll.pss-group.co.za/api/employees/:employee_id
```

This is an HTTP DELETE request to URL ->

Where the parameter `:employee_id` is the PandaPayroll system ID of an employee.

#### Response

> Response

```json
{
  "success": true,
  "message": "Employee EMP001 has been deleted successfully."
}
```

If executed correctly the response body is a JSON object with details of the transaction, and should be similar to this snippet ->

## Payroll Periods

### Get a List of Payroll Periods

#### Request

> Request

```
https://payroll.pss-group.co.za/api/companies/:company_code/payroll-periods
```

This is an HTTP GET request to URL ->

Where the parameter `:company_code` is the company code of a specific company obtained from the call to get a list of companies.

You can use query parameters to filter the results:
- `employee_id` - Filter by specific employee
- `status` - Filter by status: "open", "processing", "finalized", "locked"
- `start_date` - Filter periods starting from this date (YYYY-MM-DD)
- `end_date` - Filter periods ending before this date (YYYY-MM-DD)

#### Response

> Response

```json
[
  {
    "payrollPeriod": {
      "id": "period_id_1",
      "company": "company_id",
      "employee": "employee_id",
      "startDate": "2025-01-01",
      "endDate": "2025-01-31",
      "status": "finalized",
      "isFinalized": true,
      "basicSalary": 25000,
      "grossPay": 25000,
      "totalDeductions": 4500,
      "netPay": 20500,
      "PAYE": 3200,
      "UIF": 250,
      "SDL": 375,
      "finalizedAt": "2025-01-31T23:59:59.000Z",
      "finalizedBy": "user_id"
    }
  }
]
```

If executed correctly the response body is a JSON array with a list of payroll periods, which should be similar to this snippet ->

### Get a Specific Payroll Period

#### Request

> Request

```
https://payroll.pss-group.co.za/api/payroll-periods/:period_id
```

This is an HTTP GET request to URL ->

Where the parameter `:period_id` is the ID of a specific payroll period obtained from the call to get a list of payroll periods.

#### Response

> Response

```json
{
  "payrollPeriod": {
    "id": "period_id_1",
    "company": "company_id",
    "employee": "employee_id",
    "startDate": "2025-01-01",
    "endDate": "2025-01-31",
    "status": "finalized",
    "isFinalized": true,
    "basicSalary": 25000,
    "grossPay": 25000,
    "totalDeductions": 4500,
    "netPay": 20500,
    "PAYE": 3200,
    "UIF": 250,
    "SDL": 375,
    "calculations": {
      "payeRate": 0.18,
      "uifRate": 0.01,
      "sdlRate": 0.01
    },
    "finalizedAt": "2025-01-31T23:59:59.000Z",
    "finalizedBy": "user_id"
  }
}
```

If executed correctly the response body is a JSON object with the payroll period details, which should be similar to this snippet ->

### Finalize Payroll Periods

#### Request

> Request

```
https://payroll.pss-group.co.za/api/companies/:company_code/payroll/finalize
```

This is an HTTP POST request to URL ->

Where the parameter `:company_code` is the company code of a specific company.

#### Request Body

> Request Body

```json
{
  "periodIds": ["period_id_1", "period_id_2"]
}
```

The request body should be a JSON object containing the IDs of the payroll periods to be finalized. An example is shown ->

**Note:** Only payroll periods with status "processing" can be finalized. Finalized periods cannot be modified.

#### Response

> Response

```json
{
  "success": true,
  "message": "Payroll periods finalized successfully",
  "finalizedCount": 2,
  "finalizedPeriods": [
    {
      "id": "period_id_1",
      "status": "finalized",
      "finalizedAt": "2025-01-10T10:00:00.000Z"
    },
    {
      "id": "period_id_2",
      "status": "finalized",
      "finalizedAt": "2025-01-10T10:00:00.000Z"
    }
  ]
}
```

If executed correctly the response body is a JSON object with details of the finalization process, and should be similar to this snippet ->

## Payslips

### Get a List of Payslips for an Employee

#### Request

> Request

```
https://payroll.pss-group.co.za/api/employees/:employee_id/payslips
```

This is an HTTP GET request to URL ->

Where the parameter `:employee_id` is the ID of a specific employee obtained from the call to get a list of employees.

You can use query parameters to filter the results:
- `status` - Filter by status: "draft", "approved", "finalized"
- `start_date` - Filter payslips from this date (YYYY-MM-DD)
- `end_date` - Filter payslips to this date (YYYY-MM-DD)
- `pay_run_id` - Filter by specific pay run

#### Response

> Response

```json
[
  {
    "payslip": {
      "id": "payslip_id_1",
      "payRun": "payrun_id",
      "payrollPeriod": "period_id",
      "employee": "employee_id",
      "status": "finalized",
      "grossPay": 25000,
      "totalDeductions": 4500,
      "netPay": 20500,
      "paymentMethod": "EFT",
      "earnings": [
        {
          "type": "basic",
          "description": "Basic Salary",
          "amount": 25000
        }
      ],
      "deductions": [
        {
          "type": "tax",
          "description": "PAYE",
          "amount": 3200
        },
        {
          "type": "benefits",
          "description": "UIF",
          "amount": 250
        }
      ],
      "bankDetails": {
        "accountHolder": "John Doe",
        "accountNumber": "**********",
        "bankName": "First National Bank",
        "branchCode": "250655"
      }
    }
  }
]
```

If executed correctly the response body is a JSON array with a list of payslips, which should be similar to this snippet ->

### Get a Specific Payslip

#### Request

> Request

```
https://payroll.pss-group.co.za/api/payslips/:payslip_id
```

This is an HTTP GET request to URL ->

Where the parameter `:payslip_id` is the ID of a specific payslip obtained from the call to get a list of payslips.

#### Response

> Response

```json
{
  "payslip": {
    "id": "payslip_id_1",
    "payRun": "payrun_id",
    "payrollPeriod": "period_id",
    "employee": "employee_id",
    "status": "finalized",
    "grossPay": 25000,
    "totalDeductions": 4500,
    "netPay": 20500,
    "paymentMethod": "EFT",
    "earnings": [
      {
        "type": "basic",
        "description": "Basic Salary",
        "amount": 25000
      }
    ],
    "deductions": [
      {
        "type": "tax",
        "description": "PAYE",
        "amount": 3200
      },
      {
        "type": "benefits",
        "description": "UIF",
        "amount": 250
      },
      {
        "type": "benefits",
        "description": "SDL",
        "amount": 375
      }
    ],
    "bankDetails": {
      "accountHolder": "John Doe",
      "accountNumber": "**********",
      "bankName": "First National Bank",
      "branchCode": "250655"
    },
    "company": "company_id"
  }
}
```

If executed correctly the response body is a JSON object with the payslip details, which should be similar to this snippet ->

### Finalize a Payslip

#### Request

> Request

```
https://payroll.pss-group.co.za/api/companies/:company_code/payslip/:payslip_id/finalize
```

This is an HTTP POST request to URL ->

Where the parameters `:company_code` is the company code and `:payslip_id` is the ID of the payslip to finalize.

#### Response

> Response

```json
{
  "success": true,
  "message": "Payslip finalized successfully",
  "payslip": {
    "id": "payslip_id",
    "status": "finalized",
    "finalizedAt": "2025-01-10T10:00:00.000Z"
  }
}
```

If executed correctly the response body is a JSON object with details of the finalization, and should be similar to this snippet ->

### Download Payslip PDF

#### Request

> Request

```
https://payroll.pss-group.co.za/api/payslips/:payslip_id/pdf
```

This is an HTTP GET request to URL ->

Where the parameter `:payslip_id` is the ID of a specific payslip.

#### Response

The response will be a PDF file download of the payslip.

## Pay Runs

### Get a List of Pay Runs

#### Request

> Request

```
https://payroll.pss-group.co.za/api/companies/:company_code/pay-runs
```

This is an HTTP GET request to URL ->

Where the parameter `:company_code` is the company code of a specific company.

You can use query parameters to filter the results:
- `status` - Filter by status: "draft", "processing", "review", "approved", "finalized"
- `pay_run_type` - Filter by type: "regular", "bonus", "commission", "adjustment"
- `start_date` - Filter pay runs from this date
- `end_date` - Filter pay runs to this date

#### Response

> Response

```json
[
  {
    "payRun": {
      "id": "payrun_id_1",
      "company": "company_id",
      "period": "January 2025",
      "monthYear": "2025-01",
      "payRunType": "regular",
      "reference": "PR-2025-01-REGULAR-1",
      "status": "finalized",
      "description": "January 2025 Regular Payroll",
      "startDate": "2025-01-01",
      "endDate": "2025-01-31",
      "paymentDate": "2025-02-01",
      "payrollPeriods": ["period_id_1", "period_id_2"],
      "totals": {
        "grossPay": 55000,
        "totalDeductions": 10300,
        "netPay": 44700,
        "employerContributions": 1500
      },
      "createdBy": "user_id",
      "finalized": true,
      "finalizedAt": "2025-01-31T23:59:59.000Z"
    }
  }
]
```

If executed correctly the response body is a JSON array with a list of pay runs, which should be similar to this snippet ->

### Create a Pay Run

#### Request

> Request

```
https://payroll.pss-group.co.za/api/companies/:company_code/pay-runs
```

This is an HTTP POST request to URL ->

Where the parameter `:company_code` is the company code of a specific company.

#### Request Body

> Request Body

```json
{
  "periodIds": ["period_id_1", "period_id_2"],
  "payRunType": "regular",
  "description": "February 2025 Regular Payroll",
  "releaseToSelfService": true
}
```

The request body should be a JSON object containing the details of the pay run to be created. An example is shown ->

#### Response

> Response

```json
{
  "success": true,
  "message": "Pay run created successfully",
  "payRun": {
    "id": "payrun_id",
    "reference": "PR-2025-02-REGULAR-1",
    "status": "draft",
    "payslipCount": 2,
    "totals": {
      "grossPay": 55000,
      "totalDeductions": 10300,
      "netPay": 44700
    }
  }
}
```

If executed correctly the response body is a JSON object with details of the created pay run, and should be similar to this snippet ->

### Get Pay Run Details

#### Request

> Request

```
https://payroll.pss-group.co.za/api/pay-runs/:pay_run_id
```

This is an HTTP GET request to URL ->

Where the parameter `:pay_run_id` is the ID of a specific pay run.

#### Response

> Response

```json
{
  "payRun": {
    "id": "payrun_id_1",
    "company": "company_id",
    "period": "January 2025",
    "monthYear": "2025-01",
    "payRunType": "regular",
    "reference": "PR-2025-01-REGULAR-1",
    "status": "finalized",
    "description": "January 2025 Regular Payroll",
    "payrollPeriods": ["period_id_1", "period_id_2"],
    "payslips": ["payslip_id_1", "payslip_id_2"],
    "totals": {
      "grossPay": 55000,
      "totalDeductions": 10300,
      "netPay": 44700,
      "employerContributions": 1500
    },
    "createdBy": "user_id",
    "approvedBy": "user_id",
    "finalized": true,
    "finalizedAt": "2025-01-31T23:59:59.000Z"
  }
}
```

If executed correctly the response body is a JSON object with the pay run details, which should be similar to this snippet ->

## Leave Management

### Get Employee Leave Balances

#### Request

> Request

```
https://payroll.pss-group.co.za/api/employees/:employee_id/leave-balances
```

This is an HTTP GET request to URL ->

Where the parameter `:employee_id` is the ID of a specific employee. The desired date can be passed as a parameter `?date=2025-01-01` or use `?date=latest` to get the most recent balances.

#### Response

> Response

```json
{
  "annual_leave": 15.5,
  "sick_leave": 28,
  "family_responsibility": 3,
  "maternity_leave": 0
}
```

If executed correctly the response body is a JSON object containing leave type names and current balances, which should be similar to this snippet ->

### Get a List of Leave Types

#### Request

> Request

```
https://payroll.pss-group.co.za/api/companies/:company_code/leave-types
```

This is an HTTP GET request to URL ->

Where the parameter `:company_code` is the company code of a specific company.

#### Response

> Response

```json
[
  {
    "leaveType": {
      "id": "leave_type_id_1",
      "company": "company_id",
      "name": "Annual Leave",
      "category": "annual",
      "daysPerYear": 21,
      "paidLeave": true,
      "requiresApproval": true,
      "description": "Annual vacation leave as per South African labor law"
    }
  },
  {
    "leaveType": {
      "id": "leave_type_id_2",
      "company": "company_id",
      "name": "Sick Leave",
      "category": "sick",
      "daysPerYear": 30,
      "paidLeave": true,
      "requiresApproval": false,
      "description": "Sick leave for medical purposes"
    }
  }
]
```

If executed correctly the response body is a JSON array with leave types, which should be similar to this snippet ->

### Create a Leave Request

#### Request

> Request

```
https://payroll.pss-group.co.za/api/employees/:employee_id/leave-requests
```

This is an HTTP POST request to URL ->

Where the parameter `:employee_id` is the ID of a specific employee.

#### Request Body

> Request Body

```json
{
  "leaveRequest": {
    "leaveTypeId": "leave_type_id_1",
    "startDate": "2025-02-10",
    "endDate": "2025-02-14",
    "days": 5,
    "reason": "Family vacation",
    "halfDay": false
  }
}
```

#### Response

> Response

```json
{
  "success": true,
  "message": "Leave request created successfully",
  "leaveRequest": {
    "id": "leave_request_id",
    "status": "pending",
    "submittedAt": "2025-01-10T10:00:00.000Z"
  }
}
```

### Update a Leave Request

#### Request

> Request

```
https://payroll.pss-group.co.za/api/leave-requests/:request_id/:action
```

This is an HTTP PUT request to URL ->

Where `:request_id` is the leave request ID and `:action` is either "approve" or "reject".

#### Request Body

> Request Body

```json
{
  "rejectionReason": "Insufficient notice period"
}
```

## EFT & Banking

### Get EFT Settings

#### Request

> Request

```
https://payroll.pss-group.co.za/api/companies/:company_code/eft-settings
```

This is an HTTP GET request to URL ->

#### Response

> Response

```json
{
  "eftSettings": {
    "eftFormat": "FNB",
    "bank": "First National Bank",
    "accountNumber": "**********",
    "branchCode": "250655",
    "accountType": "Current",
    "accountHolder": "Company Name"
  }
}
```

### Update EFT Settings

#### Request

> Request

```
https://payroll.pss-group.co.za/api/companies/:company_code/eft-settings
```

This is an HTTP POST request to URL ->

#### Request Body

> Request Body

```json
{
  "eftFormat": "Standard Bank",
  "bank": "Standard Bank",
  "accountNumber": "0*********",
  "branchCode": "051001",
  "accountType": "Current",
  "accountHolder": "Company Name"
}
```

### Generate EFT File

#### Request

> Request

```
https://payroll.pss-group.co.za/api/pay-runs/:pay_run_id/eft-file
```

This is an HTTP GET request to URL ->

Where the parameter `:pay_run_id` is the ID of a finalized pay run.

#### Response

The response will be an EFT file download in the appropriate bank format.

## Compliance & Reporting

### Get Payroll Calendar Events

#### Request

> Request

```
https://payroll.pss-group.co.za/api/payroll-calendar/events
```

This is an HTTP GET request to URL ->

Query parameters:
- `start_date` - Filter events from this date (YYYY-MM-DD)
- `end_date` - Filter events to this date (YYYY-MM-DD)
- `type` - Filter by event type: "emp201", "emp501", "irp5", "uif", "sdl"

#### Response

> Response

```json
[
  {
    "event": {
      "id": "event_id_1",
      "title": "EMP201 Submission",
      "type": "emp201",
      "date": "2025-02-07",
      "status": "pending",
      "priority": "high",
      "description": "Monthly EMP201 submission deadline"
    }
  }
]
```

### Generate Tax Reports

#### Request

> Request

```
https://payroll.pss-group.co.za/api/companies/:company_code/tax-reports
```

This is an HTTP POST request to URL ->

#### Request Body

> Request Body

```json
{
  "reportType": "emp201",
  "period": "2025-01",
  "year": 2025
}
```

### Get Compliance Status

#### Request

> Request

```
https://payroll.pss-group.co.za/api/companies/:company_code/compliance-status
```

This is an HTTP GET request to URL ->

#### Response

> Response

```json
{
  "compliance": {
    "emp201Status": "submitted",
    "lastSubmission": "2025-01-07",
    "nextDeadline": "2025-02-07",
    "outstandingItems": []
  }
}
```

## Rate Limits

The PandaPayroll API makes use of rate limits. The default maximum number of requests per hour is 1000. The below headers communicate information about these limits:

- `X-RateLimit-Limit`: the maximum number of allowed requests in a 60 minute window
- `X-RateLimit-Remaining`: the number of remaining requests in the current 60 minute window
- `X-RateLimit-Reset`: A unix timestamp (in UTC) indicating when the current window ends

The default limit of 1000 can be raised by contacting Support, along with justification that it is required.

## Errors

> Response

```json
{
  "success": false,
  "message": "API User is not authorized or does not exist.",
  "error": "AuthenticationError"
}
```

The PandaPayroll API utilizes conventional HTTP response codes to indicate the success or failure of a request. There will also be a JSON response containing a message, similar to the example on the right ->

| HTTP Code | Meaning |
|-----------|---------|
| 200 | Success |
| 201 | Created |
| 400 | Validation Error - Invalid input data |
| 401 | Unauthorized - Invalid or missing authentication |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Requested resource not found |
| 429 | Rate limit exceeded - Too many requests |
| 500 | Internal Server Error - Server-side error |

### Common Error Types

- `ValidationError` - Invalid input data or missing required fields
- `AuthenticationError` - Invalid or missing JWT token
- `AuthorizationError` - Insufficient permissions for the requested operation
- `NotFoundError` - Requested resource does not exist
- `DuplicateError` - Resource already exists (e.g., employee number)
- `BusinessLogicError` - Operation violates business rules (e.g., finalizing already finalized payroll)

---

*This documentation covers the core API endpoints of PandaPayroll. For additional endpoints or detailed implementation examples, please contact our support <NAME_EMAIL>.*

## API Endpoints

### Authentication APIs

#### POST /api/auth/login
Authenticate user and receive JWT token.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "user_id",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "currentCompany": "company_id"
  }
}
```

#### POST /api/auth/refresh
Refresh JWT token.

**Response:**
```json
{
  "success": true,
  "token": "new_jwt_token",
  "message": "Token refreshed"
}
```

#### GET /api/auth/test
Test protected route access.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "success": true,
  "message": "Protected route accessed successfully",
  "user": {
    "id": "user_id",
    "email": "<EMAIL>"
  }
}
```

#### GET /api/auth/health
Public health check endpoint.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2025-01-10T10:00:00.000Z",
  "uptime": 3600
}
```

### Employee Management APIs

#### GET /api/employee/:employeeId/hourly-status
Get employee hourly paid status.

**Parameters:**
- `employeeId` (string) - Employee ID

**Response:**
```json
{
  "success": true,
  "hourlyPaid": false
}
```

#### POST /api/employee/:employeeId/hourly-status
Update employee hourly paid status.

**Request Body:**
```json
{
  "hourlyPaid": true
}
```

**Response:**
```json
{
  "success": true,
  "message": "Hourly paid status updated successfully"
}
```

#### POST /api/employees/reinstate
Reinstate terminated employees.

**Request Body:**
```json
{
  "updates": [
    {
      "employeeNumber": "EMP001",
      "fromDate": "2025-01-01"
    }
  ]
}
```

**Response:**
```json
{
  "message": "All reinstatements completed successfully",
  "successes": ["Employee EMP001 reinstated successfully"]
}
```

#### POST /api/employees/undo-end-of-service
Undo employee termination.

**Request Body:**
```json
{
  "updates": [
    {
      "employeeNumber": "EMP001"
    }
  ]
}
```

### Payroll Management APIs

#### GET /clients/:companyCode/payrollhub
Get payroll hub dashboard with employee payroll periods.

**Parameters:**
- `companyCode` (string) - Company code

**Query Parameters:**
- `period` (string, optional) - Filter by pay period
- `status` (string, optional) - Filter by status

**Response:**
```json
{
  "employees": [
    {
      "id": "employee_id",
      "name": "John Doe",
      "employeeNumber": "EMP001",
      "payrollPeriods": [
        {
          "id": "period_id",
          "startDate": "2025-01-01",
          "endDate": "2025-01-31",
          "basicSalary": 25000,
          "grossPay": 25000,
          "totalDeductions": 5000,
          "netPay": 20000,
          "status": "finalized"
        }
      ]
    }
  ]
}
```

#### POST /clients/:companyCode/payrollhub/finalize
Finalize payroll periods.

**Request Body:**
```json
{
  "periodIds": ["period_id_1", "period_id_2"]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Payroll periods finalized successfully",
  "finalizedCount": 2
}
```

### Payslip Management APIs

#### POST /clients/:companyCode/payslip/:payslipId/finalize
Finalize a payslip.

**Parameters:**
- `companyCode` (string) - Company code
- `payslipId` (string) - Payslip ID

**Response:**
```json
{
  "success": true,
  "message": "Payslip finalized successfully",
  "payslip": {
    "id": "payslip_id",
    "status": "finalized",
    "finalizedAt": "2025-01-10T10:00:00.000Z"
  }
}
```

#### GET /clients/:companyCode/payslip/bulk-download/:payRunId
Download all payslips for a pay run as ZIP file.

**Parameters:**
- `companyCode` (string) - Company code
- `payRunId` (string) - Pay run ID

**Response:** ZIP file download

### Pay Run Management APIs

#### GET /clients/:companyCode/pay-run/new
Get pay run creation page with finalized payslips.

**Response:** HTML page with available payslips for pay run creation

#### POST /clients/:companyCode/pay-run/create
Create a new pay run.

**Request Body:**
```json
{
  "periodString": "January 2025",
  "monthYear": "2025-01",
  "taxPeriodString": "2025-01",
  "payRunType": "regular",
  "description": "January 2025 Regular Payroll",
  "payslipIds": ["payslip_id_1", "payslip_id_2"]
}
```

**Response:**
```json
{
  "success": true,
  "payRun": {
    "id": "payrun_id",
    "reference": "PR-2025-01-REGULAR-1",
    "status": "draft",
    "totalPayslips": 2
  }
}
```

#### POST /clients/:companyCode/payruns
Create pay run from finalized periods.

**Request Body:**
```json
{
  "periodIds": ["period_id_1", "period_id_2"],
  "releaseToSelfService": true
}
```

**Response:**
```json
{
  "success": true,
  "payRun": {
    "id": "payrun_id",
    "reference": "PR-2025-01-REGULAR-1",
    "payslipCount": 2
  }
}
```

### Leave Management APIs

#### GET /api/leave/types
Get all leave types for a company.

**Query Parameters:**
- `companyCode` (string) - Company code

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "leave_type_id",
      "name": "Annual Leave",
      "category": "annual",
      "daysPerYear": 21,
      "paidLeave": true
    }
  ]
}
```

#### POST /api/leave/types
Create a new leave type.

**Request Body:**
```json
{
  "name": "Sick Leave",
  "category": "sick",
  "description": "Medical leave",
  "daysPerYear": 30,
  "paidLeave": true,
  "requiresApproval": true,
  "companyCode": "COMP001"
}
```

#### PUT /api/leave/request/:id/:action
Approve or reject leave request.

**Parameters:**
- `id` (string) - Leave request ID
- `action` (string) - "approve" or "reject"

**Request Body:**
```json
{
  "rejectionReason": "Insufficient notice period"
}
```

### EFT & Banking APIs

#### POST /api/eft/settings/eft
Save EFT settings for company.

**Request Body:**
```json
{
  "eftFormat": "FNB",
  "bank": "First National Bank",
  "accountNumber": "**********",
  "branchCode": "250655",
  "accountType": "Current",
  "accountHolder": "Company Name"
}
```

#### GET /api/eft/additional-accounts
Get additional bank accounts.

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "account_id",
      "accountDescription": "Payroll Account",
      "bankName": "Standard Bank",
      "accountNumber": "0*********",
      "branchCode": "051001",
      "accountType": "Current"
    }
  ]
}
```

#### POST /api/eft/additional-accounts
Add additional bank account.

**Request Body:**
```json
{
  "accountDescription": "Bonus Account",
  "bankName": "ABSA",
  "accountNumber": "**********",
  "branchCode": "632005",
  "accountType": "Current"
}
```

#### PUT /api/eft/additional-accounts/:accountId
Update additional bank account.

**Parameters:**
- `accountId` (string) - Account ID

**Request Body:**
```json
{
  "accountDescription": "Updated Account",
  "bankName": "Nedbank",
  "accountNumber": "**********",
  "branchCode": "198765",
  "accountType": "Savings"
}
```

#### DELETE /api/eft/additional-accounts/:accountId
Delete additional bank account.

**Parameters:**
- `accountId` (string) - Account ID

**Response:**
```json
{
  "success": true,
  "message": "Additional bank account deleted successfully"
}
```

### Payroll Calendar APIs

#### GET /api/payrollCalendar/events
Get payroll calendar events.

**Query Parameters:**
- `startDate` (string, optional) - Filter start date (YYYY-MM-DD)
- `endDate` (string, optional) - Filter end date (YYYY-MM-DD)
- `type` (string, optional) - Event type filter
- `status` (string, optional) - Status filter

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "event_id",
      "title": "EMP201 Submission",
      "type": "emp201",
      "date": "2025-02-07",
      "status": "pending",
      "priority": "high"
    }
  ],
  "count": 1
}
```

#### POST /api/payrollCalendar/events
Create new payroll calendar event.

**Request Body:**
```json
{
  "title": "Custom Compliance Event",
  "type": "custom",
  "date": "2025-02-15",
  "priority": "medium",
  "description": "Custom compliance requirement"
}
```

#### GET /api/payrollCalendar/upcoming
Get upcoming events (next 30 days).

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "event_id",
      "title": "EMP201 Submission",
      "date": "2025-02-07",
      "status": "pending"
    }
  ],
  "count": 1
}
```

#### POST /api/payrollCalendar/initialize-comprehensive-compliance
Initialize South African compliance events.

**Request Body:**
```json
{
  "year": 2025,
  "force": false
}
```

**Response:**
```json
{
  "success": true,
  "message": "Successfully created 24 comprehensive South African compliance events for 2025",
  "details": {
    "totalCreated": 24,
    "year": 2025,
    "createdByType": {
      "emp201": 12,
      "emp501": 1,
      "irp5": 1,
      "uif": 4,
      "sdl": 1
    }
  }
}
```

### Session Management APIs

#### GET /api/session/status
Get current session status.

**Response:**
```json
{
  "success": true,
  "sessionActive": true,
  "timeRemaining": 3600,
  "user": {
    "id": "user_id",
    "email": "<EMAIL>"
  }
}
```

#### POST /api/session/extend
Extend current session.

**Response:**
```json
{
  "success": true,
  "message": "Session extended",
  "newExpiration": "2025-01-10T11:00:00.000Z"
}
```

### Internal APIs

#### GET /internal-api/leave-types/:companyCode
Get active leave types for a company (internal use).

**Parameters:**
- `companyCode` (string) - Company code

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "name": "Annual Leave",
      "description": "Annual vacation leave",
      "category": "annual",
      "daysPerYear": 21
    }
  ]
}
```

### OID Returns API

#### POST /api/oidReturns/generate
Generate OID return for a company.

**Request Body:**
```json
{
  "year": 2024
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "year": 2024,
    "totalEmployees": 50,
    "totalInjuries": 2,
    "returnData": "..."
  }
}
```

## Data Models

### Employee Model
```json
{
  "globalEmployeeId": "string (unique)",
  "companyEmployeeNumber": "string (required)",
  "firstName": "string",
  "lastName": "string",
  "dob": "date",
  "doa": "date (date of appointment)",
  "email": "string",
  "phone": "string",
  "idType": "enum: ['rsa', 'passport', 'other']",
  "idNumber": "string",
  "basicSalary": "number",
  "paymentMethod": "enum: ['EFT', 'Cash', 'Cheque']",
  "status": "enum: ['Active', 'Inactive', 'Terminated']",
  "company": "ObjectId (ref: Company)",
  "payFrequency": "ObjectId (ref: PayFrequency)",
  "personalDetails": {
    "idType": "string",
    "idNumber": "string",
    "nationality": "string",
    "countryOfBirth": "string",
    "taxResidence": "string"
  },
  "bankDetails": {
    "bank": "string",
    "accountType": "enum: ['Current', 'Savings', 'Transmission']",
    "accountNumber": "string",
    "branchCode": "string",
    "accountHolder": "enum: ['Own', 'Joint', 'Third Party']"
  }
}
```

### Payroll Model
```json
{
  "company": "ObjectId (ref: Company)",
  "employee": "ObjectId (ref: Employee)",
  "payrollPeriod": "ObjectId (ref: PayrollPeriod)",
  "month": "date",
  "finalised": "boolean",
  "finalisedDate": "date",
  "basicSalary": "number",
  "commission": "number",
  "travelAllowance": {
    "fixedAllowanceAmount": "number",
    "actualCosts": "number"
  },
  "PAYE": "number",
  "UIF": "number",
  "SDL": "number",
  "pensionFund": "number",
  "providentFund": "number",
  "retirementAnnuity": "number",
  "medical": {
    "medicalAid": "number",
    "gapCover": "number"
  },
  "status": "enum: ['draft', 'processing', 'finalized', 'locked']",
  "customIncomeItems": [
    {
      "customIncomeId": "ObjectId (ref: CustomItem)",
      "name": "string",
      "inputType": "string",
      "amount": "number",
      "calculatedAmount": "number"
    }
  ]
}
```

### PayRun Model
```json
{
  "company": "ObjectId (ref: Company)",
  "period": "string",
  "monthYear": "string",
  "payRunType": "enum: ['regular', 'bonus', 'commission', 'adjustment', 'termination']",
  "description": "string",
  "reference": "string",
  "frequency": "enum: ['weekly', 'biweekly', 'monthly']",
  "startDate": "date",
  "endDate": "date",
  "paymentDate": "date",
  "taxPeriod": "string",
  "status": "enum: ['draft', 'processing', 'review', 'approved', 'finalized', 'released']",
  "payrollPeriods": "array of ObjectId (ref: PayrollPeriod)",
  "payslips": "array of ObjectId (ref: Payslip)",
  "totals": {
    "grossPay": "number",
    "totalDeductions": "number",
    "netPay": "number",
    "employerContributions": "number"
  },
  "createdBy": "ObjectId (ref: User)",
  "approvedBy": "ObjectId (ref: User)",
  "finalized": "boolean",
  "finalizedAt": "date"
}
```

### PayrollPeriod Model
```json
{
  "company": "ObjectId (ref: Company)",
  "employee": "ObjectId (ref: Employee)",
  "startDate": "date",
  "endDate": "date",
  "payFrequency": "ObjectId (ref: PayFrequency)",
  "status": "enum: ['open', 'processing', 'finalized', 'locked']",
  "isFinalized": "boolean",
  "basicSalary": "number",
  "grossPay": "number",
  "totalDeductions": "number",
  "netPay": "number",
  "PAYE": "number",
  "UIF": "number",
  "SDL": "number",
  "finalizedAt": "date",
  "finalizedBy": "ObjectId (ref: User)"
}
```

### Company Model
```json
{
  "name": "string (required)",
  "companyCode": "string (required, unique)",
  "owner": "ObjectId (ref: User)",
  "employees": "array of ObjectId (ref: Employee)",
  "registrationNumber": "string",
  "taxNumber": "string",
  "uifNumber": "string",
  "sdlNumber": "string",
  "address": {
    "street": "string",
    "city": "string",
    "province": "string",
    "postalCode": "string",
    "country": "string"
  },
  "contactDetails": {
    "phone": "string",
    "email": "string",
    "website": "string"
  },
  "employerDetails": "ObjectId (ref: EmployerDetails)"
}
```

### Payslip Model
```json
{
  "payRun": "ObjectId (ref: PayRun)",
  "payrollPeriod": "ObjectId (ref: PayrollPeriod)",
  "employee": "ObjectId (ref: Employee)",
  "earnings": [
    {
      "type": "enum: ['basic', 'overtime', 'bonus', 'allowance']",
      "description": "string",
      "amount": "number"
    }
  ],
  "deductions": [
    {
      "type": "enum: ['tax', 'benefits', 'loan']",
      "description": "string",
      "amount": "number"
    }
  ],
  "grossPay": "number",
  "totalDeductions": "number",
  "netPay": "number",
  "paymentMethod": "enum: ['EFT', 'Cash', 'Cheque']",
  "bankDetails": {
    "accountHolder": "string",
    "accountNumber": "string",
    "bankName": "string",
    "branchCode": "string"
  },
  "status": "enum: ['draft', 'approved', 'paid']",
  "company": "ObjectId (ref: Company)"
}
```

### LeaveType Model
```json
{
  "company": "ObjectId (ref: Company)",
  "name": "string (required)",
  "category": "enum: ['annual', 'sick', 'maternity', 'paternity', 'family', 'study', 'custom']",
  "description": "string",
  "daysPerYear": "number",
  "accrualRate": "number",
  "carryOverLimit": "number",
  "paidLeave": "boolean",
  "requiresApproval": "boolean",
  "requiresDocument": "boolean",
  "documentRequiredAfterDays": "number",
  "minDaysNotice": "number",
  "gender": "enum: ['all', 'male', 'female']",
  "allowEmployeeSpecificAllocation": "boolean",
  "active": "boolean",
  "createdBy": "ObjectId (ref: User)"
}
```

## Webhooks

### WhatsApp Integration
PandaPayroll supports WhatsApp webhook integration for notifications and employee communication.

#### POST /api/whatsapp/webhook
WhatsApp webhook endpoint for receiving messages and status updates.

**Verification (GET):**
```http
GET /api/whatsapp/webhook?hub.mode=subscribe&hub.verify_token=token&hub.challenge=challenge
```

**Response:** Returns the challenge value for verification.

**Message Webhook (POST):**
```json
{
  "object": "whatsapp_business_account",
  "entry": [
    {
      "id": "entry_id",
      "changes": [
        {
          "value": {
            "messaging_product": "whatsapp",
            "metadata": {
              "display_phone_number": "***********",
              "phone_number_id": "phone_number_id"
            },
            "messages": [
              {
                "from": "***********",
                "id": "message_id",
                "timestamp": "**********",
                "text": {
                  "body": "Hello, I need help with my payslip"
                },
                "type": "text"
              }
            ]
          }
        }
      ]
    }
  ]
}
```

## File Upload APIs

### POST /direct-upload/:companyCode
Direct file upload endpoint for employee bulk import (bypasses authentication for specific use cases).

**Parameters:**
- `companyCode` (string) - Company code

**Request:** Multipart form data with Excel file

**Response:**
```json
{
  "success": true,
  "message": "File uploaded and processed successfully",
  "file": {
    "name": "employees.xlsx",
    "size": 12345,
    "type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
  },
  "preview": [
    {
      "First Name": "John",
      "Last Name": "Doe",
      "Email": "<EMAIL>",
      "Basic Salary": 25000
    }
  ],
  "totalRows": 50,
  "maxEmployees": 1000
}
```

### POST /direct-confirm-upload/:companyCode
Confirm and process uploaded employee data.

**Parameters:**
- `companyCode` (string) - Company code

**Request Body:**
```json
{
  "employees": [
    {
      "First Name": "John",
      "Last Name": "Doe",
      "Email": "<EMAIL>",
      "Date of Birth": "1990-01-01",
      "Date of Appointment": "2025-01-01",
      "Basic Salary": 25000,
      "ID Type": "RSA ID",
      "ID Number": "*************",
      "Payment Method": "EFT",
      "Bank Name": "FNB",
      "Account Number": "**********",
      "Branch Code": "250655",
      "Account Type": "Current"
    }
  ]
}
```

**Response:**
```json
{
  "successful": 45,
  "failed": 5,
  "errors": [
    {
      "name": "Jane Smith",
      "error": "Employee number EMP002 already exists in this company"
    }
  ]
}
```

## Rate Limiting

API endpoints are subject to rate limiting to ensure fair usage:

- **Authentication endpoints**: 5 requests per minute per IP
- **General API endpoints**: 100 requests per minute per authenticated user
- **File upload endpoints**: 10 requests per hour per company

Rate limit headers are included in responses:
```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: **********
```

## Error Codes Reference

### Authentication Errors
- `AUTH_001` - Invalid credentials
- `AUTH_002` - Token expired
- `AUTH_003` - Token malformed
- `AUTH_004` - Insufficient permissions

### Validation Errors
- `VAL_001` - Required field missing
- `VAL_002` - Invalid field format
- `VAL_003` - Field value out of range
- `VAL_004` - Duplicate value

### Business Logic Errors
- `BIZ_001` - Payroll period already finalized
- `BIZ_002` - Employee not found
- `BIZ_003` - Company not found
- `BIZ_004` - Pay run already exists for period

## SDK and Integration Examples

### JavaScript/Node.js Example
```javascript
const axios = require('axios');

class PandaPayrollAPI {
  constructor(baseURL, token) {
    this.client = axios.create({
      baseURL,
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
  }

  async getEmployees(companyCode) {
    const response = await this.client.get(`/clients/${companyCode}/employees`);
    return response.data;
  }

  async createPayRun(companyCode, payRunData) {
    const response = await this.client.post(`/clients/${companyCode}/pay-run/create`, payRunData);
    return response.data;
  }

  async finalizePayslip(companyCode, payslipId) {
    const response = await this.client.post(`/clients/${companyCode}/payslip/${payslipId}/finalize`);
    return response.data;
  }
}

// Usage
const api = new PandaPayrollAPI('https://payroll.pss-group.co.za', 'your-jwt-token');
const employees = await api.getEmployees('COMP001');
```

### Python Example
```python
import requests

class PandaPayrollAPI:
    def __init__(self, base_url, token):
        self.base_url = base_url
        self.headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }

    def get_employees(self, company_code):
        response = requests.get(
            f'{self.base_url}/clients/{company_code}/employees',
            headers=self.headers
        )
        return response.json()

    def create_pay_run(self, company_code, pay_run_data):
        response = requests.post(
            f'{self.base_url}/clients/{company_code}/pay-run/create',
            json=pay_run_data,
            headers=self.headers
        )
        return response.json()

# Usage
api = PandaPayrollAPI('https://payroll.pss-group.co.za', 'your-jwt-token')
employees = api.get_employees('COMP001')
```

---

*This comprehensive API documentation covers all major endpoints and functionality of the PandaPayroll system. For additional support or questions about specific implementations, please contact the development team.*