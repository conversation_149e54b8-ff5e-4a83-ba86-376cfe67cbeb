const mongoose = require("mongoose");
const Employee = require("../models/Employee");
const Company = require("../models/Company");

mongoose.connect("your_mongodb_connection_string", {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

async function associateEmployeesWithCompany() {
  try {
    const company = await Company.findOne(); // Assuming you have only one company for now
    if (!company) {
      console.log("No company found. Please create a company first.");
      return;
    }

    const updatedEmployees = await Employee.updateMany(
      { company: { $exists: false } },
      { $set: { company: company._id } }
    );

    console.log(`Updated ${updatedEmployees.nModified} employees.`);
  } catch (error) {
    console.error("Error associating employees with company:", error);
  } finally {
    mongoose.disconnect();
  }
}

associateEmployeesWithCompany();
