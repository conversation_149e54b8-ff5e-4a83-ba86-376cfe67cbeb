const mongoose = require("mongoose");
const Employee = require("../models/Employee");
const Company = require("../models/Company");
require("dotenv").config();

mongoose.connect(process.env.MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

async function syncEmployeesWithCompanies() {
  try {
    const employees = await Employee.find();

    for (const employee of employees) {
      if (employee.company) {
        await Company.findByIdAndUpdate(employee.company, {
          $addToSet: { employees: employee._id },
        });
      }
    }

    console.log("Sync completed successfully");
  } catch (error) {
    console.error("Error syncing employees with companies:", error);
  } finally {
    mongoose.disconnect();
  }
}

syncEmployeesWithCompanies();
