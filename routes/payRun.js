const express = require("express");
const router = express.Router();
const { ensureAuthenticated } = require("../config/ensureAuthenticated");
const csrfProtection = require("../middleware/csrfProtection");
const PayRun = require("../models/payRun");
const PayrollPeriod = require("../models/PayrollPeriod");
const Payslip = require("../models/Payslip");
const Payroll = require("../models/Payroll");
const payrollCalculations = require("../utils/payrollCalculations");
const Company = require("../models/Company");
const moment = require("moment");
const EFTSettings = require("../models/EFTSettings");
const EFTDetails = require("../models/eftDetails");
const { getOrCreateEFTSettings } = require("../utils/eftSettingsUtils");
const { generateFNBBankFile } = require("../utils/bankFileUtils");
const Employee = require("../models/Employee");

// Debug route registration

// Route to create a pay run for a single period
router.post("/create-period-payrun", ensureAuthenticated, async (req, res) => {
  try {
    const { periodId } = req.body;

    // Find the period and populate employee data
    const period = await Payroll.findById(periodId)
      .populate("employee")
      .populate("company");

    if (!period) {
      return res
        .status(404)
        .json({ success: false, message: "Period not found" });
    }

    if (!period.isFinalized) {
      return res.status(400).json({
        success: false,
        message: "Period must be finalized before creating a pay run",
      });
    }

    // Check if a pay run already exists for this period
    if (period.payRun) {
      return res.status(400).json({
        success: false,
        message: "Pay run already exists for this period",
      });
    }

    // Create new pay run
    const newPayRun = new PayRun({
      company: period.company._id,
      frequency: period.frequency,
      startDate: period.startDate,
      endDate: period.endDate,
      payslips: [period._id], // Link the period as a payslip
      totalPayable: period.calculateNetPay(),
      totalAmount: period.calculateTotalEarnings(),
      status: "created",
    });

    await newPayRun.save();

    // Update the period with the pay run reference
    period.payRun = newPayRun._id;
    await period.save();

    res.json({
      success: true,
      message: "Pay run created successfully",
      payRun: {
        _id: newPayRun._id,
        totalPayable: newPayRun.totalPayable,
        frequency: newPayRun.frequency,
        startDate: newPayRun.startDate,
        endDate: newPayRun.endDate,
      },
    });
  } catch (error) {
    console.error("Error creating pay run:", error);
    res.status(500).json({
      success: false,
      message: "Error creating pay run",
      error: error.message,
    });
  }
});

// Batch pay run creation
router.post("/create-batch-payruns", ensureAuthenticated, async (req, res) => {
  try {
    const { periodIds } = req.body;
    const results = [];

    for (const periodId of periodIds) {
      const period = await Payroll.findById(periodId)
        .populate("employee")
        .populate("company");

      if (!period || !period.isFinalized || period.payRun) {
        results.push({
          periodId,
          success: false,
          message: "Period invalid or already has pay run",
        });
        continue;
      }

      const newPayRun = new PayRun({
        company: period.company._id,
        frequency: period.frequency,
        startDate: period.startDate,
        endDate: period.endDate,
        payslips: [period._id],
        totalPayable: period.calculateNetPay(),
        totalAmount: period.calculateTotalEarnings(),
        status: "created",
      });

      await newPayRun.save();
      period.payRun = newPayRun._id;
      await period.save();

      results.push({
        periodId,
        success: true,
        payRunId: newPayRun._id,
      });
    }

    res.json({
      success: true,
      results,
    });
  } catch (error) {
    console.error("Error in batch pay run creation:", error);
    res.status(500).json({ success: false, message: error.message });
  }
});

// POST /payrun/create - Create a new pay run
router.post("/create", async (req, res) => {
  try {
    const { payPeriod } = req.body;
    const payslips = await Payslip.find({ status: "finalised", payPeriod });
    if (payslips.length === 0) {
      return res
        .status(404)
        .send("No finalised payslips found for this period");
    }
    const payRun = new PayRun({
      payPeriod,
      payslips: payslips.map((p) => p._id),
    });
    await payRun.save();
    res.status(200).send("Pay run created successfully");
  } catch (err) {
    res.status(500).send(err.message);
  }
});

// GET /payrun/list - List all pay runs
router.get("/list", async (req, res) => {
  try {
    const payRuns = await PayRun.find().populate("payslips");
    res.status(200).json(payRuns);
  } catch (err) {
    res.status(500).send(err.message);
  }
});

// GET /payrun/:id - Display a specific pay run
router.get("/:id", async (req, res) => {
  try {
    const payRun = await PayRun.findById(req.params.id).populate({
      path: "payslips",
      populate: {
        path: "employee",
        model: "Employee",
      },
    });

    if (!payRun) {
      return res.status(404).send("Pay run not found");
    }

    let totalGrossPay = 0;
    let totalNetPay = 0;
    let totalUIF = 0;
    let totalSDL = 0;
    let totalPAYE = 0;

    // Fetch the actual payroll data for each payslip
    const payrollData = await Promise.all(
      payRun.payslips.map(async (payslipId) => {
        return await Payroll.findOne({ _id: payslipId }).populate("employee");
      })
    );

    payrollData.forEach((payroll) => {
      if (payroll && payroll.employee) {
        try {
          const employeeAge =
            payrollCalculations.calculateAge(payroll.employee.dateOfBirth) || 0;
          const totalIncome =
            payrollCalculations.calculateTotalIncome(payroll) || 0;
          const payeAmount =
            payrollCalculations.calculate_PAYE(employeeAge, totalIncome) || 0;
          const medicalAidCredit =
            payrollCalculations.calculateMedicalAidCredit(
              payroll.employee.medicalAidMembers || 0
            ) || 0;
          const totalDeductions =
            payrollCalculations.calculateTotalDeductions(
              payroll,
              payeAmount,
              medicalAidCredit
            ) || 0;
          const netPay = Math.max(totalIncome - totalDeductions, 0); // Ensure net pay is not negative


          totalGrossPay += totalIncome;
          totalNetPay += netPay;
          totalUIF += Math.min(totalIncome * 0.01, 148.72);
          totalSDL += totalIncome * 0.01;
          totalPAYE += payeAmount;
        } catch (error) {
          console.error(
            `Error processing payroll for employee ${payroll.employee._id}:`,
            error
          );
        }
      }
    });


    res.render("payrun", {
      payRun,
      totalGrossPay: totalGrossPay || 0,
      totalNetPay: totalNetPay || 0,
      totalUIF: totalUIF || 0,
      totalSDL: totalSDL || 0,
      totalPAYE: totalPAYE || 0,
      moment: moment,
    });
  } catch (err) {
    console.error("Error in /payrun/:id route:", err);
    res.status(500).send("An error occurred while processing the pay run");
  }
});

// GET /payrun/new - Render new pay run creation page
router.get("/new", ensureAuthenticated, async (req, res) => {
  try {
    const finalizedPayslips = await Payslip.find({
      company: req.user.currentCompany,
      isFinalized: true,
      payRun: { $exists: false },
    })
      .populate("employee")
      .sort({ period: -1 });

    const payPeriods = [...new Set(finalizedPayslips.map((p) => p.period))];

    res.render("payrun/new", {
      title: "Create New Pay Run",
      payslips: finalizedPayslips,
      payPeriods,
      user: req.user,
    });
  } catch (error) {
    console.error("Error loading pay run creation page:", error);
    req.flash("error", "Failed to load pay run creation page");
    res.redirect("/payroll/dashboard");
  }
});

// POST /payrun/create - Create a new pay run
router.post("/create", ensureAuthenticated, async (req, res) => {
  try {
    const { payslipIds } = req.body;

    // Validate payslips
    const payslips = await Payslip.find({
      _id: { $in: payslipIds },
      company: req.user.currentCompany,
      isFinalized: true,
      payRun: { $exists: false },
    }).populate("employee");

    if (payslips.length === 0) {
      return res.status(400).json({
        success: false,
        message: "No valid payslips found for pay run creation",
      });
    }

    // Group payslips by period to create separate pay runs
    const payslipsByPeriod = payslips.reduce((acc, payslip) => {
      if (!acc[payslip.period]) {
        acc[payslip.period] = [];
      }
      acc[payslip.period].push(payslip);
      return acc;
    }, {});

    const results = [];

    // Create pay runs for each period
    for (const [period, periodPayslips] of Object.entries(payslipsByPeriod)) {
      const totalPayable = periodPayslips.reduce((sum, p) => sum + p.netPay, 0);
      const totalAmount = periodPayslips.reduce(
        (sum, p) => sum + p.grossPay,
        0
      );

      const newPayRun = new PayRun({
        company: req.user.currentCompany,
        period,
        payslips: periodPayslips.map((p) => p._id),
        totalPayable,
        totalAmount,
        status: "created",
        createdBy: req.user._id,
        createdAt: new Date(),
      });

      await newPayRun.save();

      // Update payslips with pay run reference
      await Payslip.updateMany(
        { _id: { $in: periodPayslips.map((p) => p._id) } },
        { $set: { payRun: newPayRun._id } }
      );

      results.push({
        period,
        payRunId: newPayRun._id,
        totalPayable,
        totalAmount,
        payslipCount: periodPayslips.length,
      });
    }

    res.json({
      success: true,
      message: "Pay run(s) created successfully",
      results,
    });
  } catch (error) {
    console.error("Error creating pay run:", error);
    res.status(500).json({
      success: false,
      message: "Failed to create pay run",
      error: error.message,
    });
  }
});

// GET /payrun/:id/review - Review pay run before finalizing
router.get("/:id/review", ensureAuthenticated, async (req, res) => {
  try {
    const payRun = await PayRun.findOne({
      _id: req.params.id,
      company: req.user.currentCompany,
    }).populate({
      path: "payslips",
      populate: {
        path: "employee",
        select: "firstName lastName employeeId",
      },
    });

    if (!payRun) {
      req.flash("error", "Pay run not found");
      return res.redirect("/payroll/dashboard");
    }

    res.render("payrun/review", {
      title: "Review Pay Run",
      payRun,
      user: req.user,
    });
  } catch (error) {
    console.error("Error loading pay run review:", error);
    req.flash("error", "Failed to load pay run review");
    res.redirect("/payroll/dashboard");
  }
});

// Finalize pay run
router.post("/:id/finalize", ensureAuthenticated, async (req, res) => {
  try {
    const payRun = await PayRun.findById(req.params.id);

    if (!payRun) {
      return res.status(404).json({
        success: false,
        message: "Pay run not found",
      });
    }

    if (payRun.status === "finalized" || payRun.finalized) {
      return res.status(400).json({
        success: false,
        message: "Pay run is already finalized",
      });
    }

    // Update pay run status
    payRun.status = "finalized";
    payRun.finalizedAt = new Date();
    payRun.finalizedBy = req.user._id;

    // Calculate and update totals
    await payRun.updateTotals();

    await payRun.save();

    // Update all associated payslips status to paid
    await Payslip.updateMany(
      { payRun: payRun._id },
      {
        $set: {
          status: "paid",
          finalizedAt: new Date(),
          finalizedBy: req.user._id,
        },
      }
    );

    res.json({
      success: true,
      message: "Pay run finalized successfully",
      payRun: {
        _id: payRun._id,
        status: payRun.status,
        finalized: payRun.finalized,
        finalizedAt: payRun.finalizedAt,
        finalizedBy: payRun.finalizedBy,
      },
    });
  } catch (error) {
    console.error("Error finalizing pay run:", error);
    res.status(500).json({
      success: false,
      message: "Error finalizing pay run",
      error: error.message,
    });
  }
});

// POST /:id/finalize - Finalize a pay run
router.post(
  "/:id/finalize",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {

      const payRun = await PayRun.findOne({
        _id: req.params.id,
        company: req.user.currentCompany,
        status: { $ne: "finalized" },
      });


      if (!payRun) {
        return res.status(404).json({
          success: false,
          message: "Pay run not found or already finalized",
        });
      }

      // Set required fields
      payRun.status = "finalized";
      payRun.finalizedBy = req.user._id;
      payRun.finalizedAt = new Date();

      // Set monthYear if not already set
      if (!payRun.monthYear) {
        const date = payRun.startDate || new Date();
        payRun.monthYear = date.toISOString().slice(0, 7); // Format: YYYY-MM
      }

      // Calculate and update totals
      await payRun.updateTotals();

      await payRun.save();

      // Update all associated payslips status to paid
      await Payslip.updateMany(
        { payRun: payRun._id },
        {
          $set: {
            status: "paid",
            finalizedAt: new Date(),
            finalizedBy: req.user._id,
          },
        }
      );


      res.json({
        success: true,
        message: "Pay run finalized successfully",
        status: "finalized", // Send back the new status
      });
    } catch (error) {
      console.error("Error finalizing pay run:", error);
      res.status(500).json({
        success: false,
        message: "Failed to finalize pay run",
        error: error.message,
      });
    }
  }
);

// GET /payrun/:id/view - View a specific pay run details
router.get(
  "/:id/view",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {

      const payRun = await PayRun.findById(req.params.id)
        .populate({
          path: "payslips",
          populate: {
            path: "employee",
            model: "Employee",
          },
        })
        .populate("company");

      if (!payRun) {
        return res.status(404).render("error", {
          message: "Pay Run not found",
          error: { status: 404 },
          user: req.user,
          company: req.user.currentCompany,
        });
      }

      // Fetch detailed payroll information for each payslip
      const payrollDetails = await Promise.all(
        payRun.payslips.map(async (payslip) => {
          const payroll = await Payroll.findOne({ _id: payslip._id }).populate(
            "employee"
          );

          return {
            payslip: payslip,
            payroll: payroll,
            netPay: payroll ? payroll.calculateNetPay() : 0,
          };
        })
      );


      // Render the view with detailed pay run information
      res.render("payroll/pay-run/view", {
        title: `Pay Run - ${moment(payRun.startDate).format("MMMM YYYY")}`,
        payRun: payRun,
        payrollDetails: payrollDetails,
        company: payRun.company,
        user: req.user,
        csrfToken: req.csrfToken(),
        moment: moment,
      });
    } catch (error) {
      console.error("Error viewing pay run:", error);
      res.status(500).render("error", {
        message: "Error retrieving pay run details",
        error: { status: 500 },
        user: req.user,
        company: req.user.currentCompany,
      });
    }
  }
);

// GET /payrun/:id/edit - Edit a specific pay run
router.get(
  "/:id/edit",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {

      const payRun = await PayRun.findById(req.params.id)
        .populate({
          path: "payslips",
          populate: [
            {
              path: "employee",
              model: "Employee",
              select: "firstName lastName employeeNumber paymentMethod",
            },
            {
              path: "payroll",
              model: "Payroll",
              populate: {
                path: "employee",
                model: "Employee",
                select: "firstName lastName employeeNumber paymentMethod",
              },
            },
          ],
        })
        .populate("company");

      if (!payRun) {
        return res.status(404).render("error", {
          message: "Pay Run not found",
          error: { status: 404 },
          user: req.user,
          company: req.user.currentCompany,
        });
      }

      // Check if pay run can be edited
      if (payRun.status === "finalized") {
        return res.status(400).render("error", {
          message: "Cannot edit a finalized pay run",
          error: { status: 400 },
          user: req.user,
          company: req.user.currentCompany,
        });
      }

      // Fetch detailed payroll information for each payslip
      const payrollDetails = await Promise.all(
        payRun.payslips.map(async (payslip) => {
          const payroll = await Payroll.findOne({ _id: payslip._id }).populate(
            "employee"
          );

          return {
            payslip: payslip,
            payroll: payroll,
            netPay: payroll ? payroll.calculateNetPay() : 0,
            deductions: payroll ? payroll.calculateTotalDeductions() : 0,
          };
        })
      );


      // Render the edit view
      res.render("payroll/pay-run/edit", {
        title: `Edit Pay Run - ${moment(payRun.startDate).format("MMMM YYYY")}`,
        payRun: payRun,
        payrollDetails: payrollDetails,
        company: payRun.company,
        user: req.user,
        csrfToken: req.csrfToken(),
        moment: moment,
      });
    } catch (error) {
      console.error("Error editing pay run:", error);
      res.status(500).render("error", {
        message: "Error retrieving pay run details for editing",
        error: { status: 500 },
        user: req.user,
        company: req.user.currentCompany,
      });
    }
  }
);

// Bank file generation endpoint
router.post(
  "/:payRunId/bank-file/:format",
  ensureAuthenticated,
  async (req, res) => {

    try {
      // Validate action date
      const { actionDate } = req.body;
      if (!actionDate) {
        return res.status(400).json({
          success: false,
          message: "Action date is required",
        });
      }

      // Validate date format
      const parsedDate = new Date(actionDate);
      if (isNaN(parsedDate.getTime())) {
        return res.status(400).json({
          success: false,
          message: "Invalid action date format",
        });
      }

      const payRun = await PayRun.findById(req.params.payRunId).populate({
        path: "payrollPeriods",
        select: "_id startDate endDate",
      });

      if (!payRun) {
        return res.status(404).json({
          success: false,
          message: "Pay run not found",
        });
      }

      // Debug logging for payroll periods and payslips

      // Fetch payslips with more flexible criteria
      const payslipSearchCriteria = {
        company: payRun.company,
        $or: [
          { payrollPeriod: { $in: payRun.payrollPeriods } },
          { payRun: payRun._id },
        ],
        status: { $nin: ["deleted", "cancelled"] },
      };

      const payslips = await Payslip.find(payslipSearchCriteria)
        .populate("employee", "firstName lastName")
        .populate("payrollPeriod", "startDate endDate frequency")
        .lean();


      if (payslips.length === 0) {
        return res.status(400).json({
          success: false,
          message:
            "No payslips found for this pay run. Please process payroll first.",
          details: {
            payRunId: payRun._id,
            payrollPeriods: payRun.payrollPeriods.map((p) => p.toString()),
            searchCriteria: payslipSearchCriteria,
          },
        });
      }

      // Populate payslips with full employee details
      const payslipsWithDetails = await Promise.all(
        payslips.map(async (payslip) => {
          // Populate full employee details
          const fullPayslip = await Payslip.findById(payslip._id).populate(
            "employee"
          );

          if (!fullPayslip.employee) {
            return null;
          }

          const employee = fullPayslip.employee;

          // Destructure employee details for bank file generation
          const {
            bank,
            accountNumber,
            branchCode,
            accountType,
            firstName,
            lastName,
          } = fullPayslip.employee;

          // Validate bank details
          const missingDetails = [];

          // Check for required bank details with actual string values
          if (!bank || typeof bank !== "string" || bank.trim() === "") {
            missingDetails.push("Bank Name");
            bank = "Missing";
          }
          if (
            !accountNumber ||
            typeof accountNumber !== "string" ||
            accountNumber.trim() === ""
          ) {
            missingDetails.push("Account Number");
            accountNumber = "Missing";
          }
          if (
            !branchCode ||
            typeof branchCode !== "string" ||
            branchCode.trim() === ""
          ) {
            missingDetails.push("Branch Code");
            branchCode = "Missing";
          }
          if (
            !accountType ||
            typeof accountType !== "string" ||
            accountType.trim() === ""
          ) {
            missingDetails.push("Account Type");
            accountType = "Missing";
          }

          // Detailed logging for bank details

          return {
            ...fullPayslip.toObject(),
            bankDetails: {
              bank,
              accountNumber,
              branchCode,
              accountType,
              holderName: `${firstName} ${lastName}`,
            },
            missingDetails,
            employeeName: `${firstName} ${lastName}`,
            missingBankDetails: missingDetails.length > 0,
          };
        })
      ).then((details) => details.filter((detail) => detail !== null));

      // Separate valid and invalid payslips
      const invalidPayslips = payslipsWithDetails.filter(
        (payslip) => payslip.missingDetails.length > 0
      );

      // If any payslips have missing details, provide detailed error
      if (invalidPayslips.length > 0) {
        const errorDetails = invalidPayslips.map((payslip) => ({
          employeeName: payslip.employeeName,
          missingFields: payslip.missingDetails,
          payslipId: payslip._id,
        }));


        return res.status(400).json({
          success: false,
          message:
            "Cannot generate bank file. Some employees have incomplete bank details.",
          invalidPayslips: errorDetails,
        });
      }

      // If we reach here, all payslips have complete bank details
      const validPayslips = payslipsWithDetails;

      // Update pay run with found payslips
      payRun.payslips = validPayslips.map((p) => p._id);
      await payRun.save();

      // Get or create EFT settings for the company
      const eftSettings = await getOrCreateEFTSettings(payRun.company);

      if (!eftSettings) {
        console.error("EFT Settings not found for company:", payRun.company);
        return res.status(400).json({
          success: false,
          message: "EFT Settings not configured",
        });
      }


      // Validate bank details for payslips
      const payslipBankDetailsErrors = payRun.payslips.filter(
        (payslip) =>
          !payslip.employeeDetails?.accountNumber ||
          !payslip.employeeDetails?.branchCode ||
          !payslip.employeeDetails?.bank
      );

      if (payslipBankDetailsErrors.length > 0) {
        const missingBankDetails = payRun.payslips
          .filter((p) => p.paymentMethod === "EFT")
          .map((p) => {
            const employee = p.employee;
            return {
              employeeName: employee
                ? `${employee.firstName} ${employee.lastName}`
                : "Unknown Employee",
              missingAccountNumber: !employee?.accountNumber,
              missingBranchCode: !employee?.branchCode,
              missingBank: !employee?.bank,
            };
          });

        return res.status(400).json({
          success: false,
          message: "Some payslips have incomplete bank details",
          invalidPayslips: missingBankDetails,
        });
      }

      // Import the new bank file generator factory
      const BankFileGeneratorFactory = require('../utils/bankFileGenerators/BankFileGeneratorFactory');
      const { mapEftFormatToBankFileFormat, getFileExtensionForFormat } = require('../utils/eftSettingsUtils');

      // Get EFT format from company settings
      const EFTDetails = require('../models/eftDetails');
      const eftDetails = await EFTDetails.findOne({ company: payRun.company });
      const eftFormat = eftDetails?.eftFormat || 'FNB Online Banking (ACB)';

      // Map EFT format to bank file format
      const bankFileFormat = mapEftFormatToBankFileFormat(eftFormat);

      console.log(`Generating bank file: EFT Format = ${eftFormat}, Bank File Format = ${bankFileFormat}`);

      // Generate bank file using the new factory
      let bankFileResult;
      try {
        bankFileResult = await BankFileGeneratorFactory.generateBankFile(
          eftFormat,
          bankFileFormat,
          { ...payRun.toObject(), payrollPeriods: validPayslips },
          eftSettings,
          actionDate
        );
      } catch (error) {
        console.error("Bank file generation failed:", error);
        return res.status(500).json({
          success: false,
          message: "Failed to generate bank file",
          error: error.message
        });
      }

      if (!bankFileResult || !bankFileResult.content) {
        console.error("Failed to generate bank file - no content returned");
        return res.status(500).json({
          success: false,
          message: "Failed to generate bank file",
        });
      }

      // Save bank file to PayRun
      payRun.bankFile = {
        format: eftFormat,
        bankFileFormat: bankFileFormat,
        content: bankFileResult.content,
        generatedAt: new Date(),
        actionDate: new Date(actionDate),
        generator: bankFileResult.generator
      };
      await payRun.save();

      // Set response headers with proper file extension and MIME type
      res.set({
        "Content-Type": bankFileResult.mimeType,
        "Content-Disposition": `attachment; filename="${bankFileResult.filename}"`,
        "Content-Transfer-Encoding": "binary",
      });
      res.send(bankFileResult.content);
    } catch (error) {
      console.error("Error in Bank File Generation Route:", error);
      res.status(500).json({
        success: false,
        message: "Internal server error",
        error: error.message,
      });
    }
  }
);

// Also support the route without /api prefix
router.post(
  "/api/pay-runs/:id/bank-file/:format",
  ensureAuthenticated,
  (req, res) => {
    // Forward to the API route
    req.url = `/api${req.url}`;
    return router.handle(req, res);
  }
);

// POST /payrun/:id/update - Update a specific pay run
router.post(
  "/:id/update",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {

      const payRun = await PayRun.findById(req.params.id);

      if (!payRun) {
        return res.status(404).json({
          success: false,
          message: "Pay Run not found",
        });
      }

      // Check if pay run can be edited
      if (payRun.status === "finalized") {
        return res.status(400).json({
          success: false,
          message: "Cannot edit a finalized pay run",
        });
      }

      // Update pay run details
      if (req.body.startDate) payRun.startDate = new Date(req.body.startDate);
      if (req.body.endDate) payRun.endDate = new Date(req.body.endDate);
      if (req.body.totalPayable)
        payRun.totalPayable = parseFloat(req.body.totalPayable);
      if (req.body.notes) payRun.notes = req.body.notes;

      // Update individual payslips
      if (req.body.payslips && Array.isArray(req.body.payslips)) {
        for (const payslipUpdate of req.body.payslips) {
          const payroll = await Payroll.findOne({
            _id: payslipUpdate.payslipId,
          });

          if (payroll) {
            // Update basic salary
            if (payslipUpdate.basicSalary) {
              payroll.basicSalary = parseFloat(payslipUpdate.basicSalary);
            }

            // Update payment method
            if (payslipUpdate.paymentMethod) {
              const employee = await Employee.findById(payroll.employee);
              if (employee) {
                employee.paymentMethod = payslipUpdate.paymentMethod;
                await employee.save();
              }
            }

            await payroll.save();
          }
        }
      }

      // Save updated pay run
      await payRun.save();

      res.json({
        success: true,
        message: "Pay run updated successfully",
        payRunId: payRun._id,
      });
    } catch (error) {
      console.error("Error updating pay run:", error);
      res.status(500).json({
        success: false,
        message: "Error updating pay run",
        error: error.message,
      });
    }
  }
);

// GET /payrun/:id/reports/download - Download pay run reports
router.get(
  "/:id/reports/download",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {

      // Find the pay run with populated details
      const payRun = await PayRun.findById(req.params.id)
        .populate({
          path: "payslips",
          populate: [
            {
              path: "employee",
              model: "Employee",
              select:
                "firstName lastName employeeNumber email phone department",
            },
            {
              path: "payroll",
              model: "Payroll",
              populate: {
                path: "employee",
                model: "Employee",
                select: "firstName lastName employeeNumber",
              },
            },
          ],
        })
        .populate("company");

      if (!payRun) {
        return res.status(404).json({
          error: "Pay Run not found",
          message: "The specified pay run could not be located in the system.",
        });
      }

      // Prepare report data
      const reportData = payRun.payslips.map((payslip, index) => {
        const employee = payslip.employee;
        const payroll = payslip.payroll;

        return {
          employeeName: `${employee.firstName} ${employee.lastName}`,
          employeeNumber: employee.employeeNumber,
          department: employee.department || "Not Specified",
          basicSalary: payroll.basicSalary
            ? payroll.basicSalary.toFixed(2)
            : "0.00",
          deductions: payroll.calculateTotalDeductions
            ? payroll.calculateTotalDeductions().toFixed(2)
            : "0.00",
          netPay: payroll.calculateNetPay
            ? payroll.calculateNetPay().toFixed(2)
            : "0.00",
          paymentMethod: payroll.paymentMethod || "Not Specified",
        };
      });

      // Generate CSV content
      const csvContent = [
        // CSV Header
        "Employee Name,Employee Number,Department,Basic Salary,Deductions,Net Pay,Payment Method",

        // CSV Data Rows
        ...reportData.map(
          (row) =>
            `"${row.employeeName}","${row.employeeNumber}","${row.department}","R ${row.basicSalary}","R ${row.deductions}","R ${row.netPay}","${row.paymentMethod}"`
        ),
      ].join("\n");

      // Generate filename
      const filename = `payrun_report_${moment(payRun.startDate).format(
        "YYYY-MM-DD"
      )}_${moment(payRun.endDate).format("YYYY-MM-DD")}.csv`;

      // Set headers for file download
      res.setHeader("Content-Type", "text/csv");
      res.setHeader(
        "Content-Disposition",
        `attachment; filename="${filename}"`
      );

      // Send CSV content
      res.send(csvContent);
    } catch (error) {
      console.error("Error downloading pay run reports:", error);
      res.status(500).json({
        error: "Server Error",
        message: "An error occurred while generating the pay run report.",
        details: error.message,
      });
    }
  }
);

// POST /payrun/:id/finalize - Finalize a pay run
router.post(
  "/:id/finalize",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {

      // Find the pay run
      const payRun = await PayRun.findById(req.params.id)
        .populate("company")
        .populate({
          path: "payslips",
          populate: [
            { path: "employee", model: "Employee" },
            { path: "payroll", model: "Payroll" },
          ],
        });

      // Validate pay run exists
      if (!payRun) {
        return res.status(404).json({
          success: false,
          error: "Pay Run not found",
          message: "The specified pay run could not be located in the system.",
        });
      }

      // Check if pay run is already finalized
      if (payRun.status === "finalized") {
        return res.status(400).json({
          success: false,
          error: "Already Finalized",
          message:
            "This pay run has already been finalized and cannot be modified.",
        });
      }

      // Validate all payslips are ready
      const incompletePayslips = payRun.payslips.filter(
        (payslip) =>
          !payslip.payroll ||
          !payslip.payroll.basicSalary ||
          payslip.payroll.basicSalary <= 0
      );

      if (incompletePayslips.length > 0) {
        return res.status(400).json({
          success: false,
          error: "Incomplete Payslips",
          message:
            "Some payslips are incomplete. Please ensure all employee payroll details are filled out.",
          incompleteCount: incompletePayslips.length,
        });
      }

      // Update pay run status
      payRun.status = "finalized";
      payRun.finalizedAt = new Date();
      payRun.finalizedBy = req.user._id;

      // Calculate and update totals
      await payRun.updateTotals();

      // Update individual payslip statuses
      await Promise.all(
        payRun.payslips.map(async (payslip) => {
          payslip.status = "finalized";

          // Update corresponding payroll record
          await Payroll.findByIdAndUpdate(payslip.payroll._id, {
            status: "processed",
            processedAt: new Date(),
          });

          return payslip.save();
        })
      );

      // Save the updated pay run
      await payRun.save();

      // Log the finalization

      // Send success response
      res.status(200).json({
        success: true,
        message: "Pay Run successfully finalized",
        payRun: {
          id: payRun._id,
          status: payRun.status,
          finalizedAt: payRun.finalizedAt,
        },
      });
    } catch (error) {
      console.error("Error finalizing pay run:", error);
      res.status(500).json({
        success: false,
        error: "Server Error",
        message: "An error occurred while finalizing the pay run.",
        details: error.message,
      });
    }
  }
);

// EFT format route with explicit path handling
router.get("/:payRunId/eft-format", ensureAuthenticated, async (req, res) => {

  try {
    const payRun = await PayRun.findById(req.params.payRunId);

    if (!payRun) {
      return res.status(404).json({
        success: false,
        message: "Pay run not found",
      });
    }


    // Get or create EFT settings for the company
    const eftSettings = await getOrCreateEFTSettings(payRun.company);


    if (!eftSettings) {
      return res.status(400).json({
        success: false,
        message: "EFT settings not configured",
      });
    }

    // Return the EFT format
    res.json({
      success: true,
      format: eftSettings.bankFileFormat,
    });
  } catch (error) {
    console.error("Error retrieving EFT format:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
});

// Bank file generation route with explicit path handling
router.post(
  "/:payRunId/bank-file/:format",
  ensureAuthenticated,
  async (req, res) => {

    try {
      // Validate action date
      const { actionDate } = req.body;
      if (!actionDate) {
        return res.status(400).json({
          success: false,
          message: "Action date is required",
        });
      }

      // Validate date format
      const parsedDate = new Date(actionDate);
      if (isNaN(parsedDate.getTime())) {
        return res.status(400).json({
          success: false,
          message: "Invalid action date format",
        });
      }

      const payRun = await PayRun.findById(req.params.payRunId).populate({
        path: "payrollPeriods",
        select: "_id startDate endDate",
      });

      if (!payRun) {
        return res.status(404).json({
          success: false,
          message: "Pay run not found",
        });
      }

      // Debug logging for payroll periods and payslips

      // Fetch payslips with more flexible criteria
      const payslipSearchCriteria = {
        company: payRun.company,
        $or: [
          { payrollPeriod: { $in: payRun.payrollPeriods } },
          { payRun: payRun._id },
        ],
        status: { $nin: ["deleted", "cancelled"] },
      };

      const payslips = await Payslip.find(payslipSearchCriteria)
        .populate("employee", "firstName lastName")
        .populate("payrollPeriod", "startDate endDate frequency")
        .lean();


      if (payslips.length === 0) {
        return res.status(400).json({
          success: false,
          message:
            "No payslips found for this pay run. Please process payroll first.",
          details: {
            payRunId: payRun._id,
            payrollPeriods: payRun.payrollPeriods.map((p) => p.toString()),
            searchCriteria: payslipSearchCriteria,
          },
        });
      }

      // Populate payslips with full employee details
      const payslipsWithDetails = await Promise.all(
        payslips.map(async (payslip) => {
          // Populate full employee details
          const fullPayslip = await Payslip.findById(payslip._id).populate(
            "employee"
          );

          if (!fullPayslip.employee) {
            return null;
          }

          const employee = fullPayslip.employee;

          // Destructure employee details for bank file generation
          const {
            bank,
            accountNumber,
            branchCode,
            accountType,
            firstName,
            lastName,
          } = employee;

          // Validate bank details
          const missingDetails = [];

          // Check for required bank details with actual string values
          if (!bank || typeof bank !== "string" || bank.trim() === "") {
            missingDetails.push("Bank Name");
            bank = "Missing";
          }
          if (
            !accountNumber ||
            typeof accountNumber !== "string" ||
            accountNumber.trim() === ""
          ) {
            missingDetails.push("Account Number");
            accountNumber = "Missing";
          }
          if (
            !branchCode ||
            typeof branchCode !== "string" ||
            branchCode.trim() === ""
          ) {
            missingDetails.push("Branch Code");
            branchCode = "Missing";
          }
          if (
            !accountType ||
            typeof accountType !== "string" ||
            accountType.trim() === ""
          ) {
            missingDetails.push("Account Type");
            accountType = "Missing";
          }

          // Detailed logging for bank details

          return {
            ...fullPayslip.toObject(),
            bankDetails: {
              bank,
              accountNumber,
              branchCode,
              accountType,
              holderName: `${firstName} ${lastName}`,
            },
            missingDetails,
            employeeName: `${firstName} ${lastName}`,
            missingBankDetails: missingDetails.length > 0,
          };
        })
      ).then((details) => details.filter((detail) => detail !== null));

      // Separate valid and invalid payslips
      const invalidPayslips = payslipsWithDetails.filter(
        (payslip) => payslip.missingDetails.length > 0
      );

      // If any payslips have missing details, provide detailed error
      if (invalidPayslips.length > 0) {
        const errorDetails = invalidPayslips.map((payslip) => ({
          employeeName: payslip.employeeName,
          missingFields: payslip.missingDetails,
          payslipId: payslip._id,
        }));


        return res.status(400).json({
          success: false,
          message:
            "Cannot generate bank file. Some employees have incomplete bank details.",
          invalidPayslips: errorDetails,
        });
      }

      // If we reach here, all payslips have complete bank details
      const validPayslips = payslipsWithDetails;

      // Update pay run with found payslips
      payRun.payslips = validPayslips.map((p) => p._id);
      await payRun.save();

      // Get or create EFT settings for the company
      const eftSettings = await getOrCreateEFTSettings(payRun.company);

      if (!eftSettings) {
        console.error("EFT Settings not found for company:", payRun.company);
        return res.status(400).json({
          success: false,
          message: "EFT Settings not configured",
        });
      }


      // Validate bank details for payslips
      const payslipBankDetailsErrors = payRun.payslips.filter(
        (payslip) =>
          !payslip.employeeDetails?.accountNumber ||
          !payslip.employeeDetails?.branchCode ||
          !payslip.employeeDetails?.bank
      );

      if (payslipBankDetailsErrors.length > 0) {
        const missingBankDetails = payRun.payslips
          .filter((p) => p.paymentMethod === "EFT")
          .map((p) => {
            const employee = p.employee;
            return {
              employeeName: employee
                ? `${employee.firstName} ${employee.lastName}`
                : "Unknown Employee",
              missingAccountNumber: !employee?.accountNumber,
              missingBranchCode: !employee?.branchCode,
              missingBank: !employee?.bank,
            };
          });

        return res.status(400).json({
          success: false,
          message: "Some payslips have incomplete bank details",
          invalidPayslips: missingBankDetails,
        });
      }

      // Bank file generation logic based on format
      let bankFile;
      switch (format.toLowerCase()) {
        case "fnb":
          bankFile = await generateFNBBankFile(payRun, eftSettings, actionDate);
          break;
        // Add other bank formats here
        default:
          console.error("Unsupported bank file format:", format);
          return res.status(400).json({
            success: false,
            message: "Unsupported bank file format",
          });
      }

      if (!bankFile) {
        console.error("Failed to generate bank file");
        return res.status(500).json({
          success: false,
          message: "Failed to generate bank file",
        });
      }

      // Save bank file to PayRun
      payRun.bankFile = {
        format: format,
        content: bankFile,
        generatedAt: new Date(),
        actionDate: new Date(actionDate),
      };
      await payRun.save();

      const filename = `payrun_${payRunId}_${format}.acb`;
      res.set({
        "Content-Type": "application/octet-stream",
        "Content-Disposition": `attachment; filename="${filename}"`,
        "Content-Transfer-Encoding": "binary",
      });
      res.send(bankFile);
    } catch (error) {
      console.error("Error in Bank File Generation Route:", error);
      res.status(500).json({
        success: false,
        message: "Internal server error",
        error: error.message,
      });
    }
  }
);

// Also support the route without /api prefix
router.post(
  "/api/pay-runs/:id/bank-file/:format",
  ensureAuthenticated,
  (req, res) => {
    // Forward to the API route
    req.url = `/api${req.url}`;
    return router.handle(req, res);
  }
);

// POST /payrun/:id/update - Update a specific pay run
router.post(
  "/:id/update",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {

      const payRun = await PayRun.findById(req.params.id);

      if (!payRun) {
        return res.status(404).json({
          success: false,
          message: "Pay Run not found",
        });
      }

      // Check if pay run can be edited
      if (payRun.status === "finalized") {
        return res.status(400).json({
          success: false,
          message: "Cannot edit a finalized pay run",
        });
      }

      // Update pay run details
      if (req.body.startDate) payRun.startDate = new Date(req.body.startDate);
      if (req.body.endDate) payRun.endDate = new Date(req.body.endDate);
      if (req.body.totalPayable)
        payRun.totalPayable = parseFloat(req.body.totalPayable);
      if (req.body.notes) payRun.notes = req.body.notes;

      // Update individual payslips
      if (req.body.payslips && Array.isArray(req.body.payslips)) {
        for (const payslipUpdate of req.body.payslips) {
          const payroll = await Payroll.findOne({
            _id: payslipUpdate.payslipId,
          });

          if (payroll) {
            // Update basic salary
            if (payslipUpdate.basicSalary) {
              payroll.basicSalary = parseFloat(payslipUpdate.basicSalary);
            }

            // Update payment method
            if (payslipUpdate.paymentMethod) {
              const employee = await Employee.findById(payroll.employee);
              if (employee) {
                employee.paymentMethod = payslipUpdate.paymentMethod;
                await employee.save();
              }
            }

            await payroll.save();
          }
        }
      }

      // Save updated pay run
      await payRun.save();

      res.json({
        success: true,
        message: "Pay run updated successfully",
        payRunId: payRun._id,
      });
    } catch (error) {
      console.error("Error updating pay run:", error);
      res.status(500).json({
        success: false,
        message: "Error updating pay run",
        error: error.message,
      });
    }
  }
);

// GET /payrun/:id/reports/download - Download pay run reports
router.get(
  "/:id/reports/download",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {

      // Find the pay run with populated details
      const payRun = await PayRun.findById(req.params.id)
        .populate({
          path: "payslips",
          populate: [
            {
              path: "employee",
              model: "Employee",
              select:
                "firstName lastName employeeNumber email phone department",
            },
            {
              path: "payroll",
              model: "Payroll",
              populate: {
                path: "employee",
                model: "Employee",
                select: "firstName lastName employeeNumber",
              },
            },
          ],
        })
        .populate("company");

      if (!payRun) {
        return res.status(404).json({
          error: "Pay Run not found",
          message: "The specified pay run could not be located in the system.",
        });
      }

      // Prepare report data
      const reportData = payRun.payslips.map((payslip, index) => {
        const employee = payslip.employee;
        const payroll = payslip.payroll;

        return {
          employeeName: `${employee.firstName} ${employee.lastName}`,
          employeeNumber: employee.employeeNumber,
          department: employee.department || "Not Specified",
          basicSalary: payroll.basicSalary
            ? payroll.basicSalary.toFixed(2)
            : "0.00",
          deductions: payroll.calculateTotalDeductions
            ? payroll.calculateTotalDeductions().toFixed(2)
            : "0.00",
          netPay: payroll.calculateNetPay
            ? payroll.calculateNetPay().toFixed(2)
            : "0.00",
          paymentMethod: payroll.paymentMethod || "Not Specified",
        };
      });

      // Generate CSV content
      const csvContent = [
        // CSV Header
        "Employee Name,Employee Number,Department,Basic Salary,Deductions,Net Pay,Payment Method",

        // CSV Data Rows
        ...reportData.map(
          (row) =>
            `"${row.employeeName}","${row.employeeNumber}","${row.department}","R ${row.basicSalary}","R ${row.deductions}","R ${row.netPay}","${row.paymentMethod}"`
        ),
      ].join("\n");

      // Generate filename
      const filename = `payrun_report_${moment(payRun.startDate).format(
        "YYYY-MM-DD"
      )}_${moment(payRun.endDate).format("YYYY-MM-DD")}.csv`;

      // Set headers for file download
      res.setHeader("Content-Type", "text/csv");
      res.setHeader(
        "Content-Disposition",
        `attachment; filename="${filename}"`
      );

      // Send CSV content
      res.send(csvContent);
    } catch (error) {
      console.error("Error downloading pay run reports:", error);
      res.status(500).json({
        error: "Server Error",
        message: "An error occurred while generating the pay run report.",
        details: error.message,
      });
    }
  }
);

// POST /payrun/:id/finalize - Finalize a pay run
router.post(
  "/:id/finalize",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {

      // Find the pay run
      const payRun = await PayRun.findById(req.params.id)
        .populate("company")
        .populate({
          path: "payslips",
          populate: [
            { path: "employee", model: "Employee" },
            { path: "payroll", model: "Payroll" },
          ],
        });

      // Validate pay run exists
      if (!payRun) {
        return res.status(404).json({
          success: false,
          error: "Pay Run not found",
          message: "The specified pay run could not be located in the system.",
        });
      }

      // Check if pay run is already finalized
      if (payRun.status === "finalized") {
        return res.status(400).json({
          success: false,
          error: "Already Finalized",
          message:
            "This pay run has already been finalized and cannot be modified.",
        });
      }

      // Validate all payslips are ready
      const incompletePayslips = payRun.payslips.filter(
        (payslip) =>
          !payslip.payroll ||
          !payslip.payroll.basicSalary ||
          payslip.payroll.basicSalary <= 0
      );

      if (incompletePayslips.length > 0) {
        return res.status(400).json({
          success: false,
          error: "Incomplete Payslips",
          message:
            "Some payslips are incomplete. Please ensure all employee payroll details are filled out.",
          incompleteCount: incompletePayslips.length,
        });
      }

      // Update pay run status
      payRun.status = "finalized";
      payRun.finalizedAt = new Date();
      payRun.finalizedBy = req.user._id;

      // Calculate and update totals
      await payRun.updateTotals();

      // Update individual payslip statuses
      await Promise.all(
        payRun.payslips.map(async (payslip) => {
          payslip.status = "finalized";

          // Update corresponding payroll record
          await Payroll.findByIdAndUpdate(payslip.payroll._id, {
            status: "processed",
            processedAt: new Date(),
          });

          return payslip.save();
        })
      );

      // Save the updated pay run
      await payRun.save();

      // Log the finalization

      // Send success response
      res.status(200).json({
        success: true,
        message: "Pay Run successfully finalized",
        payRun: {
          id: payRun._id,
          status: payRun.status,
          finalizedAt: payRun.finalizedAt,
        },
      });
    } catch (error) {
      console.error("Error finalizing pay run:", error);
      res.status(500).json({
        success: false,
        error: "Server Error",
        message: "An error occurred while finalizing the pay run.",
        details: error.message,
      });
    }
  }
);

// EFT format route with explicit path handling
router.get("/:payRunId/eft-format", ensureAuthenticated, async (req, res) => {

  try {
    const payRun = await PayRun.findById(req.params.payRunId);

    if (!payRun) {
      return res.status(404).json({
        success: false,
        message: "Pay run not found",
      });
    }


    // Get or create EFT settings for the company
    const eftSettings = await getOrCreateEFTSettings(payRun.company);


    if (!eftSettings) {
      return res.status(400).json({
        success: false,
        message: "EFT settings not configured",
      });
    }

    // Return the EFT format
    res.json({
      success: true,
      format: eftSettings.bankFileFormat,
    });
  } catch (error) {
    console.error("Error retrieving EFT format:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
});

// Bank file generation route with explicit path handling
router.post(
  "/:payRunId/bank-file/:format",
  ensureAuthenticated,
  async (req, res) => {

    try {
      // Validate action date
      const { actionDate } = req.body;
      if (!actionDate) {
        return res.status(400).json({
          success: false,
          message: "Action date is required",
        });
      }

      // Validate date format
      const parsedDate = new Date(actionDate);
      if (isNaN(parsedDate.getTime())) {
        return res.status(400).json({
          success: false,
          message: "Invalid action date format",
        });
      }

      const payRun = await PayRun.findById(req.params.payRunId).populate({
        path: "payrollPeriods",
        select: "_id startDate endDate",
      });

      if (!payRun) {
        return res.status(404).json({
          success: false,
          message: "Pay run not found",
        });
      }

      // Debug logging for payroll periods and payslips

      // Fetch payslips with more flexible criteria
      const payslipSearchCriteria = {
        company: payRun.company,
        $or: [
          { payrollPeriod: { $in: payRun.payrollPeriods } },
          { payRun: payRun._id },
        ],
        status: { $nin: ["deleted", "cancelled"] },
      };

      const payslips = await Payslip.find(payslipSearchCriteria)
        .populate("employee", "firstName lastName")
        .populate("payrollPeriod", "startDate endDate frequency")
        .lean();


      if (payslips.length === 0) {
        return res.status(400).json({
          success: false,
          message:
            "No payslips found for this pay run. Please process payroll first.",
          details: {
            payRunId: payRun._id,
            payrollPeriods: payRun.payrollPeriods.map((p) => p.toString()),
            searchCriteria: payslipSearchCriteria,
          },
        });
      }

      // Populate payslips with full employee details
      const payslipsWithDetails = await Promise.all(
        payslips.map(async (payslip) => {
          // Populate full employee details
          const fullPayslip = await Payslip.findById(payslip._id).populate(
            "employee"
          );

          if (!fullPayslip.employee) {
            return null;
          }

          const employee = fullPayslip.employee;

          // Destructure employee details for bank file generation
          const {
            bank,
            accountNumber,
            branchCode,
            accountType,
            firstName,
            lastName,
          } = employee;

          // Validate bank details
          const missingDetails = [];

          // Check for required bank details with actual string values
          if (!bank || typeof bank !== "string" || bank.trim() === "") {
            missingDetails.push("Bank Name");
            bank = "Missing";
          }
          if (
            !accountNumber ||
            typeof accountNumber !== "string" ||
            accountNumber.trim() === ""
          ) {
            missingDetails.push("Account Number");
            accountNumber = "Missing";
          }
          if (
            !branchCode ||
            typeof branchCode !== "string" ||
            branchCode.trim() === ""
          ) {
            missingDetails.push("Branch Code");
            branchCode = "Missing";
          }
          if (
            !accountType ||
            typeof accountType !== "string" ||
            accountType.trim() === ""
          ) {
            missingDetails.push("Account Type");
            accountType = "Missing";
          }

          // Detailed logging for bank details

          return {
            ...fullPayslip.toObject(),
            bankDetails: {
              bank,
              accountNumber,
              branchCode,
              accountType,
              holderName: `${firstName} ${lastName}`,
            },
            missingDetails,
            employeeName: `${firstName} ${lastName}`,
            missingBankDetails: missingDetails.length > 0,
          };
        })
      ).then((details) => details.filter((detail) => detail !== null));

      // Separate valid and invalid payslips
      const invalidPayslips = payslipsWithDetails.filter(
        (payslip) => payslip.missingDetails.length > 0
      );

      // If any payslips have missing details, provide detailed error
      if (invalidPayslips.length > 0) {
        const errorDetails = invalidPayslips.map((payslip) => ({
          employeeName: payslip.employeeName,
          missingFields: payslip.missingDetails,
          payslipId: payslip._id,
        }));


        return res.status(400).json({
          success: false,
          message:
            "Cannot generate bank file. Some employees have incomplete bank details.",
          invalidPayslips: errorDetails,
        });
      }

      // If we reach here, all payslips have complete bank details
      const validPayslips = payslipsWithDetails;

      // Update pay run with found payslips
      payRun.payslips = validPayslips.map((p) => p._id);
      await payRun.save();

      // Get or create EFT settings for the company
      const eftSettings = await getOrCreateEFTSettings(payRun.company);

      if (!eftSettings) {
        console.error("EFT Settings not found for company:", payRun.company);
        return res.status(400).json({
          success: false,
          message: "EFT Settings not configured",
        });
      }


      // Validate bank details for payslips
      const payslipBankDetailsErrors = payRun.payslips.filter(
        (payslip) =>
          !payslip.employeeDetails?.accountNumber ||
          !payslip.employeeDetails?.branchCode ||
          !payslip.employeeDetails?.bank
      );

      if (payslipBankDetailsErrors.length > 0) {
        const missingBankDetails = payRun.payslips
          .filter((p) => p.paymentMethod === "EFT")
          .map((p) => {
            const employee = p.employee;
            return {
              employeeName: employee
                ? `${employee.firstName} ${employee.lastName}`
                : "Unknown Employee",
              missingAccountNumber: !employee?.accountNumber,
              missingBranchCode: !employee?.branchCode,
              missingBank: !employee?.bank,
            };
          });

        return res.status(400).json({
          success: false,
          message: "Some payslips have incomplete bank details",
          invalidPayslips: missingBankDetails,
        });
      }

      // Bank file generation logic based on format
      let bankFile;
      switch (format.toLowerCase()) {
        case "fnb":
          bankFile = await generateFNBBankFile(payRun, eftSettings, actionDate);
          break;
        // Add other bank formats here
        default:
          console.error("Unsupported bank file format:", format);
          return res.status(400).json({
            success: false,
            message: "Unsupported bank file format",
          });
      }

      if (!bankFile) {
        console.error("Failed to generate bank file");
        return res.status(500).json({
          success: false,
          message: "Failed to generate bank file",
        });
      }

      // Save bank file to PayRun
      payRun.bankFile = {
        format: format,
        content: bankFile,
        generatedAt: new Date(),
        actionDate: new Date(actionDate),
      };
      await payRun.save();

      const filename = `payrun_${payRunId}_${format}.acb`;
      res.set({
        "Content-Type": "application/octet-stream",
        "Content-Disposition": `attachment; filename="${filename}"`,
        "Content-Transfer-Encoding": "binary",
      });
      res.send(bankFile);
    } catch (error) {
      console.error("Error in Bank File Generation Route:", error);
      res.status(500).json({
        success: false,
        message: "Internal server error",
        error: error.message,
      });
    }
  }
);

// Also support the route without /api prefix
router.post(
  "/api/pay-runs/:id/bank-file/:format",
  ensureAuthenticated,
  (req, res) => {
    // Forward to the API route
    req.url = `/api${req.url}`;
    return router.handle(req, res);
  }
);

// POST /payrun/:id/update - Update a specific pay run
router.post(
  "/:id/update",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {

      const payRun = await PayRun.findById(req.params.id);

      if (!payRun) {
        return res.status(404).json({
          success: false,
          message: "Pay Run not found",
        });
      }

      // Check if pay run can be edited
      if (payRun.status === "finalized") {
        return res.status(400).json({
          success: false,
          message: "Cannot edit a finalized pay run",
        });
      }

      // Update pay run details
      if (req.body.startDate) payRun.startDate = new Date(req.body.startDate);
      if (req.body.endDate) payRun.endDate = new Date(req.body.endDate);
      if (req.body.totalPayable)
        payRun.totalPayable = parseFloat(req.body.totalPayable);
      if (req.body.notes) payRun.notes = req.body.notes;

      // Update individual payslips
      if (req.body.payslips && Array.isArray(req.body.payslips)) {
        for (const payslipUpdate of req.body.payslips) {
          const payroll = await Payroll.findOne({
            _id: payslipUpdate.payslipId,
          });

          if (payroll) {
            // Update basic salary
            if (payslipUpdate.basicSalary) {
              payroll.basicSalary = parseFloat(payslipUpdate.basicSalary);
            }

            // Update payment method
            if (payslipUpdate.paymentMethod) {
              const employee = await Employee.findById(payroll.employee);
              if (employee) {
                employee.paymentMethod = payslipUpdate.paymentMethod;
                await employee.save();
              }
            }

            await payroll.save();
          }
        }
      }

      // Save updated pay run
      await payRun.save();

      res.json({
        success: true,
        message: "Pay run updated successfully",
        payRunId: payRun._id,
      });
    } catch (error) {
      console.error("Error updating pay run:", error);
      res.status(500).json({
        success: false,
        message: "Error updating pay run",
        error: error.message,
      });
    }
  }
);

// GET /payrun/:id/reports/download - Download pay run reports
router.get(
  "/:id/reports/download",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {

      // Find the pay run with populated details
      const payRun = await PayRun.findById(req.params.id)
        .populate({
          path: "payslips",
          populate: [
            {
              path: "employee",
              model: "Employee",
              select:
                "firstName lastName employeeNumber email phone department",
            },
            {
              path: "payroll",
              model: "Payroll",
              populate: {
                path: "employee",
                model: "Employee",
                select: "firstName lastName employeeNumber",
              },
            },
          ],
        })
        .populate("company");

      if (!payRun) {
        return res.status(404).json({
          error: "Pay Run not found",
          message: "The specified pay run could not be located in the system.",
        });
      }

      // Prepare report data
      const reportData = payRun.payslips.map((payslip, index) => {
        const employee = payslip.employee;
        const payroll = payslip.payroll;

        return {
          employeeName: `${employee.firstName} ${employee.lastName}`,
          employeeNumber: employee.employeeNumber,
          department: employee.department || "Not Specified",
          basicSalary: payroll.basicSalary
            ? payroll.basicSalary.toFixed(2)
            : "0.00",
          deductions: payroll.calculateTotalDeductions
            ? payroll.calculateTotalDeductions().toFixed(2)
            : "0.00",
          netPay: payroll.calculateNetPay
            ? payroll.calculateNetPay().toFixed(2)
            : "0.00",
          paymentMethod: payroll.paymentMethod || "Not Specified",
        };
      });

      // Generate CSV content
      const csvContent = [
        // CSV Header
        "Employee Name,Employee Number,Department,Basic Salary,Deductions,Net Pay,Payment Method",

        // CSV Data Rows
        ...reportData.map(
          (row) =>
            `"${row.employeeName}","${row.employeeNumber}","${row.department}","R ${row.basicSalary}","R ${row.deductions}","R ${row.netPay}","${row.paymentMethod}"`
        ),
      ].join("\n");

      // Generate filename
      const filename = `payrun_report_${moment(payRun.startDate).format(
        "YYYY-MM-DD"
      )}_${moment(payRun.endDate).format("YYYY-MM-DD")}.csv`;

      // Set headers for file download
      res.setHeader("Content-Type", "text/csv");
      res.setHeader(
        "Content-Disposition",
        `attachment; filename="${filename}"`
      );

      // Send CSV content
      res.send(csvContent);
    } catch (error) {
      console.error("Error downloading pay run reports:", error);
      res.status(500).json({
        error: "Server Error",
        message: "An error occurred while generating the pay run report.",
        details: error.message,
      });
    }
  }
);

// POST /payrun/:id/finalize - Finalize a pay run
router.post(
  "/:id/finalize",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {

      // Find the pay run
      const payRun = await PayRun.findById(req.params.id)
        .populate("company")
        .populate({
          path: "payslips",
          populate: [
            { path: "employee", model: "Employee" },
            { path: "payroll", model: "Payroll" },
          ],
        });

      // Validate pay run exists
      if (!payRun) {
        return res.status(404).json({
          success: false,
          error: "Pay Run not found",
          message: "The specified pay run could not be located in the system.",
        });
      }

      // Check if pay run is already finalized
      if (payRun.status === "finalized") {
        return res.status(400).json({
          success: false,
          error: "Already Finalized",
          message:
            "This pay run has already been finalized and cannot be modified.",
        });
      }

      // Validate all payslips are ready
      const incompletePayslips = payRun.payslips.filter(
        (payslip) =>
          !payslip.payroll ||
          !payslip.payroll.basicSalary ||
          payslip.payroll.basicSalary <= 0
      );

      if (incompletePayslips.length > 0) {
        return res.status(400).json({
          success: false,
          error: "Incomplete Payslips",
          message:
            "Some payslips are incomplete. Please ensure all employee payroll details are filled out.",
          incompleteCount: incompletePayslips.length,
        });
      }

      // Update pay run status
      payRun.status = "finalized";
      payRun.finalizedAt = new Date();
      payRun.finalizedBy = req.user._id;

      // Calculate and update totals
      await payRun.updateTotals();

      // Update individual payslip statuses
      await Promise.all(
        payRun.payslips.map(async (payslip) => {
          payslip.status = "finalized";

          // Update corresponding payroll record
          await Payroll.findByIdAndUpdate(payslip.payroll._id, {
            status: "processed",
            processedAt: new Date(),
          });

          return payslip.save();
        })
      );

      // Save the updated pay run
      await payRun.save();

      // Log the finalization

      // Send success response
      res.status(200).json({
        success: true,
        message: "Pay Run successfully finalized",
        payRun: {
          id: payRun._id,
          status: payRun.status,
          finalizedAt: payRun.finalizedAt,
        },
      });
    } catch (error) {
      console.error("Error finalizing pay run:", error);
      res.status(500).json({
        success: false,
        error: "Server Error",
        message: "An error occurred while finalizing the pay run.",
        details: error.message,
      });
    }
  }
);

// EFT format route with explicit path handling
router.get("/:payRunId/eft-format", ensureAuthenticated, async (req, res) => {

  try {
    const payRun = await PayRun.findById(req.params.payRunId);

    if (!payRun) {
      return res.status(404).json({
        success: false,
        message: "Pay run not found",
      });
    }


    // Get or create EFT settings for the company
    const eftSettings = await getOrCreateEFTSettings(payRun.company);


    if (!eftSettings) {
      return res.status(400).json({
        success: false,
        message: "EFT settings not configured",
      });
    }

    // Return the EFT format
    res.json({
      success: true,
      format: eftSettings.bankFileFormat,
    });
  } catch (error) {
    console.error("Error retrieving EFT format:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
});

// Bank file generation route with explicit path handling
router.post(
  "/:payRunId/bank-file/:format",
  ensureAuthenticated,
  async (req, res) => {

    try {
      // Validate action date
      const { actionDate } = req.body;
      if (!actionDate) {
        return res.status(400).json({
          success: false,
          message: "Action date is required",
        });
      }

      // Validate date format
      const parsedDate = new Date(actionDate);
      if (isNaN(parsedDate.getTime())) {
        return res.status(400).json({
          success: false,
          message: "Invalid action date format",
        });
      }

      const payRun = await PayRun.findById(req.params.payRunId).populate({
        path: "payrollPeriods",
        select: "_id startDate endDate",
      });

      if (!payRun) {
        return res.status(404).json({
          success: false,
          message: "Pay run not found",
        });
      }

      // Debug logging for payroll periods and payslips

      // Fetch payslips with more flexible criteria
      const payslipSearchCriteria = {
        company: payRun.company,
        $or: [
          { payrollPeriod: { $in: payRun.payrollPeriods } },
          { payRun: payRun._id },
        ],
        status: { $nin: ["deleted", "cancelled"] },
      };

      const payslips = await Payslip.find(payslipSearchCriteria)
        .populate("employee", "firstName lastName")
        .populate("payrollPeriod", "startDate endDate frequency")
        .lean();


      if (payslips.length === 0) {
        return res.status(400).json({
          success: false,
          message:
            "No payslips found for this pay run. Please process payroll first.",
          details: {
            payRunId: payRun._id,
            payrollPeriods: payRun.payrollPeriods.map((p) => p.toString()),
            searchCriteria: payslipSearchCriteria,
          },
        });
      }

      // Populate payslips with full employee details
      const payslipsWithDetails = await Promise.all(
        payslips.map(async (payslip) => {
          // Populate full employee details
          const fullPayslip = await Payslip.findById(payslip._id).populate(
            "employee"
          );

          if (!fullPayslip.employee) {
            return null;
          }

          const employee = fullPayslip.employee;

          // Destructure employee details for bank file generation
          const {
            bank,
            accountNumber,
            branchCode,
            accountType,
            firstName,
            lastName,
          } = employee;

          // Validate bank details
          const missingDetails = [];

          // Check for required bank details with actual string values
          if (!bank || typeof bank !== "string" || bank.trim() === "") {
            missingDetails.push("Bank Name");
            bank = "Missing";
          }
          if (
            !accountNumber ||
            typeof accountNumber !== "string" ||
            accountNumber.trim() === ""
          ) {
            missingDetails.push("Account Number");
            accountNumber = "Missing";
          }
          if (
            !branchCode ||
            typeof branchCode !== "string" ||
            branchCode.trim() === ""
          ) {
            missingDetails.push("Branch Code");
            branchCode = "Missing";
          }
          if (
            !accountType ||
            typeof accountType !== "string" ||
            accountType.trim() === ""
          ) {
            missingDetails.push("Account Type");
            accountType = "Missing";
          }

          // Detailed logging for bank details

          return {
            ...fullPayslip.toObject(),
            bankDetails: {
              bank,
              accountNumber,
              branchCode,
              accountType,
              holderName: `${firstName} ${lastName}`,
            },
            missingDetails,
            employeeName: `${firstName} ${lastName}`,
            missingBankDetails: missingDetails.length > 0,
          };
        })
      ).then((details) => details.filter((detail) => detail !== null));

      // Separate valid and invalid payslips
      const invalidPayslips = payslipsWithDetails.filter(
        (payslip) => payslip.missingDetails.length > 0
      );

      // If any payslips have missing details, provide detailed error
      if (invalidPayslips.length > 0) {
        const errorDetails = invalidPayslips.map((payslip) => ({
          employeeName: payslip.employeeName,
          missingFields: payslip.missingDetails,
          payslipId: payslip._id,
        }));


        return res.status(400).json({
          success: false,
          message:
            "Cannot generate bank file. Some employees have incomplete bank details.",
          invalidPayslips: errorDetails,
        });
      }

      // If we reach here, all payslips have complete bank details
      const validPayslips = payslipsWithDetails;

      // Update pay run with found payslips
      payRun.payslips = validPayslips.map((p) => p._id);
      await payRun.save();

      // Get or create EFT settings for the company
      const eftSettings = await getOrCreateEFTSettings(payRun.company);

      if (!eftSettings) {
        console.error("EFT Settings not found for company:", payRun.company);
        return res.status(400).json({
          success: false,
          message: "EFT Settings not configured",
        });
      }


      // Validate bank details for payslips
      const payslipBankDetailsErrors = payRun.payslips.filter(
        (payslip) =>
          !payslip.employeeDetails?.accountNumber ||
          !payslip.employeeDetails?.branchCode ||
          !payslip.employeeDetails?.bank
      );

      if (payslipBankDetailsErrors.length > 0) {
        const missingBankDetails = payRun.payslips
          .filter((p) => p.paymentMethod === "EFT")
          .map((p) => {
            const employee = p.employee;
            return {
              employeeName: employee
                ? `${employee.firstName} ${employee.lastName}`
                : "Unknown Employee",
              missingAccountNumber: !employee?.accountNumber,
              missingBranchCode: !employee?.branchCode,
              missingBank: !employee?.bank,
            };
          });

        return res.status(400).json({
          success: false,
          message: "Some payslips have incomplete bank details",
          invalidPayslips: missingBankDetails,
        });
      }

      // Bank file generation logic based on format
      let bankFile;
      switch (format.toLowerCase()) {
        case "fnb":
          bankFile = await generateFNBBankFile(payRun, eftSettings, actionDate);
          break;
        // Add other bank formats here
        default:
          console.error("Unsupported bank file format:", format);
          return res.status(400).json({
            success: false,
            message: "Unsupported bank file format",
          });
      }

      if (!bankFile) {
        console.error("Failed to generate bank file");
        return res.status(500).json({
          success: false,
          message: "Failed to generate bank file",
        });
      }

      // Save bank file to PayRun
      payRun.bankFile = {
        format: format,
        content: bankFile,
        generatedAt: new Date(),
        actionDate: new Date(actionDate),
      };
      await payRun.save();

      const filename = `payrun_${payRunId}_${format}.acb`;
      res.set({
        "Content-Type": "application/octet-stream",
        "Content-Disposition": `attachment; filename="${filename}"`,
        "Content-Transfer-Encoding": "binary",
      });
      res.send(bankFile);
    } catch (error) {
      console.error("Error in Bank File Generation Route:", error);
      res.status(500).json({
        success: false,
        message: "Internal server error",
        error: error.message,
      });
    }
  }
);

// Also support the route without /api prefix
router.post(
  "/api/pay-runs/:id/bank-file/:format",
  ensureAuthenticated,
  (req, res) => {
    // Forward to the API route
    req.url = `/api${req.url}`;
    return router.handle(req, res);
  }
);

// POST /payrun/:id/update - Update a specific pay run
router.post(
  "/:id/update",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {

      const payRun = await PayRun.findById(req.params.id);

      if (!payRun) {
        return res.status(404).json({
          success: false,
          message: "Pay Run not found",
        });
      }

      // Check if pay run can be edited
      if (payRun.status === "finalized") {
        return res.status(400).json({
          success: false,
          message: "Cannot edit a finalized pay run",
        });
      }

      // Update pay run details
      if (req.body.startDate) payRun.startDate = new Date(req.body.startDate);
      if (req.body.endDate) payRun.endDate = new Date(req.body.endDate);
      if (req.body.totalPayable)
        payRun.totalPayable = parseFloat(req.body.totalPayable);
      if (req.body.notes) payRun.notes = req.body.notes;

      // Update individual payslips
      if (req.body.payslips && Array.isArray(req.body.payslips)) {
        for (const payslipUpdate of req.body.payslips) {
          const payroll = await Payroll.findOne({
            _id: payslipUpdate.payslipId,
          });

          if (payroll) {
            // Update basic salary
            if (payslipUpdate.basicSalary) {
              payroll.basicSalary = parseFloat(payslipUpdate.basicSalary);
            }

            // Update payment method
            if (payslipUpdate.paymentMethod) {
              const employee = await Employee.findById(payroll.employee);
              if (employee) {
                employee.paymentMethod = payslipUpdate.paymentMethod;
                await employee.save();
              }
            }

            await payroll.save();
          }
        }
      }

      // Save updated pay run
      await payRun.save();

      res.json({
        success: true,
        message: "Pay run updated successfully",
        payRunId: payRun._id,
      });
    } catch (error) {
      console.error("Error updating pay run:", error);
      res.status(500).json({
        success: false,
        message: "Error updating pay run",
        error: error.message,
      });
    }
  }
);

// GET /payrun/:id/reports/download - Download pay run reports
router.get(
  "/:id/reports/download",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {

      // Find the pay run with populated details
      const payRun = await PayRun.findById(req.params.id)
        .populate({
          path: "payslips",
          populate: [
            {
              path: "employee",
              model: "Employee",
              select:
                "firstName lastName employeeNumber email phone department",
            },
            {
              path: "payroll",
              model: "Payroll",
              populate: {
                path: "employee",
                model: "Employee",
                select: "firstName lastName employeeNumber",
              },
            },
          ],
        })
        .populate("company");

      if (!payRun) {
        return res.status(404).json({
          error: "Pay Run not found",
          message: "The specified pay run could not be located in the system.",
        });
      }

      // Prepare report data
      const reportData = payRun.payslips.map((payslip, index) => {
        const employee = payslip.employee;
        const payroll = payslip.payroll;

        return {
          employeeName: `${employee.firstName} ${employee.lastName}`,
          employeeNumber: employee.employeeNumber,
          department: employee.department || "Not Specified",
          basicSalary: payroll.basicSalary
            ? payroll.basicSalary.toFixed(2)
            : "0.00",
          deductions: payroll.calculateTotalDeductions
            ? payroll.calculateTotalDeductions().toFixed(2)
            : "0.00",
          netPay: payroll.calculateNetPay
            ? payroll.calculateNetPay().toFixed(2)
            : "0.00",
          paymentMethod: payroll.paymentMethod || "Not Specified",
        };
      });

      // Generate CSV content
      const csvContent = [
        // CSV Header
        "Employee Name,Employee Number,Department,Basic Salary,Deductions,Net Pay,Payment Method",

        // CSV Data Rows
        ...reportData.map(
          (row) =>
            `"${row.employeeName}","${row.employeeNumber}","${row.department}","R ${row.basicSalary}","R ${row.deductions}","R ${row.netPay}","${row.paymentMethod}"`
        ),
      ].join("\n");

      // Generate filename
      const filename = `payrun_report_${moment(payRun.startDate).format(
        "YYYY-MM-DD"
      )}_${moment(payRun.endDate).format("YYYY-MM-DD")}.csv`;

      // Set headers for file download
      res.setHeader("Content-Type", "text/csv");
      res.setHeader(
        "Content-Disposition",
        `attachment; filename="${filename}"`
      );

      // Send CSV content
      res.send(csvContent);
    } catch (error) {
      console.error("Error downloading pay run reports:", error);
      res.status(500).json({
        error: "Server Error",
        message: "An error occurred while generating the pay run report.",
        details: error.message,
      });
    }
  }
);

// POST /payrun/:id/finalize - Finalize a pay run
router.post(
  "/:id/finalize",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {

      // Find the pay run
      const payRun = await PayRun.findById(req.params.id)
        .populate("company")
        .populate({
          path: "payslips",
          populate: [
            { path: "employee", model: "Employee" },
            { path: "payroll", model: "Payroll" },
          ],
        });

      // Validate pay run exists
      if (!payRun) {
        return res.status(404).json({
          success: false,
          error: "Pay Run not found",
          message: "The specified pay run could not be located in the system.",
        });
      }

      // Check if pay run is already finalized
      if (payRun.status === "finalized") {
        return res.status(400).json({
          success: false,
          error: "Already Finalized",
          message:
            "This pay run has already been finalized and cannot be modified.",
        });
      }

      // Validate all payslips are ready
      const incompletePayslips = payRun.payslips.filter(
        (payslip) =>
          !payslip.payroll ||
          !payslip.payroll.basicSalary ||
          payslip.payroll.basicSalary <= 0
      );

      if (incompletePayslips.length > 0) {
        return res.status(400).json({
          success: false,
          error: "Incomplete Payslips",
          message:
            "Some payslips are incomplete. Please ensure all employee payroll details are filled out.",
          incompleteCount: incompletePayslips.length,
        });
      }

      // Update pay run status
      payRun.status = "finalized";
      payRun.finalizedAt = new Date();
      payRun.finalizedBy = req.user._id;

      // Calculate and update totals
      await payRun.updateTotals();

      // Update individual payslip statuses
      await Promise.all(
        payRun.payslips.map(async (payslip) => {
          payslip.status = "finalized";

          // Update corresponding payroll record
          await Payroll.findByIdAndUpdate(payslip.payroll._id, {
            status: "processed",
            processedAt: new Date(),
          });

          return payslip.save();
        })
      );

      // Save the updated pay run
      await payRun.save();

      // Log the finalization

      // Send success response
      res.status(200).json({
        success: true,
        message: "Pay Run successfully finalized",
        payRun: {
          id: payRun._id,
          status: payRun.status,
          finalizedAt: payRun.finalizedAt,
        },
      });
    } catch (error) {
      console.error("Error finalizing pay run:", error);
      res.status(500).json({
        success: false,
        error: "Server Error",
        message: "An error occurred while finalizing the pay run.",
        details: error.message,
      });
    }
  }
);

// EFT format route with explicit path handling
router.get("/:payRunId/eft-format", ensureAuthenticated, async (req, res) => {

  try {
    const payRun = await PayRun.findById(req.params.payRunId);

    if (!payRun) {
      return res.status(404).json({
        success: false,
        message: "Pay run not found",
      });
    }


    // Get or create EFT settings for the company
    const eftSettings = await getOrCreateEFTSettings(payRun.company);


    if (!eftSettings) {
      return res.status(400).json({
        success: false,
        message: "EFT settings not configured",
      });
    }

    // Return the EFT format
    res.json({
      success: true,
      format: eftSettings.bankFileFormat,
    });
  } catch (error) {
    console.error("Error retrieving EFT format:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
});

// Bank file generation route with explicit path handling
router.post(
  "/:payRunId/bank-file/:format",
  ensureAuthenticated,
  async (req, res) => {

    try {
      // Validate action date
      const { actionDate } = req.body;
      if (!actionDate) {
        return res.status(400).json({
          success: false,
          message: "Action date is required",
        });
      }

      // Validate date format
      const parsedDate = new Date(actionDate);
      if (isNaN(parsedDate.getTime())) {
        return res.status(400).json({
          success: false,
          message: "Invalid action date format",
        });
      }

      const payRun = await PayRun.findById(req.params.payRunId).populate({
        path: "payrollPeriods",
        select: "_id startDate endDate",
      });

      if (!payRun) {
        return res.status(404).json({
          success: false,
          message: "Pay run not found",
        });
      }

      // Debug logging for payroll periods and payslips

      // Fetch payslips with more flexible criteria
      const payslipSearchCriteria = {
        company: payRun.company,
        $or: [
          { payrollPeriod: { $in: payRun.payrollPeriods } },
          { payRun: payRun._id },
        ],
        status: { $nin: ["deleted", "cancelled"] },
      };

      const payslips = await Payslip.find(payslipSearchCriteria)
        .populate("employee", "firstName lastName")
        .populate("payrollPeriod", "startDate endDate frequency")
        .lean();


      if (payslips.length === 0) {
        return res.status(400).json({
          success: false,
          message:
            "No payslips found for this pay run. Please process payroll first.",
          details: {
            payRunId: payRun._id,
            payrollPeriods: payRun.payrollPeriods.map((p) => p.toString()),
            searchCriteria: payslipSearchCriteria,
          },
        });
      }

      // Populate payslips with full employee details
      const payslipsWithDetails = await Promise.all(
        payslips.map(async (payslip) => {
          // Populate full employee details
          const fullPayslip = await Payslip.findById(payslip._id).populate(
            "employee"
          );

          if (!fullPayslip.employee) {
            return null;
          }

          const employee = fullPayslip.employee;

          // Destructure employee details for bank file generation
          const {
            bank,
            accountNumber,
            branchCode,
            accountType,
            firstName,
            lastName,
          } = employee;

          // Validate bank details
          const missingDetails = [];

          // Check for required bank details with actual string values
          if (!bank || typeof bank !== "string" || bank.trim() === "") {
            missingDetails.push("Bank Name");
            bank = "Missing";
          }
          if (
            !accountNumber ||
            typeof accountNumber !== "string" ||
            accountNumber.trim() === ""
          ) {
            missingDetails.push("Account Number");
            accountNumber = "Missing";
          }
          if (
            !branchCode ||
            typeof branchCode !== "string" ||
            branchCode.trim() === ""
          ) {
            missingDetails.push("Branch Code");
            branchCode = "Missing";
          }
          if (
            !accountType ||
            typeof accountType !== "string" ||
            accountType.trim() === ""
          ) {
            missingDetails.push("Account Type");
            accountType = "Missing";
          }

          // Detailed logging for bank details

          return {
            ...fullPayslip.toObject(),
            bankDetails: {
              bank,
              accountNumber,
              branchCode,
              accountType,
              holderName: `${firstName} ${lastName}`,
            },
            missingDetails,
            employeeName: `${firstName} ${lastName}`,
            missingBankDetails: missingDetails.length > 0,
          };
        })
      ).then((details) => details.filter((detail) => detail !== null));

      // Separate valid and invalid payslips
      const invalidPayslips = payslipsWithDetails.filter(
        (payslip) => payslip.missingDetails.length > 0
      );

      // If any payslips have missing details, provide detailed error
      if (invalidPayslips.length > 0) {
        const errorDetails = invalidPayslips.map((payslip) => ({
          employeeName: payslip.employeeName,
          missingFields: payslip.missingDetails,
          payslipId: payslip._id,
        }));


        return res.status(400).json({
          success: false,
          message:
            "Cannot generate bank file. Some employees have incomplete bank details.",
          invalidPayslips: errorDetails,
        });
      }

      // If we reach here, all payslips have complete bank details
      const validPayslips = payslipsWithDetails;

      // Update pay run with found payslips
      payRun.payslips = validPayslips.map((p) => p._id);
      await payRun.save();

      // Get or create EFT settings for the company
      const eftSettings = await getOrCreateEFTSettings(payRun.company);

      if (!eftSettings) {
        console.error("EFT Settings not found for company:", payRun.company);
        return res.status(400).json({
          success: false,
          message: "EFT Settings not configured",
        });
      }


      // Validate bank details for payslips
      const payslipBankDetailsErrors = payRun.payslips.filter(
        (payslip) =>
          !payslip.employeeDetails?.accountNumber ||
          !payslip.employeeDetails?.branchCode ||
          !payslip.employeeDetails?.bank
      );

      if (payslipBankDetailsErrors.length > 0) {
        const missingBankDetails = payRun.payslips
          .filter((p) => p.paymentMethod === "EFT")
          .map((p) => {
            const employee = p.employee;
            return {
              employeeName: employee
                ? `${employee.firstName} ${employee.lastName}`
                : "Unknown Employee",
              missingAccountNumber: !employee?.accountNumber,
              missingBranchCode: !employee?.branchCode,
              missingBank: !employee?.bank,
            };
          });

        return res.status(400).json({
          success: false,
          message: "Some payslips have incomplete bank details",
          invalidPayslips: missingBankDetails,
        });
      }

      // Bank file generation logic based on format
      let bankFile;
      switch (format.toLowerCase()) {
        case "fnb":
          bankFile = await generateFNBBankFile(payRun, eftSettings, actionDate);
          break;
        // Add other bank formats here
        default:
          console.error("Unsupported bank file format:", format);
          return res.status(400).json({
            success: false,
            message: "Unsupported bank file format",
          });
      }

      if (!bankFile) {
        console.error("Failed to generate bank file");
        return res.status(500).json({
          success: false,
          message: "Failed to generate bank file",
        });
      }

      // Save bank file to PayRun
      payRun.bankFile = {
        format: format,
        content: bankFile,
        generatedAt: new Date(),
        actionDate: new Date(actionDate),
      };
      await payRun.save();

      const filename = `payrun_${payRunId}_${format}.acb`;
      res.set({
        "Content-Type": "application/octet-stream",
        "Content-Disposition": `attachment; filename="${filename}"`,
        "Content-Transfer-Encoding": "binary",
      });
      res.send(bankFile);
    } catch (error) {
      console.error("Error in Bank File Generation Route:", error);
      res.status(500).json({
        success: false,
        message: "Internal server error",
        error: error.message,
      });
    }
  }
);

// Bank File Generators
// FNB ACB Generator
const FNBACBGenerator = {
  formatAmount(amount) {
      return Math.round(amount * 100).toString();
  },

  formatText(text, length, padChar = " ") {
      return (text || "").toString().substring(0, length).padEnd(length, padChar);
  },

  getFNBAccountType(type) {
      return type?.toUpperCase() === 'SAVINGS' ? '2' : '1';
  },

  generateHeaderRecord(eftSettings, actionDate) {
      const today = moment();
      const formattedCreationDate = today.format('YYMMDD');
      const formattedActionDate = moment(actionDate).format('YYMMDD');
      const sequenceNumber = Math.floor(Math.random() * 9999).toString().padStart(4, '0');

      return '02' + '0'.repeat(196);
  },

  generateUserHeaderRecord(eftSettings) {
      return '04' + '0'.repeat(196);
  },

  generateDetailRecord(payslip, sequence) {
      let record = [
          '10',                                           // Record Type (2)
          '*********',                                   // Branch Code (9)
          '********',                                    // Account Number (8)
          '0'.repeat(9),                                // Zeros (9)
          sequence.toString().padStart(6, '0'),         // Sequence Number (6)
          this.formatAmount(payslip.netPay).padStart(11, '0'), // Amount (11)
          '0'.repeat(7),                                // Zeros (7)
          '518100',                                     // Branch Code (6)
          '*********',                                  // Account Number (9)
          '0'.repeat(13),                               // Zeros (13)
          this.formatText('Demo Company', 40),          // Company Name (40)
          this.formatText(`${payslip.employee.firstName} ${payslip.employee.lastName}`, 30), // Name (30)
          '0'.repeat(48)                                // Zeros (48)
      ].join('');

      return record.padEnd(198, '0');
  },

  generateContraRecord(totalAmount, eftSettings) {
      return '92' + '0'.repeat(196);
  },

  generateTrailerRecord(totalAmount, totalRecords) {
      return '94' + '0'.repeat(196);
  },

  generate(payRun, eftSettings, actionDate) {
      try {
          const records = [];
          let totalAmount = 0;
          let sequence = 1;

          // Header Records
          records.push(this.generateHeaderRecord(eftSettings, actionDate));
          records.push(this.generateUserHeaderRecord(eftSettings));

          // Transaction Records
          if (payRun.payrollPeriods && payRun.payrollPeriods.length > 0) {
              for (const period of payRun.payrollPeriods) {
                  if (!period.employee || period.employee.paymentMethod !== 'EFT' || 
                      !period.employee.accountNumber || !period.employee.branchCode) {
                      continue;
                  }
                  records.push(this.generateDetailRecord(period, sequence++));
                  totalAmount += period.netPay;
              }
          }

          // Contra and Trailer Records
          records.push(this.generateContraRecord(totalAmount, eftSettings));
          records.push(this.generateTrailerRecord(totalAmount, sequence));

          return records.join('\n');
      } catch (error) {
          console.error('Error generating FNB ACB file:', error);
          throw error;
      }
  }
};

// Debug route registration
// DELETE /:id - Delete a pay run with validation
router.delete(
  "/:id",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {

      const PayRunValidationService = require("../services/PayRunValidationService");
      const { force, reason } = req.body;

      // Validate deletion
      const validation = await PayRunValidationService.validateDeletion(
        req.params.id,
        req.user._id
      );

      if (!validation.isValid) {
        return res.status(400).json({
          success: false,
          message: validation.errors.join("; "),
          errors: validation.errors,
        });
      }

      // Check if confirmation is required
      if (!force && validation.warnings.length > 0) {
        return res.status(200).json({
          success: false,
          message: "Deletion requires confirmation",
          warnings: validation.warnings,
          requiresConfirmation: true,
          payRun: {
            _id: validation.payRun._id,
            reference: validation.payRun.reference,
            status: validation.payRun.status,
          },
        });
      }

      // Perform deletion
      const deletionResult = await PayRunValidationService.deletePayRun(
        req.params.id,
        req.user._id,
        reason || "Manual deletion via interface",
        force
      );

      if (!deletionResult.success) {
        return res.status(400).json(deletionResult);
      }


      res.json({
        success: true,
        message: "Pay run deleted successfully",
        payRun: deletionResult.payRun,
        warnings: deletionResult.warnings,
      });
    } catch (error) {
      console.error("Error in pay run deletion route:", error);
      res.status(500).json({
        success: false,
        message: "Failed to delete pay run",
        error: error.message,
      });
    }
  }
);

// GET /:id/deletion-analysis - Analyze deletion impact
router.get(
  "/:id/deletion-analysis",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const PayRunValidationService = require("../services/PayRunValidationService");

      const analysis = await PayRunValidationService.getAffectedEntities(req.params.id);

      if (analysis.error) {
        return res.status(404).json({
          success: false,
          message: analysis.error,
        });
      }

      res.json({
        success: true,
        analysis: analysis,
      });
    } catch (error) {
      console.error("Error analyzing deletion impact:", error);
      res.status(500).json({
        success: false,
        message: "Failed to analyze deletion impact",
        error: error.message,
      });
    }
  }
);


module.exports = router;
