const express = require("express");
const QuickBooks = require("node-quickbooks");
const Integration = require("../models/Integration");
const Company = require("../models/Company");
const QuickBooksAccountMapping = require("../models/quickbooksAccountMapping");
const QuickBooksTokenManager = require("../services/quickbooksTokenManager");
const { ensureAuthenticated } = require("../middleware/auth");

// Create router with mergeParams to access parent router params
const router = express.Router({ mergeParams: true });

// Initialize token manager instance
const tokenManager = new QuickBooksTokenManager();

// State management utilities
const crypto = require("crypto");

const generateState = () => {
  return crypto.randomBytes(32).toString("hex");
};

const validateState = (savedState, receivedState) => {
  return savedState === receivedState;
};

/**
 * GET /quickbooks/connect/:companyCode
 * Initiate QuickBooks OAuth 2.0 connection
 */
router.get("/connect/:companyCode", ensureAuthenticated, async (req, res) => {
  const { companyCode } = req.params;
  const logContext = {
    companyCode,
    userId: req.user?.id,
    operation: "quickbooks_connect_initiate",
  };

  try {
    console.log("[QuickBooks Integration] Starting connection process", logContext);

    // Validate company
    const company = await Company.findOne({ companyCode });
    if (!company) {
      console.error("[QuickBooks Integration] Company not found", logContext);
      return res.status(404).json({
        success: false,
        error: "Company not found"
      });
    }

    // Check if user has access to this company
    const userCompanyIds = req.user.companies ?
      req.user.companies.map(c => c._id ? c._id.toString() : c.toString()) :
      [];

    if (!userCompanyIds.includes(company._id.toString())) {
      console.error("[QuickBooks Integration] Unauthorized access attempt", {
        ...logContext,
        userCompanies: userCompanyIds,
        requestedCompany: company._id.toString()
      });
      return res.status(403).json({
        success: false,
        error: "Unauthorized access to company"
      });
    }

    // Generate state for security
    const state = generateState();

    // Store state and company code in session
    req.session.quickbooks_state = state;
    req.session.quickbooks_company_code = companyCode;

    // Set cookie for additional security
    res.cookie('quickbooks_company_code', companyCode, {
      httpOnly: true,
      maxAge: 300000, // 5 minutes
      sameSite: 'lax'
    });

    // Save session immediately and wait for it to complete
    await new Promise((resolve, reject) => {
      req.session.save((err) => {
        if (err) {
          console.error("[QuickBooks Integration] Failed to save session", {
            ...logContext,
            error: err.message,
          });
          reject(err);
          return;
        }
        resolve();
      });
    });

    // Get authorization URL
    const authUrl = tokenManager.getAuthorizationUrl(state);

    console.log("[QuickBooks Integration] Redirecting to QuickBooks authorization", {
      ...logContext,
      authUrl: authUrl.substring(0, 100) + "...", // Log partial URL for security
    });

    // Redirect to QuickBooks authorization
    res.redirect(authUrl);

  } catch (error) {
    console.error("[QuickBooks Integration] Connection initiation failed", {
      ...logContext,
      error: error.message,
      stack: error.stack,
    });

    res.status(500).json({
      success: false,
      error: "Failed to initiate QuickBooks connection",
      details: error.message,
    });
  }
});

/**
 * GET /quickbooks/callback
 * Handle QuickBooks OAuth 2.0 callback
 */
router.get("/callback", async (req, res) => {
  const { code, realmId, state, error } = req.query;
  const logContext = {
    operation: "quickbooks_oauth_callback",
    hasCode: !!code,
    hasRealmId: !!realmId,
    hasState: !!state,
    hasError: !!error,
  };

  try {
    console.log("[QuickBooks Integration] Processing OAuth callback", logContext);

    // Check for OAuth errors
    if (error) {
      console.error("[QuickBooks Integration] OAuth error received", {
        ...logContext,
        error: error,
      });
      return res.redirect(`/clients/${req.session.quickbooks_company_code}/settings/accounting?error=oauth_error&message=${encodeURIComponent(error)}`);
    }

    // Validate required parameters
    if (!code || !realmId || !state) {
      console.error("[QuickBooks Integration] Missing required OAuth parameters", logContext);
      return res.redirect(`/clients/${req.session.quickbooks_company_code}/settings/accounting?error=missing_params`);
    }

    // Validate state parameter
    const savedState = req.session.quickbooks_state;
    if (!validateState(savedState, state)) {
      console.error("[QuickBooks Integration] Invalid state parameter", {
        ...logContext,
        savedState: savedState?.substring(0, 10) + "...",
        receivedState: state?.substring(0, 10) + "...",
      });
      return res.redirect(`/clients/${req.session.quickbooks_company_code}/settings/accounting?error=invalid_state`);
    }

    // Get company code from session
    const companyCode = req.session.quickbooks_company_code;
    if (!companyCode) {
      console.error("[QuickBooks Integration] No company code in session", logContext);
      return res.redirect(`/settings/accounting?error=session_expired`);
    }

    logContext.companyCode = companyCode;

    // Exchange code for tokens
    console.log("[QuickBooks Integration] Exchanging code for tokens", logContext);
    const tokenData = await tokenManager.exchangeCodeForTokens(code, realmId, state);

    // Find company
    const company = await Company.findOne({ companyCode });
    if (!company) {
      console.error("[QuickBooks Integration] Company not found during callback", logContext);
      return res.redirect(`/clients/${companyCode}/settings/accounting?error=company_not_found`);
    }

    // Save integration
    const integration = await Integration.findOneAndUpdate(
      { company: company._id, type: "accounting", provider: "quickbooks" },
      {
        company: company._id,
        type: "accounting",
        provider: "quickbooks",
        status: "active",
        lastSync: new Date()
      },
      { upsert: true, new: true }
    );

    // Set token data
    tokenManager.setTokenData(integration, tokenData);
    await integration.save();

    console.log("[QuickBooks Integration] Integration saved successfully", {
      ...logContext,
      integrationId: integration._id,
      realmId: realmId,
    });

    // Clear session data
    delete req.session.quickbooks_state;
    delete req.session.quickbooks_company_code;
    res.clearCookie('quickbooks_company_code');

    // Redirect to success page
    res.redirect(`/clients/${companyCode}/settings/accounting?success=quickbooks_connected`);

  } catch (error) {
    console.error("[QuickBooks Integration] Callback processing failed", {
      ...logContext,
      error: error.message,
      stack: error.stack,
    });

    const companyCode = req.session.quickbooks_company_code || 'unknown';
    res.redirect(`/clients/${companyCode}/settings/accounting?error=connection_failed&message=${encodeURIComponent(error.message)}`);
  }
});

/**
 * POST /quickbooks/disconnect/:companyCode
 * Disconnect QuickBooks integration
 */
router.post("/disconnect/:companyCode", ensureAuthenticated, async (req, res) => {
  const { companyCode } = req.params;
  const logContext = {
    companyCode,
    userId: req.user?.id,
    operation: "quickbooks_disconnect",
  };

  try {
    console.log("[QuickBooks Integration] Starting disconnection process", logContext);

    // Validate company
    const company = await Company.findOne({ companyCode });
    if (!company) {
      console.error("[QuickBooks Integration] Company not found", logContext);
      return res.status(404).json({
        success: false,
        error: "Company not found"
      });
    }

    // Check if user has access to this company
    const userCompanyIds = req.user.companies ?
      req.user.companies.map(c => c._id ? c._id.toString() : c.toString()) :
      [];

    if (!userCompanyIds.includes(company._id.toString())) {
      console.error("[QuickBooks Integration] Unauthorized access attempt", {
        ...logContext,
        userCompanies: userCompanyIds,
        requestedCompany: company._id.toString()
      });
      return res.status(403).json({
        success: false,
        error: "Unauthorized access to company"
      });
    }

    // Revoke tokens and update integration
    const success = await tokenManager.revokeTokens(company._id);

    if (success) {
      console.log("[QuickBooks Integration] Successfully disconnected", logContext);
      res.json({
        success: true,
        message: "QuickBooks integration disconnected successfully"
      });
    } else {
      console.warn("[QuickBooks Integration] Token revocation failed but integration deactivated", logContext);
      res.json({
        success: true,
        message: "QuickBooks integration disconnected (token revocation failed)"
      });
    }

  } catch (error) {
    console.error("[QuickBooks Integration] Disconnection failed", {
      ...logContext,
      error: error.message,
      stack: error.stack,
    });

    res.status(500).json({
      success: false,
      error: "Failed to disconnect QuickBooks integration",
      details: error.message,
    });
  }
});

/**
 * POST /quickbooks/refresh/:companyCode
 * Refresh QuickBooks access token
 */
router.post("/refresh/:companyCode", ensureAuthenticated, async (req, res) => {
  const { companyCode } = req.params;
  const logContext = {
    companyCode,
    userId: req.user?.id,
    operation: "quickbooks_token_refresh",
  };

  try {
    console.log("[QuickBooks Integration] Starting token refresh", logContext);

    // Validate company
    const company = await Company.findOne({ companyCode });
    if (!company) {
      console.error("[QuickBooks Integration] Company not found", logContext);
      return res.status(404).json({
        success: false,
        error: "Company not found"
      });
    }

    // Check if user has access to this company
    const userCompanyIds = req.user.companies ?
      req.user.companies.map(c => c._id ? c._id.toString() : c.toString()) :
      [];

    if (!userCompanyIds.includes(company._id.toString())) {
      console.error("[QuickBooks Integration] Unauthorized access attempt", {
        ...logContext,
        userCompanies: userCompanyIds,
        requestedCompany: company._id.toString()
      });
      return res.status(403).json({
        success: false,
        error: "Unauthorized access to company"
      });
    }

    // Refresh token
    const result = await tokenManager.validateAndRefreshToken(company._id);

    if (result.success) {
      console.log("[QuickBooks Integration] Token refresh successful", {
        ...logContext,
        refreshed: result.refreshed,
        expiresAt: result.expiresAt,
      });

      res.json({
        success: true,
        refreshed: result.refreshed,
        expiresAt: result.expiresAt,
        timeUntilExpiry: result.timeUntilExpiry,
        message: result.refreshed ? "Token refreshed successfully" : "Token is still valid"
      });
    } else {
      console.error("[QuickBooks Integration] Token refresh failed", {
        ...logContext,
        error: result.error,
        needsReauthentication: result.needsReauthentication,
      });

      res.status(401).json({
        success: false,
        error: result.error,
        needsReauthentication: result.needsReauthentication,
        message: "Token refresh failed"
      });
    }

  } catch (error) {
    console.error("[QuickBooks Integration] Token refresh error", {
      ...logContext,
      error: error.message,
      stack: error.stack,
    });

    res.status(500).json({
      success: false,
      error: "Failed to refresh QuickBooks token",
      details: error.message,
    });
  }
});

/**
 * GET /quickbooks/status/:companyCode
 * Get QuickBooks integration status
 */
router.get("/status/:companyCode", ensureAuthenticated, async (req, res) => {
  const { companyCode } = req.params;
  const logContext = {
    companyCode,
    userId: req.user?.id,
    operation: "quickbooks_status_check",
  };

  try {
    // Validate company
    const company = await Company.findOne({ companyCode });
    if (!company) {
      return res.status(404).json({
        success: false,
        error: "Company not found"
      });
    }

    // Check if user has access to this company
    const userCompanyIds = req.user.companies ?
      req.user.companies.map(c => c._id ? c._id.toString() : c.toString()) :
      [];

    if (!userCompanyIds.includes(company._id.toString())) {
      return res.status(403).json({
        success: false,
        error: "Unauthorized access to company"
      });
    }

    // Get integration
    const integration = await Integration.findOne({
      company: company._id,
      type: 'accounting',
      provider: 'quickbooks'
    });

    if (!integration) {
      return res.json({
        success: true,
        connected: false,
        status: 'inactive',
        message: 'QuickBooks integration not found'
      });
    }

    const tokenData = tokenManager.getTokenData(integration);
    const isExpired = tokenManager.isTokenExpired(integration);
    const timeUntilExpiry = tokenManager.getTimeUntilExpiry(integration);

    res.json({
      success: true,
      connected: integration.status === 'active',
      status: integration.status,
      realmId: tokenData?.realmId,
      expiresAt: tokenData?.expiresAt,
      isExpired: isExpired,
      timeUntilExpiry: timeUntilExpiry,
      lastSync: integration.lastSync,
      message: integration.status === 'active' ? 'QuickBooks integration is active' : 'QuickBooks integration is inactive'
    });

  } catch (error) {
    console.error("[QuickBooks Integration] Status check failed", {
      ...logContext,
      error: error.message,
      stack: error.stack,
    });

    res.status(500).json({
      success: false,
      error: "Failed to check QuickBooks status",
      details: error.message,
    });
  }
});

/**
 * POST /:companyCode/sync-payroll
 * Sync payroll data to QuickBooks
 */
router.post("/:companyCode/sync-payroll", ensureAuthenticated, async (req, res) => {
  const { payrollId } = req.body;

  try {
    const { companyCode } = req.params;
    const { payrollData, description } = req.body;

    console.log("[QuickBooks Sync] Starting payroll sync", {
      companyCode,
      payrollId,
      description,
      payrollDataSummary: {
        totalAmount: payrollData?.basicSalary,
        employeeCount: payrollData?.employeeCount,
        period: payrollData?.period,
      },
    });

    // Validate request
    if (!payrollId || !payrollData) {
      throw new Error("Missing required payroll data");
    }

    // Get company and check integration
    const company = await Company.findOne({ companyCode });
    if (!company) {
      throw new Error("Company not found");
    }

    const integration = await Integration.findOne({
      company: company._id,
      provider: "quickbooks",
      type: "accounting",
      status: "active",
    });

    if (!integration) {
      throw new Error("No active QuickBooks integration found");
    }

    // Validate and refresh token if needed
    const tokenValidation = await tokenManager.validateAndRefreshToken(company._id);
    if (!tokenValidation.success) {
      throw new Error("QuickBooks token validation failed: " + tokenValidation.error);
    }

    // Get account mappings
    const QuickBooksAccountMapping = require("../models/quickbooksAccountMapping");
    const mappings = await QuickBooksAccountMapping.find({
      company: company._id,
    });

    if (!mappings.length) {
      throw new Error("No QuickBooks account mappings configured. Please set up account mappings first.");
    }

    // Create mapping lookup for easier access
    const mappingLookup = {};
    mappings.forEach(mapping => {
      mappingLookup[mapping.payrollAccountId] = mapping;
    });

    // Validate required mappings exist
    const requiredMappings = ['BASIC_SALARY', 'PAYE_LIABILITY', 'UIF_TOTAL', 'NET_PAY_CLEARING'];
    const missingMappings = requiredMappings.filter(required => !mappingLookup[required]);

    if (missingMappings.length > 0) {
      throw new Error(`Missing required account mappings: ${missingMappings.join(', ')}`);
    }

    // Get token data and initialize QuickBooks client
    const tokenData = tokenManager.getTokenData(integration);
    const qbo = tokenManager.initializeClient(tokenData);

    // Create journal entry for QuickBooks
    const journalEntry = {
      Name: description || `Payroll Journal Entry - ${payrollData.period}`,
      TxnDate: new Date().toISOString().split('T')[0],
      Line: []
    };

    // Add debit lines (expenses)
    if (payrollData.basicSalary > 0 && mappingLookup['BASIC_SALARY']) {
      journalEntry.Line.push({
        Amount: payrollData.basicSalary,
        DetailType: "JournalEntryLineDetail",
        JournalEntryLineDetail: {
          PostingType: "Debit",
          AccountRef: {
            value: mappingLookup['BASIC_SALARY'].quickbooksAccountId
          }
        }
      });
    }

    // Add credit lines (liabilities and net pay)
    let totalCredits = 0;

    if (payrollData.paye > 0 && mappingLookup['PAYE_LIABILITY']) {
      journalEntry.Line.push({
        Amount: payrollData.paye,
        DetailType: "JournalEntryLineDetail",
        JournalEntryLineDetail: {
          PostingType: "Credit",
          AccountRef: {
            value: mappingLookup['PAYE_LIABILITY'].quickbooksAccountId
          }
        }
      });
      totalCredits += payrollData.paye;
    }

    if (payrollData.uif > 0 && mappingLookup['UIF_TOTAL']) {
      journalEntry.Line.push({
        Amount: payrollData.uif,
        DetailType: "JournalEntryLineDetail",
        JournalEntryLineDetail: {
          PostingType: "Credit",
          AccountRef: {
            value: mappingLookup['UIF_TOTAL'].quickbooksAccountId
          }
        }
      });
      totalCredits += payrollData.uif;
    }

    // Calculate net pay (basic salary minus deductions)
    const netPay = payrollData.basicSalary - payrollData.paye - payrollData.uif;

    if (netPay > 0 && mappingLookup['NET_PAY_CLEARING']) {
      journalEntry.Line.push({
        Amount: netPay,
        DetailType: "JournalEntryLineDetail",
        JournalEntryLineDetail: {
          PostingType: "Credit",
          AccountRef: {
            value: mappingLookup['NET_PAY_CLEARING'].quickbooksAccountId
          }
        }
      });
      totalCredits += netPay;
    }

    console.log("[QuickBooks Sync] Journal entry prepared", {
      totalDebits: payrollData.basicSalary,
      totalCredits: totalCredits,
      lineCount: journalEntry.Line.length
    });

    // Create journal entry in QuickBooks
    const journalEntryResult = await new Promise((resolve, reject) => {
      qbo.createJournalEntry(journalEntry, (err, result) => {
        if (err) {
          console.error("[QuickBooks Sync] Journal entry creation failed", err);
          reject(err);
        } else {
          console.log("[QuickBooks Sync] Journal entry created successfully", {
            id: result.Id,
            txnDate: result.TxnDate
          });
          resolve(result);
        }
      });
    });

    // Update integration last sync time
    integration.lastSync = new Date();
    await integration.save();

    res.json({
      success: true,
      message: "Successfully synced payroll data to QuickBooks",
      journalEntryId: journalEntryResult.Id,
      syncedAt: new Date(),
    });

  } catch (error) {
    console.error("[QuickBooks Sync] Error syncing payroll:", error);
    res.status(500).json({
      success: false,
      message: error.message || "Failed to sync with QuickBooks",
    });
  }
});

module.exports = router;