const express = require('express');
const router = express.Router();
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');

// Sandbox data - in-memory storage for demo purposes
let sandboxData = {
  users: [
    {
      id: 'sandbox_user_1',
      email: '<EMAIL>',
      password: '$2a$10$sandbox.demo.password.hash', // 'demo123'
      firstName: 'Demo',
      lastName: 'User',
      role: 'admin',
      currentCompany: 'sandbox_company_1'
    }
  ],
  companies: [
    {
      id: 'sandbox_company_1',
      name: 'Demo Company Ltd',
      companyCode: 'DEMO001',
      registrationNumber: '2023/123456/07',
      taxNumber: 'TAX123456789',
      uifNumber: 'UIF123456',
      sdlNumber: 'SDL123456'
    }
  ],
  employees: [
    {
      id: 'sandbox_emp_1',
      globalEmployeeId: 'DEMO001-001',
      companyEmployeeNumber: 'EMP001',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      basicSalary: 25000,
      status: 'Active',
      company: 'sandbox_company_1',
      dob: '1990-01-15',
      doa: '2024-01-01',
      idType: 'rsa',
      idNumber: '9001150000000',
      paymentMethod: 'EFT'
    },
    {
      id: 'sandbox_emp_2',
      globalEmployeeId: 'DEMO001-002',
      companyEmployeeNumber: 'EMP002',
      firstName: 'Jane',
      lastName: 'Smith',
      email: '<EMAIL>',
      basicSalary: 30000,
      status: 'Active',
      company: 'sandbox_company_1',
      dob: '1985-05-20',
      doa: '2024-02-01',
      idType: 'rsa',
      idNumber: '8505200000000',
      paymentMethod: 'EFT'
    }
  ],
  payrollPeriods: [
    {
      id: 'sandbox_period_1',
      company: 'sandbox_company_1',
      employee: 'sandbox_emp_1',
      startDate: '2025-01-01',
      endDate: '2025-01-31',
      basicSalary: 25000,
      grossPay: 25000,
      totalDeductions: 4500,
      netPay: 20500,
      PAYE: 3200,
      UIF: 250,
      SDL: 375,
      status: 'finalized',
      isFinalized: true
    },
    {
      id: 'sandbox_period_2',
      company: 'sandbox_company_1',
      employee: 'sandbox_emp_2',
      startDate: '2025-01-01',
      endDate: '2025-01-31',
      basicSalary: 30000,
      grossPay: 30000,
      totalDeductions: 5800,
      netPay: 24200,
      PAYE: 4500,
      UIF: 300,
      SDL: 450,
      status: 'finalized',
      isFinalized: true
    }
  ],
  payRuns: [
    {
      id: 'sandbox_payrun_1',
      company: 'sandbox_company_1',
      period: 'January 2025',
      monthYear: '2025-01',
      payRunType: 'regular',
      reference: 'PR-2025-01-REGULAR-1',
      status: 'finalized',
      payrollPeriods: ['sandbox_period_1', 'sandbox_period_2'],
      totals: {
        grossPay: 55000,
        totalDeductions: 10300,
        netPay: 44700
      }
    }
  ],
  leaveTypes: [
    {
      id: 'sandbox_leave_1',
      company: 'sandbox_company_1',
      name: 'Annual Leave',
      category: 'annual',
      daysPerYear: 21,
      paidLeave: true,
      requiresApproval: true
    },
    {
      id: 'sandbox_leave_2',
      company: 'sandbox_company_1',
      name: 'Sick Leave',
      category: 'sick',
      daysPerYear: 30,
      paidLeave: true,
      requiresApproval: false
    }
  ]
};

// Middleware to add sandbox headers
router.use((req, res, next) => {
  res.setHeader('X-Sandbox-Mode', 'true');
  res.setHeader('X-Sandbox-Version', '1.0');
  next();
});

// Helper function to generate sandbox JWT
function generateSandboxToken(user) {
  return jwt.sign(
    {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      role: user.role,
      currentCompany: user.currentCompany,
      sandbox: true
    },
    process.env.JWT_SECRET || 'sandbox-secret',
    { expiresIn: '24h' }
  );
}

// Sandbox Authentication
router.post('/auth/login', async (req, res) => {
  try {
    const { email, password } = req.body;
    
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Email and password are required'
      });
    }

    // For sandbox, accept demo credentials
    if (email === '<EMAIL>' && password === 'demo123') {
      const user = sandboxData.users[0];
      const token = generateSandboxToken(user);
      
      return res.json({
        success: true,
        message: 'Sandbox authentication successful',
        token,
        user: {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role,
          currentCompany: user.currentCompany
        },
        sandbox: true
      });
    }

    res.status(401).json({
      success: false,
      message: 'Invalid sandbox credentials. Use <EMAIL> / demo123'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Sandbox authentication error',
      error: error.message
    });
  }
});

// Sandbox Health Check
router.get('/auth/health', (req, res) => {
  res.json({
    status: 'healthy',
    sandbox: true,
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: 'sandbox',
    version: '1.0.0'
  });
});

// Get sandbox companies
router.get('/companies', (req, res) => {
  res.json({
    success: true,
    data: sandboxData.companies,
    sandbox: true
  });
});

// Get sandbox employees
router.get('/companies/:companyCode/employees', (req, res) => {
  const { companyCode } = req.params;
  const company = sandboxData.companies.find(c => c.companyCode === companyCode);
  
  if (!company) {
    return res.status(404).json({
      success: false,
      message: 'Company not found in sandbox'
    });
  }

  const employees = sandboxData.employees.filter(e => e.company === company.id);
  
  res.json({
    success: true,
    data: employees,
    sandbox: true
  });
});

// Get sandbox payroll periods
router.get('/companies/:companyCode/payroll-periods', (req, res) => {
  const { companyCode } = req.params;
  const company = sandboxData.companies.find(c => c.companyCode === companyCode);
  
  if (!company) {
    return res.status(404).json({
      success: false,
      message: 'Company not found in sandbox'
    });
  }

  const periods = sandboxData.payrollPeriods.filter(p => p.company === company.id);
  
  res.json({
    success: true,
    data: periods,
    sandbox: true
  });
});

// Get sandbox pay runs
router.get('/companies/:companyCode/pay-runs', (req, res) => {
  const { companyCode } = req.params;
  const company = sandboxData.companies.find(c => c.companyCode === companyCode);
  
  if (!company) {
    return res.status(404).json({
      success: false,
      message: 'Company not found in sandbox'
    });
  }

  const payRuns = sandboxData.payRuns.filter(pr => pr.company === company.id);
  
  res.json({
    success: true,
    data: payRuns,
    sandbox: true
  });
});

// Create sandbox pay run
router.post('/companies/:companyCode/pay-runs', (req, res) => {
  const { companyCode } = req.params;
  const { periodIds, description } = req.body;
  
  const company = sandboxData.companies.find(c => c.companyCode === companyCode);
  
  if (!company) {
    return res.status(404).json({
      success: false,
      message: 'Company not found in sandbox'
    });
  }

  const newPayRun = {
    id: `sandbox_payrun_${Date.now()}`,
    company: company.id,
    period: 'February 2025',
    monthYear: '2025-02',
    payRunType: 'regular',
    reference: `PR-2025-02-REGULAR-${sandboxData.payRuns.length + 1}`,
    status: 'draft',
    description: description || 'Sandbox Pay Run',
    payrollPeriods: periodIds || [],
    totals: {
      grossPay: 55000,
      totalDeductions: 10300,
      netPay: 44700
    },
    createdAt: new Date().toISOString()
  };

  sandboxData.payRuns.push(newPayRun);

  res.status(201).json({
    success: true,
    data: newPayRun,
    message: 'Sandbox pay run created successfully',
    sandbox: true
  });
});

// Get sandbox leave types
router.get('/companies/:companyCode/leave-types', (req, res) => {
  const { companyCode } = req.params;
  const company = sandboxData.companies.find(c => c.companyCode === companyCode);
  
  if (!company) {
    return res.status(404).json({
      success: false,
      message: 'Company not found in sandbox'
    });
  }

  const leaveTypes = sandboxData.leaveTypes.filter(lt => lt.company === company.id);
  
  res.json({
    success: true,
    data: leaveTypes,
    sandbox: true
  });
});

// Reset sandbox data
router.post('/reset', (req, res) => {
  // Reset to initial state
  sandboxData.payRuns = sandboxData.payRuns.slice(0, 1); // Keep only the first pay run
  
  res.json({
    success: true,
    message: 'Sandbox data reset to initial state',
    sandbox: true
  });
});

// Get sandbox statistics
router.get('/stats', (req, res) => {
  res.json({
    success: true,
    data: {
      totalCompanies: sandboxData.companies.length,
      totalEmployees: sandboxData.employees.length,
      totalPayrollPeriods: sandboxData.payrollPeriods.length,
      totalPayRuns: sandboxData.payRuns.length,
      totalLeaveTypes: sandboxData.leaveTypes.length
    },
    sandbox: true
  });
});

module.exports = router;
