const express = require('express');
const router = express.Router();
const { apiJwtAuth } = require('../../middleware/jwtAuth');
const PayRun = require('../../models/payRun');
const PayrollPeriod = require('../../models/PayrollPeriod');
const Company = require('../../models/Company');

/**
 * @route GET /api/companies/:company_code/pay-runs
 * @desc Get a list of pay runs for a company
 * @access Private
 */
router.get('/companies/:company_code/pay-runs', apiJwtAuth, async (req, res) => {
  try {
    const { company_code } = req.params;
    const { status, pay_run_type, start_date, end_date } = req.query;

    // Find the company first
    const company = await Company.findOne({
      companyCode: company_code,
      $or: [
        { owner: req.jwtUser.id },
        { users: req.jwtUser.id }
      ]
    });

    if (!company) {
      return res.status(404).json({
        success: false,
        message: 'Company not found'
      });
    }

    // Build query for pay runs
    let query = { company: company._id };

    if (status) {
      query.status = status;
    }

    if (pay_run_type) {
      query.payRunType = pay_run_type;
    }

    if (start_date || end_date) {
      query.startDate = {};
      if (start_date) query.startDate.$gte = new Date(start_date);
      if (end_date) query.startDate.$lte = new Date(end_date);
    }

    const payRuns = await PayRun.find(query)
      .populate('createdBy', 'firstName lastName')
      .populate('approvedBy', 'firstName lastName')
      .sort({ createdAt: -1 });

    const formattedPayRuns = payRuns.map(payRun => ({
      payRun: {
        id: payRun._id,
        company: payRun.company,
        period: payRun.period,
        monthYear: payRun.monthYear,
        payRunType: payRun.payRunType,
        reference: payRun.reference,
        status: payRun.status,
        description: payRun.description,
        startDate: payRun.startDate,
        endDate: payRun.endDate,
        paymentDate: payRun.paymentDate,
        taxPeriod: payRun.taxPeriod,
        payrollPeriods: payRun.payrollPeriods,
        payslips: payRun.payslips,
        totals: payRun.totals,
        createdBy: payRun.createdBy?._id,
        createdByName: payRun.createdBy ? `${payRun.createdBy.firstName} ${payRun.createdBy.lastName}` : null,
        approvedBy: payRun.approvedBy?._id,
        approvedByName: payRun.approvedBy ? `${payRun.approvedBy.firstName} ${payRun.approvedBy.lastName}` : null,
        finalized: payRun.finalized,
        finalizedAt: payRun.finalizedAt,
        createdAt: payRun.createdAt
      }
    }));

    res.json(formattedPayRuns);
  } catch (error) {
    console.error('Error fetching pay runs:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch pay runs',
      error: error.message
    });
  }
});

/**
 * @route POST /api/companies/:company_code/pay-runs
 * @desc Create a new pay run
 * @access Private
 */
router.post('/companies/:company_code/pay-runs', apiJwtAuth, async (req, res) => {
  try {
    const { company_code } = req.params;
    const { periodIds, payRunType, description, releaseToSelfService } = req.body;

    // Find the company first
    const company = await Company.findOne({
      companyCode: company_code,
      $or: [
        { owner: req.jwtUser.id },
        { users: req.jwtUser.id }
      ]
    });

    if (!company) {
      return res.status(404).json({
        success: false,
        message: 'Company not found'
      });
    }

    // Validate that all period IDs exist and belong to this company
    const periods = await PayrollPeriod.find({
      _id: { $in: periodIds },
      company: company._id,
      isFinalized: true
    });

    if (periods.length !== periodIds.length) {
      return res.status(400).json({
        success: false,
        message: 'Some payroll periods not found or not finalized'
      });
    }

    // Calculate totals from periods
    const totals = periods.reduce((acc, period) => {
      acc.grossPay += period.grossPay || 0;
      acc.totalDeductions += period.totalDeductions || 0;
      acc.netPay += period.netPay || 0;
      return acc;
    }, { grossPay: 0, totalDeductions: 0, netPay: 0 });

    // Generate pay run reference
    const currentDate = new Date();
    const monthYear = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}`;
    const existingPayRuns = await PayRun.countDocuments({
      company: company._id,
      monthYear: monthYear,
      payRunType: payRunType || 'regular'
    });
    
    const reference = `PR-${monthYear}-${(payRunType || 'regular').toUpperCase()}-${existingPayRuns + 1}`;

    // Create new pay run
    const newPayRun = new PayRun({
      company: company._id,
      period: `${currentDate.toLocaleString('default', { month: 'long' })} ${currentDate.getFullYear()}`,
      monthYear,
      payRunType: payRunType || 'regular',
      reference,
      status: 'draft',
      description: description || `${currentDate.toLocaleString('default', { month: 'long' })} ${currentDate.getFullYear()} Payroll`,
      payrollPeriods: periodIds,
      totals,
      createdBy: req.jwtUser.id,
      finalized: false
    });

    await newPayRun.save();

    res.status(201).json({
      success: true,
      message: 'Pay run created successfully',
      payRun: {
        id: newPayRun._id,
        reference: newPayRun.reference,
        status: newPayRun.status,
        payslipCount: periodIds.length,
        totals: newPayRun.totals
      }
    });
  } catch (error) {
    console.error('Error creating pay run:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create pay run',
      error: error.message
    });
  }
});

/**
 * @route GET /api/pay-runs/:pay_run_id
 * @desc Get pay run details
 * @access Private
 */
router.get('/:pay_run_id', apiJwtAuth, async (req, res) => {
  try {
    const { pay_run_id } = req.params;

    const payRun = await PayRun.findById(pay_run_id)
      .populate('company', 'name companyCode')
      .populate('createdBy', 'firstName lastName')
      .populate('approvedBy', 'firstName lastName')
      .populate({
        path: 'payrollPeriods',
        populate: {
          path: 'employee',
          select: 'firstName lastName companyEmployeeNumber'
        }
      });

    if (!payRun) {
      return res.status(404).json({
        success: false,
        message: 'Pay run not found'
      });
    }

    // Check if user has access to this pay run's company
    const hasAccess = payRun.company.owner?.toString() === req.jwtUser.id || 
                     payRun.company.users?.includes(req.jwtUser.id);
    
    if (!hasAccess) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    res.json({
      payRun: {
        id: payRun._id,
        company: payRun.company._id,
        companyName: payRun.company.name,
        companyCode: payRun.company.companyCode,
        period: payRun.period,
        monthYear: payRun.monthYear,
        payRunType: payRun.payRunType,
        reference: payRun.reference,
        status: payRun.status,
        description: payRun.description,
        startDate: payRun.startDate,
        endDate: payRun.endDate,
        paymentDate: payRun.paymentDate,
        taxPeriod: payRun.taxPeriod,
        payrollPeriods: payRun.payrollPeriods.map(period => ({
          id: period._id,
          employee: {
            id: period.employee._id,
            name: `${period.employee.firstName} ${period.employee.lastName}`,
            employeeNumber: period.employee.companyEmployeeNumber
          },
          startDate: period.startDate,
          endDate: period.endDate,
          grossPay: period.grossPay,
          netPay: period.netPay
        })),
        payslips: payRun.payslips,
        totals: payRun.totals,
        createdBy: payRun.createdBy?._id,
        createdByName: payRun.createdBy ? `${payRun.createdBy.firstName} ${payRun.createdBy.lastName}` : null,
        approvedBy: payRun.approvedBy?._id,
        approvedByName: payRun.approvedBy ? `${payRun.approvedBy.firstName} ${payRun.approvedBy.lastName}` : null,
        finalized: payRun.finalized,
        finalizedAt: payRun.finalizedAt,
        createdAt: payRun.createdAt
      }
    });
  } catch (error) {
    console.error('Error fetching pay run:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch pay run',
      error: error.message
    });
  }
});

/**
 * @route GET /api/pay-runs/:pay_run_id/eft-file
 * @desc Generate EFT file for a pay run
 * @access Private
 */
router.get('/:pay_run_id/eft-file', apiJwtAuth, async (req, res) => {
  try {
    const { pay_run_id } = req.params;

    const payRun = await PayRun.findById(pay_run_id)
      .populate('company', 'name companyCode');

    if (!payRun) {
      return res.status(404).json({
        success: false,
        message: 'Pay run not found'
      });
    }

    // Check if user has access to this pay run's company
    const hasAccess = payRun.company.owner?.toString() === req.jwtUser.id || 
                     payRun.company.users?.includes(req.jwtUser.id);
    
    if (!hasAccess) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    // Check if pay run is finalized
    if (!payRun.finalized) {
      return res.status(400).json({
        success: false,
        message: 'Pay run must be finalized before generating EFT file'
      });
    }

    // For now, return a message that EFT file generation is not implemented
    // In a real implementation, you would generate and return the EFT file
    res.status(501).json({
      success: false,
      message: 'EFT file generation not yet implemented',
      note: 'This endpoint will generate and download an EFT file when implemented'
    });
  } catch (error) {
    console.error('Error generating EFT file:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate EFT file',
      error: error.message
    });
  }
});

module.exports = router;
