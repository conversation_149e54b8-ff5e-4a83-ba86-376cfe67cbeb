
const express = require('express');
const router = express.Router();
const { apiJwtAuth } = require('../../middleware/jwtAuth');
const PayrollPeriod = require('../../models/PayrollPeriod');
const Payslip = require('../../models/Payslip');
const Employee = require('../../models/Employee');
const Company = require('../../models/Company');
const mongoose = require('mongoose');

/**
 * @route GET /api/payroll/periods/:period_id
 * @desc Get a specific payroll period
 * @access Private
 */
router.get('/periods/:period_id', apiJwtAuth, async (req, res) => {
  try {
    const { period_id } = req.params;

    const payrollPeriod = await PayrollPeriod.findById(period_id)
      .populate('employee', 'firstName lastName companyEmployeeNumber')
      .populate('company', 'name companyCode');

    if (!payrollPeriod) {
      return res.status(404).json({
        success: false,
        message: 'Payroll period not found'
      });
    }

    // Check if user has access to this payroll period's company
    const hasAccess = payrollPeriod.company.owner?.toString() === req.jwtUser.id ||
                     payrollPeriod.company.users?.includes(req.jwtUser.id);

    if (!hasAccess) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    res.json({
      payrollPeriod: {
        id: payrollPeriod._id,
        company: payrollPeriod.company._id,
        employee: payrollPeriod.employee._id,
        employeeName: `${payrollPeriod.employee.firstName} ${payrollPeriod.employee.lastName}`,
        employeeNumber: payrollPeriod.employee.companyEmployeeNumber,
        startDate: payrollPeriod.startDate,
        endDate: payrollPeriod.endDate,
        status: payrollPeriod.status,
        isFinalized: payrollPeriod.isFinalized,
        basicSalary: payrollPeriod.basicSalary,
        grossPay: payrollPeriod.grossPay,
        totalDeductions: payrollPeriod.totalDeductions,
        netPay: payrollPeriod.netPay,
        PAYE: payrollPeriod.PAYE,
        UIF: payrollPeriod.UIF,
        SDL: payrollPeriod.SDL,
        calculations: {
          payeRate: payrollPeriod.payeRate || 0.18,
          uifRate: payrollPeriod.uifRate || 0.01,
          sdlRate: payrollPeriod.sdlRate || 0.01
        },
        finalizedAt: payrollPeriod.finalizedAt,
        finalizedBy: payrollPeriod.finalizedBy
      }
    });
  } catch (error) {
    console.error('Error fetching payroll period:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch payroll period',
      error: error.message
    });
  }
});

/**
 * @route POST /api/payroll/finalize
 * @desc Finalize payroll periods
 * @access Private
 */
router.post('/finalize', apiJwtAuth, async (req, res) => {
  const session = await mongoose.startSession();
  session.startTransaction();

  try {
    const { periodIds } = req.body;

    if (!periodIds || !Array.isArray(periodIds) || periodIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Period IDs are required'
      });
    }

    // Find all payroll periods
    const payrollPeriods = await PayrollPeriod.find({
      _id: { $in: periodIds }
    }).populate('company').session(session);

    if (payrollPeriods.length !== periodIds.length) {
      await session.abortTransaction();
      return res.status(404).json({
        success: false,
        message: 'Some payroll periods not found'
      });
    }

    // Check access to all companies
    for (const period of payrollPeriods) {
      const hasAccess = period.company.owner?.toString() === req.jwtUser.id ||
                       period.company.users?.includes(req.jwtUser.id);

      if (!hasAccess) {
        await session.abortTransaction();
        return res.status(403).json({
          success: false,
          message: 'Access denied to one or more payroll periods'
        });
      }

      if (period.status === 'finalized') {
        await session.abortTransaction();
        return res.status(400).json({
          success: false,
          message: `Payroll period ${period._id} is already finalized`
        });
      }
    }

    // Finalize all periods
    const finalizedPeriods = [];
    for (const period of payrollPeriods) {
      period.status = 'finalized';
      period.isFinalized = true;
      period.finalizedAt = new Date();
      period.finalizedBy = req.jwtUser.id;

      await period.save({ session });

      finalizedPeriods.push({
        id: period._id,
        status: period.status,
        finalizedAt: period.finalizedAt
      });
    }

    await session.commitTransaction();

    res.json({
      success: true,
      message: 'Payroll periods finalized successfully',
      finalizedCount: finalizedPeriods.length,
      finalizedPeriods
    });

  } catch (error) {
    console.error('Error finalizing payroll periods:', error);
    await session.abortTransaction();
    res.status(500).json({
      success: false,
      message: 'Failed to finalize payroll periods',
      error: error.message
    });
  } finally {
    session.endSession();
  }
});

module.exports = router;
