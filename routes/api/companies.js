const express = require('express');
const router = express.Router();
const { apiJwtAuth } = require('../../middleware/jwtAuth');
const Company = require('../../models/Company');
const Employee = require('../../models/Employee');
const PayrollPeriod = require('../../models/PayrollPeriod');

/**
 * @route GET /api/companies
 * @desc Get a list of companies for the authenticated user
 * @access Private
 */
router.get('/', apiJwtAuth, async (req, res) => {
  try {
    // Get companies where the user is the owner or has access
    const companies = await Company.find({
      $or: [
        { owner: req.jwtUser.id },
        { users: req.jwtUser.id }
      ]
    }).select('name companyCode registrationNumber taxNumber uifNumber sdlNumber address contactDetails');

    const formattedCompanies = companies.map(company => ({
      company: {
        id: company._id,
        name: company.name,
        companyCode: company.companyCode,
        registrationNumber: company.registrationNumber,
        taxNumber: company.taxNumber,
        uifNumber: company.uifNumber,
        sdlNumber: company.sdlNumber,
        address: company.address,
        contactDetails: company.contactDetails
      }
    }));

    res.json(formattedCompanies);
  } catch (error) {
    console.error('Error fetching companies:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch companies',
      error: error.message
    });
  }
});

/**
 * @route GET /api/companies/:company_code
 * @desc Get a specific company by company code
 * @access Private
 */
router.get('/:company_code', apiJwtAuth, async (req, res) => {
  try {
    const { company_code } = req.params;

    const company = await Company.findOne({
      companyCode: company_code,
      $or: [
        { owner: req.jwtUser.id },
        { users: req.jwtUser.id }
      ]
    }).select('name companyCode registrationNumber taxNumber uifNumber sdlNumber address contactDetails');

    if (!company) {
      return res.status(404).json({
        success: false,
        message: 'Company not found'
      });
    }

    res.json({
      company: {
        id: company._id,
        name: company.name,
        companyCode: company.companyCode,
        registrationNumber: company.registrationNumber,
        taxNumber: company.taxNumber,
        uifNumber: company.uifNumber,
        sdlNumber: company.sdlNumber,
        address: company.address,
        contactDetails: company.contactDetails
      }
    });
  } catch (error) {
    console.error('Error fetching company:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch company',
      error: error.message
    });
  }
});

/**
 * @route GET /api/companies/:company_code/employees
 * @desc Get a list of employees for a specific company
 * @access Private
 */
router.get('/:company_code/employees', apiJwtAuth, async (req, res) => {
  try {
    const { company_code } = req.params;
    const { include } = req.query;

    // Find the company first
    const company = await Company.findOne({
      companyCode: company_code,
      $or: [
        { owner: req.jwtUser.id },
        { users: req.jwtUser.id }
      ]
    });

    if (!company) {
      return res.status(404).json({
        success: false,
        message: 'Company not found'
      });
    }

    // Build the query for employees
    let employeeQuery = Employee.find({ company: company._id });

    // Handle include parameter
    const includeEntities = include ? include.split(',') : [];
    
    if (includeEntities.includes('bank_details')) {
      employeeQuery = employeeQuery.select('+bankDetails');
    }

    const employees = await employeeQuery.exec();

    // Format employees for API response
    const formattedEmployees = await Promise.all(employees.map(async (employee) => {
      const employeeData = {
        id: employee._id,
        globalEmployeeId: employee.globalEmployeeId,
        companyEmployeeNumber: employee.companyEmployeeNumber,
        firstName: employee.firstName,
        lastName: employee.lastName,
        email: employee.email,
        dob: employee.dob,
        doa: employee.doa,
        basicSalary: employee.basicSalary,
        status: employee.status,
        idType: employee.idType,
        idNumber: employee.idNumber,
        paymentMethod: employee.paymentMethod,
        personalDetails: employee.personalDetails,
      };

      // Include bank details if requested
      if (includeEntities.includes('bank_details') && employee.bankDetails) {
        employeeData.bankDetails = employee.bankDetails;
      }

      // Include recent payroll periods if requested
      if (includeEntities.includes('payroll_periods')) {
        const recentPeriods = await PayrollPeriod.find({
          employee: employee._id
        }).sort({ startDate: -1 }).limit(3);
        
        employeeData.payrollPeriods = recentPeriods;
      }

      // Include leave balances if requested
      if (includeEntities.includes('leave_balances')) {
        // This would need to be implemented based on your leave balance calculation logic
        employeeData.leaveBalances = {
          annual_leave: 15.5,
          sick_leave: 28,
          family_responsibility: 3,
          maternity_leave: 0
        };
      }

      return { employee: employeeData };
    }));

    res.json(formattedEmployees);
  } catch (error) {
    console.error('Error fetching employees:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch employees',
      error: error.message
    });
  }
});

/**
 * @route POST /api/companies/:company_code/employees
 * @desc Create a new employee for a specific company
 * @access Private
 */
router.post('/:company_code/employees', apiJwtAuth, async (req, res) => {
  try {
    const { company_code } = req.params;
    const { employee: employeeData } = req.body;

    // Find the company first
    const company = await Company.findOne({
      companyCode: company_code,
      $or: [
        { owner: req.jwtUser.id },
        { users: req.jwtUser.id }
      ]
    });

    if (!company) {
      return res.status(404).json({
        success: false,
        message: 'Company not found'
      });
    }

    // Check if employee number already exists
    const existingEmployee = await Employee.findOne({
      company: company._id,
      companyEmployeeNumber: employeeData.companyEmployeeNumber
    });

    if (existingEmployee) {
      return res.status(400).json({
        success: false,
        message: `Employee number ${employeeData.companyEmployeeNumber} already exists in this company`
      });
    }

    // Create global employee ID
    const globalEmployeeId = `${company.companyCode}-${employeeData.companyEmployeeNumber}`;

    // Create new employee
    const newEmployee = new Employee({
      ...employeeData,
      company: company._id,
      globalEmployeeId,
      status: employeeData.status || 'Active'
    });

    await newEmployee.save();

    res.status(201).json({
      success: true,
      message: 'Employee has been created successfully',
      employee: {
        id: newEmployee._id,
        globalEmployeeId: newEmployee.globalEmployeeId,
        companyEmployeeNumber: newEmployee.companyEmployeeNumber
      }
    });
  } catch (error) {
    console.error('Error creating employee:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create employee',
      error: error.message
    });
  }
});

/**
 * @route GET /api/companies/:company_code/payroll-periods
 * @desc Get payroll periods for a company
 * @access Private
 */
router.get('/:company_code/payroll-periods', apiJwtAuth, async (req, res) => {
  try {
    const { company_code } = req.params;
    const { employee_id, status, start_date, end_date } = req.query;

    // Find the company first
    const company = await Company.findOne({
      companyCode: company_code,
      $or: [
        { owner: req.jwtUser.id },
        { users: req.jwtUser.id }
      ]
    });

    if (!company) {
      return res.status(404).json({
        success: false,
        message: 'Company not found'
      });
    }

    // Build query for payroll periods
    let query = { company: company._id };

    if (employee_id) {
      query.employee = employee_id;
    }

    if (status) {
      query.status = status;
    }

    if (start_date || end_date) {
      query.startDate = {};
      if (start_date) query.startDate.$gte = new Date(start_date);
      if (end_date) query.startDate.$lte = new Date(end_date);
    }

    const payrollPeriods = await PayrollPeriod.find(query)
      .populate('employee', 'firstName lastName companyEmployeeNumber')
      .sort({ startDate: -1 });

    const formattedPeriods = payrollPeriods.map(period => ({
      payrollPeriod: {
        id: period._id,
        company: period.company,
        employee: period.employee._id,
        employeeName: `${period.employee.firstName} ${period.employee.lastName}`,
        employeeNumber: period.employee.companyEmployeeNumber,
        startDate: period.startDate,
        endDate: period.endDate,
        status: period.status,
        isFinalized: period.isFinalized,
        basicSalary: period.basicSalary,
        grossPay: period.grossPay,
        totalDeductions: period.totalDeductions,
        netPay: period.netPay,
        PAYE: period.PAYE,
        UIF: period.UIF,
        SDL: period.SDL,
        finalizedAt: period.finalizedAt,
        finalizedBy: period.finalizedBy
      }
    }));

    res.json(formattedPeriods);
  } catch (error) {
    console.error('Error fetching payroll periods:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch payroll periods',
      error: error.message
    });
  }
});

module.exports = router;
