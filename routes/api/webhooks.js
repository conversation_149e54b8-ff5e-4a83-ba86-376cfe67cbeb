const express = require('express');
const router = express.Router();
const axios = require('axios');
const crypto = require('crypto');

/**
 * Webhook System for External Integrations (Helios.io, etc.)
 */

// Store webhook configurations (in production, this should be in database)
const webhookConfigs = new Map();

/**
 * @route POST /api/webhooks/register
 * @desc Register a webhook endpoint for notifications
 * @access Private
 */
router.post('/register', async (req, res) => {
  try {
    const { 
      name, 
      url, 
      events, 
      secret, 
      companyCode,
      active = true 
    } = req.body;

    if (!name || !url || !events || !Array.isArray(events)) {
      return res.status(400).json({
        success: false,
        message: 'Name, URL, and events array are required'
      });
    }

    const webhookId = crypto.randomUUID();
    
    const webhookConfig = {
      id: webhookId,
      name,
      url,
      events,
      secret,
      companyCode,
      active,
      createdAt: new Date(),
      lastTriggered: null,
      successCount: 0,
      failureCount: 0
    };

    webhookConfigs.set(webhookId, webhookConfig);

    res.json({
      success: true,
      message: 'Webhook registered successfully',
      webhook: {
        id: webhookId,
        name,
        url,
        events,
        active
      }
    });
  } catch (error) {
    console.error('Error registering webhook:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to register webhook',
      error: error.message
    });
  }
});

/**
 * @route GET /api/webhooks
 * @desc Get all registered webhooks
 * @access Private
 */
router.get('/', async (req, res) => {
  try {
    const webhooks = Array.from(webhookConfigs.values()).map(webhook => ({
      id: webhook.id,
      name: webhook.name,
      url: webhook.url,
      events: webhook.events,
      active: webhook.active,
      companyCode: webhook.companyCode,
      createdAt: webhook.createdAt,
      lastTriggered: webhook.lastTriggered,
      successCount: webhook.successCount,
      failureCount: webhook.failureCount
    }));

    res.json({
      success: true,
      webhooks
    });
  } catch (error) {
    console.error('Error fetching webhooks:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch webhooks',
      error: error.message
    });
  }
});

/**
 * @route DELETE /api/webhooks/:webhookId
 * @desc Delete a webhook
 * @access Private
 */
router.delete('/:webhookId', async (req, res) => {
  try {
    const { webhookId } = req.params;

    if (!webhookConfigs.has(webhookId)) {
      return res.status(404).json({
        success: false,
        message: 'Webhook not found'
      });
    }

    webhookConfigs.delete(webhookId);

    res.json({
      success: true,
      message: 'Webhook deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting webhook:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete webhook',
      error: error.message
    });
  }
});

/**
 * Function to trigger webhooks for specific events
 * This function is called from other parts of the application
 */
async function triggerWebhooks(eventType, data, companyCode = null) {
  const relevantWebhooks = Array.from(webhookConfigs.values()).filter(webhook => 
    webhook.active && 
    webhook.events.includes(eventType) &&
    (!companyCode || !webhook.companyCode || webhook.companyCode === companyCode)
  );

  const results = [];

  for (const webhook of relevantWebhooks) {
    try {
      const payload = {
        event: eventType,
        timestamp: new Date().toISOString(),
        data,
        webhook_id: webhook.id
      };

      // Create signature if secret is provided
      let headers = {
        'Content-Type': 'application/json',
        'User-Agent': 'PandaPayroll-Webhook/1.0'
      };

      if (webhook.secret) {
        const signature = crypto
          .createHmac('sha256', webhook.secret)
          .update(JSON.stringify(payload))
          .digest('hex');
        headers['X-PandaPayroll-Signature'] = `sha256=${signature}`;
      }

      const response = await axios.post(webhook.url, payload, {
        headers,
        timeout: 10000 // 10 second timeout
      });

      webhook.lastTriggered = new Date();
      webhook.successCount++;

      results.push({
        webhookId: webhook.id,
        success: true,
        status: response.status,
        response: response.data
      });

      console.log(`✅ Webhook ${webhook.name} triggered successfully for event ${eventType}`);
    } catch (error) {
      webhook.failureCount++;
      
      results.push({
        webhookId: webhook.id,
        success: false,
        error: error.message,
        status: error.response?.status
      });

      console.error(`❌ Webhook ${webhook.name} failed for event ${eventType}:`, error.message);
    }
  }

  return results;
}

/**
 * @route POST /api/webhooks/test/:webhookId
 * @desc Test a specific webhook
 * @access Private
 */
router.post('/test/:webhookId', async (req, res) => {
  try {
    const { webhookId } = req.params;

    const webhook = webhookConfigs.get(webhookId);
    if (!webhook) {
      return res.status(404).json({
        success: false,
        message: 'Webhook not found'
      });
    }

    const testPayload = {
      event: 'webhook.test',
      timestamp: new Date().toISOString(),
      data: {
        message: 'This is a test webhook from PandaPayroll',
        webhook_id: webhookId
      },
      webhook_id: webhookId
    };

    let headers = {
      'Content-Type': 'application/json',
      'User-Agent': 'PandaPayroll-Webhook/1.0'
    };

    if (webhook.secret) {
      const signature = crypto
        .createHmac('sha256', webhook.secret)
        .update(JSON.stringify(testPayload))
        .digest('hex');
      headers['X-PandaPayroll-Signature'] = `sha256=${signature}`;
    }

    const response = await axios.post(webhook.url, testPayload, {
      headers,
      timeout: 10000
    });

    res.json({
      success: true,
      message: 'Webhook test successful',
      response: {
        status: response.status,
        data: response.data
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Webhook test failed',
      error: error.message,
      status: error.response?.status
    });
  }
});

// Export the triggerWebhooks function for use in other modules
module.exports = router;
module.exports.triggerWebhooks = triggerWebhooks;

// Available webhook events for Helios integration:
module.exports.WEBHOOK_EVENTS = {
  PAYSLIP_FINALIZED: 'payslip.finalized',
  PAYROLL_CALCULATED: 'payroll.calculated',
  PAY_RUN_CREATED: 'payrun.created',
  PAY_RUN_FINALIZED: 'payrun.finalized',
  EMPLOYEE_CREATED: 'employee.created',
  EMPLOYEE_UPDATED: 'employee.updated',
  EFT_FILE_GENERATED: 'eft.file_generated'
};
