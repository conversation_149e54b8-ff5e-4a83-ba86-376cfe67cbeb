const express = require('express');
const router = express.Router();
const { apiJwtAuth } = require('../../middleware/jwtAuth');
const Payslip = require('../../models/Payslip');
const PayrollPeriod = require('../../models/PayrollPeriod');
const Employee = require('../../models/Employee');
const Company = require('../../models/Company');

/**
 * @route GET /api/employees/:employee_id/payslips
 * @desc Get a list of payslips for an employee
 * @access Private
 */
router.get('/employees/:employee_id/payslips', apiJwtAuth, async (req, res) => {
  try {
    const { employee_id } = req.params;
    const { status, start_date, end_date, pay_run_id } = req.query;

    // Verify employee exists and user has access
    const employee = await Employee.findById(employee_id).populate('company');
    if (!employee) {
      return res.status(404).json({
        success: false,
        message: 'Employee not found'
      });
    }

    // Check if user has access to this employee's company
    const hasAccess = employee.company.owner.toString() === req.jwtUser.id || 
                     employee.company.users?.includes(req.jwtUser.id);
    
    if (!hasAccess) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    // Build query for payslips
    let query = { employee: employee_id };

    if (status) {
      query.status = status;
    }

    if (pay_run_id) {
      query.payRun = pay_run_id;
    }

    if (start_date || end_date) {
      // Get payroll periods within date range
      let periodQuery = { employee: employee_id };
      if (start_date || end_date) {
        periodQuery.startDate = {};
        if (start_date) periodQuery.startDate.$gte = new Date(start_date);
        if (end_date) periodQuery.startDate.$lte = new Date(end_date);
      }
      
      const periods = await PayrollPeriod.find(periodQuery).select('_id');
      query.payrollPeriod = { $in: periods.map(p => p._id) };
    }

    const payslips = await Payslip.find(query)
      .populate('payrollPeriod', 'startDate endDate')
      .populate('payRun', 'reference period')
      .sort({ createdAt: -1 });

    const formattedPayslips = payslips.map(payslip => ({
      payslip: {
        id: payslip._id,
        payRun: payslip.payRun?._id,
        payRunReference: payslip.payRun?.reference,
        payrollPeriod: payslip.payrollPeriod._id,
        periodStartDate: payslip.payrollPeriod.startDate,
        periodEndDate: payslip.payrollPeriod.endDate,
        employee: payslip.employee,
        status: payslip.status,
        grossPay: payslip.grossPay,
        totalDeductions: payslip.totalDeductions,
        netPay: payslip.netPay,
        paymentMethod: payslip.paymentMethod,
        earnings: payslip.earnings,
        deductions: payslip.deductions,
        bankDetails: payslip.bankDetails,
        createdAt: payslip.createdAt,
        finalizedAt: payslip.finalizedAt
      }
    }));

    res.json(formattedPayslips);
  } catch (error) {
    console.error('Error fetching payslips:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch payslips',
      error: error.message
    });
  }
});

/**
 * @route GET /api/payslips/:payslip_id
 * @desc Get a specific payslip
 * @access Private
 */
router.get('/:payslip_id', apiJwtAuth, async (req, res) => {
  try {
    const { payslip_id } = req.params;

    const payslip = await Payslip.findById(payslip_id)
      .populate('payrollPeriod', 'startDate endDate')
      .populate('payRun', 'reference period')
      .populate('employee', 'firstName lastName companyEmployeeNumber')
      .populate('company', 'name companyCode');

    if (!payslip) {
      return res.status(404).json({
        success: false,
        message: 'Payslip not found'
      });
    }

    // Check if user has access to this payslip's company
    const hasAccess = payslip.company.owner?.toString() === req.jwtUser.id || 
                     payslip.company.users?.includes(req.jwtUser.id);
    
    if (!hasAccess) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    res.json({
      payslip: {
        id: payslip._id,
        payRun: payslip.payRun?._id,
        payRunReference: payslip.payRun?.reference,
        payrollPeriod: payslip.payrollPeriod._id,
        periodStartDate: payslip.payrollPeriod.startDate,
        periodEndDate: payslip.payrollPeriod.endDate,
        employee: payslip.employee._id,
        employeeName: `${payslip.employee.firstName} ${payslip.employee.lastName}`,
        employeeNumber: payslip.employee.companyEmployeeNumber,
        status: payslip.status,
        grossPay: payslip.grossPay,
        totalDeductions: payslip.totalDeductions,
        netPay: payslip.netPay,
        paymentMethod: payslip.paymentMethod,
        earnings: payslip.earnings,
        deductions: payslip.deductions,
        bankDetails: payslip.bankDetails,
        company: payslip.company._id,
        companyName: payslip.company.name,
        companyCode: payslip.company.companyCode,
        createdAt: payslip.createdAt,
        finalizedAt: payslip.finalizedAt
      }
    });
  } catch (error) {
    console.error('Error fetching payslip:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch payslip',
      error: error.message
    });
  }
});

/**
 * @route POST /api/companies/:company_code/payslip/:payslip_id/finalize
 * @desc Finalize a payslip
 * @access Private
 */
router.post('/companies/:company_code/payslip/:payslip_id/finalize', apiJwtAuth, async (req, res) => {
  try {
    const { company_code, payslip_id } = req.params;

    // Find the company first
    const company = await Company.findOne({
      companyCode: company_code,
      $or: [
        { owner: req.jwtUser.id },
        { users: req.jwtUser.id }
      ]
    });

    if (!company) {
      return res.status(404).json({
        success: false,
        message: 'Company not found'
      });
    }

    // Find the payslip
    const payslip = await Payslip.findOne({
      _id: payslip_id,
      company: company._id
    });

    if (!payslip) {
      return res.status(404).json({
        success: false,
        message: 'Payslip not found'
      });
    }

    // Check if already finalized
    if (payslip.status === 'finalized') {
      return res.status(400).json({
        success: false,
        message: 'Payslip is already finalized'
      });
    }

    // Finalize the payslip
    payslip.status = 'finalized';
    payslip.finalizedAt = new Date();
    payslip.finalizedBy = req.jwtUser.id;

    await payslip.save();

    res.json({
      success: true,
      message: 'Payslip finalized successfully',
      payslip: {
        id: payslip._id,
        status: payslip.status,
        finalizedAt: payslip.finalizedAt
      }
    });
  } catch (error) {
    console.error('Error finalizing payslip:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to finalize payslip',
      error: error.message
    });
  }
});

/**
 * @route GET /api/payslips/:payslip_id/pdf
 * @desc Download payslip as PDF
 * @access Private
 */
router.get('/:payslip_id/pdf', apiJwtAuth, async (req, res) => {
  try {
    const { payslip_id } = req.params;

    const payslip = await Payslip.findById(payslip_id)
      .populate('employee', 'firstName lastName companyEmployeeNumber')
      .populate('company', 'name companyCode');

    if (!payslip) {
      return res.status(404).json({
        success: false,
        message: 'Payslip not found'
      });
    }

    // Check if user has access to this payslip's company
    const hasAccess = payslip.company.owner?.toString() === req.jwtUser.id || 
                     payslip.company.users?.includes(req.jwtUser.id);
    
    if (!hasAccess) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    // For now, return a message that PDF generation is not implemented
    // In a real implementation, you would generate and return the PDF file
    res.status(501).json({
      success: false,
      message: 'PDF generation not yet implemented',
      note: 'This endpoint will generate and download a PDF payslip when implemented'
    });
  } catch (error) {
    console.error('Error generating payslip PDF:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate payslip PDF',
      error: error.message
    });
  }
});

module.exports = router;
