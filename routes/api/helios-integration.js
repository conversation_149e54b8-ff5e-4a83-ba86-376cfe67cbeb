const express = require('express');
const router = express.Router();
const { apiJwtAuth } = require('../../middleware/jwtAuth');
const Employee = require('../../models/Employee');
const PayrollPeriod = require('../../models/PayrollPeriod');
const Company = require('../../models/Company');
const mongoose = require('mongoose');

/**
 * Helios.io Integration API Endpoints
 * These endpoints are specifically designed for Helios integration
 */

/**
 * @route POST /api/helios/employees/batch
 * @desc Batch create/update employees from Helios
 * @access Private
 */
router.post('/employees/batch', apiJwtAuth, async (req, res) => {
  try {
    const { employees, companyCode } = req.body;

    // Find the company
    const company = await Company.findOne({
      companyCode,
      $or: [
        { owner: req.jwtUser.id },
        { users: req.jwtUser.id }
      ]
    });

    if (!company) {
      return res.status(404).json({
        success: false,
        message: 'Company not found'
      });
    }

    const results = {
      created: [],
      updated: [],
      errors: []
    };

    for (const employeeData of employees) {
      try {
        // Check if employee exists
        const existingEmployee = await Employee.findOne({
          company: company._id,
          $or: [
            { companyEmployeeNumber: employeeData.companyEmployeeNumber },
            { email: employeeData.email }
          ]
        });

        if (existingEmployee) {
          // Update existing employee
          Object.assign(existingEmployee, employeeData);
          await existingEmployee.save();
          results.updated.push({
            id: existingEmployee._id,
            employeeNumber: existingEmployee.companyEmployeeNumber
          });
        } else {
          // Create new employee
          const globalEmployeeId = `${company.companyCode}-${employeeData.companyEmployeeNumber}`;
          const newEmployee = new Employee({
            ...employeeData,
            company: company._id,
            globalEmployeeId,
            status: employeeData.status || 'Active'
          });
          
          await newEmployee.save();
          results.created.push({
            id: newEmployee._id,
            employeeNumber: newEmployee.companyEmployeeNumber
          });
        }
      } catch (error) {
        results.errors.push({
          employeeNumber: employeeData.companyEmployeeNumber,
          error: error.message
        });
      }
    }

    res.json({
      success: true,
      message: 'Batch employee operation completed',
      results
    });
  } catch (error) {
    console.error('Error in batch employee operation:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to process batch employee operation',
      error: error.message
    });
  }
});

/**
 * @route POST /api/helios/payroll/inputs
 * @desc Submit payroll inputs from Helios (hours, bonuses, etc.)
 * @access Private
 */
router.post('/payroll/inputs', apiJwtAuth, async (req, res) => {
  try {
    const { companyCode, payrollInputs } = req.body;

    // Find the company
    const company = await Company.findOne({
      companyCode,
      $or: [
        { owner: req.jwtUser.id },
        { users: req.jwtUser.id }
      ]
    });

    if (!company) {
      return res.status(404).json({
        success: false,
        message: 'Company not found'
      });
    }

    const results = {
      processed: [],
      errors: []
    };

    for (const input of payrollInputs) {
      try {
        const { employeeNumber, periodStart, periodEnd, inputs } = input;

        // Find employee
        const employee = await Employee.findOne({
          company: company._id,
          companyEmployeeNumber: employeeNumber
        });

        if (!employee) {
          results.errors.push({
            employeeNumber,
            error: 'Employee not found'
          });
          continue;
        }

        // Find or create payroll period
        let payrollPeriod = await PayrollPeriod.findOne({
          employee: employee._id,
          startDate: new Date(periodStart),
          endDate: new Date(periodEnd)
        });

        if (!payrollPeriod) {
          payrollPeriod = new PayrollPeriod({
            employee: employee._id,
            company: company._id,
            startDate: new Date(periodStart),
            endDate: new Date(periodEnd),
            basicSalary: employee.basicSalary,
            status: 'processing'
          });
        }

        // Apply inputs
        if (inputs.hours) {
          payrollPeriod.hoursWorked = inputs.hours;
        }
        if (inputs.overtime) {
          payrollPeriod.overtimeHours = inputs.overtime;
        }
        if (inputs.bonuses) {
          payrollPeriod.bonuses = inputs.bonuses;
        }
        if (inputs.deductions) {
          payrollPeriod.additionalDeductions = inputs.deductions;
        }
        if (inputs.allowances) {
          payrollPeriod.allowances = inputs.allowances;
        }

        await payrollPeriod.save();

        results.processed.push({
          employeeNumber,
          periodId: payrollPeriod._id
        });
      } catch (error) {
        results.errors.push({
          employeeNumber: input.employeeNumber,
          error: error.message
        });
      }
    }

    res.json({
      success: true,
      message: 'Payroll inputs processed',
      results
    });
  } catch (error) {
    console.error('Error processing payroll inputs:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to process payroll inputs',
      error: error.message
    });
  }
});

/**
 * @route POST /api/helios/payroll/calculate
 * @desc Calculate gross-to-net for payroll periods
 * @access Private
 */
router.post('/payroll/calculate', apiJwtAuth, async (req, res) => {
  try {
    const { periodIds } = req.body;

    const calculations = [];

    for (const periodId of periodIds) {
      const period = await PayrollPeriod.findById(periodId)
        .populate('employee', 'firstName lastName companyEmployeeNumber')
        .populate('company', 'name companyCode');

      if (!period) {
        continue;
      }

      // Check access
      const hasAccess = period.company.owner?.toString() === req.jwtUser.id || 
                       period.company.users?.includes(req.jwtUser.id);
      
      if (!hasAccess) {
        continue;
      }

      // Perform gross-to-net calculation
      const grossPay = period.basicSalary + (period.bonuses || 0) + (period.allowances || 0);
      const paye = grossPay * 0.18; // Simplified PAYE calculation
      const uif = Math.min(grossPay * 0.01, 177.12); // UIF cap
      const sdl = grossPay * 0.01; // SDL for employer
      const totalDeductions = paye + uif + (period.additionalDeductions || 0);
      const netPay = grossPay - totalDeductions;

      // Update period with calculations
      period.grossPay = grossPay;
      period.PAYE = paye;
      period.UIF = uif;
      period.SDL = sdl;
      period.totalDeductions = totalDeductions;
      period.netPay = netPay;
      period.status = 'calculated';

      await period.save();

      calculations.push({
        periodId: period._id,
        employee: {
          id: period.employee._id,
          name: `${period.employee.firstName} ${period.employee.lastName}`,
          employeeNumber: period.employee.companyEmployeeNumber
        },
        grossPay,
        deductions: {
          paye,
          uif,
          additional: period.additionalDeductions || 0
        },
        employerContributions: {
          uif,
          sdl
        },
        netPay
      });
    }

    res.json({
      success: true,
      message: 'Payroll calculations completed',
      calculations
    });
  } catch (error) {
    console.error('Error calculating payroll:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to calculate payroll',
      error: error.message
    });
  }
});

/**
 * @route GET /api/helios/payroll/results/:periodId
 * @desc Get detailed payroll results for Helios
 * @access Private
 */
router.get('/payroll/results/:periodId', apiJwtAuth, async (req, res) => {
  try {
    const { periodId } = req.params;

    const period = await PayrollPeriod.findById(periodId)
      .populate('employee', 'firstName lastName companyEmployeeNumber bankDetails')
      .populate('company', 'name companyCode');

    if (!period) {
      return res.status(404).json({
        success: false,
        message: 'Payroll period not found'
      });
    }

    // Check access
    const hasAccess = period.company.owner?.toString() === req.jwtUser.id || 
                     period.company.users?.includes(req.jwtUser.id);
    
    if (!hasAccess) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    res.json({
      success: true,
      payrollResult: {
        periodId: period._id,
        employee: {
          id: period.employee._id,
          name: `${period.employee.firstName} ${period.employee.lastName}`,
          employeeNumber: period.employee.companyEmployeeNumber,
          bankDetails: period.employee.bankDetails
        },
        company: {
          id: period.company._id,
          name: period.company.name,
          code: period.company.companyCode
        },
        period: {
          startDate: period.startDate,
          endDate: period.endDate
        },
        earnings: {
          basicSalary: period.basicSalary,
          bonuses: period.bonuses || 0,
          allowances: period.allowances || 0,
          grossPay: period.grossPay
        },
        deductions: {
          paye: period.PAYE,
          uif: period.UIF,
          additional: period.additionalDeductions || 0,
          total: period.totalDeductions
        },
        employerContributions: {
          uif: period.UIF,
          sdl: period.SDL,
          total: (period.UIF || 0) + (period.SDL || 0)
        },
        netPay: period.netPay,
        status: period.status,
        finalizedAt: period.finalizedAt
      }
    });
  } catch (error) {
    console.error('Error fetching payroll results:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch payroll results',
      error: error.message
    });
  }
});

/**
 * @route PUT /api/helios/companies/:company_code/cost-centers
 * @desc Update cost center mappings
 * @access Private
 */
router.put('/companies/:company_code/cost-centers', apiJwtAuth, async (req, res) => {
  try {
    const { company_code } = req.params;
    const { costCenters } = req.body;

    // Find the company
    const company = await Company.findOne({
      companyCode: company_code,
      $or: [
        { owner: req.jwtUser.id },
        { users: req.jwtUser.id }
      ]
    });

    if (!company) {
      return res.status(404).json({
        success: false,
        message: 'Company not found'
      });
    }

    // Update cost centers (in a real implementation, you'd have a CostCenter model)
    // For now, we'll store it in the company document
    company.costCenters = costCenters;
    await company.save();

    res.json({
      success: true,
      message: 'Cost centers updated successfully',
      costCenters: company.costCenters
    });
  } catch (error) {
    console.error('Error updating cost centers:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update cost centers',
      error: error.message
    });
  }
});

/**
 * @route GET /api/helios/companies/:company_code/cost-centers
 * @desc Get cost center lookup data
 * @access Private
 */
router.get('/companies/:company_code/cost-centers', apiJwtAuth, async (req, res) => {
  try {
    const { company_code } = req.params;

    // Find the company
    const company = await Company.findOne({
      companyCode: company_code,
      $or: [
        { owner: req.jwtUser.id },
        { users: req.jwtUser.id }
      ]
    });

    if (!company) {
      return res.status(404).json({
        success: false,
        message: 'Company not found'
      });
    }

    res.json({
      success: true,
      costCenters: company.costCenters || []
    });
  } catch (error) {
    console.error('Error fetching cost centers:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch cost centers',
      error: error.message
    });
  }
});

module.exports = router;
