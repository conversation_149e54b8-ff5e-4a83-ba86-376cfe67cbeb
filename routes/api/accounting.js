const express = require('express');
const router = express.Router();
const { apiJwtAuth } = require('../../middleware/jwtAuth');
const PayRun = require('../../models/payRun');
const PayrollPeriod = require('../../models/PayrollPeriod');
const Company = require('../../models/Company');

/**
 * @route GET /api/accounting/journals/:pay_run_id
 * @desc Get general ledger entries for a pay run
 * @access Private
 */
router.get('/journals/:pay_run_id', apiJwtAuth, async (req, res) => {
  try {
    const { pay_run_id } = req.params;

    const payRun = await PayRun.findById(pay_run_id)
      .populate('company', 'name companyCode costCenters')
      .populate({
        path: 'payrollPeriods',
        populate: {
          path: 'employee',
          select: 'firstName lastName companyEmployeeNumber costCenter'
        }
      });

    if (!payRun) {
      return res.status(404).json({
        success: false,
        message: 'Pay run not found'
      });
    }

    // Check if user has access to this pay run's company
    const hasAccess = payRun.company.owner?.toString() === req.jwtUser.id || 
                     payRun.company.users?.includes(req.jwtUser.id);
    
    if (!hasAccess) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    // Generate journal entries
    const journals = [];
    const costCenterTotals = {};
    let totalSalaries = 0;
    let totalPAYE = 0;
    let totalUIF = 0;
    let totalSDL = 0;
    let totalNetPay = 0;

    // Calculate totals by cost center
    for (const period of payRun.payrollPeriods) {
      const costCenter = period.employee.costCenter || 'DEFAULT';
      
      if (!costCenterTotals[costCenter]) {
        costCenterTotals[costCenter] = {
          grossPay: 0,
          paye: 0,
          uif: 0,
          sdl: 0,
          netPay: 0
        };
      }

      costCenterTotals[costCenter].grossPay += period.grossPay || 0;
      costCenterTotals[costCenter].paye += period.PAYE || 0;
      costCenterTotals[costCenter].uif += period.UIF || 0;
      costCenterTotals[costCenter].sdl += period.SDL || 0;
      costCenterTotals[costCenter].netPay += period.netPay || 0;

      totalSalaries += period.grossPay || 0;
      totalPAYE += period.PAYE || 0;
      totalUIF += period.UIF || 0;
      totalSDL += period.SDL || 0;
      totalNetPay += period.netPay || 0;
    }

    // Create journal entries for each cost center
    for (const [costCenter, totals] of Object.entries(costCenterTotals)) {
      const costCenterInfo = payRun.company.costCenters?.find(cc => cc.code === costCenter);
      const glAccount = costCenterInfo?.glAccount || '6000-000';
      const costCenterName = costCenterInfo?.name || 'General';

      // Salary expense entry
      journals.push({
        account: glAccount,
        description: `Salaries - ${costCenterName}`,
        debit: Math.round(totals.grossPay * 100) / 100,
        credit: 0,
        costCenter: costCenter,
        reference: payRun.reference
      });
    }

    // PAYE Payable
    if (totalPAYE > 0) {
      journals.push({
        account: '2000-001',
        description: 'PAYE Payable',
        debit: 0,
        credit: Math.round(totalPAYE * 100) / 100,
        costCenter: null,
        reference: payRun.reference
      });
    }

    // UIF Payable (Employee + Employer)
    if (totalUIF > 0) {
      journals.push({
        account: '2000-002',
        description: 'UIF Payable - Employee',
        debit: 0,
        credit: Math.round(totalUIF * 100) / 100,
        costCenter: null,
        reference: payRun.reference
      });

      journals.push({
        account: '2000-002',
        description: 'UIF Payable - Employer',
        debit: 0,
        credit: Math.round(totalUIF * 100) / 100,
        costCenter: null,
        reference: payRun.reference
      });

      // UIF Employer Expense
      journals.push({
        account: '6100-001',
        description: 'UIF Employer Contribution',
        debit: Math.round(totalUIF * 100) / 100,
        credit: 0,
        costCenter: null,
        reference: payRun.reference
      });
    }

    // SDL Payable (Employer only)
    if (totalSDL > 0) {
      journals.push({
        account: '2000-003',
        description: 'SDL Payable',
        debit: 0,
        credit: Math.round(totalSDL * 100) / 100,
        costCenter: null,
        reference: payRun.reference
      });

      // SDL Employer Expense
      journals.push({
        account: '6100-002',
        description: 'Skills Development Levy',
        debit: Math.round(totalSDL * 100) / 100,
        credit: 0,
        costCenter: null,
        reference: payRun.reference
      });
    }

    // Net Pay Payable
    if (totalNetPay > 0) {
      journals.push({
        account: '2000-004',
        description: 'Salaries Payable',
        debit: 0,
        credit: Math.round(totalNetPay * 100) / 100,
        costCenter: null,
        reference: payRun.reference
      });
    }

    // Calculate totals for verification
    const totalDebits = journals.reduce((sum, entry) => sum + entry.debit, 0);
    const totalCredits = journals.reduce((sum, entry) => sum + entry.credit, 0);

    res.json({
      success: true,
      payRun: {
        id: payRun._id,
        reference: payRun.reference,
        period: payRun.period,
        company: payRun.company.name
      },
      journals,
      summary: {
        totalDebits: Math.round(totalDebits * 100) / 100,
        totalCredits: Math.round(totalCredits * 100) / 100,
        balanced: Math.abs(totalDebits - totalCredits) < 0.01,
        employeeCount: payRun.payrollPeriods.length
      }
    });
  } catch (error) {
    console.error('Error generating accounting journals:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate accounting journals',
      error: error.message
    });
  }
});

/**
 * @route GET /api/accounting/chart-of-accounts/:company_code
 * @desc Get chart of accounts for a company
 * @access Private
 */
router.get('/chart-of-accounts/:company_code', apiJwtAuth, async (req, res) => {
  try {
    const { company_code } = req.params;

    // Find the company
    const company = await Company.findOne({
      companyCode: company_code,
      $or: [
        { owner: req.jwtUser.id },
        { users: req.jwtUser.id }
      ]
    });

    if (!company) {
      return res.status(404).json({
        success: false,
        message: 'Company not found'
      });
    }

    // Standard South African payroll chart of accounts
    const chartOfAccounts = [
      // Assets
      { account: '1000-001', name: 'Bank Account - Current', type: 'Asset' },
      
      // Liabilities
      { account: '2000-001', name: 'PAYE Payable', type: 'Liability' },
      { account: '2000-002', name: 'UIF Payable', type: 'Liability' },
      { account: '2000-003', name: 'SDL Payable', type: 'Liability' },
      { account: '2000-004', name: 'Salaries Payable', type: 'Liability' },
      { account: '2000-005', name: 'Pension Fund Payable', type: 'Liability' },
      { account: '2000-006', name: 'Medical Aid Payable', type: 'Liability' },
      
      // Expenses
      { account: '6000-001', name: 'Salaries - Sales', type: 'Expense' },
      { account: '6000-002', name: 'Salaries - Administration', type: 'Expense' },
      { account: '6000-003', name: 'Salaries - Production', type: 'Expense' },
      { account: '6100-001', name: 'UIF Employer Contribution', type: 'Expense' },
      { account: '6100-002', name: 'Skills Development Levy', type: 'Expense' },
      { account: '6100-003', name: 'Pension Fund Employer Contribution', type: 'Expense' },
      { account: '6100-004', name: 'Medical Aid Employer Contribution', type: 'Expense' }
    ];

    res.json({
      success: true,
      company: {
        code: company.companyCode,
        name: company.name
      },
      chartOfAccounts
    });
  } catch (error) {
    console.error('Error fetching chart of accounts:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch chart of accounts',
      error: error.message
    });
  }
});

/**
 * @route POST /api/accounting/export/:pay_run_id
 * @desc Export accounting data in various formats
 * @access Private
 */
router.post('/export/:pay_run_id', apiJwtAuth, async (req, res) => {
  try {
    const { pay_run_id } = req.params;
    const { format = 'json' } = req.body; // json, csv, xml

    // Get the journals first
    const journalsResponse = await router.get('/journals/' + pay_run_id);
    
    if (format === 'csv') {
      // Convert to CSV format
      const csvHeader = 'Account,Description,Debit,Credit,Cost Center,Reference\n';
      const csvRows = journalsResponse.journals.map(journal => 
        `${journal.account},"${journal.description}",${journal.debit},${journal.credit},"${journal.costCenter || ''}","${journal.reference}"`
      ).join('\n');
      
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename="journals_${pay_run_id}.csv"`);
      res.send(csvHeader + csvRows);
    } else {
      // Return JSON format
      res.json({
        success: true,
        message: 'Export completed',
        format,
        data: journalsResponse
      });
    }
  } catch (error) {
    console.error('Error exporting accounting data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to export accounting data',
      error: error.message
    });
  }
});

module.exports = router;
