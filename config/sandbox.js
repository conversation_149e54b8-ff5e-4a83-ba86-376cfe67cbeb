// Sandbox Configuration for PandaPayroll API
module.exports = {
  // Sandbox environment settings
  environment: {
    name: 'sandbox',
    version: '1.0.0',
    description: 'PandaPayroll API Sandbox Environment',
    baseUrl: process.env.SANDBOX_BASE_URL || 'http://localhost:3002/sandbox',
    rateLimiting: {
      enabled: true,
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 1000, // Limit each IP to 1000 requests per windowMs
      message: 'Too many requests from this IP in sandbox environment'
    }
  },

  // Default sandbox credentials
  credentials: {
    email: '<EMAIL>',
    password: 'demo123',
    companyCode: 'DEMO001'
  },

  // Sandbox data templates
  templates: {
    company: {
      id: 'sandbox_company_template',
      name: 'Demo Company Ltd',
      companyCode: 'DEMO001',
      registrationNumber: '2023/123456/07',
      taxNumber: 'TAX123456789',
      uifNumber: 'UIF123456',
      sdlNumber: 'SDL123456',
      address: {
        street: '123 Business Street',
        city: 'Cape Town',
        province: 'Western Cape',
        postalCode: '8001',
        country: 'South Africa'
      },
      contactDetails: {
        phone: '+27 21 123 4567',
        email: '<EMAIL>',
        website: 'https://democompany.com'
      }
    },

    employee: {
      template1: {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        basicSalary: 25000,
        dob: '1990-01-15',
        doa: '2024-01-01',
        idType: 'rsa',
        idNumber: '9001150000000',
        paymentMethod: 'EFT',
        status: 'Active'
      },
      template2: {
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        basicSalary: 30000,
        dob: '1985-05-20',
        doa: '2024-02-01',
        idType: 'rsa',
        idNumber: '8505200000000',
        paymentMethod: 'EFT',
        status: 'Active'
      },
      template3: {
        firstName: 'Michael',
        lastName: 'Johnson',
        email: '<EMAIL>',
        basicSalary: 35000,
        dob: '1988-03-10',
        doa: '2024-03-01',
        idType: 'rsa',
        idNumber: '8803100000000',
        paymentMethod: 'EFT',
        status: 'Active'
      }
    },

    payrollPeriod: {
      template: {
        startDate: '2025-01-01',
        endDate: '2025-01-31',
        status: 'finalized',
        isFinalized: true,
        calculations: {
          paye: 0.18, // 18% PAYE rate
          uif: 0.01, // 1% UIF rate
          sdl: 0.01 // 1% SDL rate
        }
      }
    },

    leaveTypes: [
      {
        name: 'Annual Leave',
        category: 'annual',
        daysPerYear: 21,
        paidLeave: true,
        requiresApproval: true,
        description: 'Annual vacation leave as per South African labor law'
      },
      {
        name: 'Sick Leave',
        category: 'sick',
        daysPerYear: 30,
        paidLeave: true,
        requiresApproval: false,
        description: 'Sick leave for medical purposes'
      },
      {
        name: 'Maternity Leave',
        category: 'maternity',
        daysPerYear: 120,
        paidLeave: false,
        requiresApproval: true,
        description: 'Maternity leave as per South African labor law'
      },
      {
        name: 'Family Responsibility Leave',
        category: 'family',
        daysPerYear: 3,
        paidLeave: true,
        requiresApproval: true,
        description: 'Family responsibility leave'
      }
    ]
  },

  // Sandbox limitations and rules
  limitations: {
    maxEmployees: 50,
    maxPayRuns: 20,
    maxPayrollPeriods: 100,
    maxLeaveRequests: 200,
    dataRetentionDays: 30, // Auto-reset sandbox data after 30 days
    allowedOperations: [
      'CREATE_EMPLOYEE',
      'UPDATE_EMPLOYEE',
      'CREATE_PAYROLL',
      'FINALIZE_PAYROLL',
      'CREATE_PAYRUN',
      'CREATE_LEAVE_REQUEST',
      'APPROVE_LEAVE',
      'GENERATE_PAYSLIP',
      'DOWNLOAD_REPORTS'
    ],
    restrictedOperations: [
      'DELETE_COMPANY',
      'PERMANENT_DELETE_EMPLOYEE',
      'MODIFY_TAX_SETTINGS',
      'CHANGE_COMPANY_REGISTRATION'
    ]
  },

  // Mock calculation settings for South African tax
  taxCalculations: {
    paye: {
      brackets: [
        { min: 0, max: 237100, rate: 0.18, rebate: 16425 },
        { min: 237101, max: 370500, rate: 0.26, rebate: 16425 },
        { min: 370501, max: 512800, rate: 0.31, rebate: 16425 },
        { min: 512801, max: 673000, rate: 0.36, rebate: 16425 },
        { min: 673001, max: 857900, rate: 0.39, rebate: 16425 },
        { min: 857901, max: 1817000, rate: 0.41, rebate: 16425 },
        { min: 1817001, max: Infinity, rate: 0.45, rebate: 16425 }
      ]
    },
    uif: {
      rate: 0.01, // 1%
      maxMonthly: 177.12, // Maximum UIF contribution per month
      employeeRate: 0.01,
      employerRate: 0.01
    },
    sdl: {
      rate: 0.01, // 1%
      threshold: 500000, // Annual payroll threshold
      employerOnly: true
    }
  },

  // Sandbox API endpoints configuration
  endpoints: {
    authentication: [
      'POST /sandbox/auth/login',
      'GET /sandbox/auth/health',
      'POST /sandbox/auth/refresh'
    ],
    companies: [
      'GET /sandbox/companies',
      'GET /sandbox/companies/:companyCode'
    ],
    employees: [
      'GET /sandbox/companies/:companyCode/employees',
      'POST /sandbox/companies/:companyCode/employees',
      'GET /sandbox/companies/:companyCode/employees/:employeeId',
      'PUT /sandbox/companies/:companyCode/employees/:employeeId'
    ],
    payroll: [
      'GET /sandbox/companies/:companyCode/payroll-periods',
      'POST /sandbox/companies/:companyCode/payroll-periods',
      'POST /sandbox/companies/:companyCode/payroll/finalize'
    ],
    payRuns: [
      'GET /sandbox/companies/:companyCode/pay-runs',
      'POST /sandbox/companies/:companyCode/pay-runs',
      'GET /sandbox/companies/:companyCode/pay-runs/:payRunId'
    ],
    leave: [
      'GET /sandbox/companies/:companyCode/leave-types',
      'POST /sandbox/companies/:companyCode/leave-types',
      'GET /sandbox/companies/:companyCode/leave-requests',
      'POST /sandbox/companies/:companyCode/leave-requests'
    ],
    utilities: [
      'GET /sandbox/stats',
      'POST /sandbox/reset',
      'GET /sandbox/health'
    ]
  },

  // Response templates for consistent sandbox responses
  responseTemplates: {
    success: {
      success: true,
      sandbox: true,
      timestamp: () => new Date().toISOString()
    },
    error: {
      success: false,
      sandbox: true,
      timestamp: () => new Date().toISOString()
    },
    pagination: {
      page: 1,
      limit: 50,
      total: 0,
      pages: 1
    }
  },

  // Sandbox features and capabilities
  features: {
    realTimeCalculations: true,
    southAfricanCompliance: true,
    multiCurrency: false, // Sandbox only supports ZAR
    integrations: {
      whatsapp: false, // Disabled in sandbox
      email: false, // Disabled in sandbox
      banking: false, // Disabled in sandbox
      accounting: false // Disabled in sandbox
    },
    reporting: {
      payslips: true,
      payRuns: true,
      taxReports: true,
      leaveReports: true,
      complianceReports: true
    }
  },

  // Sandbox documentation and help
  documentation: {
    gettingStarted: '/apiDocumentation#sandbox',
    endpoints: '/apiDocumentation#api-endpoints',
    examples: '/apiDocumentation#examples',
    support: '<EMAIL>'
  }
};
